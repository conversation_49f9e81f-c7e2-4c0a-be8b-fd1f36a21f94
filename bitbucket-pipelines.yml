image: node:16

pipelines:
  branches:
    production:
      - step:
          name: Build
          caches:
            - node
          script:
            - yarn install
            - yarn build
          artifacts:
            - dist/**
      - parallel:
          - step:
              name: Sync
              image: alpine:latest
              deployment: production
              script:
                - apk add --update openssh-client rsync sshpass
                - chmod -R 755 *
                - sshpass -p "$FTP_PASSWORD" rsync -v -e "ssh -p $FTP_PORT -o StrictHostKeyChecking=no" -arz --exclude='.git/' dist/* $FTP_USER@$FTP_SERVER:$FTP_PATH
          - step:
              name: Sync App1
              image: alpine:latest
              deployment: App1
              script:
                - apk add --update openssh-client rsync sshpass
                - chmod -R 755 *
                - sshpass -p "$FTP_PASSWORD" rsync -v -e "ssh -p $FTP_PORT -o StrictHostKeyChecking=no" -arz --exclude='.git/' dist/* $FTP_USER@$FTP_SERVER:$FTP_PATH
          - step:
              name: Sync App2
              image: alpine:latest
              deployment: App2
              script:
                - apk add --update openssh-client rsync sshpass
                - chmod -R 755 *
                - sshpass -p "$FTP_PASSWORD" rsync -v -e "ssh -p $FTP_PORT -o StrictHostKeyChecking=no" -arz --exclude='.git/' dist/* $FTP_USER@$FTP_SERVER:$FTP_PATH

    master:
      - step:
          name: Build
          caches:
            - node
          script:
            - yarn install
            - yarn build-staging
          artifacts:
            - dist/**
      - step:
          name: Sync
          deployment: master
          script:
            - pipe: atlassian/rsync-deploy:0.11.0
              variables:
                USER: $SSH_USER
                SERVER: $SSH_SERVER
                REMOTE_PATH: $SSH_MASTER_PATH
                SSH_PORT: $SSH_PORT
                LOCAL_PATH: 'dist/**'
                EXTRA_ARGS:
                  - "--exclude=.git/"

    test:
      - step:
          name: Build
          caches:
            - node
          script:
            - yarn install
            - yarn build-test
          artifacts:
            - dist/**
      - step:
          name: Sync
          deployment: test
          script:
            - pipe: atlassian/rsync-deploy:0.11.0
              variables:
                USER: $SSH_USER
                SERVER: $SSH_SERVER
                REMOTE_PATH: $SSH_TEST_PATH
                SSH_PORT: $SSH_PORT
                LOCAL_PATH: 'dist/**'
                EXTRA_ARGS:
                  - "--exclude=.git/"
    
    sandbox:
      - step:
          name: Build
          caches:
            - node
          script:
            - yarn install
            - yarn build-sandbox
          artifacts:
            - dist/**
      - step:
          name: Sync
          image: alpine:latest
          deployment: sandbox
          script:
            - apk add --update openssh-client rsync sshpass
            - chmod -R 755 *
            - sshpass -p "$FTP_PASSWORD" rsync -v -e "ssh -p $FTP_PORT -o StrictHostKeyChecking=no" -arz --exclude='.git/' dist/* $FTP_USER@$FTP_SERVER:$FTP_PATH
    beta:
      - step:
          name: Build
          caches:
            - node
          script:
            - yarn install
            - yarn build-beta
          artifacts:
            - dist/**
      - step:
          name: Sync
          image: alpine:latest
          deployment: beta
          script:
            - apk add --update openssh-client rsync sshpass
            - chmod -R 755 *
            - sshpass -p "$FTP_PASSWORD" rsync -v -e "ssh -p $FTP_PORT -o StrictHostKeyChecking=no" -arz --exclude='.git/' dist/* $FTP_USER@$FTP_SERVER:$FTP_PATH            