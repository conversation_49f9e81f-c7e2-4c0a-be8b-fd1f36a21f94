{"name": "vue-base", "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build", "build-test": "vite build --mode test", "build-beta": "vite build --mode beta", "build-test3": "vite build --mode test-3", "build-staging": "vite build --mode staging", "build-sandbox": "vite build --mode sandbox", "pro": "vite --mode pro", "beta": "vite --mode beta", "test": "vite --mode test", "preview": "vite preview"}, "dependencies": {"@canvasjs/vue-charts": "^1.0.1", "@element-plus/icons-vue": "^0.2.4", "@iconify-icons/ri": "^1.2.1", "@vueup/vue-quill": "^1.2.0", "@yzfe/svgicon": "^1.1.0", "@yzfe/vue3-svgicon": "^1.0.1", "apexcharts": "^3.36.0", "axios": "^0.24.0", "chart.js": "^3.7.1", "element-plus": "2.0.5", "firebase": "10.7.2", "is-mobile": "^3.0.0", "jwt-decode": "^3.1.2", "moment": "^2.29.1", "moment-timezone": "^0.5.34", "monent": "^0.0.2-security", "node-sass": "^7.0.0", "nprogress": "^0.2.0", "parse-address": "^1.1.2", "ramda": "^0.27.1", "sass": "^1.45.0", "sass-loader": "^12.4.0", "socket.io-client": "^4.7.2", "tiny-emitter": "^2.1.0", "vue": "^3.2.25", "vue-chart-3": "^3.1.4", "vue-i18n": "9", "vue-router": "4", "vue3-apexcharts": "^1.4.1", "vuedraggable": "^4.1.0", "vuex": "^4.0.2", "xlsx": "^0.18.5"}, "devDependencies": {"@iconify/vue": "^3.1.4", "@vitejs/plugin-vue": "^2.0.0", "autoprefixer": "^10.4.2", "postcss": "^8.4.6", "tailwindcss": "^3.0.18", "vite": "^2.7.2", "vite-plugin-svgicon": "^1.0.0"}, "prettier": {"singleQuote": true, "semi": true}}