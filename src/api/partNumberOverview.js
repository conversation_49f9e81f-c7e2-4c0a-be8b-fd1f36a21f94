import http from '@/utilities/http';

const PATH = '/part-number-overview';

export function fetch(params = {}) {
  return http.get(`${PATH}`, { params });
}

export function history(params = {}, id) {
  return http.get(`${PATH}/history/${id}`, { params });
}

export function exportCsv(params = {}, config) {
  params = new URLSearchParams(params);
  return http.get(`${PATH}/export?${ params }`, config);
}
