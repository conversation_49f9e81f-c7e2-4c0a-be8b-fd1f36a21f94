import http from "@/utilities/http";

export function performanceReportManager(params = {}) {
  return http.get("performance-report/manager", { params });
}
export function performanceReportManagerV2(params = {}) {
  return http.get("performance-report/manager-v2", { params });
}

export function reportDepartmentsForManage(params = {}) {
  return http.get("performance-report/report-departments-for-manager", {
    params,
  });
}

export function topEmployeeOfMonth(params = {}) {
  return http.get("performance-report/top-employees", { params });
}

export function summaryReportForLeader(params) {
  return http.get("performance-report/summary-report-for-leader", { params });
}
export function reportDepartmentForLeader(params) {
  return http.get("performance-report/report-department-for-leader", {
    params,
  });
}
export function reportAllEmployeesForLeader(params) {
  return http.get("performance-report/report-all-employees", { params });
}
export function reportAllEmployeesByTimeForLeader(params) {
  return http.get("performance-report/report-all-employees-by-time", { params });
}
export function fetchEmployeeByDepartment(params) {
  return http.get("performance-report/fetch-employees-by-department", { params });
}
