import http from "@/utilities/http";

export function getList(params = {}) {
    return http.get("/printing-preset-sku", { params });
}

export function getDetail(id) {
    return http.get(`/printing-preset-sku/${id}`);
}

export function create(data = {}) {
    return http.post("/printing-preset-sku", data);
}

export function update(id, data = {}) {
    return http.put(`/printing-preset-sku/${id}`, data);
}

export function destroy(id) {
    return http.delete(`/printing-preset-sku/${id}`);
}
