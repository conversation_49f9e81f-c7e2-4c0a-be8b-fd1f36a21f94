import http from "@/utilities/http";
import { STORAGE_URL , S3_URL } from "@/utilities/constants";

export function list(params = {}) {
  return http.get("/sale-order-item-image", { params });
}

export function exportExcel(params = {}) {
  return http.get("/sale-order-item-image/export", { params });
}

export function add(params = {}) {
  return http.post("/sale-order-item-image", params);
}

export function update(params = {}) {
  return http.put(`/sale-order-item-image/${params.id}`, params);
}

export function destroy(id) {
  return http.delete(`/sale-order-item-image/${id}`);
}

export function getLinkManualMockup(sku, printSide) {
  return `${STORAGE_URL}/mockup/${sku}/${printSide}`;
}

export function getLinkWatermark(orderDate, sku, printSide) {
  return `${S3_URL}/thumb/proof/${orderDate}/${sku}-${printSide}.png`;
}

export function listDownloadError(params = {}) {
  return http.get('/sale-order-item-image/download-error', {params});
}
export function getCountDownloadError(params = {}) {
  return http.get('/sale-order-item-image/get-count-download-error', {params});
}

export function listDetectColorError(params = {}) {
  return http.get('/sale-order-item-image/detect-color-error', {params});
}

export function listManualUploaded(params = {}) {
  return http.get('/sale-order-item-image/manual-uploaded', {params});
}

export function retryDownload(id) {
  return http.post('/sale-order-item-image/retry-download/' + id);
}

export function retryDetectColor(id) {
  return http.post('/sale-order-item-image/retry-detect-color/' + id);
}

export function manualUpload(id, params = {}) {
  return http.post('/sale-order-item-image/manual-upload/' + id, params);
}

export function listConvertMugsError(params = {}) {
  return http.get('/sale-order-item-image/mug/convert-error', {params});
}

export function retryDownloadMug(barcode_id) {
  return http.put('/sale-order-item-image/mug/download/' + barcode_id);
}
export function updateArtFile(params = {}) {
  return http.post('/sale-order-item-image/update-art-file', params, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}
