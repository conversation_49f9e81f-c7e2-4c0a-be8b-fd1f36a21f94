import http from "@/utilities/http";

export function list(params = {}) {
  return http.get("/stock-transfer", { params });
}
export function addNewRequest(params = {}) {
  return http.post("/stock-transfer", params);
}
export function scanBox(params = {}) {
  return http.post("/stock-transfer/scan-box-number", params);
}
export function details(id) {
  return http.get(`/stock-transfer/${id}`);
}
export function generateRequestNumber(params = {}) {
  return http.get("/stock-transfer/request-number", { params });
}
export function fulfill(params = {}) {
  return http.post("/stock-transfer/fulfill", params);
}
export function countTotalPending(params = {}) {
  return http.get("/stock-transfer/count-request-pending", { params });
}
export function saveTemporaryFulfill(params = {}) {
  return http.post("/stock-transfer/save", params);
}