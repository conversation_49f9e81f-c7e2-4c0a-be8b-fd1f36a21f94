import http from "@/utilities/http";

export function list(params = {}) {
  return http.get("/store", { params });
}
export function all(params = {}) {
  return http.get("/store/all", { params });
}
export function add(params = {}) {
  return http.post("/store", params);
}
export function update(params = {}) {
  return http.put(`/store/${params.id}`, params);
}
export function destroy(id) {
  return http.delete(`/store/${id}`);
}
export function addStoreInfo(params = {}) {
  return http.post("/store/store-info", params);
}
export function addStoreShipAddress(params = {}) {
  return http.post("/store/store-ship-address", params);
}
export function addStoreReturnAddress(params = {}) {
  return http.post("/store/store-return-address", params);
}
export function addStoreAccount(params = {}) {
  return http.post("/store/store-info-account", params);
}
export function storeDetail(id) {
  return http.get(`/store/${id}`);
}
export function editStoreInfo(params = {}) {
  return http.put("/store/store-info", params);
}
export function generateToken(id) {
  return http.get(`/store/store-info-account/generate-api-key/${id}`);
}
export function addStoreAutoShipping(params = {}) {
  return http.post("/store/store-info-shipping", params);
}
export function addStoreSlaExpiration(params = {}) {
  return http.post("/store/store-sla-expiration", params);
}


export function clients() {
  return http.get("/clients/all");
}