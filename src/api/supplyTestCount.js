import http from '@/utilities/http';

export function getSupplyTestCountList(params = {}) {
  return http.get('/supply-inventory/test-count/list', { params });
}

export function createSupplyTestCount(params = {}) {
  return http.post('/supply-inventory/test-count/create', params);
}

export function searchSupply(params = {}) {
  return http.get(`/supply/search`, { params });
}

export function fetchSupplies() {
  return http.get(`/supply?without_pagination=true`);
}

export function list(params = {}) {
  return http.get('/supply-inventory/test-count', { params });
}

export function addSupplyTestCount(params = {}) {
  return http.post('/supply-inventory/test-count/complete-test-count', params);
}

export function pauseSupplyTestCount(params = {}) {
  return http.post('/supply-inventory/test-count/pause', params);
}

export function allTestCountPause(params = {}) {
  return http.get('/supply-inventory/test-count/pause', params);
}

export function details(id) {
  return http.get(`/supply-inventory/test-count/detail/${id}`);
}
