import http from "@/utilities/http";

export function getList(params = {}) {
    return http.get("/supply-unit", { params });
}
export function create(data = {}) {
    return http.post("/supply-unit", data);
}
export function update(id, data = {}) {
    return http.put(`/supply-unit/${id}`, data);
}
export function destroy(id) {
    return http.delete(`/supply-unit/${id}`);
}
export function listWeightCubic() {
    return http.get("/supply-unit");
}