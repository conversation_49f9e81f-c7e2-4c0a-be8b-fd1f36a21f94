import http from "@/utilities/http";

export function list(params = {}) {
  return http.get("/vendor", { params });
}
export function add(params = {}) {
  return http.post("/vendor", params);
}
export function details(id) {
  return http.get(`/vendor/${id}`);
}
export function update(params = {}) {
  return http.put(`/vendor/${params.id}`, params);
}
export function destroy(id) {
  return http.delete(`/vendor/${id}`);
}
