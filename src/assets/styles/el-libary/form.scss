.el-form--label-top .el-form-item__label {
  padding: 0;
}
.el-select {
  position: relative;
  &.el-select-managment, &.el-select-full-width {
    width: 100%;
  }
}
.el-select-dropdown__list {
  margin-bottom: 0 !important;
}
.el-select-managment-btn {
  background-color: var(--el-color-primary) !important;
  color: #fff;
  display: flex;
  align-items: center;
  position: sticky;
  left: 0;
  bottom: 0;
  color: #fff !important;
  text-align: center;
  &:hover {
    background-color: var(--el-color-primary) !important;
  }
  svg {
    margin-right: 4px;
    max-height: 12px;
  }
}
.el-input-number {
  width: 100%;
}
.el-item-group {
  display: flex;
  width: 100%;
  &.two {
    .el-form-item {
      width: calc((100% - 22px) / 2);
      float: left;
      margin-right: 22px;
      &:last-of-type {
        margin-right: 0;
      }
    }
  }
}

.el-form-item__label {
  line-height: 28px;
}
.el-input-barcode {
  .el-input__inner {
    height: 60px;
    line-height: 60px;
    font-size: 20px;
    border-color: var(--el-color-primary);
  }
}
.el-form-item__label {
  display: flex !important;
}
.el-form-item.is-required:not(.is-no-asterisk)>.el-form-item__label:before, .el-form-item.is-required:not(.is-no-asterisk)>.el-form-item__label-wrap>.el-form-item__label:before {
  order: 1;
  margin-right: 0;
  margin-left: 4px;
}
.el-item-checkin-employee, .el-item-checkout-employee {
  .el-input__inner {
    height: 62px;
    font-size: 1.875rem;
    &::placeholder {
      font-size: 1.875rem;
    }
  }
}
.el-input-number-custom-purchase-order {
  @apply relative;
  @apply w-24;
  .el-input-number__increase, .el-input-number__decrease {
    @apply hidden;
  }
  &:before {
    content: '$';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
  }
  input {
    @apply rounded-none;
    border-bottom: 1px solid var(--el-border-color-base);
    text-align: right !important;
    padding: 0 !important;
    padding-left: 15px !important;
    box-shadow: none !important;
  }
  & + .el-form-item__error {
    display: block;
    position: absolute;
    top: 100%;
    right: 0;
    left: auto;
    color: var(--el-color-danger);
    font-size: 12px;
    white-space: nowrap;
    
  }
}
.el-form-item.is-error .el-input-number-custom-purchase-order input {
  border-bottom: 1px solid var(--el-color-danger); // Border đỏ khi lỗi
}