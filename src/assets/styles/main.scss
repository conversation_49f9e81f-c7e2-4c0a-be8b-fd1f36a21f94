// If you just import on demand, you can ignore the following content.
// if you want to import all styles:
// @use "element-plus/theme-chalk/src/index.scss" as *;
@import "element-plus/theme-chalk/src/reset.scss";
@import "base/fonts";
@import "base/variants";
@import "base/margin";
@import "base/text";
@import "base/typo";
@import "base/scrollbar";
// Custom css element plus
@import "el-libary/variants.scss";
@import "el-libary/form.scss";
@import "el-libary/menu.scss";
@import "el-libary/dialog.scss";
@import "el-libary/button.scss";
@import "el-libary/table.scss";
@import "el-libary/dropdown.scss";
@import "el-libary/pagination.scss";
@import "el-libary/image.scss";
@import "el-libary/upload.scss";
@import "el-libary/tabs.scss";
@import "el-libary/card.scss";
@import "el-libary/notification.scss";
@import "pages/home";
@import "pages/brand";
@import "pages/payment-term";
@import "pages/product";
@import "pages/purchase-order";
@import "pages/warehouse";
@import "pages/vendor";
@import "pages/test-count";
body {
    margin: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

* {
    box-sizing: inherit;
     ::-webkit-scrollbar {
        width: 0.3rem;
        height: 4px;
    }
     ::-webkit-scrollbar-track {
        box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.05);
    }
     ::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 8px;
        cursor: pointer;
    }
}

.w-48 {
    width: 48%;
}

.print-area-form {
    width: inherit;
}

.el-dialog-custom {
    .input-v3 {
        .el-input__inner {
            border-radius: 20px!important;
        }
    }
}

.border-radius-10 {
    .el-input__inner {
        border-radius: 10px!important;
    }
}

.custom-width-full {
    .el-input--default {
        width: 100%!important;
    }
}

.custom-flex {
    .el-link--inner {
        display: flex;
        font-weight: 400;
        font-size: 16px;
    }
}

.custom-justify {
    .el-form-item__content {
        justify-content: space-between;
    }
}

.radio-custom {
    .el-radio-button__inner {
        background: none!important;
        color: #DBDBDB!important;
        border: none;
    }
}

.border-bottom-none {
    .el-dialog__body {
        border-bottom: none!important;
    }
    .el-dialog__footer {
        padding-top: unset!important;
    }
}

.width-90 {
    width: 90%!important;
}

.shadow-custom {
    background: var(--neutral-background-day, #FFF);
    box-shadow: 0px 3.2px 7.2px 0px rgba(0, 0, 0, 0.13), 0px 0.6px 1.8px 0px rgba(0, 0, 0, 0.10);
}

.custom-dialog {
    .el-dialog__body {
        border-top: 1px solid #dedede;
        border-bottom: 1px solid #dedede;
    }
    .el-dialog__header {
        padding-bottom: 14px!important;
        padding-top: 14px!important;
    }
    .el-dialog__footer {
        padding-bottom: 14px;
        padding-top: 14px;
    }
}

.hidden-close {
    .el-dialog__headerbtn {
        display: none!important;
    }
    .el-dialog__body {
        border-top: 0;
        border-bottom: 0;
        padding-top: 15px!important;
    }
    .el-dialog__footer {
        padding-bottom: 20px!important;
        padding-top: 0px!important;
    }
}

p,
span,
pre {
    word-break: break-word!important;
    font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, \5fae\8f6f\96c5\9ed1, Arial, sans-serif;
}
.word-break {
    word-break: break-word!important;
}

.text-inter {
    font-family: Inter, PingFang SC, Hiragino Sans GB, Microsoft YaHei, \5fae\8f6f\96c5\9ed1, Arial, sans-serif;
}
.custom-sidebar>div:first-child>.custom-other-sidebar>.svg-icon {
    width: 14px!important;
}

.icon-required:after {
    content: '*';
    color: #f61a1a;
    margin-left: 1px;
}

.custom-input-file {
    border: solid 1px #dcdfe6;
    border-radius: 4px;
    cursor: pointer;
    position: relative;
}

.custom-warehouse {
    height: 50px;
    line-height: 49px;
}

.custom-user-info {
    .el-sub-menu__title {
        padding-left: 10px!important;
    }
}

.break-words {
    .cell {
        word-break: break-word;
    }
}

.custom-svg {
    svg {
        display: inline;
    }
}

.custom-width {
    .el-upload {
        width: 100%;
        .el-upload-dragger {
            width: 100%;
        }
    }
}

.custom-position {
    position: absolute;
    right: 5px;
}

.position-icon {
    position: absolute;
    top: 25%;
    left: 45%;
}

.custom-round input {
    border-radius: 20px;
    padding-inline-end: 30px;
}

.custom-other-sidebar {
    padding: 10px!important;
    margin-left: 0!important;
    border-bottom: 1px solid #dcdfe5 !important;
    margin-right: 0!important;
    border-radius: 0!important;
}

.scrollbar-demo-item {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.custom-border-others {
    border-bottom: 1px solid #dcdfe5 !important;
    margin-right: 0!important;
}

.custom-sidebar {
    .el-sub-menu__title {
        padding-left: 10px!important;
    }
    .el-sub-menu__icon-arrow {
        right: 5px!important;
    }
    .el-sub-menu {
        border-bottom: 1px solid #dcdfe5 !important;
        padding: 0!important;
        &:hover {
            .el-sub-menu__title {
                border-radius: 0!important;
                &::before {
                    content: none!important;
                }
            }
        }
    }
    .el-menu-item {
        margin-left: 0!important;
        margin-right: 0!important;
        border-radius: 0!important;
        &:hover {
            &::before {
                content: none!important;
            }
        }
    }
    .el-scrollbar__view .el-menu-item {
        background-color: #f1f1f2 !important;
    }
    .cus-border {
        border-bottom: 1px solid #dcdfe5 !important;
    }
    .active {
        background-color: white!important;
    }
    .active::after {
        content: "";
        position: absolute;
        width: 0;
        height: 0;
        top: 50%;
        right: -5px;
        box-sizing: border-box;
        border: 8px solid var(--left-nav--expander-item-pointer--background-color);
        transform-origin: 0 0;
        transform: rotate(-45deg);
        box-shadow: 0 0 2px 2px var(--left-nav--expander-item-pointer--box-shadow-color);
    }
}

.slider-demo-block {
    .el-slider__button {
        width: 15px;
        height: 15px;
        background-color: #1C73E8;
    }
}

.active-menu-item {
    font-weight: bold;
    color: black;
    background-color: rgb(226, 232, 240);
}

.cus-menu-item {
    padding-left: 10px!important;
}

.active-menu {
    .el-sub-menu__title {
        font-weight: bold;
        color: black;
        background-color: rgb(226, 232, 240);
    }
    .active-sub-menu {
        font-weight: bold;
        color: black;
    }
}

.custom-menu-warehouse {
    .el-sub-menu__title {
        padding-left: 0;
    }
}

.canvasjs-chart-credit {
    display: none!important;
}

.demo-tabs {
    border: unset!important;
    .el-tabs__item {
        border: 1px solid #1b72e8 !important;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        margin-right: 1px;
        width: 130px;
        text-align: center;
        height: 35px;
        line-height: 35px;
    }
    .is-active {
        background-color: #1b72e8;
        color: white;
    }
    .el-tabs__nav {
        border: none!important;
    }
    .el-tabs__header {
        border-bottom: none;
        border-bottom: none;
    }
}

.custom-round input {
    border-radius: 20px;
}

.custom-sidebar {
    .el-sub-menu__icon-arrow {
        right: 5px!important;
    }
}

.position-item-form {
    position: relative;
}

.table-cus {
    table td {
        z-index: unset !important;
    }
}

.preview-img {
    width: 300px;
    margin-top: 10px;
    height: auto;
}

.custom-width {
    width: calc(100% - 10px)!important;
    float: right;
}

.z-index-3000 {
    z-index: 1000!important;
}

.layout-default {
    display: flex;
    .sidebar {
        top: 50px;
        width: 245px;
        height: calc(100vh - 50px);
        position: relative;
        transition: 1s;
        border-right: 1px solid var(--el-border-color-base);
        &.is-collapse {
            width: 64px;
        }
        @media screen and (max-width: 768px) {
            width: 20%;
        }
        .sidebar-select-warehouse {
            border-bottom: none;
            background: transparent;
            .el-menu-item {
                width: 100%;
                padding: 1rem;
                &.el-menu-item-one {
                    padding: 7px calc(1rem + 20px);
                    justify-content: flex-start;
                }
                svg {
                    position: relative;
                    top: -1px;
                    margin-right: 6px;
                }
            }
            .el-sub-menu {
                padding: 1rem;
                width: 100%;
                svg {
                    position: relative;
                    top: -1px;
                    margin-right: 6px;
                }
                .el-sub-menu__title {
                    height: 40px;
                    line-height: 40px;
                }
                &.el-sub-menu__hide-arrow {
                    .el-sub-menu__title {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                }
            }
        }
        .menu {
            height: calc(100% - 30px - 55px - 42px);
            overflow-y: auto;
            border-right: none;
            background-color: transparent;
            .el-sub-menu {
                padding: 0 1rem;
                .el-sub-menu__title {
                    &:hover {
                        background-color: rgb(226 232 240) !important;
                        color: var(--el-color-primary) !important;
                        border-radius: 0.25rem;
                        &::before {
                            content: "";
                            position: absolute;
                            height: 16px;
                            width: 4px;
                            left: 0px;
                            top: 12px;
                            background: #0052cc;
                            border-radius: 0px 1.998px 1.998px 0px;
                        }
                    }
                }
                ul {
                    background-color: transparent !important;
                    .el-menu-item {
                        padding-left: 20px !important;
                        background-color: transparent;
                        &::before {
                            opacity: 0;
                        }
                    }
                }
            }
            &.el-menu--collapse {
                .el-menu-item {
                    text-align: center;
                    justify-content: center;
                    padding: 0;
                    margin: 0;
                    padding-left: 0 !important;
                    svg {
                        margin-right: 0;
                    }
                }
                .el-sub-menu {
                    .el-sub-menu__title {
                        justify-content: center;
                    }
                }
            }
            .el-menu-item {
                display: flex;
                align-items: center;
                padding: 1rem;
                height: 40px;
                line-height: 40px;
                margin-left: 1rem;
                margin-right: 1rem;
                border-radius: 0.25rem;
                color: var(--el-menu-hover-text-color);
                @media screen and (max-width: 820px) {
                    padding-left: 0px !important;
                }
                &:hover {
                    background-color: rgb(226 232 240);
                    color: var(--el-color-primary);
                    &::before {
                        content: "";
                        position: absolute;
                        height: 16px;
                        width: 4px;
                        left: 0px;
                        top: 12px;
                        background: #0052cc;
                        border-radius: 0px 1.998px 1.998px 0px;
                    }
                }
                svg {
                    position: relative;
                    top: -1px;
                    margin-right: 6px;
                }
                &.active {
                    background-color: rgb(226 232 240);
                    color: var(--el-color-primary);
                    &::before {
                        content: "";
                        position: absolute;
                        height: 16px;
                        width: 4px;
                        left: 0px;
                        top: 12px;
                        background: var(--el-color-primary);
                        border-radius: 0px 1.998px 1.998px 0px;
                    }
                }
            }
            .el-sub-menu {
                .el-sub-menu__title {
                    display: flex;
                    align-items: center;
                    padding: 1rem;
                    height: 40px;
                    line-height: 40px;
                    @media screen and (max-width: 820px) {
                        max-width: 768px;
                    }
                    &:hover {
                        background-color: transparent;
                        color: var(--el-color-primary);
                    }
                    svg {
                        margin-right: 6px;
                        position: relative;
                        top: -1px;
                    }
                    .el-sub-menu__icon-arrow {
                        svg {
                            margin-right: 0;
                        }
                    }
                }
                .el-menu-item {
                    /* padding-left: 34px !important;*/
                }
                // &.is-opened {
                //   .el-menu {
                //     display: block !important;
                //   }
                // }
                &.active {
                    .el-sub-menu__title {
                        background-color: rgb(226 232 240);
                        border-radius: 4px;
                        color: var(--el-color-primary);
                        &::before {
                            content: "";
                            position: absolute;
                            height: 16px;
                            width: 4px;
                            left: 0px;
                            top: 12px;
                            background: var(--el-color-primary);
                            border-radius: 0px 1.998px 1.998px 0px;
                        }
                    }
                    &.is-opened {
                        .el-sub-menu__title {
                            background-color: rgb(226 232 240);
                            color: var(--el-menu-hover-text-color);
                            &::before {
                                content: "";
                                position: absolute;
                                height: 16px;
                                width: 4px;
                                left: 0px;
                                top: 12px;
                                background: var(--el-color-primary);
                                border-radius: 0px 1.998px 1.998px 0px;
                            }
                        }
                    }
                }
            }
        }
        .collapse {
            position: absolute;
            height: 55px;
            left: 0;
            bottom: 0;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: var(--el-menu-text-color);
            border-top: 1px solid $border-color;
            // transition: all 0.3s;
            &:hover {
                color: var(--el-color-primary);
            }
            svg {
                width: 40px;
                height: 40px;
            }
        }
    }
    .main {
        // flex: 1;
        position: relative;
        width: calc(100% - 245px);
        float: left;
        @media screen and (max-width: 768px) {
            width: calc(80%);
        }
        &.is-collapse-sidebar {
            width: calc(100% - 64px);
        }
        .main-content {
            padding: 1rem 1rem 0 1rem;
            margin-top: 50px;
            height: calc(100vh - 50px);
            overflow-y: auto;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
            &.is-full-width {
                max-width: inherit;
            }
            &.no-padding {
                padding: 0;
                height: calc(100vh - 60px);
            }
        }
        @media screen and (min-width: 768px) and (max-width: 1024px) {
            width: -webkit-fill-available;
        }
    }
}

.el-menu--popup {
    .el-menu-item,
    .el-sub-menu__title {
        display: flex;
        align-items: center;
        svg {
            margin-right: 6px;
        }
        &:hover {
            color: var(--el-color-primary);
            background-color: transparent;
        }
    }
}

.el-menu--horizontal .el-menu-item:not(.is-disabled):focus,
.el-menu--horizontal .el-menu-item:not(.is-disabled):hover {
    background-color: transparent !important;
    color: var(--el-color-primary) !important;
}

.top-head {
    display: flex;
    margin-top: 0.5rem;
    .top-head-left {
        flex: 1;
        margin-right: 20px;
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            margin: 0;
            .el-tag {
                margin-left: 8px;
            }
        }
        .heading {
            display: flex;
            align-items: center;
        }
    }
}

.icon-margin-right {
    margin-right: 4px;
}

.icon-margin-left {
    margin-right: 4px;
}

h1 {
    font-weight: 600;
    font-size: 1.3rem;
}

h3 {
    font-weight: 600;
}

.mb-4 {
    margin-bottom: 20px;
}

.justify-space-between {
    justify-content: space-between;
}

.flex-layout {
    display: flex;
}

.topbar {
    position: absolute;
    z-index: 1;
    top: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
    .menu {
        justify-content: flex-end;
        height: 50px;
        width: 20%;
        border-bottom-color: transparent;
        /*  border-bottom-color: var(--el-border-color-base);*/
        &.menu-left,
        &.menu-right {
            background-color: transparent;
        }
        &.menu-left {
            justify-content: flex-start;
        }
        .el-menu-item-lang {
            img {
                height: 16px;
                width: auto;
            }
        }
        .el-menu-item,
        .el-sub-menu__title {
            height: 50px;
            line-height: 50px;
            font-weight: 600;
            &:hover {
                color: var(--el-color-primary);
                background-color: transparent;
            }
            &.active {
                color: var(--el-color-primary) !important;
                border-bottom: 2px solid var(--el-color-primary);
            }
        }
    }
}

.shipment-item {
    .el-card.box-card.select-shipment-item {
        background-color: #cfcfcf;
    }
}

.el-dialog-custom-printer {
    margin: 30px auto;
    max-width: 1200px;
    el-table__body {
        min-height: 450px;
        max-height: 500px;
        overflow-y: auto;
        .el-scrollbar {
            min-height: 450px;
            max-height: 500px;
            overflow-y: auto;
        }
    }
    .custom-empty-text {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 90%;
        text-align: center;
        color: gray;
    }
    .el-scrollbar__view {
        height: 100%;
    }
}


/* SLIDE FADE FROM RIGHT => LEFT, LEFT => RIGHT*/

.slide-fade-right-enter-active {
    transition: all 0.3s ease-out;
}

.slide-fade-right-leave-active {
    transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-right-enter-from,
.slide-fade-right-leave-to {
    transform: translateX(30px);
    opacity: 0;
}

.topbar {
    @media screen and (max-width: 820px) {
        overflow: auto;
        width: -webkit-fill-available;
    }
}

.apexchart-donut-width {
    width: 330px;
    @media screen and (max-width: 1366px) {
        width: 330px;
    }
    @media screen and (max-width: 1024px) {
        width: 330px;
    }
}

.svg-line {
    fill: none !important;
    stroke: currentColor !important;
}