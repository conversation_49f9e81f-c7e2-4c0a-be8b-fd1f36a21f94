<template>
  <el-dialog
    v-model="openDialogAlertNewVersion"
    title="A new version is available"
    custom-class="el-dialog-custom"
    :close-on-click-modal="false"
  >
    <template #default>
      <div class="bg-blue-100 p-3 rounded">
        The Inventory App has recently updated. Please refresh your browser to
        receive the latest version. You may close and continue to work, however,
        you may experience unexpected issues.
        <hr>

        Có phiên bản mới. Bấm <strong>refresh</strong> để cập nhật hoặc <strong>close</strong> để bỏ qua!
      </div>


    </template>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">Close</el-button>
        <el-button @click="refresh" type="primary">Refresh</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import EventBus from "@/utilities/eventBus.js";
export default {
  name: "AlertNewVersion",
  data() {
    return {
      openDialogAlertNewVersion: false,
    };
  },
  mounted() {},
  created() {
    EventBus.$on("showAlertNewVersion", () => {
      this.openDialogAlertNewVersion = true;
    });
  },
  methods: {
    close() {
      this.openDialogAlertNewVersion = false;
      localStorage.removeItem('inventoryForecastNewColumns');
    },
    refresh() {
      this.close();
      location.reload();
    },
  },
};
</script>
