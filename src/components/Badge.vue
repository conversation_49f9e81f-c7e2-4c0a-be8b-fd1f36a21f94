<template>
  <div
    class="relative flex items-center py-0.5 pl-1.5 pr-2 gap-0.5 border rounded-full text-[11px] font-medium whitespace-nowrap text-ellipsis overflow-hidden text-inter"
    :style="customStyle"
  >
    <slot name="icon"></slot>
    <slot name="content"></slot>
  </div>
</template>

<script>
export default {
  name: 'Badge',
  props: {
    variant: {
      type: String,
      default: 'default',
    },
  },
  computed: {
    customStyle() {
      switch (this.variant) {
        case 'success':
          return {
            background: '#ECFDF3',
            borderColor: '#ABEFC6',
            color: '#067647',
          };
        case 'danger':
          return {
            background: '#FEF3F2',
            borderColor: '#FECDCA',
            color: '#B42318',
          };
        default:
          return {
            background: '#F3F4F6',
            borderColor: '#D1D5DB',
            color: '#374151',
          };
      }
    },
  },
};
</script>

<style scoped></style>
