<template>
    <div  class="self-center ">
      <span
          class="border-2 rounded-md text-[#3B71E6] border-[#3B71E6] py-[2px] px-[8px]"
      >
              {{ prettyTime }}
            </span>
    </div>
</template>

<script>
import formatNumberMixin from '@/mixins/formatNumber.js';
import EventBus from "@/utilities/eventBus";

  export default {
    name: "CountdownTimer",
    components: {
    },
    props: {
      time: {
        type: Number,
        default: 0
      },
    },
    mixins: [formatNumberMixin],
    data() {
      return {
        countdownTime: null
      }
    },
    created() {
      this.countdownTime = this.time;
    },
    mounted() {
      this.start()
    },
    computed: {
      prettyTime (){
        return this.secondsToDhms(this.countdownTime)
      }
    },
    watch: {
      countdownTime(value) {
        if (value === 0) {
          EventBus.$emit('expiredTime')
        }
      }
    },
    methods: {
      start () {
         setInterval( () => {
            this.countdownTime--
          }, 1000 )
      },
      secondsToDhms(seconds) {
        seconds = Number(seconds);
        var m = Math.floor(seconds % 3600 / 60);
        var s = Math.floor(seconds % 60);
        return this.formatLength(m, 2) + ':' + this.formatLength(s, 2);
      }
    }
  }
</script>

<style scoped>

</style>