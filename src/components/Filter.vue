<template>
  <div class="flex justify-center">
    <div class="label">{{ $t("Filter by:") }}</div>
    <div class="filter-item ml-2" v-for="(item, index) in filters" :key="index">
      <el-dropdown
        v-if="item.input == 'select-id'"
        trigger="click"
        class="el-dropdown-filter-item"
        :class="{ 'is-active': hasChangeFilterByItem(item.filter_key) }"
      >
        <span class="el-dropdown-link">
          <template v-if="hasChangeFilterByItem(item.filter_key)">
            <el-tooltip
              effect="dark"
              :content="$t(item.filter_label)"
              placement="top-start"
            >
              <span>{{ item.getNameById(filter[item.filter_key]) }}</span>
            </el-tooltip>
          </template>
          <template v-else>{{ $t(item.filter_label) }}</template>
          <span class="icon">
            <icon :data="iconChevronDown" />
          </span>
        </span>
        <template #dropdown>
          <div class="el-dropdown-menu-filter-item">
            <el-select
              filterable
              v-model="filter[item.filter_key]"
              :placeholder="$t(item.filter_label_select)"
              @change="onFilter"
            >
              <el-option
                v-for="item in item.list"
                :key="item.id"
                :label="item.name"
                :value="String(item.id)"
              />
            </el-select>
          </div>
        </template>
      </el-dropdown>

      <el-dropdown
        v-if="item.input == 'input'"
        :ref="item.filter_key"
        trigger="click"
        class="el-dropdown-filter-item"
        :class="{ 'is-active': hasChangeFilterByItem(item.filter_key) }"
      >
        <span class="el-dropdown-link">
          <template v-if="hasChangeFilterByItem(item.filter_key)">
            <el-tooltip
              effect="dark"
              :content="$t(item.filter_label)"
              placement="top-start"
            >
              <span>{{ filter.keyword }}</span>
            </el-tooltip>
          </template>
          <template v-else>{{ $t(item.filter_label) }}</template>
          <span class="icon">
            <icon :data="iconChevronDown" />
          </span>
        </span>
        <template #dropdown>
          <div class="el-dropdown-menu-filter-item">
            <el-input
              :placeholder="$t(item.filter_label_select)"
              class="search"
              v-model="filter.keyword"
              @keydown.enter="onFilter(item.filter_key)"
              clearable
              @clear="clearFilterItem(item.filter_key)"
            />
          </div>
        </template>
      </el-dropdown>

      <el-dropdown
        v-if="item.input == 'select-value'"
        trigger="click"
        class="el-dropdown-filter-item"
        :class="{ 'is-active': hasChangeFilterByItem(item.filter_key) }"
      >
        <span class="el-dropdown-link">
          <badge-sale-order
            :text="item.filter_label"
            :val="filter[item.filter_key]"
            :filter="item.list"
          />
        </span>
        <template #dropdown>
          <div class="el-dropdown-menu-filter-item">
            <el-select
              filterable
              v-model="filter[item.filter_key]"
              :placeholder="$t(item.filter_label_select)"
              @change="onFilter"
            >
              <el-option
                v-for="item in item.list"
                :key="item.value"
                :label="item.label"
                :value="String(item.value)"
              />
            </el-select>
          </div>
        </template>
      </el-dropdown>

      <el-dropdown
        v-if="item.input == 'date'"
        trigger="click"
        class="el-dropdown-filter-item"
        :class="{
          'is-active': item.input == 'date'
          ? hasChangeFilterByItem(item.filter_key)
          : filter[item.filter_key] && filter[item.filter_key].length }"
      >
        <span class="el-dropdown-link">
          <template v-if="item.input == 'date' ? hasChangeFilterByItem(item.filter_key) : filter[item.filter_key] && filter[item.filter_key]">
            <el-tooltip
              effect="dark"
              :content="$t(item.filter_label)"
              placement="top-start"
            >
              <span v-if="item.type == 'daterange'">{{ templateDateRange(filter[item.filter_key][0], filter[item.filter_key][1]) }}</span>
              <span v-if="item.type == 'date'">{{ filter[item.filter_key] }}</span>
            </el-tooltip>
          </template>
          <template v-else>{{ $t(item.filter_label) }}</template>
          <span class="icon">
            <icon :data="iconChevronDown" />
          </span>
        </span>
        <template #dropdown>
          <div class="el-dropdown-menu-filter-item">
            <el-date-picker
              :value-format="item.format"
              v-model="filter[item.filter_key]"
              :type="item.type"
              :placeholder="item.filter_label"
              :range-separator="item.to_label"
              :start-placeholder="$t(item.start_label)"
              :end-placeholder="$t(item.end_label)"
              @change="item.type == 'daterange' && onChangeDate(filter[item.filter_key])"
            />
          </div>
        </template>
      </el-dropdown>

      <el-dropdown
        v-if="item.input == 'select-multiple'"
        trigger="click"
        class="el-dropdown-filter-item"
        :ref="item.filter_key"
        @visible-change="handleOpenTag"
      >
        <span class="el-dropdown-link">
          <badge-sale-order
            :text="item.filter_label"
            :val="filter[item.filter_key]"
            :filter="item.list"
          />
        </span>
        <template #dropdown>
          <div class="el-dropdown-menu-filter-item">
            <el-select
              filterable
              multiple
              v-model="filter[item.filter_key]"
              :placeholder="$t(item.filter_label_select)"
              @change="item.change"
              @remove-tag="onFilter(item.filter_key)"
              :popper-append-to-body="openDropdownTag"
            >
              <el-option
                v-for="item in item.list"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="item.is_disabled"
              />
            </el-select>
          </div>
        </template>
      </el-dropdown>

      <el-dropdown
        v-if="item.input == 'select-form-tag'"
        :ref="item.filter_key"
        trigger="click"
        class="el-dropdown-filter-item"
        @visible-change="handleOpenTag"
      >
        <span class="el-dropdown-link">
          <badge-sale-order
            :text="item.filter_label"
            :val="filter[item.filter_key]"
            :filter="item.list?.map(item=> ({'value': item.id, 'label': item.name}))"
          />
        </span>
        <template #dropdown>
          <div class="el-dropdown-menu-filter-item">
            <FormTag
              @change="onFilter(item.filter_key)"
              @remove-tag="$refs[item.filter_key].handleClose()"
              ref="formtag"
              :popper-append-to-body="openDropdownTag"
              :tags="item.list"
              v-model="filter[item.filter_key]"
              :selected="filter[item.filter_key]"
            />
          </div>
        </template>
      </el-dropdown>
    </div>

    <div class="filter-item" v-if="hasFilter">
      <el-link type="danger" @click="onClearFilter" :underline="false">
        {{ $t("Clear") }}
      </el-link>
      <el-button
        v-if="fields.includes('save_filter')"
        size="small"
        class="ml-2"
        type="primary"
        @click="openSaveFilter"
        plain
      >
        {{ $t("Save Filter") }}
      </el-button>
    </div>
  </div>
</template>

<script>
import FormTag from "@/components/form/Tag.vue";
import BadgeSaleOrder from '@/modules/sale-order/components/Badge.vue';
import filterMixin from "@/mixins/filter";
import dateMixin from "@/mixins/date.js";
import saleOrderMixin from "@/mixins/saleOrder.js";
import formatNumberMixin from "@/mixins/formatNumber.js";
import warehouseMixin from "@/mixins/warehouse";

  export default {
    components: { BadgeSaleOrder, FormTag },
    mixins: [
      filterMixin,
      dateMixin,
      formatNumberMixin,
      saleOrderMixin,
      warehouseMixin,
    ],
    emits: ['on-filter', 'on-change-date', 'on-open-tag', 'on-clear-filter-item'],
    data() {
      return {
        warehouses: [],
      }
    },
    props: {
      hasFilter: {
        type: Boolean,
        default: false,
      },
      filter: {
        type: Object,
        default: {},
      },
      orderDateRange: {
        type: Array,
        default: [],
      },
      filters: {
        type: Array,
        default: {},
      },
      allow: {
        type: Object,
        default: {},
      }
    },
    methods: {
      hasChangeFilterByItem(name) {
        if (this.allow.hasOwnProperty('filter') && !!this.allow.filter) {
          const query = this.$route.query;
          if (query[name]) {
            return true;
          }
          return false;
        }
        return !!this.filter[name];
      },
      clearFilterItem(item) {
        this.filter[item] = "";
        if (this.$refs[item][0]) {
          this.$refs[item][0].handleClose();
        }
        this.onFilter();
      },
      onFilter(value) {
        if (this.allow.hasOwnProperty('filter') && !!this.allow.filter) {
          this.$emit('on-filter', value);
        }
      },
      onChangeDate(value) {
        if (value && value.length) {
          this.filter.order_date_start = this.formatDate(value[0], false);
          this.filter.order_date_end = this.formatDate(value[1], false);
        } else {
          this.filter.order_date_start = "";
          this.filter.order_date_end = "";
        }

        this.onFilter();
      },
      handleOpenTag() {
        this.$emit('on-open-tag');
      }
    }
  }
</script>