<template>
  <div class="flex gap-x-2">
    <el-select
      v-model="filter.style"
      @change="changeStyle"
      filterable
      :placeholder="$t('Select Style')"
      clearable
      @clear="onClearFilter"
    >
      <el-option
        v-for="item in styles"
        :key="item.style"
        :label="item.style"
        :value="item.style"
      />
    </el-select>
    <el-select
      v-model="filter.color"
      filterable
      :placeholder="$t('Select Color')"
      clearable
    >
      <el-option
        v-for="item in colors"
        :key="item"
        :label="item"
        :value="item"
      />
    </el-select>
    <el-select
      v-model="filter.size"
      filterable
      :placeholder="$t('Select Size')"
      clearable
    >
      <el-option
        v-for="item in sizes"
        :key="item"
        :label="item"
        :value="item"
      />
    </el-select>
    <el-button @click="onClearFilter">{{ $t(buttonLabel) }}</el-button>
  </div>
</template>

<script>
import { equals } from "ramda";

export default {
  name: 'FilterSku',
  props: {
    styles: {
      type: Array,
      default: []
    },
    index: {
      type: Number,
      default: null
    },
    buttonLabel: {
      type: String,
      default: ""
    },
    allow: {
      type: Object,
      default: {}
    }
  },
  emits: ['get-filter', 'on-click-clear'],
  watch: {
    filter: {
      handler() {
        if (!equals(this.setDefaultFilter(), this.filter)) {
          this.$emit('get-filter', this.index, this.filter);
        }
      },
      deep: true
    }
  },
  data() {
    return {
      colors: [],
      sizes: [],
      filter: {
        style: "",
        color: "",
        size: "",
      }
    }
  },
  methods: {
    changeStyle() {
      const item = this.styles.find((item) => item.style === this.filter.style);
      this.filter.size = "";
      this.filter.color = "";
      this.colors = item?.colors || [];
      this.sizes = item?.sizes || [];
    },
    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.$emit('on-click-clear', this.index);
    },
    setDefaultFilter() {
      let params = {
        style: "",
        size: "",
        color: "",
      };
      return params;
    },
  }
}

</script>