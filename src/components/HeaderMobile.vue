<template>
    <div class="header-title">
        <icon :data="iconArrowLeftSLine" width="24" height="24" class="btn-back" @click="back()"></icon>
        <h1>{{ this.name }}</h1>
    </div>
</template>
<script>

export default {
    name: "HeaderMobile",
    props: {
        name: String
    },
    data() {
        
    },
    methods: {
        back() {
            return this.$router.push({ name: 'menu_mobile' });
        },
    }
}
</script>

<style scoped>
.header-title {
    border-top: solid 2px #ccc;
    border-bottom: solid 2px #ccc;
    background-color: #eee;
    text-align: center;
    text-transform: uppercase;
    font-weight: bold;
    padding: 10px;
}

.header-title .btn-back {
    position: absolute;
    left: 10px;
    top: 15px;
}
</style>