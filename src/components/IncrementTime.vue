<template>
  <div>
      {{ formattedTime }}
  </div>
</template>

<script>
export default {
  name: "IncrementTime",
  data() {
      return {
          time: 0
      };
  },
  mounted() {
      this.start();
  },
  computed: {
      formattedTime() {
          const hours = this.pad(Math.floor(this.time / 3600));
          const minutes = this.pad(Math.floor((this.time % 3600) / 60));
          const seconds = this.pad(this.time % 60);
          return `${hours}:${minutes}:${seconds}`;
      }
  },
  methods: {
      start() {
          setInterval(() => {
              this.time++;
          }, 1000);
      },
      pad(value) {
          return value.toString().padStart(2, '0');
      }
  }
};
</script>

<style scoped>

</style>
