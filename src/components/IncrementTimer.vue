<template>
    <div >
      {{prettyTime}}
    </div>
</template>

<script>

  export default {
    name: "IncrementTimer",
    components: {
    },
    props:[],
    mixins: [],
    data() {
      return {
        time: 0
      }
    },
    created() {
    },
    mounted() {
      this.start()
    },
    computed: {
      prettyTime (){
        return this.secondsToDhms(this.time)
      }
    },
    methods: {
      start () {
         setInterval( () => {
            this.time++
          }, 1000 )
      },
      secondsToDhms(seconds) {
        seconds = Number(seconds);
        var d = Math.floor(seconds / (3600*24));
        var h = Math.floor(seconds % (3600*24) / 3600);
        var m = Math.floor(seconds % 3600 / 60);
        var s = Math.floor(seconds % 60);
        // var dDisplay = d > 0 ? d + (d == 1 ? " day, " : " days, ") : "00";
        var hDisplay = h > 0 ? h + (h == 1 ? " hour" : " hours") : "00 hour";
        var mDisplay = m > 0 ? m + (m == 1 ? " minute" : " minutes") : "00 minute";
        var sDisplay = s > 0 ? s + (s == 1 ? " second" : " seconds") : "00 second";
        return hDisplay + ' ' + mDisplay + ' ' + sDisplay;
      }
    }
  }
</script>

<style scoped>

</style>