<template>
  <div
    class="h-5 rounded-full flex items-center justify-center text-white select-none hover:shadow-sm min-w-fit w-24 !text-[12px]"
    :class="getBgColor">
    {{ getLabel }}
  </div>
</template>

<script>
export default {
  props: {
    orderType: {
      default: 1,
      type: Number,
    },
  },

  computed: {
    getLabel() {
      switch (this.orderType) {
        case 2:
          return 'Pretreated';
        case 3:
          return 'Blank';
        case 5:
          return 'Label Order';
        case 7:
          return 'Licensed Order';
        default:
          return '';
      }
    },

    getBgColor() {
      switch (this.orderType) {
        case 2:
          return 'bg-[#bb3733]';
        case 3:
          return 'bg-[#51bb35]';
        case 5:
          return 'bg-[#CB3AB4]';
        case 7:
          return 'bg-[#1AAE9F]';
        default:
          return 'bg-gray-300';
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
