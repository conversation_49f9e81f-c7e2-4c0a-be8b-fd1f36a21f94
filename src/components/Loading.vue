<template>
  <div class="topbar">
    <el-skeleton class="min-h-[50px]" animated>
      <template #template>
        <el-skeleton-item class="min-h-[50px]" />
      </template>
    </el-skeleton>
  </div>
  <div class="layout-default">
    <div class="sidebar">
      <el-skeleton class="py-5 px-3" v-for="i in 3" :key="i" animated>
        <el-skeleton-item variant="text" class="min-h-[16px]" />
      </el-skeleton>
    </div>
    <div class="main">
      <div class="main-content">
        <el-skeleton v-for="i in 5" :key="i" animated>
          <el-skeleton-item variant="text" class="min-h-[16px]" />
        </el-skeleton>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Loading',
  data() {
    return {};
  },
};
</script>
