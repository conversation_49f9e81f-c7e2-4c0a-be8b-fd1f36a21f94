<template>
  <div role="pagination" aria-label="pagination" class="el-pagination is-background">
    <button type="button" class="btn-prev is-first" :disabled="!isPrivious" :aria-disabled="!isPrivious"
      @click="handleButtonPriviousClick()">
      <i class="el-icon">
        <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
          <path fill="currentColor"
            d="M609.408 149.376 277.76 489.6a32 32 0 0 0 0 44.672l331.648 340.352a29.12 29.12 0 0 0 41.728 0 30.592 30.592 0 0 0 0-42.752L339.264 511.936l311.872-319.872a30.592 30.592 0 0 0 0-42.688 29.12 29.12 0 0 0-41.728 0z">
          </path>
        </svg>
      </i>
    </button>
    <button type="button" class="btn-next is-last" :disabled="!isNext" :aria-disabled="!isNext"
      @click="handleButtonNextClick()">
      <i class="el-icon">
        <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
          <path fill="currentColor"
            d="M340.864 149.312a30.592 30.592 0 0 0 0 42.752L652.736 512 340.864 831.872a30.592 30.592 0 0 0 0 42.752 29.12 29.12 0 0 0 41.728 0L714.24 534.336a32 32 0 0 0 0-44.672L382.592 149.376a29.12 29.12 0 0 0-41.728 0z">
          </path>
        </svg>
      </i>
    </button>
  </div>
</template>

<script>
export default {
  name: "PaginationComponent",

  props: {
    currentPage: {
      type: Number,
      default: 0,
    },
    
    isLoading: {
      type: Boolean,
      default: false,
    },

    isLoadMore: {
      type: Boolean,
      default: false,
    },
  },

  computed: {
    isNext() {
      if (this.isLoading) {
        return false
      }

      return this.isLoadMore;
    },

    isPrivious() {
      if ((this.currentPage - 1) <= 0) {
        return false;
      }

      if (this.isLoading) {
        return false
      }

      return true;
    },
  },

  methods: {
    handleButtonPriviousClick() {
      if (this.isPrivious) {
        this.$emit('changePage', (this.currentPage - 1))
      }
    },

    handleButtonNextClick() {
      if (this.isNext) {
        this.$emit('changePage', (this.currentPage + 1))
      }
    }
  }
};
</script>
