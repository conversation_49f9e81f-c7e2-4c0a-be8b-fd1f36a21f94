<template>
  <div v-if="Object.keys(employee).length" class="bg-gray-50 p-3 border rounded">
    <div class="flex justify-between ">
      <b style="font-size: 18px" class="mr-2">{{ $t('Hi') }} {{ employee.name + ',' }}
        {{ $t(' Have a nice day!') }}</b>
      <el-link type="danger" class="ml-3" @click="logoutScanEmployee" :underline="false">{{ $t('Logout')
      }}</el-link>
    </div>
    <div class="text-lg text-fuchsia-500">
      <IncrementTimer />
    </div>
  </div>
  <el-input :class="classInput" v-else :placeholder="$t('Scan Employee Code')" v-model="employeeCode"
    :disabled="isLoading" @keyup.enter="scanEmployeeCode"></el-input>
</template>
<script>
import EventBus from "@/utilities/eventBus.js";
import IncrementTimer from '@/components/IncrementTimer.vue';
import {
  employeeLogoutTimeChecking,
  employeeTimeChecking,
} from '@/api/employee.js';
import { isEmpty } from 'ramda';

export default {
  name: 'ScanEmployee',
  props: {
    jobType: {
      type: String,
    },
    classInput: {
      type: String,
      default: 'scan-employee-code'
    }
  },
  components: { IncrementTimer },
  mixins: [],
  data() {
    return {
      isLoading: false,
      employeeCode: '',
      employee: {},
      timeCheckingId: '',
    };
  },
  mounted() {
    this.focusByElClass(this.classInput);
  },
  beforeUnmount() {
    EventBus.$off("autoFocusScanEmployee");
    EventBus.$off("logoutScanEmployee");
  },
  created() {
    EventBus.$on("autoFocusScanEmployee", () => {
      this.focusByElClass(this.classInput);
    });
    EventBus.$on("logoutScanEmployee", () => {
      this.logoutScanEmployee();
    });
  },
  methods: {
    async scanEmployeeCode() {
      if (!this.employeeCode) {
        this.notification(
          'Employee code is required.',
          'error'
        );
        return;
      }
      this.isLoading = true;
      try {
        const res = await employeeTimeChecking({
          code: this.employeeCode,
          job_type: this.jobType,
        });
        if (!res.data.data) {
          this.notification(
            'Scan employee code error, please scan again.',
            'error'
          );
          return;
        }
        this.employee = res.data.data;
        this.timeCheckingId = res.data.id_time_checking;
        EventBus.$emit("idTimeChecking", this.timeCheckingId);
        this.setEmployee();
      } catch (e) {
        const data = e.response.data;
        let message = this.$t('Scan employee code error, please scan again.');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.notification(message, 'error');
      } finally {
        this.isLoading = false;
        this.focusByElClass(this.classInput);
      }
    },
    async logoutScanEmployee() {
      if (!this.timeCheckingId) {
        this.resetEmployee();
        this.setEmployee();
        return;
      }
      // await employeeLogoutTimeChecking(this.timeCheckingId);
      this.resetEmployee();
      this.setEmployee();
    },
    resetEmployee() {
      this.employee = {};
      this.employeeCode = '';
      this.timeCheckingId = '';
      this.focusByElClass(this.classInput);
    },
    setEmployee() {
      this.$emit("setEmployee", this.employee);
    }
  },
};

</script>
  
