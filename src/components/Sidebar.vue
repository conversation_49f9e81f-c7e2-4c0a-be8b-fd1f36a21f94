<template>
  <div class="sidebar bg-slate-50 relative"
    :class="{ 'is-collapse': getIsCollapse, 'bg-amber-50': !userWarehouseColor && userWarehouseId == 2, 'bg-sky-100': !userWarehouseColor && userWarehouseId == 16, '!w-4': getIsCollapse }"
    :style="{ backgroundColor: userWarehouseColor }">
    <a href="#" class="collapse" @click="setIsCollapse()">
      <icon :data="iconDoubleChevronRight" v-if="getIsCollapse" />
      <template class="z-20" v-else>
        <icon :data="iconDoubleChevronLeft" />
        Collapse sidebar
      </template>
    </a>

    <svg class="absolute top-[30px] right-[-12px] cursor-pointer z-index-3000" v-if="!getIsCollapse"
      @click="setIsCollapse" width="23" height="23" viewBox="0 0 23 23" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="23" height="23" rx="11.5" fill="#CCCCCC" />
      <path d="M13 7L8 12L13 17" fill="#CCCCCC" />
      <path d="M13 7L8 12L13 17" stroke="black" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round" />
    </svg>

    <svg class="absolute top-[30px] right-[-12px] cursor-pointer z-index-3000" v-else @click="setIsCollapse" width="23"
      height="23" viewBox="0 0 23 23" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="23" height="23" rx="11.5" fill="#CCCCCC" />
      <path d="M9 7L14 12L9 17" fill="#CCCCCC" />
      <path d="M9 7L14 12L9 17" stroke="black" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round" />
    </svg>

    <div :class="[getIsCollapse ? 'hidden' : '']">
      <el-menu class="sidebar-select-warehouse" :unique-opened="true" :collapse="getIsCollapse"
        :collapse-transition="false" v-loading="isLoadingMenu" mode="horizontal" :ellipsis="getIsCollapse">
        <el-menu-item class="el-menu-item-one ml-0" v-if="getUserWarehouses && getUserWarehouses.length === 1">
          <img class="h-[16px] ml-0 mr-2 m-auto rounded" :src="userWarehouseImage" alt="" />
          <span>{{ userWarehouseName }}</span>
        </el-menu-item>
        <el-sub-menu class="custom-menu-warehouse" v-else-if="getUserWarehouses && getUserWarehouses.length > 1">
          <template #title>
            <img class="h-[16px] ml-[20px] mr-2 m-auto rounded" :src="userWarehouseImage" alt="" />
            <span>{{ userWarehouseName }}</span>
          </template>
          <el-menu-item v-for="item in getUserWarehouses" :key="item.id" @click="selectWarehouse(item.id)"
            :disabled="userWarehouseName === item.name">
            {{ $t(item.name) }}
          </el-menu-item>
        </el-sub-menu>
      </el-menu>

      <div v-if="isShowModuleOrder" class="p-5 pt-1 relative">
        <icon v-if="filterOrders.keyword" @click="clearOrderNumber" class="absolute right-7 top-3 z-10 cursor-pointer"
          :data="iconCancel" />
        <el-input v-model="filterOrders.keyword" class="z-5 m-auto custom-round truncate"
          placeholder="Search Orders/Ref/SKU..." @keyup.enter="searchOrders">
        </el-input>
      </div>

      <div class="max-h-[calc(100vh-72px-50px)] overflow-y-auto">
        <el-menu :default-active="isShowModuleOrder ? indexOpen : ''" class="menu" @open="handleOpen"
          :unique-opened="true" :collapse="getIsCollapse" :collapse-transition="false" ref="sidebarMenu"
          v-loading="isLoadingMenu">
          <div v-if="isShowModuleOrder" :class="['custom-sidebar']">
            <template v-for="(item, index) in newMenuItems" :key="'item' + index">
              <el-sub-menu v-if="item.sub && item.sub.length"
                :class="[activeClass(mergeItem(item, index)) ? 'active-menu' : '']" :index="String(index)">
                <template #title>
                  <icon v-if="item.icon" :class="item?.iconClass" class="!min-w-[15px]" :data="renderIcon(item)"></icon>
                  <span class="flex justify-space-between w-full" @click="filterOrder(item)">
                    <span
                      :class="['mr-1 truncate', item.total ? 'max-w-[125px]' : '', activeClass(mergeItem(item, index)) ? 'active-tab' : '']">
                      <el-tooltip v-if="item.showTooltip" effect="dark" :content="$t(item.name)" placement="top-start">
                        {{ $t(item.name) }}
                      </el-tooltip>
                      <span v-else>{{ $t(item.name) }}</span>
                    </span>

                    <span class="mr-1" v-if="isShowTotalOrders(item)">
                      <span class="text-white bg-orange-400 rounded-full px-2 py-1 text-[11px]">{{ formatNum(item.total)
                      }}</span>
                    </span>
                  </span>
                </template>
                <el-scrollbar max-width="100%" max-height="200px" v-show="!getIsCollapse">
                  <template v-show="activeClass(mergeItem(item, index)) || index == indexOpen"
                    v-for="(subItem, indexSubItem) in item.sub.slice(0, 10)" :key="'sub-item' + index + indexSubItem">
                    <el-menu-item @click="filterOrder(subItem)"
                      :class="[activeClass(mergeItem(item, index), mergeItem(subItem, index)) ? 'active' : '']"
                      v-if="checkShowSub(subItem)" :key="'sub-item' + index + indexSubItem">
                      <span style="max-width: 200px" class="flex justify-space-between w-full mr-2">
                        <span
                          :class="['truncate', activeClass(mergeItem(item, index), mergeItem(subItem, index)) ? 'active-sub-menu' : '']">
                          {{ $t(subItem.name) }}
                        </span>
                        <span v-if="isShowTotalOrders(item) && subItem.total != 0">
                          <span class="text-white bg-orange-400 rounded-full px-2 py-1 text-[11px]">
                            {{ formatNum(subItem.total) }}
                          </span>
                        </span>
                      </span>
                    </el-menu-item>
                  </template>
                  <template v-if="activeClass(mergeItem(item, index)) || index == indexOpen"
                    v-for="(subItem, indexSubItem) in item.sub.slice(10)" :key="'sub-item' + index + indexSubItem">
                    <el-menu-item @click="filterOrder(subItem)"
                      :class="[activeClass(mergeItem(item, index), mergeItem(subItem, index)) ? 'active' : '']"
                      v-if="checkShowSub(subItem)" :key="'sub-item' + index + indexSubItem">
                      <span style="max-width: 200px" class="flex justify-space-between w-full mr-2">
                        <span
                          :class="['truncate', activeClass(mergeItem(item, index), mergeItem(subItem, index)) ? 'active-sub-menu' : '']">
                          {{ $t(subItem.name) }}
                        </span>
                        <span v-if="isShowTotalOrders(item) && subItem.total != 0">
                          <span class="text-white bg-orange-400 rounded-full px-2 py-1 text-[11px]">
                            {{ formatNum(subItem.total) }}
                          </span>
                        </span>
                      </span>
                    </el-menu-item>
                  </template>
                </el-scrollbar>
              </el-sub-menu>
              <div v-else>
                <el-menu-item
                  :class="[routerActiveClass(mergeItem(item, index)) ? 'active-menu-item' : '', 'custom-other-sidebar']"
                  @click="routerRedirect(item)" :index="String(index)" v-if="showMenuItemByWarehouse(item)">
                  <icon class="!min-w-[15px]" :class="item?.iconClass" :data="renderIcon(item)"></icon>
                  <span class="flex justify-space-between w-full" @click="filterOrder(item)">
                    <span
                      :class="['mr-1 truncate', item.total ? 'max-w-[125px]' : '', activeClass(mergeItem(item, index)) ? 'active-tab' : '']"><el-tooltip
                        v-if="item.showTooltip" effect="dark" :content="$t(item.name)" placement="top-start">
                        {{ $t(item.name) }}
                      </el-tooltip>
                      <span v-else>{{ $t(item.name) }}</span>
                    </span>
                    <span class="mr-2.5" v-if="item.total">
                      <span class="text-white bg-orange-400 rounded-full px-2 py-1 text-[11px]">{{ item.total }}</span>
                    </span>
                  </span>
                </el-menu-item>
              </div>
            </template>
          </div>

          <template v-else v-for="(item, index) in newMenuItems" :key="'item' + index">
            <template v-if="showMenuItemByWarehouse(item)">
              <el-sub-menu v-if="item.sub && item.sub.length"
                :class="[routerActiveClass(mergeItem(item, index)) ? 'active' : '']" :index="String(index)">
                <template #title>
                  <icon class="!min-w-[15px]" v-if="item.icon" :class="item?.iconClass" :data="renderIcon(item)"></icon>
                  <span class="truncate w-9/12" @click="routerRedirect(item)">
                    {{ $t(item.name) }}
                  </span>
                </template>
                <template v-for="(subItem, indexSubItem) in item.sub">
                  <el-menu-item @click="routerRedirect(subItem)"
                                class="!w-full"
                    :class="{ active: hasCurrentRouteItem(item) ? $route.name === subItem.router.name : routerActiveClass(mergeItem(item, index), mergeItem(subItem, index)) }"
                    v-if="checkShowSub(subItem)" :key="'sub-item' + index + indexSubItem">
                    <span>
                      {{ $t(subItem.name) }}
                      <span v-show="subItem.total && subItem.total > 0" class="bg-sky-500 rounded-full pl-2 pr-2">
                        {{ subItem.total }}
                      </span>
                    </span>
                  </el-menu-item>
                </template>
              </el-sub-menu>
              <div v-else>
                <el-menu-item :class="[routerActiveClass(mergeItem(item, index)) ? 'active' : '']"
                  @click="routerRedirect(item)" :index="String(index)" v-if="showMenuItemByWarehouse(item)">
                  <el-icon v-if="item?.elIcon" v-html="renderIcon(item)">
                  </el-icon>
                  <icon v-else class="!min-w-[15px]" :class="item?.iconClass" :data="renderIcon(item)"></icon>
                  <span class="flex justify-space-between w-full">
                    <span
                      :class="['mr-1 truncate', item.total ? 'max-w-[125px]' : '', activeClass(mergeItem(item, index)) ? 'active-tab' : '']">
                      <el-tooltip v-if="item.showTooltip" effect="dark" :content="$t(item.name)" placement="top-start">
                        {{ $t(item.name) }}
                      </el-tooltip>
                      <span v-else>{{ $t(item.name) }}</span>
                    </span>
                    <span>
                      <span v-show="item.total && item.total > 0"
                        class="text-white bg-orange-400 rounded-full !px-2 py-1 text-[11px]">
                        {{ item.total }}
                      </span>
                    </span>
                  </span>
                </el-menu-item>
              </div>
            </template>
          </template>
        </el-menu>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import warehouseMixin from '@/mixins/warehouse';
import menuItemsMixin from '@/mixins/menuItems';
import numberMixin from '@/mixins/formatNumber';
import { WAREHOUSE_MEXICO, NOT_APPLICABLE } from '@/utilities/constants';
import { equals } from 'ramda';
import EventBus from '@/utilities/eventBus.js';
import { getCount } from '@/api/saleOrder.js';
import { getCount as getCountTicket } from '@/api/internalTicket.js';
import { getCount as getCountMaintenance } from '@/api/maintenanceTracking';
import { getCount as getCountClaim } from '@/api/claimOrders.js';
import { getStatistic } from '@/api/internalRequest.js';
import { countTotalPending } from '@/api/stockTransfer.js';
import saleOrderMixin from '@/mixins/saleOrder.js';
import { getCountDownloadError } from '@/api/saleOrderItemImage';
import userRoleWarehouseMixin from '@/mixins/userRoleWarehouse.js';

export default {
  name: 'Sidebar',
  components: {},
  mixins: [warehouseMixin, menuItemsMixin, saleOrderMixin, numberMixin, userRoleWarehouseMixin],
  computed: {
    ...mapGetters(['getUserProfile']),
    ...mapGetters(['getIsCollapse']),
    ...mapGetters(['getCurrentModuleMenu']),
    ...mapGetters([
      'getUserWarehouses',
      'getDocCategories',
      'getPerformanceReportDepartments',
    ]),
    isOverflow() {
      const spanElement = this.$el;
      console.log(this.$el, spanElement.width, spanElement.offsetWidth)
      return spanElement.scrollWidth > 125;
    },
  },
  data() {
    return {
      activeIndex: '',
      arrActiveIndex: [],
      isLoadingMenu: false,
      newMenuItems: [],
      filterOrders: this.setFilterOrderDefault(),
      isShowModuleOrder: false,
      countOrders: [],
      sidebarOrderStatus: [],
      statusShowTotal: ['new_order', 'in_production', 'on_hold'],
      indexOpen: '0',
      indexOthers: 7,
      isLoaded: false,
      menuOrderId: 'orders',
      menuInsightId: 'insights',
      menuSupportDocId: 'support_doc',
      INVENTORY: 'inventory',
      TICKET: 'ticket',
      ORDER_ROUTE_NAME: 'sale_order',
      changeCurrentModule: false,
      MEXICO_WAREHOUSE_ID: WAREHOUSE_MEXICO[0],
      onlyRouteMX: ['exportation', 'part_number'],
      storeNA: NOT_APPLICABLE,
      countNewInternal: null,
      countTicket: null,
      countClaim: null,
      countMaintenance: null,
      INTERNAL_REQUEST: 'internal_request',
      INTERNAL_TICKET: 'internal_ticket',
      MAINTENANCE: 'maintenance_tracking',
      CLAIM_ORDERS: 'claim_orders',
      NEW_STATUS: 'new',
      STOCK_TRANSFER: 'stock_transfer',
      countNewStockTransfer: null,
    };
  },
  watch: {
    activeIndex: {
      handler() {
        this.$nextTick(() => {
          this.$refs.sidebarMenu.open(String(this.activeIndex));
        });
      },
      deep: true,
    },
    getCurrentModuleMenu: {
      async handler() {
        this.changeCurrentModule = true;
        this.newMenuItems = [];
        this.filterOrders = this.setFilterOrderDefault();
      },
      deep: true,
    },
    '$route.name': {
      async handler() {
        if (this.changeCurrentModule) {
          this.getMenuItemByCurrentModuleMenu().then((res) => {
            this.checkUserRoleByCurrentRouteName();
          });
          this.changeCurrentModule = false;
        }
      },
    },
    getUserProfile: {
      async handler() {
        this.filterOrders = this.setFilterOrderDefault();
        await this.getMenuItemByCurrentModuleMenu();
      },
      deep: true,
    },
    getDocCategories: {
      handler() {
        this.getMenuItemByCurrentModuleMenu();
      },
    },
    userWarehouseId: {
      handler() {
        if (this.isLoaded) {
          this.checkUserRoleByCurrentRouteName();
        }
      },
    },
  },
  async mounted() {
    EventBus.$on('clearKeyword', () => {
      this.filterOrders.keyword = '';
      EventBus.$emit('clearSearch');
    });
    await this.setCurrentModuleMenu();
    this.getMenuItemByCurrentModuleMenu().then((res) => {
      this.checkUserRoleByCurrentRouteName();
    });
  },
  created() {
    EventBus.$on('setWarehouseId', (id) => {
      this.userWarehouseId = id;
      this.userWarehouse = this.getWarehouse();
    });
  },
  methods: {
    measureTextWidth(ref) {
      const textElement = this.$refs[ref];
      if (textElement) {
        const textToMeasure = textElement.textContent;
        const canvas = document.createElement("canvas");
        const context = canvas.getContext("2d");
        context.font = window.getComputedStyle(ref).font;
        return context.measureText(textToMeasure).width;
      }
    },
    checkShowSub(subItem) {
      return (
        this.isAdmin ||
        (this.showMenuItemByWarehouse(subItem) && subItem.is_show)
      );
    },
    handleOpen(index) {
      if (this.isShowModuleOrder) {
        this.$nextTick(() => {
          this.indexOpen = String(index);
          this.$refs.sidebarMenu.open(String(index));
        });
      }
    },
    setFilterOrderDefault() {
      return {
        keyword: '',
        order_status: '',
        store_id: '',
      };
    },
    async searchOrders() {
      this.filterOrders.order_status = '';
      EventBus.$emit('searchOrders', this.filterOrders);
    },
    async filterOrder(item = {}) {
      this.filterOrders.keyword = '';
      this.filterOrders.order_status = item?.router?.query.order_status ?? '';
      this.filterOrders.store_id = item?.router?.query.store_id ?? '';
      if (this.$route.name == this.ORDER_ROUTE_NAME) {
        EventBus.$emit('filterOrders', this.filterOrders);
      } else {
        return this.routerRedirect(item);
      }
    },
    async clearOrderNumber() {
      this.filterOrders.keyword = '';
      this.filterOrders.order_status = 'new_order';
      this.searchOrders();
    },
    isShowTotalOrders(item) {
      return item.total && this.statusShowTotal.includes(item.order_status);
    },
    getOrderStatusByValue(status) {
      const selectItem = this.saleOrderStatus.find(
        (item) => item.value === status
      );
      return (selectItem && selectItem.label) || '';
    },
    getIconOrderStatus(status) {
      return 'icon' + status.replaceAll(' ', '');
    },
    async getCountTicket() {
      const { data } = await getCountTicket();
      this.countTicket = data.reduce((total, item) => {
        if (item.status !== "resolved") {
          return total + item.quantity;
        }
        return total;
      }, 0);
    },
    async getCountClaim() {
      const { data } = await getCountClaim();
      this.countClaim = data
    },
    async getCountMaintenance() {
      const { data } = await getCountMaintenance();
      this.countMaintenance = data.reduce((total, item) => {
        if (item.status !== "resolved") {
          return total + item.quantity;
        }
        return total;
      }, 0);
    },
    async getCountOrders(warehouseIds) {
      let param = { keyword: this.filterOrders.keyword };
      if (!this.isSanJoseWarehouse(this.userWarehouseId)) {
        param.warehouse = this.userWarehouseId;
      }
      const data = await getCount(param);
      this.countOrders = data.data ?? [];
      this.sidebarOrderStatus = [
        {
          type: 'item',
          name: 'All',
          id: 'all_status',
          icon: 'iconAll',
          is_show: true,
          router: {
            name: this.ORDER_ROUTE_NAME,
            query: {
              order_status: '',
              store_id: '',
              keyword: this.filterOrders.keyword,
            },
          },
        },
      ];
      for (const property in data.data) {
        let sub = [];
        let totalStatus = 0;
        data.data[property].forEach((item) => {
          totalStatus += item.total;
          sub.push({
            type: 'item',
            name: item.code || this.storeNA,
            id: item.id,
            is_show: true,
            total: item.total,
            warehouseIds: warehouseIds,
            icon: '',
            router: {
              name: this.ORDER_ROUTE_NAME,
              query: {
                order_status: property,
                store_id: item.id,
                keyword: this.filterOrders.keyword,
              },
            },
          });
        });
        this.sidebarOrderStatus.push({
          type: 'item',
          name: this.getOrderStatusByValue(property),
          icon: this.getIconOrderStatus(this.getOrderStatusByValue(property)),
          order_status: property,
          total: totalStatus,
          is_show: true,
          warehouseIds: warehouseIds,
          router: {
            name: this.ORDER_ROUTE_NAME,
            query: {
              order_status: property,
              store_id: '',
              keyword: this.filterOrders.keyword,
            },
          },
          sub: sub,
        });
      }
      this.indexOthers = this.sidebarOrderStatus.length ?? this.indexOthers;
      // let existsOther = this.sidebarOrderStatus.find(item => {
      //   return item.id == 'others'
      // })
      // if (!existsOther) {
      //   this.sidebarOrderStatus.push(
      //       {
      //         type: 'item',
      //         name: 'Others',
      //         id: 'others',
      //         icon: '',
      //         is_show: true,
      //         router: {
      //           name: '',
      //         },
      //       },
      //   );
      // }
    },
    setCurrentModuleMenu() {
      if (!this.$route?.name || this.$route?.name == 'dashboard') {
        if (+this.getUserProfile.is_admin === 1) {
          const firstItem = this.menuItems[0];
          this.defaultModuleMenu = firstItem.id;
          this.$router.push({ name: firstItem.router.name });
          this.$store.commit('setCurrentModuleMenu', this.defaultModuleMenu);
        } else {
          this.$router.push({ name: this.getUserProfile.roles[0].data.is_default });
          this.getDefaultModule(this.getUserProfile.roles[0].data);
          this.$store.commit('setCurrentModuleMenu', this.defaultModuleMenu);
        }

        return;
      }
      const convertNewMenuItems = {};

      for (let i = 0; i < this.menuItems.length; i++) {
        const menuItem = this.menuItems[i];
        convertNewMenuItems[menuItem.router.name] = menuItem.id;
        if (this.$route.name == menuItem.router.name) {
          this.$store.commit('setCurrentModuleMenu', menuItem.id);
          return;
        }

        if (!menuItem.sidebar || !menuItem.sidebar.length) {
          continue;
        }

        for (let j = 0; j < menuItem.sidebar.length; j++) {
          const item = menuItem.sidebar[j];
          convertNewMenuItems[item.router.name] = menuItem.id;
          if (this.$route.name == item.router.name) {
            this.$store.commit('setCurrentModuleMenu', menuItem.id);
            return;
          }
          if (!item.sub || !item.sub.length) {
            continue;
          }

          for (let j = 0; j < item.sub.length; j++) {
            const subItem = item.sub[j];
            convertNewMenuItems[subItem.router.name] = menuItem.id;
            if (this.$route.name == subItem.router.name) {
              this.$store.commit('setCurrentModuleMenu', menuItem.id);
              return;
            }
          }
        }
      }
    },
    checkAllowViewWarehouses(item) {
      let itemAllowViewWarehouses = [
        ...(item.includeWarehouse ? item.includeWarehouse : []),
        ...(item.warehouseIds ? item.warehouseIds : []),
      ];
      itemAllowViewWarehouses = itemAllowViewWarehouses?.length
        ? itemAllowViewWarehouses.map((mItem) => +mItem)
        : [];
      return (
        itemAllowViewWarehouses.length == 0 ||
        itemAllowViewWarehouses.includes(+this.userWarehouseId)
      );
    },
    checkUserRoleByCurrentRouteName() {
      let currentRouteName = this.$route.name;
      if (
        +this.getUserProfile.is_admin === 1 ||
        !currentRouteName ||
        ['forbidden', 'dashboard'].includes(currentRouteName)
      ) {
        return;
      }
      for (let i = 0; i < this.newMenuItems.length; i++) {
        const item = this.newMenuItems[i];
        let allowViewWarehouse = this.checkAllowViewWarehouses(item);
        if (allowViewWarehouse) {
          if (
            item.router.name == currentRouteName ||
            (item.allowView && item.allowView == currentRouteName)
          ) {
            return;
          }
        } else {
          if (item.router.name == currentRouteName) {
            window.location.href = '/403';
            return;
          }
        }
        if (!item.sub || !item.sub.length || !Array.isArray(item.sub)) continue;
        for (let subIndex = 0; subIndex < item.sub.length; subIndex++) {
          const subItem = item.sub[subIndex];
          let subAllowViewWarehouse = this.checkAllowViewWarehouses(subItem);
          if (subAllowViewWarehouse) {
            if (subItem.router.name == currentRouteName) {
              return;
            }
          } else {
            if (subItem.router.name == currentRouteName) {
              window.location.href = '/403';
              return;
            }
          }
          if (
            !subItem.allowView ||
            !subItem.allowView.length ||
            !Array.isArray(subItem.allowView)
          )
            continue;
          if (subItem.allowView == currentRouteName) {
            return;
          }
        }
      }
      if (typeof currentRouteName == 'undefined') {
        return;
      }
      window.location.href = '/403';
      return;
    },
    hasCurrentRouteItem(item) {
      return !!(
        this.$route.name === item.router.name &&
        !['performance_report'].includes(this.$route.name)
      );
    },
    async getCountDownloadError() {
      const res = await getCountDownloadError();
      this.countPrintFileError = res.data ?? 0;
    },
    async getCountInternalRequest() {
      const res = await getStatistic({
        warehouse_id: this.userWarehouseId
      });
      this.countNewInternal = res.data.new ?? 0;
    },
    async getCountPendingStockRequest() {
      const res = await countTotalPending({
        warehouse_id: this.userWarehouseId
      });
      this.countNewStockTransfer = res.data ?? 0;
    },
    async getMenuItemByCurrentModuleMenu() {
      if (!this.getUserProfile || !this.getUserProfile.id) {
        this.isLoaded = true;
        return [];
      }
      let currentModule = '';
      const SidebarItem = this.menuItems;
      currentModule = SidebarItem.find(
        (item) =>
          item.id === this.getCurrentModuleMenu &&
          (!item.includeWarehouse ||
            item.includeWarehouse.length == 0 ||
            item.includeWarehouse.includes(this.getWarehouseId()))
      );
      if (!currentModule) {
        this.isLoaded = true;
        return [];
      }
      let sidebar = currentModule.sidebar.filter((item) => {
        if (
          !item.includeWarehouse ||
          item.includeWarehouse.length == 0 ||
          item.includeWarehouse.includes(this.getWarehouseId())
        ) {
          return item;
        }
      });
      this.isShowModuleOrder = false;

      // call api
      this.isLoadingMenu = true;
      switch (currentModule.id) {
        case this.menuSupportDocId:
          if (!this?.getDocCategories?.length) {
            await this.$store.dispatch('getDocCategories');
          }
          this.newMenuItems = this.renderMenuSupportDocs();
          break;
        case this.menuInsightId:
          this.newMenuItems = sidebar;
          const item = this.newMenuItems.find(
            (i) => i.id === 'performance_report'
          );
          if (item) {
            let warehouseIds = item.warehouseIds ?? [];
            if (
              !this.getPerformanceReportDepartments ||
              !this.getPerformanceReportDepartments.length
            ) {
              await this.$store.dispatch('getPerformanceReportDepartments');
            }
            item.sub = this.renderSubmenuPerformaceReport(warehouseIds);
          }
          break;
        case this.menuOrderId:
          this.isLoaded = false;
          let newMenuItems = sidebar;
          if (!this.isLoaded) {
            this.isLoadingMenu = true;
          }
          let currentRole =
            (this.getUserProfile.roles &&
              this.getUserProfile.roles.length &&
              this.getUserProfile.roles[0]) ||
            '';
          this.isShowModuleOrder =
            this.getUserProfile.is_admin == 1 ||
              currentRole?.data?.orders?.routes?.sale_order == 1
              ? true
              : false;
          if (this.isShowModuleOrder) {
            let orderSidebar = newMenuItems.find((item) => {
              return item.router.name == this.ORDER_ROUTE_NAME;
            });
            let warehouseIds = orderSidebar?.warehouseIds ?? [];
            if (
              warehouseIds.length == 0 ||
              (warehouseIds.length > 0 &&
                warehouseIds.includes(String(this.userWarehouseId)))
            ) {
              await this.getCountOrders(warehouseIds);
            } else {
              this.isShowModuleOrder = false;
            }
          } else {
            this.sidebarOrderStatus = [];
          }
          newMenuItems = newMenuItems.filter((item) => {
            return item.router.name != this.ORDER_ROUTE_NAME;
          });
          await this.getCountDownloadError();
          newMenuItems.forEach((item) => {
            if (item.router.name == 'print_file_error') {
              item.total = this.countPrintFileError ?? 0;
            }
          });
          newMenuItems = [...this.sidebarOrderStatus, ...newMenuItems];
          if (this.getCurrentModuleMenu == 'orders') {
            this.$nextTick(() => {
              this.newMenuItems = [
                ...new Map(
                  newMenuItems.map((item) => [item['name'], item])
                ).values(),
              ];
            });
          }
          this.isLoaded = true;
          this.isLoadingMenu = false;
          return;
        case this.INVENTORY:
          this.newMenuItems = sidebar;
          await this.getCountInternalRequest();
          await this.getCountPendingStockRequest();
          this.newMenuItems.forEach((item) => {
            if (item.router.name == this.INTERNAL_REQUEST) {
              item.total = this.countNewInternal ?? 0;
            }
            if (item.router.name == this.STOCK_TRANSFER) {
              item.total = this.countNewStockTransfer ?? 0;
            }
          });
          break;
        case this.TICKET:
          this.newMenuItems = sidebar;
          await Promise.all([
            this.getCountTicket(),
            this.getCountMaintenance(),
            this.getCountClaim()
          ])
          this.newMenuItems.forEach((item) => {
            if (item.router.name == this.INTERNAL_TICKET) {
              item.total = this.countTicket ?? 0;
            }
            if (item.router.name == this.MAINTENANCE) {
              item.total = this.countMaintenance ?? 0;
            }
            if (item.router.name == this.CLAIM_ORDERS) {
              item.total = this.countClaim ?? 0;
            }
          });
          break;
        default:
          this.newMenuItems = sidebar;
          break;
      }
      if (this.userWarehouseId != this.MEXICO_WAREHOUSE_ID) {
        this.newMenuItems = this.newMenuItems.filter((item) => {
          return !this.onlyRouteMX.includes(item.router.name);
        });
      }
      this.isLoaded = true;
      this.isLoadingMenu = false;
    },
    mergeItem(item, index) {
      return {
        ...item,
        index,
      };
    },
    setIsCollapse() {
      this.$store.commit('setIsCollapse');
    },
    renderIcon(item) {
      return (item.icon && this[item.icon]) || '';
    },
    activeClass(item, subItem) {
      if (this.$route.name == this.ORDER_ROUTE_NAME) {
        if (subItem && this.$route?.query?.store_id != subItem.id) {
          return false;
        }
        if (this.$route?.query?.order_status == item.order_status) {
          this.activeIndex = String(item.index);
          if (this.indexOpen == this.activeIndex) {
            this.$nextTick(() => {
              this.$refs.sidebarMenu.open(String(this.indexOpen));
            });
          }
          return true;
        } else {
          return false;
        }
      } else {
        return;
      }
    },
    routerActiveClass(item, subItem) {
      const currentRouteName = this.$route.name;
      if (currentRouteName == this.ORDER_ROUTE_NAME) {
        if (!this.$route?.query?.order_status && item.id == 'all_status') {
          return true;
        } else {
          return false;
        }
      } else {
        let routerNames = [];
        routerNames.push(item.router.name);
        if (item.allowView?.length) {
          routerNames.push(...item.allowView);
        }
        if (subItem && subItem.router && subItem.router.name) {
          routerNames.push(subItem.router.name);
        } else {
          if (item.sub && item.sub.length > 0) {
            let isExistRoute = item.sub.find((item) => {
              return item.router.name == currentRouteName;
            });
            if (isExistRoute) return true;
          }
          routerNames.push(item.router.name);
        }
        const currentRouteNameMetaGroup =
          (this.$route && this.$route.meta && this.$route.meta.group) || '';
        if (currentRouteNameMetaGroup) {
          if (routerNames.includes(currentRouteNameMetaGroup)) {
            this.activeIndex = String(item.index);
            return true;
          } else {
            return false;
          }
        }
        if (routerNames.includes(currentRouteName)) {
          this.activeIndex = String(item.index);
          if (item?.router?.params) {
            return equals(item.router.params, this.$route.params);
          }
          if (subItem?.router?.query) {
            return equals(subItem.router.query, this.$route.query);
          }
          return true;
        } else {
          return false;
        }
      }
    },
    routerRedirect(item) {
      if (
        item.router.name == this.ORDER_ROUTE_NAME &&
        this.$route.name == this.ORDER_ROUTE_NAME
      ) {
        this.filterOrder(item);
        return;
      }
      this.filterOrders.keyword = '';
      const routerName = item.router.name || '';
      if (!routerName) {
        return;
      }
      if (item?.router?.params) {
        return this.$router.push({
          name: routerName,
          params: item.router.params,
        });
      }
      return this.$router.push({
        name: routerName,
        query: { ...item.router.query },
      });
    },
    renderSubmenuPerformaceReport(warehouseIds) {
      let items = [];
      for (let i = 0; i <= this.getPerformanceReportDepartments.length; i++) {
        const item = this.getPerformanceReportDepartments[i];
        if (!item) continue;
        items.push({
          type: 'item',
          name: item.name,
          is_show: true,
          warehouseIds: warehouseIds,
          router: {
            name: `performance_report`,
            query: {
              department_id: String(item.id),
            },
          },
        });
      }
      return items;
    },
    renderMenuSupportDocs() {
      let items = [
        {
          type: 'item',
          name: 'All',
          icon: 'iconDocument',
          router: {
            name: 'document',
          },
        },
      ];
      for (let i = 0; i <= this.getDocCategories.length; i++) {
        const item = this.getDocCategories[i];
        if (!item) continue;
        items.push({
          type: 'item',
          name: item.name,
          is_show: true,
          icon: 'iconDocument',
          router: {
            name: `documentByCategory`,
            params: {
              doc_category_id: String(item.id),
            },
          },
        });
      }
      return items;
    },
  },
};
</script>
