<template>
  <el-tooltip :content="displayTooltip(type, data)" placement="top">
    <el-tag
      type="info"
      :size="size"
      class="min-w-[40px] customTag"
      :style="getSignTagStyle(type)"
      @click="openNewTab(data?.url)"
    >
      {{ viewType ? $t(`${displaySignTagViewType(type, data)}`) : $t(`${displaySignTag(type, data)}`) }}
    </el-tag>
  </el-tooltip>
</template>

<script>
export default {
  props: {
    type: {
      type: String,
      default: '',
    },
    data: {
      type: Object,
      default: null,
    },
    size: {
      type: String,
      default: 'small',
    },
    viewType: {
      type: String,
      default: null,
    }
  },
  methods: {
    displayTooltip(key, data) {
      switch (key) {
        case 'packing_slip':
          return `Packing Slip (${data.size})`;
        case 'thankyou_card':
          return `Thank You Card (${data.size})`;
        case 'gift_message':
          return `Gift Message (${data.size})`;
        default:
          return '';
      }
    },
    displaySignTag(key, data) {
      switch (key) {
        case 'packing_slip':
          return `PS (${data.size})`;
        case 'thankyou_card':
          return `TU (${data.size})`;
        case 'gift_message':
          return `GM (${data.size})`;
        default:
          return '';
      }
    },
    getSignTagStyle(key) {
      switch (key) {
        case 'packing_slip':
          return ' background: #7efdbc; color: black;';
        case 'thankyou_card':
          return ' background: #f9ce70; color: black;';
        case 'gift_message':
          return ' background: #F7AAF9; color: black;';
        default:
          return '';
      }
    },
    openNewTab(url) {
      return url && window.open(url);
    },
    displaySignTagViewType(key, data) {
      switch (key) {
        case 'packing_slip':
          return `Packing Slip`;
        case 'thankyou_card':
          return `Thank You Card`;
        case 'gift_message':
          return `Gift Message`;
        default:
          return '';
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.customTag {
  cursor: pointer;
  // height: 30px;
  // font-size: 16px;
  padding: 0 12px;
  color: white;
  border: none;
  border-radius: 100px;
  border: none;
  border-radius: 100px;
  user-select: none;
  &:hover {
    box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
  }
}
</style>
