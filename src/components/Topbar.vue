<template>
  <div
    class="shadow items-center absolute w-screen top-0 left-0 flex justify-between h-[50px] border-b z-[99]"
    :class="{
      'bg-amber-50': !userWarehouseColor && userWarehouseId == 2,
      'bg-sky-100': !userWarehouseColor && userWarehouseId == 16,
    }"
    :style="{ backgroundColor: userWarehouseColor }"
  >
    <div class="h-full flex flex-1 items-center relative">
      <div class="flex items-center font-semibold gap-4 mx-6 whitespace-nowrap">
        <router-link @click="setCurrentModuleMenu(menuItems[0])" to="/"
          ><img class="w-5 h-auto" src="@/assets/images/logo.svg"
        /></router-link>
        <span>SWIFTPOD</span>
      </div>
      <div class="h-full hidden xl:flex items-center flex-1">
        <template v-for="item in menuItems" :key="item.id">
          <div
            class="h-full font-semibold flex items-center justify-center px-5 cursor-pointer whitespace-nowrap active:opacity-80 hover:text-primary active:text-primary active:border-b-[2px] border-[#1a73e8] active:bg-slate-50"
            @click="setCurrentModuleMenu(item)"
            :class="{
              '!text-[#1a73e8] !border-b-[2px] !border-[#1a73e8] pointer-events-none':
                getCurrentModuleMenu === item.id,
            }"
            :disabled="item.id === getCurrentModuleMenu"
            v-if="showMenuItemByWarehouse(item)"
          >
            <div class="relative">
              {{ $t(item.name) }}
              <icon v-if="item?.iconTopBar && isChangelog" class="!text-red-700 mb-3 text-xs" :data="iconError2"></icon>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div class="flex items-center h-full relative mx-4">
      <div
        class="flex flex-nowrap items-center divide-x-2 cursor-pointer h-full font-bold z-[99]"
        :class="{
          'bg-amber-50': !userWarehouseColor && userWarehouseId == 2,
          'bg-sky-100': !userWarehouseColor && userWarehouseId == 16,
        }"
        :style="{ backgroundColor: userWarehouseColor }"
      >
        <span class="flex flex-nowrap items-center pl-2 whitespace-nowrap">
          <img
            class="h-[30px] m-auto rounded"
            :src="userWarehouseImage"
            alt=""
          />
          <span
            class="px-[10px] custom-warehouse py-[4px] flex items-center whitespace-nowrap"
            >{{ userWarehouseName }}</span
          >
        </span>
        <span
          class="flex flex-nowrap items-center px-2 gap-1 whitespace-nowrap relative select-none"
          @click.stop="showLogoutPopup"
        >
          <icon :data="iconUser" />
          {{ getUserProfile.username || '' }}
          <icon :data="iconChevronDown" />
          <span
            id="logoutPopup"
            class="active:!text-[#1a73e8] font-normal flex flex-nowrap gap-2 items-center absolute top-[calc(100%+8px)] right-0 p-4 rounded-md bg-white shadow border min-w-[120px]"
            @click.stop="
              showLogout = false;
              logout();
            "
            v-if="showLogout"
          >
            <icon :data="iconLogout" />
            {{ $t('Logout') }}
          </span>
        </span>
      </div>
      <div
        class="block xl:hidden shrink-0 text-slate-800 active:!text-[#1a73e8] w-6 h-6 active:text-primary cursor-pointer"
        @click="showMenu = true"
      >
        <icon :data="iconAll" />
      </div>
    </div>
    <Teleport to="body">
      <div
        class="fixed w-screen h-screen bg-[rgba(0,0,0,0.3)] !z-[999999] top-0 left-0"
        @click="this.showMenu = false"
        v-if="this.showMenu"
      >
        <transition name="slide-fade-right" appear>
          <div
            class="w-[240px] h-[100vh] flex flex-col fixed z-[999] bg-white shadow-xl border-l top-0 right-0 pt-0"
            @click.stop=""
          >
            <div
              class="sticky top-0 py-3 bg-white w-full z-10 border-b flex justify-between"
            >
              <div
                class="flex items-center font-semibold gap-4 mx-6 whitespace-nowrap"
              >
                <router-link @click="setCurrentModuleMenu(menuItems[0])" to="/"
                  ><img class="w-5 h-auto" src="@/assets/images/logo.svg"
                /></router-link>
                <span>SWIFTPOD</span>
              </div>
              <span
                class="cursor-pointer rounded-full mr-4"
                @click="this.showMenu = false"
              >
                <icon :data="iconClose" />
              </span>
            </div>
            <template v-for="item in menuItems" :key="item.id">
              <div
                class="p-4 font-semibold flex items-center cursor-pointer whitespace-nowrap active:opacity-80 hover:text-primary active:text-primary active:border-b-[2px] border-[#1a73e8] active:bg-slate-50"
                @click="setCurrentModuleMenu(item)"
                :class="{
                  '!text-[#1a73e8] !border-b-[2px] !border-[#1a73e8] pointer-events-none':
                    getCurrentModuleMenu === item.id,
                }"
                :disabled="item.id === getCurrentModuleMenu"
                v-if="showMenuItemByWarehouse(item)"
              >
                {{ $t(item.name) }}
              </div>
            </template>
          </div>
        </transition>
      </div>
    </Teleport>
  </div>
</template>

<script>
import {mapGetters} from 'vuex';
import authMixin from '@/mixins/auth';
import menuItemsMixin from '@/mixins/menuItems';
import warehouseMixin from '@/mixins/warehouse';
import EventBus from '@/utilities/eventBus.js';
import {get} from "@/api/changelog"

export default {
  name: 'Topbar',
  mixins: [authMixin, menuItemsMixin, warehouseMixin],
  data() {
    return {
      showLogout: false,
      showMenu: false,
      isChangelog: false
    };
  },
  computed: {
    ...mapGetters(['getUserProfile']),
    ...mapGetters(['getCurrentModuleMenu']),
    ...mapGetters(['getCurrentLang']),
  },
  mounted() {
   this.fetchDataSpa()
  },
  created() {
    EventBus.$on('setWarehouseId', (id) => {
      this.userWarehouseId = id;
      this.userWarehouse = this.getWarehouse();
    });
  },
  watch: {},
  methods: {
    setLang(lang) {
      this.$i18n.locale = lang;
      this.$store.commit('setCurrentLang', lang);
    },
    setCurrentModuleMenu(item) {
      if (item.id === 'change_log') {
        const changelog = this.getCookie('changeLog')
        if (changelog) {
          const parsedChangelog = JSON.parse(changelog);
          this.setCookie(
              'changeLog',
              JSON.stringify({ createdAt: parsedChangelog.createdAt, isClickChangelog: true }),
              10 * 365
          );
          this.isChangelog = false;
        }
      }
      this.$store.commit('setCurrentModuleMenu', item.id);
      this.$router.push({ name: item.router.name });
    },
    showLogoutPopup() {
      this.showLogout = true;
      const _this = this;
      document.addEventListener('click', function (event) {
        var element = document.getElementById('logoutPopup'); // Replace with your element's ID
        if (!!element && !element.contains(event.target)) {
          // Clicked outside the element
          // Perform your desired action here
          _this.showLogout = false;
        }
      });
    },
    async fetchDataSpa() {
      const data = await get({source: 'spa'});
      if (data.data && Object.keys(data.data).length == 0) {
        this.isChangelog = false;
        return;
      }
      let changelog = this.getCookie('changeLog')
      const currentUpdatedAt = data.data.updated_at || ''
      if (changelog) {
        const { createdAt, isClickChangelog } = JSON.parse(changelog);
        // Kiểm tra nếu `createdAt` khớp với `dataSpaUpdatedAt`
        this.isChangelog = createdAt !== currentUpdatedAt || !isClickChangelog;
        // Nếu `createdAt` không khớp, cập nhật cookie
        if (createdAt !== currentUpdatedAt) {
          this.setCookie('changeLog', JSON.stringify({ createdAt: currentUpdatedAt, isClickChangelog: false }), 10 * 365);
        }
      } else {
        this.setCookie('changeLog', JSON.stringify({ createdAt: currentUpdatedAt, isClickChangelog: false }), 10 * 365);
        this.isChangelog = true;
      }
    },
    getCookie(name) {
      const value = `; ${document.cookie}`;
      const parts = value.split(`; ${name}=`);
      if (parts.length === 2) return parts.pop().split(';').shift();
    },
    setCookie(name, value, days) {
      let expires = "";
      if (days) {
        const date = new Date();
        date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000); // Số ngày
        expires = "; expires=" + date.toUTCString();
      }
      document.cookie = name + "=" + (value || "") + expires + "; path=/";
    },


  },
};
</script>
