<template>
  <el-dialog
    v-model="openDialogAddFilter"
    :title="$t('Save Filter')"
    custom-class="el-dialog-custom el-dialog-custom2 el-dialog-medium"
    :close-on-click-modal="false"
    @close="addFilterName = ''"
  >
    <template #default>
      <div class="add-location">
        <el-form
          status-icon
          @submit.prevent="saveFilter()"
          label-width="130px"
          :label-position="'left'"
        >
          <el-form-item :label="$t('Filter Name')" prop="barcode">
            <el-input
              v-model="addFilterName"
              type="text"
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
    </template>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="saveFilter()">{{
          $t("Save Filter")
        }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script>
export default {
  name: "filterAdd",
  mixins: [],
  components: {},
  computed: {},
  created() {},
  methods: {},
  mounted() {},
};
</script>
<style lang="scss"></style>
