<template>
  <el-select filterable multiple :placeholder="$t('Select tag')" popper-class="mb-8 no-clear-icon">
    <el-option
      v-for="item in listTags"
      :key="item.id"
      :label="item.name"
      :value="String(item.id)"
    >
      <span class="flex items-center justify-space-between">
        <!-- <el-tag
             size="small"
             class="mr-2 w-5 h-5 radius"
             type="info"
             :color="item.color"
             v-if="item.color"
             style="color: #fff"
           ></el-tag>
           <el-tag
             size="small"
             class="mr-2 w-5 h-5 radius"
             type="info"
             v-else
           ></el-tag> -->
        <div :style="`background: ${item.color}`" class="leading-none text-white rounded py-1 px-2">{{ item.name }}</div>
        <div class="leading-none rounded-full border-gray-400 border-solid border-2 px-2 py-1" v-if="item.account">{{item.account.name}}</div>
      </span>
    </el-option>
  </el-select>
</template>

<script>
import {clone} from "ramda";

export default {
  name: "FormTag",
  components: {},
  mixins: [],
  props: {
    tags: {
      type: Array,
      default: [],
    },
    selected: {
      type: Array,
      default: [],
    },
    account: {
      type: String,
      default: '',
    },
    showLabelTag: {
      type: Boolean,
      default: true,
    },
    protected: {
      type: Array,
      default: [],
    },
  },
  computed: {},
  data() {
    return {
      system: 'system',
      listTags: [],
    };
  },
  watch: {
    tags: {
      handler: function (value) {
        this.fetchListTags();
      },
      deep: true,
    },
    protected: {
      handler: function (value) {
        this.fetchListTags();
      },
      deep: true,
    },
  },
  mounted() {
    this.fetchListTags();
  },
  methods: {
    fetchListTags() {
      if (this.account == this.system) {
        this.listTags = clone(this.tags).filter((item) => item.account_id === 0);
      } else {
        this.listTags = clone(this.tags);
      }
      if (!this.showLabelTag) {
        this.listTags = clone(this.listTags).filter((item) => item.id !== 203);
      }
      if (this.protected.length) {
        this.listTags = clone(this.listTags).filter((item) => !this.protected.includes(String(item.id)));
      }
    },
  },
};
</script>
