import { createApp } from "vue";
import App from "./App.vue";
import "./assets/styles/base/_tailwind.css";
import ElementPlus from "element-plus";
import "element-plus/theme-chalk/src/index.scss";

import "./assets/styles/main.scss";

import router from "./router";
import store from "./store";
import "./plugins";
import svgIcon from "./mixins/svgIcon";
import mixinDate from "./mixins/date";
import notification from "./mixins/notification";
import mixinFilter from "@/mixins/filter";
import mixinFormatNumber from "@/mixins/formatNumber";

import helpers from "./mixins/helpers";

import { VueSvgIconPlugin } from "@yzfe/vue3-svgicon";
import "@yzfe/svgicon/lib/svgicon.css";

import author from "@/utilities/author.js";
import CanvasJSChart from '@canvasjs/vue-charts';

const app = createApp(App);
app.config.globalProperties.$t = (v) => v;
app.config.globalProperties.$tc = (v) => v;

app.config.globalProperties.$author = author;
app.config.globalProperties.$app = app;
app.mixin(svgIcon);
app.mixin(mixinDate);
app.mixin(notification);
app.mixin(mixinFilter);
app.mixin(helpers);
app.mixin(mixinFormatNumber);
app.use(router);
app.use(store);
app.use(ElementPlus);
app.use(VueSvgIconPlugin, {
  tagName: "icon",
});
app.use(CanvasJSChart);
app.mount("#app");
