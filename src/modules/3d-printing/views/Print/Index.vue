<style src="./Style.scss" lang="scss">
</style>
<script src="./Script.js"></script>
<template>
  <div class="mug-convert">
    <div class="border-b pb-4 flex flex-row items-center">
      <div class="mr-5 ">
        <h3>{{ $t("3D MUG") }}</h3>
      </div>
      <div class="text-right	grow ">{{ $t("Total Pending: ") }}
        <span class="font-semibold">{{ dataCount.total }}</span>
      </div>
    </div>
    <div class="flex flex-row">
      <div class="basis-1/5 border-r">
        <div v-if="dataCount?.data?.length == 0">
          <el-empty :description="$t('no data')"></el-empty>
        </div>
        <el-scrollbar style="height: calc(100vh - 150px);">
          <div v-for="item in dataCount.data" :key="item.product_id" @click="setConvert(item)"
            class="scrollbar-item bg-gradient-to-r from-cyan-200 to-blue-300 hover:from-cyan-300 hover:to-blue-400 my-3 mr-3 p-3"
            :class="{ 'border-4 border-blue-500/75': item?.product_id == stylePicked?.product_id }">
            <!-- <div class="name">{{ $t("MUG") }}</div> -->
            <div class="name">{{ item.style_name + ' ' + item.size + 'Z ' + item.color }}</div>
            <div class="sub">
              {{ $t("Pending: ") }}<strong>{{ item.count }}</strong><br> {{ $t("Last Created: ") }}
              {{ item.last_created_at ? utcToLocalTime(item.last_created_at).format('lll') : 'not yet' }}
            </div>
          </div>
        </el-scrollbar>
      </div>
      <div class="basis-4/5">
        <div class="p-3">
          <el-input-number v-model="valueInput" class="mr-3" style="width: 150px" :min="0"
            :max="stylePicked?.count <= 30 ? stylePicked?.count : 30" />
          <el-button @click="confirmConvertBtn" type="primary">{{ $t("Confirm") }}</el-button>

        </div>
        <el-dialog v-model="dialogVisible" :title="$t('Assign To Staff')" width="30%">
          <div>
            <el-input class="m-0" v-model="employee_number" size="large" :placeholder="$t('Enter The Staff Number')">
            </el-input>
          </div>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="dialogVisible = false">{{ $t("Cancel") }}</el-button>
              <el-button type="primary" @click="confirmStaff()">{{ $t("Confirm") }}</el-button>
            </span>
          </template>
        </el-dialog>
        <el-tabs v-model="activeTab" class="ml-3">
          <el-tab-pane :label="$t('Convert')" name="convert">
            <el-table stripe :data="pendingList.data" style="width: 100%">
              <el-table-column prop="id" :label="$t('ID')" width="120" />
              <el-table-column :label="$t('Created at')" width="180">
                <template #default="scope">
                  {{ utcToLocalTime(scope.row.created_at).format('lll') }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('Filter')">
                <template #default="scope">
                  <el-tag class="mr-2 mb-1" size="small" type="success">
                    {{ scope.row.style_sku + ' ' + scope.row.product.size + 'Z ' + scope.row.product.color }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column prop="employee_name" :label="$t('Staff')">
                <template #default="scope">
                  {{ scope.row.employee_convert.name }}
                </template>
              </el-table-column>
              <el-table-column prop="quantity" :label="$t('Quantity')" width="100">
                <template #default="scope">
                  {{ scope.row.convert_percent }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('Status')" width="160">
                <template #default="scope">
                  <el-tag v-if="+scope.row.convert_status == 1" type="success">{{ $t("Completed") }}</el-tag>
                  <el-tag v-else-if="+scope.row.convert_status == 2" type="danger">{{ $t("Error") }}</el-tag>
                  <el-tag v-else type="warning">{{ $t("Pending") }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column width="150" :label="$t('Action')">
                <template #default="scope">
                  <template v-if="scope.row.print_status == 0">
                    <el-button size="small" :disabled="scope.row.convert_status !== 1" type="primary"
                      @click="popupDownloadPdf(scope.row)">
                      {{ $t("Send to Photoshop") }}
                    </el-button>
                  </template>
                  <template v-if="scope.row.print_status == 1">
                    <el-button size="small" :disabled="scope.row.convert_status !== 1" type="info">{{ $t("Send to Photoshop")
                    }}
                    </el-button>
                  </template>
                </template>
              </el-table-column>
            </el-table>
            <div class="mt-3 items-center fixed">
              <div class="flex">
<!--                <el-input v-model="clientId"  class="ml-2">
                  <template #append>
                    <el-button @click="saveClientId">{{ $t("Save Client ID") }}</el-button>
                  </template>
                </el-input>-->
                <el-pagination background :page-size="limit" :pager-count="10" layout="prev, pager, next"
                               :total="pendingList.total" @current-change="changePage">
                </el-pagination>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane :label="$t('History')" name="history">
            <el-table stripe :data="historyList.data" style="width: 100%">
              <el-table-column prop="id" :label="$t('ID')" width="120" />
              <el-table-column :label="$t('Created at')" width="180">
                <template #default="scope">
                  {{ utcToLocalTime(scope.row.created_at).format('lll') }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('Filter')">
                <template #default="scope">
                  <el-tag class="mr-2 mb-1" size="small" type="success">
                    {{ scope.row.style_sku + ' ' + scope.row.product.size + 'Z ' + scope.row.product.color }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="employee_name" :label="$t('Staff')">
                <template #default="scope">
                  {{ scope.row.employee_convert.name }}
                </template>
              </el-table-column>
              <el-table-column prop="quantity" :label="$t('Quantity')" width="100">
                <template #default="scope">
                  {{ scope.row.convert_percent }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('Status')" width="160">
                <template #default="scope">
                  <el-tag v-if="+scope.row.convert_status == 1" type="success">{{ $t("Completed") }}</el-tag>
                  <template v-else>
                    <el-tag type="warning">{{ $t("Pending") }}</el-tag>
                  </template>
                </template>
              </el-table-column>
              <el-table-column :label="$t('Action')">
                <template #default="scope">
                    <el-button size="small" type="primary" @click="popupDownloadPdfHistory(scope.row)">
                    {{ $t("Send to Photoshop") }}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="mt-3 flex items-center">
              <div class="flex">
                <el-input v-model="searchLabelId" :placeholder="$t('Search Label ID')">
                  <template #append>
                    <el-button @click="searchLabelDownloaded">{{ $t("Search") }}</el-button>
                  </template>
                </el-input>
<!--                <el-input v-model="clientId"  class="ml-2">
                  <template #append>
                    <el-button @click="saveClientId">{{ $t("Save Client ID") }}</el-button>
                  </template>
                </el-input>-->
              </div>
              <el-pagination class="ml-5" background :page-size="limit" :pager-count="10" layout="prev, pager, next"
                :total="historyList.total" @current-change="changePageHistory">
              </el-pagination>
            </div>
          </el-tab-pane>
        </el-tabs>
        <el-dialog v-model="dialogVisibleDownload" :title="$t('Preview MUGS')" width="60%" top="5vh">
          <iframe ref="theFrame" class="m-0 p-0" :src="pdf + '#view=Fit'"
          style="height: 500px; width: 100%;"></iframe>
          <!-- <vue-pdf-embed v-loading="loadingPdf" :source="pdf" @rendered="handleRenderPdf" /> -->
          <div class="mt-3">
            <el-button type="primary" @click="confirmDownloadedBtn($event)">{{ $t("Confirm") }}
            </el-button>
          </div>
        </el-dialog>
        <el-dialog v-model="dialogVisibleDownloadHistory" :before-close="handleCloseHistory" :title="$t('Preview MUGS')" width="60%" top="5vh">
          <div v-if="!unlockHistory" class="bg-slate-200" style="height: 450px"></div>
          <iframe v-else ref="theFrame" class="m-0 p-0" :src="pdfHistory + '#view=Fit'"
          style="height: 500px; width: 100%;"></iframe>
          <!-- <vue-pdf-embed v-loading="loadingPdfHistory" :source="pdfHistory" @rendered="handleRenderPdfHistory" /> -->
          <h2 class="mt-2 mb-2"><strong>{{ $t("Enter Password To Unlock Download") }}</strong></h2>
          <el-input v-model="password" />
          <div class="mt-3">
            <el-button type="primary" @click="confirmDownloadedHistoryBtn($event)">{{ $t("Confirm") }}
            </el-button>
          </div>
        </el-dialog>
      </div>
    </div>
  </div>
</template>