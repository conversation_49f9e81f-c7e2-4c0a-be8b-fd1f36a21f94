<style src="./Style.scss" lang="scss" scoped>
</style>
<script src="./Script.js"></script>
<template>
  <div class="content">
    <HeaderMobile :name="$t('Ajust fulling shelves')"></HeaderMobile>
    <div class="content-body">
      <div class="add-inventory-addition flex-col">
        <el-form :label-position="'top'">
          <div class="bg-gray-50 p-3 border rounded mb-3">
            <el-form-item v-show="!Object.keys(employee).length" :error="employeeError" :label="$t('Employee ID')"
              required>
              <el-input v-model="employeeID" @change="scanEmployeeID"
                class="el-form-item-employee"></el-input>
            </el-form-item>
            <div v-if="Object.keys(employee).length">
              <div class="flex justify-between">
                <b class="text-base">Hi {{ employee.name }}, Have a nice day!</b>
                <el-link type="danger" @click="resetEmployee" :underline="false">{{ $t('Logout') }}</el-link>
              </div>
              <div class="text-lg text-fuchsia-500">
                <IncrementTimer />
              </div>
            </div>
          </div>
          <el-form-item :label="$t('SKU')" prop="sku" class="el-form-item-sku d-flex">
            <el-input class="flex-1" v-model="sku" @change="scanProductBySku">
            </el-input>
          </el-form-item>
        </el-form>
        <div class="mt-4 w-full border p-3">
          <div>
            <div class="text-lg">{{ $t('Manual SKU select') }}</div>
            <div class="text-[12px] text-red-500">{{ $t('In case you can\'t scan product SKU please select manual.') }}
            </div>
          </div>
          <el-form class="mt-2 w-full">
            <el-form-item prop="style">
              <el-select v-model="filter.product_style" @change="changeStyle" filterable :placeholder="$t('Select Style')"
                size="large" clearable @clear="onClearFilter">
                <el-option v-for="item in styles" :key="item.style" :label="item.style" :value="item.style">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="color">
              <el-select v-model="filter.product_color" @change="filterProductSku" filterable :placeholder="$t('Select Color')"
                size="large" clearable>
                <el-option v-for="item in colors" :key="item" :label="item" :value="item">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="size">
              <el-select v-model="filter.product_size" @change="filterProductSku" filterable :placeholder="$t('Select Size')"
                size="large" clearable>
                <el-option v-for="item in sizes" :key="item" :label="item" :value="item">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div v-if="quantity_available !== null">
          <el-form label-position="left" label-width="60%" @submit.prevent="onSubmit()">
            <el-form-item label="Quantity available">
              <div class="text-center w-100">
                <strong>{{ quantity_available }}</strong>
              </div>
            </el-form-item>
            <el-form-item label="New quantity on hand">
              <el-input-number v-model="quantity_on_hand" type="number" class="text-center w-100"/>
            </el-form-item>
            <el-form-item label="Quantity adjusted">
              <div class="text-center w-100">
                <strong>{{ adjustQuantity }}</strong>
              </div>
            </el-form-item>
          </el-form>
          <div class="w-100 text-right">
            <el-link type="danger" @click="onClearFilter" :underline="false" class="mr-3">
              {{ $t("Clear") }}
            </el-link>
            <el-button @click="onSubmit" type="primary" size="large" class="ml-3">Submit</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
