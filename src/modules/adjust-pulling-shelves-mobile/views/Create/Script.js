import { employeeLogoutTimeChecking, employeeTimeChecking } from "@/api/employee.js";
import { getProductAttributes, getProductByParams, getProductBySku, getPullingShelvesProductQuantity } from "@/api/product.js";
import { create } from "@/api/adjustPullingShelves";
import IncrementTimer from "@/components/IncrementTimer.vue";
import HeaderMobile from "@/components/HeaderMobile.vue";
import { isEmpty } from "ramda";

export default {
    name: "Add Just Fulling Shelves Mobile",
    components: {
        IncrementTimer,
        HeaderMobile
    },
    mixins: [],
    computed: {
        adjustQuantity() {
            return this.quantity_on_hand - this.quantity_available;
        },
    },
    data() {
        return {
            employeeID: '',
            employee: {},
            employeeError: '',
            id_time_checking: null,
            job_type: 'filling_shelves',
            sku: null,
            styles: [],
            colors: [],
            sizes: [],
            filter: this.setDefaultFilter(),
            quantity_on_hand: null,
            quantity_available: null,
        };
    },
    mounted() {
        this.fetchProductAttributes();
    },
    methods: {
        async resetEmployee() {
            const res = await employeeLogoutTimeChecking(this.id_time_checking);
            this.employee = {};
            this.employeeError = '';
            this.employeeID = '';
            this.id_time_checking = null
        },
        async scanEmployeeID() {
            if (this.id_time_checking) {
                return true;
            }
            if (!this.employeeID) {
                this.employeeError = "Employee ID field cannot be left blank.";
                return false;
            }

            const res = await employeeTimeChecking({
                code: Number(this.employeeID),
                job_type: this.job_type
            })

            if (!res.data.data) {
                this.employeeError = "Can't find your employee ID, please scan again";
                return false;
            }
            this.employeeError = "";
            this.employee = res.data.data;
            this.id_time_checking = res.data.id_time_checking;
            this.focusByElClass();
            return true;
        },
        changeStyle() {
            const item = this.styles.find((item) => item.style === this.filter.product_style);
            this.filter.product_size = "";
            this.filter.product_color = "";
            this.colors = item?.colors || [];
            this.sizes = item?.sizes || [];
        },
        async fetchProductAttributes() {
            const res = await getProductAttributes();
            const data = res.data || {};
            this.styles = Object.values(data);
        },
        async scanProductBySku() {
            if (!this.scanEmployeeID()) return;
            try {
                const res = await getProductBySku({
                    sku: this.sku
                })
                this.filter.product_style = res.data.style
                this.filter.product_color = res.data.color
                this.filter.product_size = res.data.size
                this.getPullingShelvesProductQuantity()
            } catch (e) {
                const data = e.response.data;

                let message = this.$t('Product not found');
                if (!isEmpty(data)) {
                    message = data.message;
                }
                this.sku = '';
                this.notification(message, "error");

            }
        },
        async filterProductSku() {
            if (this.filter.product_style && this.filter.product_color && this.filter.product_size) {
                const res = await getProductByParams({
                    style: this.filter.product_style,
                    size: this.filter.product_size,
                    color: this.filter.product_color
                });

                const product = res.data ? res.data : {};
                if (!!product && product.hasOwnProperty('sku') && !!product.sku) {
                    this.sku = product.sku;
                    this.getPullingShelvesProductQuantity();
                } else {
                    this.notification(this.$t('SKU not found.'), "error");
                }
            }
        },
        async getPullingShelvesProductQuantity() {
            try {
                if (this.filter.product_style && this.filter.product_color && this.filter.product_size) {
                    const res = await getPullingShelvesProductQuantity(this.filter);
                    this.quantity_available = res.data;
                    this.quantity_on_hand = this.quantity_available;
                    this.notification(this.$t('Get product success'), "success");
                }
            } catch (e) {
                this.onClearFilter()
                const message = this.$t('The product is not found or has been deleted, please select the appropriate attribute again!');
                this.notification(message, "error");
            }
        },
        async onSubmit() {
            if (!this.scanEmployeeID()) return;
            if (Number(this.quantity_available) === this.quantity_on_hand) {
                this.notification(this.$t('The quantity is matched, so no need to adjust!'), "error");
                return;
            }
            this.isLoading = true;
            try {
                let data = {
                    id_time_checking: this.id_time_checking,
                    employee_id: this.employeeID,
                    product_color: this.filter.product_color,
                    product_size: this.filter.product_size,
                    product_style: this.filter.product_style,
                    quantity: this.quantity_on_hand,
                }
                const res = await create(data);
                this.notification(res.data.message);
                this.onClearFilter();
            } catch (e) {
                let message = e.response.data.message || "Adjust pulling shelves error!";
                this.notification(message, "error");
            } finally {
                this.isLoading = false;
            }
        },
        focusByElClass(elClass = "el-form-item-sku") {
            this.$nextTick(() => {
                const el = document.getElementsByClassName(elClass);
                if (!el || !el.length) {
                    return;
                }
                const firtsElError = (el && el[0]) || undefined;
                const input = firtsElError.querySelector("input");
                document.body.scrollTop = (input.offsetTop - 120) | 0;
                input.focus();
            });
        },

        onClearFilter() {
            this.filter = this.setDefaultFilter();
            this.sku = null;
            this.quantity_available = null;
            this.quantity_on_hand = null;
            this.focusByElClass();
        },
        setDefaultFilter() {
            let params = {
                product_style: "",
                product_size: "",
                product_color: "",
            };
            return params;
        },
    },
};
