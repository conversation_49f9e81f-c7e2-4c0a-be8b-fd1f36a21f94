<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <el-dialog
      v-model="dialogVisible"
      :title="$t('Create Adjust Pulling Shelves')"
      custom-class="el-dialog-custom"
      width="50%"
      :destroy-on-close="true"
  >
    <el-form
        status-icon
        ref="createAdjustPullingShelves"
        :model="data"
        :rules="dataRules"
        @submit.prevent="onSubmit()"
        :label-position="'top'"
        class="w-full"
    >
      <div class="bg-gray-50 p-3 border rounded mb-3">
        <el-form-item v-show="!Object.keys(employee).length" :error="employeeError" :label="$t('Employee ID')" required>
          <el-input v-model="data.employee_id" @keyup.enter="scanEmployeeID"></el-input>
        </el-form-item>
        <div v-if="Object.keys(employee).length" >
          <div class="flex justify-between">
            <b class="text-base">Hi {{ employee.name }}, Have a nice day!</b>
            <el-link type="danger" @click="resetEmployee" :underline="false">{{ $t('Logout') }}</el-link>
          </div>
          <div class="text-lg text-fuchsia-500" >
            <IncrementTimer/>
          </div>
        </div>
      </div>
      <div class="mb-2">
        <el-form-item :label="$t('Scan SKU')" >
          <el-input v-model="productSku" ref="productSku" @keyup.enter="scanProductBySku"></el-input>
        </el-form-item>

      </div>
      <div class="flex mb-2">
        <el-form-item :label="$t('Product Style')" prop="product_style" class="w-full mr-2">
          <el-select
              v-model="data.product_style"
              @change="resetColorSize"
              ref="productStyle"
              @keyup.enter="selectOptionByEnter('style', $event)"
              filterable
              class="w-full"
              :placeholder="$t('Select Style')"
          >
            <el-option
                v-for="item in productStyles"
                :key="item.style"
                :label="item.style"
                :value="item.style"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('Product Color')" prop="product_color" class="w-full mr-2">
          <el-select
              v-model="data.product_color"
              @change="getPullingShelvesProductQuantity"
              ref="productColor"
              @keyup.enter="selectOptionByEnter('color', $event)"
              filterable
              class="w-full"
              :placeholder="$t('Select Color')"
          >
            <el-option
                v-for="item in productColors"
                :key="item"
                :label="item"
                :value="item"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('Product Size')" prop="product_size" class="w-full">
          <el-select
              v-model="data.product_size"
              @change="getPullingShelvesProductQuantity"
              ref="productSize"
              @keyup.enter="selectOptionByEnter('size', $event)"
              filterable
              class="w-full"
              :placeholder="$t('Select Size')"
          >
            <el-option
                v-for="item in productSizes"
                :key="item"
                :label="item"
                :value="item"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </div>
      <div class="flex mb-2">
        <el-form-item :label="$t('Quantity available')" class="w-full mr-2">
          <h3 class="text-center w-full mb-0">
            {{ availableQuantity }}
          </h3>
        </el-form-item>
        <el-form-item :label="$t('New quantity on hand')" prop="quantity" class="w-full mr-2">
          <el-input-number
              v-model="data.quantity"
              :precision="0"
              :step="1"
              :min="0"
              class="w-full"
          />
        </el-form-item>
        <el-form-item :label="$t('Quantity adjusted')" class="w-full">
          <h3 class="text-center w-full mb-0">
            {{ adjustQuantity }}
          </h3>
        </el-form-item>
      </div>
      <div class="flex mb-2">
        <el-form-item :label="$t('Cost Value Available')" class="w-full mr-2">
          <h3 class="text-center w-full mb-0">
            ${{ formatCostValue(costValueAvailable) || 0 }}
          </h3>
        </el-form-item>
        <el-form-item :label="$t('Cost Value On Hand')" prop="quantity_1" class="w-full mr-2">
          <h3 class="text-center w-full mb-0">
            ${{ formatCostValue(costValueOnHand) || 0 }}
          </h3>
        </el-form-item>
        <el-form-item :label="$t('Cost Value Adjusted')" class="w-full">
          <h3 class="text-center w-full mb-0">
            <span v-if="costValueAdjusted < 0">
                 - ${{formatCostValue(Math.abs(costValueAdjusted))}}
            </span>
            <span v-else>
              ${{ formatCostValue(costValueAdjusted)}}
            </span>
          </h3>
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="resetData">{{ $t('Reset') }}</el-button>
      <el-button
          type="primary"
          @click="onSubmit('createAdjustPullingShelves')"
          :disabled="isLoading"
          :loading="isLoading"
      >{{ $t('Create') }}</el-button>
    </template>
  </el-dialog>
</template>
