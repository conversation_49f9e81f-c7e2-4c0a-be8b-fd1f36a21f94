import EventBus from '@/utilities/eventBus';
import {
  getPullingShelvesProductQuantity,
  getProductAttributes,
  getProductBySku,
  getProductByParams,
} from '@/api/product';
import { create } from '@/api/adjustPullingShelves';
import {
  employeeLogoutTimeChecking,
  employeeTimeChecking,
} from '@/api/employee.js';
import IncrementTimer from '@/components/IncrementTimer.vue';
import { isEmpty } from 'ramda';
import { WAREHOUSE_MEXICO } from '@/utilities/constants';

export default {
  name: 'CreateAdjustPullingShelves',
  props: ['employees'],
  components: {
    IncrementTimer,
  },
  data() {
    return {
      dialogVisible: false,
      productStyles: [],
      data: this.setDefaultData(),
      isLoading: false,
      dataRules: {
        quantity: [this.commonRule()],
        product_style: [this.commonRule()],
        product_color: [this.commonRule()],
        product_size: [this.commonRule()],
      },
      availableQuantity: 0,
      price: 0,
      employee: {},
      employeeError: '',
      job_type: 'adjust_pullling_shelves',
      id_time_checking: null,
      productSku: '',
      warehouseMexico: WAREHOUSE_MEXICO,
    };
  },
  watch: {
    data: {
      handler() {
        if (
          !this.data.product_style ||
          !this.data.product_color ||
          !this.data.product_size
        )
          return;
        this.getProductByParams();
      },
      deep: true,
    },
  },
  computed: {
    adjustQuantity() {
      return this.data.quantity - this.availableQuantity;
    },
    productColors() {
      let style = this.productStyles[this.data.product_style];
      return style ? style.colors : [];
    },
    productSizes() {
      let style = this.productStyles[this.data.product_style];
      return style ? style.sizes : [];
    },
    costValueAvailable() {
      return this.availableQuantity * this.price;
    },
    costValueOnHand() {
      return this.data.quantity * this.price;
    },
    costValueAdjusted() {
      return (this.data.quantity - this.availableQuantity) * this.price;
    },
  },
  created() {
    EventBus.$on('showCreateAdjustPullingShelves', () => {
      this.dialogVisible = true;
    });
  },
  mounted() {
    this.getProductAttributes();
  },
  methods: {
    async getProductByParams() {
      this.isLoading = true;
      const payload = {
        style: this.data.product_style,
        color: this.data.product_color,
        size: this.data.product_size,
      };
      const res = await getProductByParams(payload);
      const product = res.data ? res.data : {};
      this.isLoading = false;
      if (!product?.sku) {
        this.notification('Product not found', 'error');
        return;
      }
      this.productSku = product.sku;
    },
    async resetEmployee() {
      const res = await employeeLogoutTimeChecking(this.id_time_checking);
      this.employee = {};
      this.employeeError = '';
      this.data.employee_id = '';
      this.id_time_checking = null;
    },
    async scanEmployeeID() {
      if (this.id_time_checking) {
        return true;
      }
      if (!this.data.employee_id) {
        this.employeeError = 'Employee ID field cannot be left blank.';
        return false;
      }
      const res = await employeeTimeChecking({
        code: Number(this.data.employee_id),
        job_type: this.job_type,
      });
      if (!res.data.data) {
        this.employeeError = "Can't find your employee ID, please scan again";
        return false;
      }
      this.employeeError = '';
      this.employee = res.data.data;
      this.id_time_checking = res.data.id_time_checking;
      this.$refs.productSku.focus();
      return true;
    },
    resetData() {
      const fieldsIgnore = ['employee_id'];
      const defaultData = this.setDefaultData();
      for (let property in defaultData) {
        if (fieldsIgnore.includes(property)) continue;
        this.data[property] = defaultData[property];
      }
      this.productSku = '';
      this.$refs.createAdjustPullingShelves.resetFields();
      this.availableQuantity = 0;
    },
    commonRule() {
      return {
        required: true,
        message: this.$t('This field cannot be left blank.'),
        trigger: 'change',
      };
    },
    setDefaultData() {
      return {
        quantity: '',
        product_style: '',
        product_color: '',
        product_size: '',
        employee_id: '',
      };
    },
    async getProductAttributes() {
      const { data } = await getProductAttributes();
      this.productStyles = data || [];
    },
    resetColorSize() {
      this.data.product_color = '';
      this.data.product_size = '';
    },
    selectOptionByEnter(type, evt) {
      const value = evt.target.value || '';
      if (!value) return;
      if (type === 'style') {
        for (const key in this.productStyles) {
          if (key.toLowerCase() === value.toLowerCase()) {
            this.data.product_style = key;
            this.$refs.productStyle.blur();
            this.resetColorSize();
            return;
          }
        }
      } else if (type === 'color') {
        for (let color of this.productColors) {
          if (value.toLowerCase() === color.toLowerCase()) {
            this.data.product_color = color;
            this.$refs.productColor.blur();
            return;
          }
        }
      } else {
        for (let size of this.productSizes) {
          if (value.toLowerCase() === size.toLowerCase()) {
            this.data.product_size = size;
            this.$refs.productSize.blur();
            return;
          }
        }
      }
    },
    async getPullingShelvesProductQuantity() {
      try {
        if (
          this.data.product_style &&
          this.data.product_color &&
          this.data.product_size
        ) {
          const res = await getPullingShelvesProductQuantity(this.data);
          this.availableQuantity = res.data.quantity;
          this.price = res.data.price;
        }
      } catch (e) {
        this.data.product_style = '';
        this.data.product_color = '';
        this.data.product_size = '';
        const message = this.$t(
          'The product is not found or has been deleted, please select the appropriate attribute again!'
        );
        this.notification(message, 'error');
      }
    },
    async onSubmit(formName) {
      if (!this.scanEmployeeID()) return;
      const isValid = await this.$refs[formName].validate();
      if (!isValid) {
        return;
      }
      if (Number(this.availableQuantity) === this.data.quantity) {
        this.notification(
          this.$t('The quantity is matched, so no need to adjust!'),
          'error'
        );
        return;
      }
      this.isLoading = true;
      this.data.id_time_checking = this.id_time_checking;
      try {
        const res = await create(this.data);
        this.dialogVisible = false;
        this.availableQuantity = this.data.quantity;
        this.notification(res.data.message);
        this.$emit('refresh');
      } catch (e) {
        let message =
          e.response.data.message || 'Adjust pulling shelves error!';
        this.notification(message, 'error');
      } finally {
        this.isLoading = false;
      }
    },
    async scanProductBySku() {
      if (!this.scanEmployeeID()) return;
      try {
        const res = await getProductBySku({
          sku: this.productSku,
        });
        this.data.product_style = res.data.style;
        this.data.product_color = res.data.color;
        this.data.product_size = res.data.size;
        this.getPullingShelvesProductQuantity();
      } catch (e) {
        const data = e.response.data;

        let message = this.$t('Product not found');
        if (!isEmpty(data)) {
          message = data.message;
        }
        this.resetData();
        this.productSku = '';
        this.notification(message, 'error');
      }
    },
    formatCostValue(value){
      return this.formatNumber(parseFloat(value).toFixed(2));
    }
  },
};
