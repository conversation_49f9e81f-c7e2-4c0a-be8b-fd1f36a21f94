<script src="./Script.js"></script>
<template>
  <el-dialog v-model="dialogVisible" :title="$t('Export Adjust Pulling Shelves')" custom-class="el-dialog-custom"
    width="50%" @close="handleDialogClose">

    <div class="flex items-center gap-3 mt-5">
      <div class="whitespace-nowrap">
        {{ $t('Select date range') }}
      </div>
      <el-date-picker v-model="daterange" type="daterange" range-separator="To" start-placeholder="Start date" clearable
        end-placeholder="End date">
      </el-date-picker>
      <el-button type="primary" @click="handleBtnExportClick" :disabled="daterange == null" :loading="isLoading">
        {{ $t('Export') }}
      </el-button>
    </div>

    <el-table class="mt-5" :data="tableData" style="width: 100%" border stripe>
      <el-table-column prop="date" label="Date" min-width="180">
      </el-table-column>
      <el-table-column label="User" min-width="120">
        <template #default="scope">
          <span>{{ scope.row.user.username }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="date_range_filter" label="Date Range Filter" min-width="200">
      </el-table-column>
      <el-table-column label="Status" min-width="120">
        <template #default="scope">
          <span class="text-primary" v-if="scope.row.status == 'pending'">Pending</span>
          <span class="text-success" v-if="scope.row.status == 'completed'">Completed</span>
          <span class="text-danger" v-if="scope.row.status == 'failed'">Failed</span>
        </template>
      </el-table-column>
      <el-table-column label="Action" min-width="120">
        <template #default="scope">
          <el-button :disabled="scope.row.status !== 'completed'" type="primary"
            @click="handleBtnDownloadClick(scope.row.url)">
            {{ $t('Download') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<style scoped>
::v-deep .cell {
  word-break: break-word;
}

::v-deep .el-button svg {
  margin: 0;
  top: 0;
}
</style>
