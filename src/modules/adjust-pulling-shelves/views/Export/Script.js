import EventBus from '@/utilities/eventBus';
import { getList, create } from '@/api/exportHistory';
import moment from 'moment';
const MODULE = 'adjust_pulling_shelves';

export default {
  name: 'ExportAdjustPullingShelves',

  data() {
    return {
      dialogVisible: false,
      isLoading: false,
      tableData: [],
      daterange: null,
      reload: null,
      payload: {
        module: MODULE,
        start_date: "",
        end_date: "",
      },
    };
  },

  created() {
    EventBus.$on('showExportAdjustPullingShelves', () => {
      this.dialogVisible = true;
      this.getList();
      this.reload = setInterval(() => {
        this.getList();
      }, 10000)
    });
  },

  methods: {
    async getList() {
      const res = await getList({ module: MODULE });
      this.tableData = res?.data?.data || [];
    },

    handleDialogClose() {
      this.tableData = [];
      clearInterval(this.reload);
    },

    async handleBtnExportClick() {
      if (this.isLoading) {
        return;
      }

      let startDate = this.daterange?.[0];
      let endDate = this.daterange?.[1];

      if (startDate && endDate) {
        this.payload.start_date = moment(startDate).format('YYYY-MM-DD');
        this.payload.end_date = moment(endDate).format('YYYY-MM-DD');;
        this.isLoading = true;

        try {
          await create(this.payload);
          this.getList();
        } catch (error) {
          console.error(error);
        }

        this.isLoading = false;
      }
    },

    async handleBtnDownloadClick(fileUrl) {
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = '';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
  },
};
