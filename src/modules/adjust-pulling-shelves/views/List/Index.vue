<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head mb-4">
      <div class="top-head-left">
        <h1>{{ $t('Adjust Pulling Shelves') }}</h1>
      </div>
      <div class="top-head-right">
        <el-button type="primary" @click="createAdjustPullingShelves">
          <span class="icon-margin-right">
            <icon :data="iconAdd" />
          </span>{{ $t('Create') }}
        </el-button>
        <el-button type="primary" plain @click="exportAdjustPullingShelves">
          <span>
            <icon :data="iconExport" />
          </span>{{ $t('Export') }}
        </el-button>
      </div>
    </div>

    <div class="table-content">
      <div class="filter">
        <el-select :placeholder="$t('Type')" class="mr-2" v-model="filter.type" @change="onFilter">
          <el-option v-for="item in types" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
        <template v-if="filter.type === 'product_style'">
          <el-select ref="productStyle" v-model="filter.style" @change="resetColorSize"
            @keyup.enter="selectOption('style', $event)" filterable clearable class="mr-2"
            :placeholder="$t('Select style')">
            <el-option v-for="item in styleOptions" :key="item.style" :label="item.style" :value="item.style" />
          </el-select>
          <el-select ref="productColor" v-model="filter.color" @keyup.enter="selectOption('color', $event)" filterable
            clearable class="mr-2" :placeholder="$t('Select color')">
            <el-option v-for="item in colorOptions" :key="item" :label="item" :value="item" />
          </el-select>
          <el-select ref="productSize" v-model="filter.size" @keyup.enter="selectOption('size', $event)" filterable
            clearable class="mr-2" :placeholder="$t('Select size')">
            <el-option v-for="item in sizeOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </template>
        <el-input v-else-if="filter.type === 'product_sku'" :placeholder="$t('Search')" class="search mr-2"
          v-model="filter.keyword" @keyup.enter="onFilter" />
        <el-date-picker v-else v-model="filter.date" class="mr-2" type="daterange" range-separator="To"
          :start-placeholder="$t('Start date')" :end-placeholder="$t('End date')" />
        <div class="btn-filter">
          <template v-if="hasFilter">
            <el-link type="danger" @click="onClearFilter" :underline="false" class="whitespace-nowrap">
              {{ $t('Clear') }}
            </el-link>
          </template>
          <el-button type="primary" @click="onFilter">
            <span class="icon-margin-right">
              <icon :data="iconFilter" />
            </span>{{ $t('Filter') }}
          </el-button>
        </div>
      </div>

      <el-table border stripe size="small" :data="items" :max-height="maxHeight" v-loading="isLoading"
        element-loading-text="Loading...">
        <el-table-column :label="$t('ID')" min-width="100">
          <template #default="scope">
            {{ scope.row.id }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Date')" min-width="200">
          <template #default="scope">
            {{ listViewDateFormat(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Product')" min-width="200">
          <template #default="scope">
            {{ scope.row?.product?.name }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Available')" min-width="100">
          <template #default="scope">
            {{ scope.row.product_available }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('On Hand')" min-width="100">
          <template #default="scope">
            {{ scope.row.product_on_hand }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Adjust')" min-width="100">
          <template #default="scope">
            {{ scope.row.product_adjust }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Cost Value On Hand')" min-width="150">
          <template #default="scope">
            <span v-if="scope.row.cost_value_on_hand">
              ${{ formatNumber(scope.row.cost_value_on_hand) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('Cost Value Adjusted')" min-width="150">
          <template #default="scope">
            <span v-if="scope.row.cost_value_adjusted">
              <template v-if="scope.row.cost_value_adjusted < 0">
                - ${{ formatNumber(Math.abs(scope.row.cost_value_adjusted)) }}
              </template>
              <template v-else>
                ${{ formatNumber(scope.row.cost_value_adjusted) }}
              </template>
            </span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('User')" min-width="100">
          <template #default="scope">
            {{ scope.row?.user?.username }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Employee')" min-width="100">
          <template #default="scope">
            {{ scope.row?.employee?.name }}
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom">
        <div class="total">{{ $t('Total:') }} {{ items.length ? formatNumber(total) : 0 }}</div>
        <el-pagination :disabled="isLoading" background layout="prev, pager, next" :page-size="filter.limit"
          :total="total" @current-change="changePage" v-model:currentPage="filter.page">
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select v-model="filter.limit" :placeholder="$t('Select')" size="mini" @change="onFilter">
            <el-option v-for="item in limits" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
  </div>

  <create-adjust-pulling-shelves @refresh="getAdjustPullingShelvesList" :employees="employees" />
  <export-adjust-pulling-shelves />
</template>