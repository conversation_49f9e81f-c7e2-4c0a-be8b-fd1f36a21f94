import { list } from "@/api/adjustPullingShelves";
import EventBus from "@/utilities/eventBus";
import CreateAdjustPullingShelves from "@/modules/adjust-pulling-shelves/views/Create/Index.vue";
import ExportAdjustPullingShelves from "@/modules/adjust-pulling-shelves/views/Export/Index.vue";
import { getProductAttributes } from "@/api/product";
import { mapGetters } from "vuex";
import { WAREHOUSE_MEXICO } from "@/utilities/constants";

export default {
    name: "AdjustPullingShelves",

    components: {
        CreateAdjustPullingShelves,
        ExportAdjustPullingShelves,
    },

    data() {
        return {
            items: [],
            isLoading: false,
            filter: this.setDefaultFilter(),
            types: [
                {
                    label: this.$t('Product Style'),
                    value: "product_style",
                },
                {
                    label: this.$t('Product SKU'),
                    value: "product_sku",
                },
                {
                    label: this.$t('Date'),
                    value: "date",
                }
            ],
            styleOptions: [],
            warehouseMexico: WAREHOUSE_MEXICO,
        }
    },

    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 230);
        },

        colorOptions() {
            let style = this.styleOptions[this.filter.style];
            return style ? style.colors : [];
        },

        sizeOptions() {
            let style = this.styleOptions[this.filter.style];
            return style ? style.sizes : [];
        },

        ...mapGetters({
            employees: "getEmployees"
        })
    },

    mounted() {
        this.getData();
    },

    methods: {
        async fetchEmployee() {
            await this.$store.dispatch("getEmployees");
        },

        getData() {
            this.filter = this.getRouteParam();
            this.fetchEmployee();
            this.getProductAttributes();
            this.getAdjustPullingShelvesList();
        },

        setDefaultFilter() {
            return {
                limit: 25,
                page: 1,
                type: "product_style",
                keyword: "",
                style: "",
                color: "",
                size: "",
                date: ""
            };
        },

        async getProductAttributes() {
            const { data } = await getProductAttributes();
            this.styleOptions = data || [];
        },

        async getAdjustPullingShelvesList() {
            this.isLoading = true;
            this.setRouteParam('adjust_pulling_shelves');
            const { data } = await list(this.filter);
            this.items = data.data;
            this.total = data.total;
            this.isLoading = false;
        },

        resetColorSize() {
            this.filter.color = '';
            this.filter.size = '';
        },

        selectOption(type, evt) {
            const value = evt.target.value || "";

            if (!value) {
                return;
            }

            if (type === 'style') {
                for (const key in this.styleOptions) {
                    if (key.toLowerCase() === value.toLowerCase()) {
                        this.filter.style = key;
                        this.$refs.productStyle.blur();

                        return;
                    }
                }
            } else if (type === 'color') {
                for (let color of this.colorOptions) {
                    if (value.toLowerCase() === color.toLowerCase()) {
                        this.filter.color = color;
                        this.$refs.productColor.blur();

                        return;
                    }
                }
            } else {
                for (let size of this.sizeOptions) {
                    if (value.toLowerCase() === size.toLowerCase()) {
                        this.filter.size = size;
                        this.$refs.productSize.blur();

                        return;
                    }
                }
            }
        },

        onClearFilter() {
            this.filter = this.setDefaultFilter();
            this.getAdjustPullingShelvesList();
        },

        changePage(page) {
            this.filter.page = page;
            this.getAdjustPullingShelvesList();
        },

        onFilter() {
            this.filter.page = 1;
            this.getAdjustPullingShelvesList();
        },

        createAdjustPullingShelves() {
            EventBus.$emit("showCreateAdjustPullingShelves");
        },

        exportAdjustPullingShelves() {
            EventBus.$emit("showExportAdjustPullingShelves");
        },
    }
}