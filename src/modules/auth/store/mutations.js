import authMixins from '@/mixins/auth';
import notificationMixins from '@/mixins/notification';

export const mutations = {
  setUserProfile(state, user) {
    state.userProfile = user;
  },
  setUserWarehouses(state, userWarehouses) {
    state.userWarehouses = userWarehouses;
  },
  setUsers(state, users) {
    state.users = users;
  },
  setCurrentLang(state, currentLang) {
    state.currentLang = currentLang;
    localStorage.setItem('currentLang', currentLang);
  },

  updateLastActive(state) {
    state.lastActive = Date.now();

    if (state.sessionTimeout) {
      clearTimeout(state.sessionTimeout);
      state.sessionTimeout = null;
    }
  },
};
