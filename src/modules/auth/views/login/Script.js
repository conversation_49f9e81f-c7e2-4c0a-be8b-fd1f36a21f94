import { saveToken, setTokenExpires } from '@/utilities/jwt.js';
import { login, googleLogin } from '@/api/auth.js';
import { selectWarehouse } from '@/api/warehouse.js';
import http from '@/utilities/http.js';
import SidebarItem from '@/utilities/SidebarItem.js';
import { clone } from 'ramda';
import { mapGetters } from 'vuex';
import GoogleLogin from '@/modules/auth/views/login/GoogleLogin.vue';

export default {
  name: 'Login',
  minxins: [],
  components: { GoogleLogin },
  data() {
    return {
      SidebarItem: clone(SidebarItem),
      formData: {
        email: '',
        password: '',
      },
      rules: {
        email: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: 'blur',
          },
          {
            type: 'email',
            message: this.$t('Invalid email.'),
            trigger: ['blur', 'change'],
          },
        ],
        password: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: 'blur',
          },
        ],
      },
      isLoading: false,
      ipViolation: false,

    };
  },
  mounted() {
    this.checkIpViolation();
  },
  beforeUnmount() {
    window.removeEventListener('storage', this.checkIpViolation);
  },

  computed: {
    ...mapGetters(['getSettingByKey']),
    loginBackgroundImage() {
      const loginBackgroundImage = this.getSettingByKey.find(
        (item) => item.label === 'login_background_image'
      );
      if (!loginBackgroundImage?.value || loginBackgroundImage?.value == 0) {
        return;
      }
      return loginBackgroundImage?.value;
    },
    loginHolidayImage() {
      const loginHolidayImage = this.getSettingByKey.find(
        (item) => item.label === 'login_holiday_image'
      );
      if (!loginHolidayImage?.value || loginHolidayImage?.value == 0) {
        return;
      }
      return loginHolidayImage?.value;
    },
  },
  methods: {
    async onSubmit(formName) {
      const isValid = await this.$refs[formName].validate();
      if (!isValid) {
        return;
      }
      this.isLoading = true;

      try {
        const res = await login(this.formData);
        this.isLoading = false;
        const data = res.data || {};
        await this.handleLoginSuccess(data);
      } catch (e) {
        this.isLoading = false;
        let errorMessage = this.$t('Email or password is required');
        if (e.response.status === 401) {
          errorMessage = this.$t('Incorrect password or email.');
        }
        this.notification(errorMessage, 'error');
      }
    },
    getDefaultModule(currentRoleData) {
      this.SidebarItem.forEach((item) => {
        item.sidebar.forEach((sidebarItem) => {
          if (currentRoleData.is_default == sidebarItem.router.name) {
            this.defaultModuleMenu = item.id;
          }
        });
      });
    },
    onReset(formName) {
      this.$refs[formName].resetFields();
    },
    checkIpViolation() {
      const isViolation = window.localStorage.getItem('IP_VIOLATION') === 'true';
      if (isViolation) {
        this.ipViolation = true;
        this.notification(this.$t('Your IP access is not authorized.'), 'error');
        window.localStorage.removeItem('IP_VIOLATION');
      }
    },
    async handleGoogleCredentialResponse(response) {
      this.isLoading = true;
      try {
        const id_token = response.credential
        const res = await googleLogin({ id_token })
        
        await this.handleLoginSuccess(res.data || {});
      } catch (err) {
        const errorMessage = err.response?.data?.message || 'Server Error';
        this.notification(errorMessage, 'error');
      }
      finally {
        this.isLoading = false;
      }
    },

    async handleLoginSuccess(data) {
      let token = data.access_token || '';
      const tokenType = data.token_type || '';
      let tokenExpiresIn = data.expires_in;
      if (!token) {
        this.notification(this.$t('Refresh page.'), 'error');
        return;
      }
      const user = data.user;
      const warehouses = user.warehouses || [];

      this.$store.commit('setUserWarehouses', user.warehouses || []);
      this.$store.commit('setUserProfile', user);
      token = tokenType + ' ' + token;
      saveToken(token);
      setTokenExpires(tokenExpiresIn);
      http.defaults.headers.common['Authorization'] = token;

      // Select warehouse when 1 userWarehouses
      if (warehouses && warehouses.length === 1) {
        const resSelectWarehouse = await selectWarehouse(warehouses[0].id);
        token =
          (resSelectWarehouse.data && resSelectWarehouse.data.access_token) ||
          '';
        tokenExpiresIn =
          (resSelectWarehouse.data && resSelectWarehouse.data.expires_in) ||
          0;

        token = tokenType + ' ' + token;
        saveToken(token);
        setTokenExpires(tokenExpiresIn);
        const currentRole =
          (user.roles && user.roles.length && user.roles[0]) || '';
        const currentRoleData = currentRole && currentRole.data;
        if (!currentRoleData) {
          return;
        }
        this.getDefaultModule(currentRoleData);
        // if (
        //   /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        //     navigator.userAgent
        //   )
        // ) {
        //   this.$router.push({ name: 'menu_mobile' });
        //   return;
        // }
        this.$router.push({ name: currentRoleData.is_default });
        this.$store.commit('setCurrentModuleMenu', this.defaultModuleMenu);
      } else {
        this.$router.push({ name: 'select_warehouse' });
      }
      this.notification(this.$t('Login successfully.'));
  },
  },
};
