<style scoped>
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;600&display=swap');
</style>
<script src="./Script.js"></script>
<template>
  <div v-loading="isLoading">
    <div class="m-4 !text-3xl font-bold" v-if="!isAllowIp">
      <span class="block">{{ $t('403') }}</span>
      <span>{{ $t('Forbidden') }}</span>
    </div>
    <div v-else style="font-family: 'Roboto'">
      <div class="p-3 text-2xl bg-slate-200">
        <div class="uppercase flex justify-between 	">
          <div>
            <span class="font-bold uppercase">DATE:</span> {{ currentDate }}
          </div>
          <div>
            <span class="font-bold uppercase">Time:</span> {{ currentTime }}
          </div>
          <div>
            <span class="font-bold uppercase">DEPARTMENT:</span> {{ departmentName }}
          </div>
          <div>
            <span class="font-bold uppercase">WAREHOUSE:</span> {{ warehouseName }}
          </div>
        </div>
      </div>
      <div class="grid">
        <div class="" :style="{
        height: ((maxHeight / 2)) + 'px'
      }">
          <div class="bg-[#c6e2ff] text-center text-5xl uppercase font-bold  p-3">
            {{ departmentName }} Backlog
          </div>
          <div class="font-bold text-9xl flex items-center justify-center text-primary text-[20rem] " :style="{
          height: ((maxHeight / 2) - 72) + 'px'
        }">
            {{ this.formatNumber(totalAll) }}
          </div>
        </div>
        <div class="grid grid-cols-2 border-b-2 border-b-slate-200" :style="{
        height: ((maxHeight / 2)) + 'px'
      }">
          <div class="border-r-2 border-r-slate-200">
            <div class="bg-[#c6e2ff] text-center text-5xl uppercase font-bold  p-3">
              {{titleLeft}}
            </div>
            <div class="font-bold text-[12rem] flex items-center justify-center text-primary" :style="{
            height: ((maxHeight / 2) - 72) + 'px'
          }">
              {{ this.formatNumber(totalLeft) }}
            </div>
          </div>
          <div class="">
            <div class="bg-[#c6e2ff] text-center text-5xl uppercase font-bold p-3">
              {{titleRight}}
            </div>
            <div class="font-bold text-[12rem] flex items-center justify-center text-primary" :style="{
            height: ((maxHeight / 2) - 72) + 'px'
          }">
              {{ this.formatNumber(totalRight) }}
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>
