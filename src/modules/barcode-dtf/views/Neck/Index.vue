<style src="./Style.scss" lang="scss"></style>
<script src="./Script.js"></script>
<template>
  <div class="dtf-printing">
    <div class="border-b pb-4 flex flex-row items-center">
      <div class="mr-5 ">
        <h3>{{ $t("Create Batch Neck") }}</h3>
      </div>
    </div>

    <div class="flex flex-row">
      <div class="basis-1/4 border-r mt-3 pr-3 shrink-0">
        <ScanEmployee @setEmployee="setEmployee" :jobType="'neck_printing'" />
        <div v-if="Object.keys(employee).length > 0">
          <el-select :disabled="!firstScan" v-model="countrySelected" ref='country' value-key="name" class="mt-3 w-full"
            placeholder="Select Country" @change="selectCountry($event)">
            <el-option v-for="item in listCountries" :key="item.id" :label="item.name" :value="item" />
          </el-select>
          <el-input ref='qrCode' class="mt-3" v-model="label" placeholder="Scan Qr Code" size="default"
            :disabled="loadingScan" @keyup.enter="scanLabelId()" />
          <el-dialog v-model="dialogConfirmLabel" title="Warning" width="30%">
            <span>Label ID has been generated. Do you want to continue?</span>
            <template #footer>
              <span class="dialog-footer">
                <el-button @click="cancelGenerateLabel">Cancel</el-button>
                <el-button type="primary" @click="confirmPrint">
                  Confirm
                </el-button>
              </span>
            </template>
          </el-dialog>

          <div v-if="listLabels.length > 0" class="mt-3 text-end">
            <el-button :disabled="clickGenerate" type="info" @click="resetConvertList($event)">
              {{ $t("Reset") }}
            </el-button>
            <el-button :disabled="clickGenerate" type="primary" @click="confirmGenerate($event)">
              {{ $t("Generate") }}
            </el-button>
          </div>
          <el-scrollbar v-if="listLabels.length > 0" style="height: calc(100vh - 300px);" class="mt-3">
            <el-table :data="listLabels" border stripe style="width: 100%">
              <el-table-column label="#" width="40">
                <template #default="scope">
                  <span>{{ scope.row.order }}</span>
                </template>
              </el-table-column>
              <el-table-column label="Label ID">
                <template #default="scope">
                  <span :class="scope.row.is_deleted == 1 ? 'line-through' : ''">
                    {{ `${scope.row.label_id}-${scope.row.side}` }}</span>
                </template>
              </el-table-column>
              <el-table-column label="Action" width="80">
                <template #default="scope">
                  <div class="cursor-pointer text-rose-700" @click="removeLabel(scope.row)">
                    <icon :data="iconDelete" />
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-scrollbar>
        </div>
      </div>

      <div class="basis-3/4">
        <el-tabs v-model="activeTab" class="ml-3">
          <el-tab-pane :label="$t('Convert')" name="convert">
            <el-table stripe :data="pendingList.data" style="width: 100%">
              <el-table-column prop="id" :label="$t('ID')" width="100" />
              <el-table-column prop="batch" :label="$t('Batch Number')" width="120">
                <template #default="scope">
                  {{ scope.row.batch_number }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('Created at')" width="180">
                <template #default="scope">
                  {{ utcToLocalTime(scope.row.created_at).format('lll') }}
                </template>
              </el-table-column>
              <el-table-column prop="country" :label="$t('Country')" width="120">
                <template #default="scope">
                  {{ scope.row.country.name }}
                </template>
              </el-table-column>
              <el-table-column prop="employee_name" :label="$t('Staff')">
                <template #default="scope">
                  {{ scope.row.employee_convert.name }}
                </template>
              </el-table-column>
              <el-table-column prop="quantity" :label="$t('Quantity')" width="100">
                <template #default="scope">
                  {{ scope.row.convert_percent }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('Status')" width="160">
                <template #default="scope">
                  <el-tag v-if="+scope.row.convert_status == 1" type="success">{{ $t("Completed") }}</el-tag>
                  <el-tag v-else-if="+scope.row.convert_status == 2" type="danger">{{ $t("Error") }}</el-tag>
                  <el-tag v-else type="warning">{{ $t("Pending") }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column width="400" :label="$t('Action')">
                <template #default="scope">
                  <template v-if="scope.row.download_status == 0">
                    <el-button size="small" :disabled="scope.row.convert_status !== 1" type="primary"
                      @click="popupDownloadPdf(scope.row)">
                      {{ $t("Download") }}
                    </el-button>
                    <el-button size="small" type="success" @click="popupDownloadPdf(scope.row, 'new')">
                      {{ $t("Download Version 2") }}
                    </el-button>
                  </template>
                </template>
              </el-table-column>
            </el-table>
            <div class="mt-3 items-center fixed">
              <el-pagination background :page-size="limit" :pager-count="10" layout="prev, pager, next"
                :total="pendingList.total" @current-change="changePage">
              </el-pagination>
            </div>
          </el-tab-pane>
          <el-tab-pane :label="$t('History')" name="history">
            <el-table stripe :data="historyList.data" style="width: 100%">
              <el-table-column prop="id" :label="$t('ID')" width="100" />
              <el-table-column prop="batch" :label="$t('Batch Number')" width="120">
                <template #default="scope">
                  {{ scope.row.batch_number }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('Created at')" width="180">
                <template #default="scope">
                  {{ utcToLocalTime(scope.row.created_at).format('lll') }}
                </template>
              </el-table-column>
              <el-table-column prop="country" :label="$t('Country')" width="120">
                <template #default="scope">
                  {{ scope.row.country.name }}
                </template>
              </el-table-column>
              <el-table-column prop="employee_name" :label="$t('Staff')">
                <template #default="scope">
                  {{ scope.row.employee_convert.name }}
                </template>
              </el-table-column>
              <el-table-column prop="quantity" :label="$t('Quantity')" width="100">
                <template #default="scope">
                  {{ scope.row.convert_percent }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('Status')" width="120">
                <template #default="scope">
                  <el-tag v-if="+scope.row.convert_status == 1" type="success">{{ $t("Completed") }}</el-tag>
                  <template v-else>
                    <el-tag type="warning">{{ $t("Pending") }}</el-tag>
                  </template>
                </template>
              </el-table-column>
              <el-table-column width="400" :label="$t('Action')">
                <template #default="scope">
                  <el-button size="small" type="primary" @click="popupDownloadPdfHistory(scope.row)">
                    {{ $t("Re-Download") }}
                  </el-button>
                  <el-button size="small" type="success" @click="popupDownloadPdfHistory(scope.row, 'new')">
                    {{ $t("Re-Download Version 2") }}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="mt-3 flex items-center">
              <div class="flex">
                <el-input v-model="searchLabelId" :placeholder="$t('Search Label ID')">
                  <template #append>
                    <el-button @click="searchLabelDownloaded">{{ $t("Search") }}</el-button>
                  </template>
                </el-input>
              </div>
              <el-pagination class="ml-5" background :page-size="limit" :pager-count="10" layout="prev, pager, next"
                :total="historyList.total" @current-change="changePageHistory">
              </el-pagination>
            </div>
          </el-tab-pane>
        </el-tabs>

        <el-dialog v-model="dialogVisibleDownload" :title="$t('Preview DTF')" width="20%" top="5vh">
          <div class="mt-3">
            <el-button type="primary" @click="confirmDownloadedBtn($event)">
              {{ $t("Confirm") }}
            </el-button>
          </div>
        </el-dialog>

        <el-dialog v-model="dialogVisibleDownloadHistory" :before-close="handleCloseHistory" :title="$t('Preview DTF')"
          width="20%" top="5vh">
          <h2 class="mt-2 mb-2">
            <strong>{{ $t("Enter Password To Unlock Download") }}</strong>
          </h2>
          <el-input v-model="password" />
          <div class="mt-3">
            <el-button type="primary" @click="confirmDownloadedHistoryBtn($event)">
              {{ $t("Confirm") }}
            </el-button>
          </div>
        </el-dialog>
      </div>
    </div>
  </div>
</template>