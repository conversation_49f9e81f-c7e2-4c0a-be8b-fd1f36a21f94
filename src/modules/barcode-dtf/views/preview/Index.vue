<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div class="top-head">
    <div class="top-head-left">
      <h1>{{ $t('DTF Preview') }}</h1>
    </div>
  </div>

  <div class="top-head table-content">
    <el-form ref="scanSku" @submit.prevent="getScanBarcodeQualityControl()">
      <div class="scan-sku">
        <el-form-item prop="label">
          <el-input :placeholder="$t('Scan QR code')" class="mt-2 el-form-item-tracking-number border-stone-500	"
            style="min-width: 400px" size="large" v-model="filter.label" />
        </el-form-item>
      </div>
    </el-form>
  </div>

  <div v-if="label && option">
    <div class="my-3  grid gap-6 grid-cols-2">
      <div class="grow bg-slate-50 p-4 border rounded flex">
        <h1>
          {{ label }}
        </h1>
      </div>
      <div class="grow bg-slate-50	p-4 border rounded">
        <h1>
          {{ product }}
        </h1>
      </div>
    </div>
    <div class="font-semibold uppercase">{{ getNamePrintSide(option.name ?? '') }}</div>
    <div class="grid-cols-2 grid gap-6 mt-2">
      <div>
        <div class=" mb-2">{{ $t('Artwork') }}</div>
        <div :class="['border rounded']" style="height : calc(100vh - 420px)">
          <img :style="option.artwork ? 'background-color: ' + color_code : ''" v-if="option.artwork" class="preview"
            :src="option.artwork" alt="">
          <el-empty v-else :description="$t('no data')" />
        </div>
      </div>
      <div>
        <div class=" mb-2">{{ $t('Preview File') }}</div>
        <div :class="['border rounded']" style="height : calc(100vh - 420px)">
          <img v-if="option.PreviewFiles" class="preview" :src="option.PreviewFiles" alt="">
          <el-empty v-else :description="$t('no data')" />
        </div>
      </div>
    </div>
  </div>

  <div v-else class="border rounded mt-3">
    <el-empty :description="$t('Please scan QR code')">
      <template #image>
        <img src="@/assets/images/scan-label-id.png">
      </template>
    </el-empty>
  </div>
</template>