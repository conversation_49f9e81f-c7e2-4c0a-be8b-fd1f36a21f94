import {
    confirmPrinted,
    printBarcodeWIP,
    reprintBarcodeWIP
} from "@/api/barcode.js";
import { countPending, fetchPrinting, fetchHistory, confirm } from "@/api/barcodeLatex"
import { getByCode } from "@/api/employee.js";
import { ElMessage } from 'element-plus'
import date from "@/mixins/date";
import moment from "moment/moment";

export default {
    name: "Barcode",
    mixins: [date],
    data() {
        return {
            styles: [],
            totalCount: 0,
            maxAllowPrint: 0,
            filterPrint: {},
            filterBarcodePrinted: {
                limit: 11,
                page: 1,
                keyword: ''
            },
            printedList: {},
            pendingList: {},
            isLoading: false,
            dialogVisible: false,
            activeTab: 'printing',
            pdf: '',
            dialogVisiblePrint: false,
            dialogVisibleRePrint: false,
            password: null,
            skuPrinted: {
                id: null,
                start: null,
                end: null
            },
            refreshCount: null,
            refreshPrinting: null,
            lockRePrintStatus: true,
            passwordReprint: '',
            clickConfirm: false,
            onThisPage: true,
            maxItems: 3000,
            itemSelected: {},
            xqc: {
                total: 0,
            },
            eps: {
                total: 0,
            },
            reprint: {
                total: 0,
            },
            manualProcess: {
                total: 0,
            },
            reroute: {
                total: 0,
            },
            tiktok: {
                total: 0,
            },
            fba: {
                total: 0,
            },
            bulkOrder: {
                total: 0,
            },
        };
    },

    created() {
        this.initFilterPrint();
    },

    mounted() {
        let today = new Date();
        let dd = String(today.getDate()).padStart(2, '0');
        this.passwordReprint = dd;
        this.fetchPrintedList();
        this.fetchPendingList();
        this.countBarcodePendingByType();

        this.refreshCount = setInterval(() => {
            this.fetchPrintedList();
            this.fetchPendingList();
            this.countBarcodePendingByType();
        }, 20 * 1000);
    },

    beforeDestroy() {
        clearInterval(this.refreshCount);
    },

    watch: {
        activeTab: {
            async handler() {
                this.filterBarcodePrinted.keyword = '';
            }
        },

        dialogVisiblePrint: {
            handler: function (value) {
                if (!value) {
                    const iframes = document.querySelectorAll('.pdf-iframe');
                    iframes.forEach((iframe) => {
                        URL.revokeObjectURL(iframe.src);
                        document.body.removeChild(iframe);
                    });
                }
            },
            deep: true,
        },
    },

    methods: {
        initFilterPrint() {
            this.filterPrint = {
                style_sku: null,
                all: 0,
                employee_id: null,
                limit: 0,
                employee_number: null,
                is_xqc: null,
                is_reprint: null,
                is_manual: null,
                is_reroute: null,
                is_tiktok: null,
                is_fba: null,
                is_eps: null,
                is_bulk_order: null,
            };
        },

        async printWIP() {
            try {
                if (!this.itemSelected?.id) {
                    return;
                }

                this.isLoading = true;
                await printBarcodeWIP(this.itemSelected.id);
                await this.printPdfFromBlob(this.pdf);
                this.itemSelected.printed_at = moment().format('YYYY-MM-DD hh:mm:ss');
            } catch (e) {
                this.notification(e?.response?.data?.message ?? 'This WIP has been printed.', "error");

            } finally {
                this.isLoading = false;
            }
        },

        async reprintWIP() {
            try {
                if (!this.itemSelected?.id) {
                    return;
                }

                this.isLoading = true;
                await reprintBarcodeWIP(this.itemSelected.id);
                await this.printPdfFromBlob(this.pdf);
                this.itemSelected.printed_at = moment().format('YYYY-MM-DD hh:mm:ss');
            } catch (e) {
                this.notification(e?.response?.data?.message ?? 'Error', "error");


            } finally {
                this.isLoading = false;
            }
        },

        async searchBarcodePrinted() {
            this.filterBarcodePrinted.page = 1;
            await this.fetchPrintedList();
        },

        changePage(page) {
            this.filterBarcodePrinted.page = page;
            this.$nextTick(() => {
                this.fetchPrintedList();
            });
        },

        printPDF(item) {
            this.dialogVisiblePrint = true;
            this.skuPrinted.id = item.id
            this.itemSelected = item;
            this.pdf = item.wip_url;
        },

        async countBarcodePendingByType() {
            this.totalCount = 0;

            try {
                const res = await countPending();

                const data = res?.data?.data || [];
                this.xqc = data.xqc;
                this.eps = data.eps;
                this.reprint = data.reprint;
                this.manualProcess = data.manual;
                this.reroute = data.reroute;
                this.fba = data.fba;
                this.bulkOrder = data.bulk_order;
                this.tiktok = data.tiktok;
                this.styles = data.styles.data.filter(({ total }) => total > 0);

                Object.keys(data).forEach(key => {
                    this.totalCount += data[key]?.total || 0;
                });
            } catch (e) {
                this.notification(e.response.data.message, 'error', false, { duration: 10000 });
            }
        },

        async fetchPrintedList() {
            let params = {
                page: this.filterBarcodePrinted.page,
                limit: this.filterBarcodePrinted.limit,
                label_id: this.filterBarcodePrinted.keyword,
            };

            try {
                const res = await fetchHistory(params);

                this.printedList = res.data.data;
            } catch (e) {
                this.notification(e.response.data.message, 'error', false, { duration: 10000 });
            }
        },

        async fetchPendingList() {
            let params = {
                page: this.filterBarcodePrinted.page,
                limit: this.filterBarcodePrinted.limit,
            };

            try {
                const res = await fetchPrinting(params);

                this.pendingList = res.data.data;
                clearTimeout(this.refreshPrinting);
            } catch (e) {
                this.notification(e.response.data.message, 'error', false, { duration: 10000 });
            }
        },

        async confirmStaff() {
            this.clickConfirm = true;

            if (!this.filterPrint.employee_number) {
                ElMessage({
                    type: 'warning',
                    message: this.$t('Please enter the staff number to confirm the printed label'),
                })
                this.clickConfirm = false;

                return false;
            }

            const res = await getByCode(this.filterPrint.employee_number)

            if (!res.data.data) {
                ElMessage({
                    type: 'error',
                    message: this.$t('The staff number does not exist'),
                })
                this.clickConfirm = false;

                return false;
            }

            try {
                let params = {
                    ...this.filterPrint,
                    employee_id: res.data.data.id,
                };

                await confirm(params);

                this.dialogVisible = false;
                this.filterPrint.employee_number = null;
                this.fetchPendingList();
                this.countBarcodePendingByType();
            } catch (err) {
                this.dialogVisible = false;
                let data = err.response.data;

                if (err.response.status == 422) {
                    const keyFirstData = Object.keys(data)[0];
                    const firstData = data[keyFirstData];
                    this.notification(firstData[0], 'error', false, { duration: 10000 });
                } else {
                    this.notification(data.message, 'error', false, { duration: 10000 });
                }
            }

            this.clickConfirm = false;
        },

        confirmPrintBtn() {
            if (this.filterPrint.style_sku == null
                && this.filterPrint.is_xqc == null
                && this.filterPrint.is_manual == null
                && this.filterPrint.is_reroute == null
                && this.filterPrint.is_tiktok == null
                && this.filterPrint.is_fba == null
                && this.filterPrint.is_eps == null
                && this.filterPrint.is_reprint == null
                && this.filterPrint.is_bulk_order == null
                && this.filterPrint.all === 0
            ) {
                ElMessage({
                    type: 'warning',
                    message: this.$t('Please select the option to print'),
                });

                return false;
            }

            if (this.filterPrint.limit == 0) {
                ElMessage({
                    type: 'warning',
                    message: this.$t('Limit must be greater than zero'),
                });

                return false;
            }

            this.dialogVisible = true;
        },

        confirmPrintedBtn() {
            if (this.skuPrinted.start == '' || this.skuPrinted.end == '') {
                ElMessage({
                    type: 'warning',
                    message: this.$t('Please scan the first SKU and the last SKU'),
                })

                return false
            }

            confirmPrinted(this.skuPrinted.id, this.skuPrinted).then((res) => {
                if (res.data.status == true) {
                    ElMessage({
                        type: 'success',
                        message: this.$t('Successful print confirmation'),
                    })

                    this.fetchPendingList()
                    this.fetchPrintedList()
                    this.dialogVisiblePrint = false
                    this.skuPrinted = {
                        id: null,
                        start: null,
                        end: null
                    }
                } else {
                    ElMessage({
                        type: 'error ',
                        message: this.$t('The first or the last SKU is incorrect'),
                    })
                }
            })
        },

        rePrintedBtn(item) {
            let today = new Date();
            let dd = String(today.getDate()).padStart(2, '0');
            this.dialogVisibleRePrint = true;
            this.passwordReprint = dd;
            this.pdf = item.wip_url;
        },

        unLockReprint() {
            if (this.passwordReprint != this.password) {
                ElMessage({
                    type: 'error',
                    message: this.$t('The password is incorrect'),
                })
            } else {
                this.lockRePrintStatus = false;
            }
        },

        handleCloseReprint() {
            this.lockRePrintStatus = true
            this.dialogVisibleRePrint = false
            this.password = null;
        },
        closeModalPrint() {
            this.password = null;
            this.dialogVisiblePrint = false;
            this.skuPrinted = {
                id: null,
                start: null,
                end: null
            }
        },

        setStyle(item) {
            this.initFilterPrint();
            this.maxAllowPrint = item.total > this.maxItems ? this.maxItems : item.total;
            this.filterPrint.style_sku = item.sku;
            this.filterPrint.limit = item.total;
        },

        setXQC() {
            this.initFilterPrint();
            this.filterPrint.limit = this.xqc.total;
            this.maxAllowPrint = this.xqc.total > this.maxItems ? this.maxItems : this.xqc.total;
            this.filterPrint.is_xqc = 1;
        },

        setEps() {
            this.initFilterPrint();
            this.filterPrint.limit = this.eps.total;
            this.maxAllowPrint = this.eps.total > this.maxItems ? this.maxItems : this.eps.total;
            this.filterPrint.is_eps = 1;
        },

        setReprint() {
            this.initFilterPrint();
            this.filterPrint.limit = this.reprint.total;
            this.maxAllowPrint = this.reprint.total > this.maxItems ? this.maxItems : this.reprint.total;
            this.filterPrint.is_reprint = 1;
        },

        setManualProcess() {
            this.initFilterPrint();
            this.filterPrint.limit = this.manualProcess.total;
            this.maxAllowPrint = this.manualProcess.total > this.maxItems ? this.maxItems : this.manualProcess.total;
            this.filterPrint.is_manual = 1;
        },

        setReroute() {
            this.initFilterPrint();
            this.filterPrint.limit = this.reroute.total;
            this.maxAllowPrint = this.reroute.total > this.maxItems ? this.maxItems : this.reroute.total;
            this.filterPrint.is_reroute = 1;
        },

        setFba() {
            this.initFilterPrint();
            this.filterPrint.limit = this.fba.total;
            this.maxAllowPrint = this.fba.total > this.maxItems ? this.maxItems : this.fba.total;
            this.filterPrint.is_fba = 1;
        },

        setTiktok() {
            this.initFilterPrint();
            this.filterPrint.limit = this.tiktok.total;
            this.maxAllowPrint = this.tiktok.total > this.maxItems ? this.maxItems : this.tiktok.total;
            this.filterPrint.is_tiktok = 1;
        },

        setBulkOrder() {
            this.initFilterPrint();
            this.filterPrint.limit = this.bulkOrder.total;
            this.maxAllowPrint = this.bulkOrder.total > this.maxItems ? this.maxItems : this.bulkOrder.total;
            this.filterPrint.is_bulk_order = 1;
        },
    },

    beforeUnmount() {
        console.log('clear all refresh')
        clearTimeout(this.refreshCount);
        clearTimeout(this.refreshPrinting);
    },

    unmounted() {
        console.log('unmounted')
        clearTimeout(this.refreshCount);
        clearTimeout(this.refreshPrinting);
    },

    beforeRouteLeave(to, from, next) {
        this.onThisPage = false;
        next();
    },
};
