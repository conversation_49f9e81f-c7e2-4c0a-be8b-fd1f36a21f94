import { list } from "@/api/boxMoving.js";
import EventBus from "@/utilities/eventBus.js";
import CreateBoxMoving from "@/modules/box-moving/views/Create/Index.vue";
import filterMixin from "@/mixins/filter";
import { equals } from "ramda";
import dateMixin from "@/mixins/date.js";
import { mapGetters } from "vuex";
import PullingShelveToRack from "@/modules/box-moving/views/PullingShelveToRack/Index.vue";
import { WAREHOUSE_MEXICO } from "@/utilities/constants";

export default {
  name: "BoxMoving",
  mixins: [filterMixin, dateMixin],
  components: {
    CreateBoxMoving,
    PullingShelveToRack
  },
  data() {
    return {
      items: [],
      isLoading: false,
      filter: this.setDefaultFilter(),
      warehouseMexico: WAREHOUSE_MEXICO,
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 230);
    },
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },
    ...mapGetters({
      getUsers: "getUsers",
      employees: "getEmployees"
    }),
  },
  beforeUnmount() {
    EventBus.$off("showCreateBoxMoving");
    EventBus.$off("showPullingShelveToRack");
  },
  mounted() {
    this.fetchData();
    this.fetchUser();
    this.fetchEmployee();
  },
  methods: {
    async fetchEmployee() {
      await this.$store.dispatch("getEmployees");
    },
    getUserNameById(id) {
      const item = this.getUsers.find((item) => +item.id === +id);
      return item && item.username;
    },
    async fetchUser() {
      await this.$store.dispatch("getUsers");
    },
    onFilter(item = "") {
      this.filter.page = 1;
      this.$nextTick(() => {
        this.fetchBoxMoving();
        if (item) {
          this.$refs[item].handleClose();
        }
      });
    },
    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.$nextTick(() => {
        this.fetchBoxMoving();
      });
    },
    onChangeDate() {
      if (this.filter.date && this.filter.date.length) {
        this.filter.date[0] = this.formatDate(this.filter.date[0], false);
        this.filter.date[1] = this.formatDate(this.filter.date[1], false);
        this.onFilter();
      }
    },
    clearFilterItem(item) {
      this.filter[item] = "";
      this.$refs[item].handleClose();
      this.onFilter();
    },
    setDefaultFilter() {
      return {
        limit: 25,
        page: 1,
        box: "",
        location: "",
        user_id: "",
        product: "",
        cost_value: "",
      };
    },
    setRouteParam() {
      const params = this.filter;
      this.$router.replace({ name: "box_moving", query: params });
    },
    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);
      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }
      filter.page = +filter.page || 1;
      return filter;
    },
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchBoxMoving();
      });
    },
    createBoxMoving() {
      EventBus.$emit("showCreateBoxMoving");
    },
    async fetchData() {
      this.filter = this.getRouteParam();
      this.fetchBoxMoving();
    },
    async fetchBoxMoving() {
      this.isLoading = true;
      this.setRouteParam();
      const res = await list(this.filter);
      this.isLoading = false;
      const data = res.data || [];
      this.total = data.total;
      this.items = data.data;
    },
    async deleteBoxMoving(item) {
      await revert({ id: item.id });
      this.fetchData();
    },
    tableRowClassName(data) {
      return data.row.is_deleted ? "is-delete" : "";
    },
    hasChangeFilterByItem(name) {
      const query = this.$route.query;
      return !!query[name];
    },
    CreateBoxMovingPullingShelveToRack(){
      EventBus.$emit("showPullingShelveToRack");
    }
  },
};
