import EventBus from "@/utilities/eventBus.js";
import {
  list,
  generate, print
} from "@/api/countSticker.js";
import IncrementTimer from "@/components/IncrementTimer.vue";
import warehouseMixin from '@/mixins/warehouse';
import dateMixin from '@/mixins/date';
import {S3_URL} from "@/utilities/constants";

export default {
  name: "GenerateCountSticker",
  mixins: [warehouseMixin, dateMixin],
  props: ['productStyles', 'employees'],
  components: {IncrementTimer},
  data() {
    return {
      isLoading: false,
      openDialogGenerateCountSticker: false,
      serverErrors: [],
      employee: {},
      employeeError: '',
      warehouse_id: null,
      dialogVisiblePrint: false,
      filter: this.setDefaultFilter(),
      formData: {
        quantity: 200
      },
      items: [],
      s3: S3_URL,
      errorMessages: '',
    };
  },
  computed: {

  },
  created() {
    EventBus.$on("showGenerateCountStickers", () => {
      this.formData = {
        quantity: 200
      };
      this.openDialogGenerateCountSticker = true;
      this.warehouse_id = this.userWarehouseId;
    });
  },
  mounted() {
    this.warehouse_id = this.userWarehouseId
    this.fetchData();

  },
  watch: {
  },
  methods: {
    async generate() {
      this.errorMessages = '';
      if (!this.formData.quantity || this.formData.quantity < 1) {
        this.notification(this.$t('Please enter a valid quantity.'), 'error');
        return;
      }
      if (this.formData.quantity > 1000) {
        this.errorMessages = this.$t('Max 1000 stickers allowed.');

        return;
      }
      this.isLoading = true;
      try {
        const response = await generate(this.formData);
        this.notification(this.$t('Generate count sticker successfully.'));
        this.fetchData();
      } catch (e) {
        this.serverErrors = e.response.data.errors || [];
        this.notification(this.$t('Failed to generate count sticker.'), 'error');
      }
      this.isLoading = false;
    },
    isError(field) {
      return !!this.serverErrors[field];
    },
    getErrorMessage(field) {
      return this.serverErrors[field][0];
    },
    async fetchData() {
      this.filter = this.getRouteParam();
      this.fetchStickers();
    },
    setDefaultFilter() {
      return {
        warehouse_id: this.warehouse_id,
        limit: 10,
        page: 1,
      };
    },
    async printPDF(item) {
      try {
        await print({ batch_id: item.id });
        this.dialogVisiblePrint = true;
        this.pdf = S3_URL + '/' + item.url;
        this.fetchStickers();
      } catch (e) {
        this.notification(e?.response?.data?.message ?? this.$t('Failed to update print status.'), 'error');
      }

    },
    getRouteParam() {
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
      };
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);
      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }
      filter.page = +filter.page || 1;
      return filter;
    },
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchStickers();
      });
    },
    async fetchStickers() {
      this.isLoading = true;
      const res = await list(this.filter);
      const data = res.data || [];
      this.total = data.total;
      this.items = data.data;
      this.isLoading = false;
    },
    async updatePrintStatus(id) {

    },
    onClose() {
      // this.resetData();
      // this.resetEmployee();
      this.$emit("refresh");
    }
  },
};
