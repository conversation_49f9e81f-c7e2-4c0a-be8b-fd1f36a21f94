import {destroy, list} from "@/api/box.js";
import { getProductAttributes } from "@/api/product";
import EventBus from "@/utilities/eventBus.js";
import CreateBox from "@/modules/box/views/Create/Index.vue";
import GenerateBox from "@/modules/box/views/Generate/Index.vue";
import GenerateCountSticker from "@/modules/box/views/GenerateCountSticker/Index.vue";
import EditBox from "@/modules/box/views/Edit/Index.vue";
import ImportBox from "@/modules/box/views/Import/Index.vue";
import {equals} from "ramda";
import {mapGetters} from "vuex";
import warehouseMixin from '@/mixins/warehouse';
import { API_URL } from "@/utilities/constants";

export default {
  name: "Box",
  components: {
    CreateBox,
    EditBox,
    ImportBox,
    GenerateBox,
    GenerateCountSticker
  },
  mixins: [warehouseMixin],
  data() {
    return {
      items: [],
      isLoading: false,
      filter: this.setDefaultFilter(),
      types: [
        {
          label: this.$t('Box ID'),
          value: "box_id",
        },
        {
          label: this.$t('Location'),
          value: "location",
        },
        {
          label: this.$t('Product Style'),
          value: "product_style",
        },
        {
          label: this.$t('Product SKU'),
          value: "product_sku",
        },
      ],
      styleOptions: [],
      warehouse_id : null
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 230);
    },
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },
    colorOptions() {
      let style = this.styleOptions[this.filter.style];
      return style ? style.colors : [];
    },
    sizeOptions() {
      let style = this.styleOptions[this.filter.style];
      return style ? style.sizes : [];
    },
    ...mapGetters({
      employees: "getEmployees"
    }),
    buildLinkDownload() {
      const link = `${API_URL}/manage-box-export`;
    
      const params = new URLSearchParams({
        ...this.filter,
        warehouse_id: this.userWarehouseId,
      });

      return `${link}?${params.toString()}`;
    },
    isDisableExportExcel() {
      if (
        (this.filter.type === 'product_sku' && this.filter.keyword !== "")
        || this.filter.type === 'product_style' && this.filter.style !== ""
      ) {
        return false;
      }
      return true;
    }
  },
  beforeUnmount() {
    EventBus.$off("showCreateBox");
    EventBus.$off("showGenerateBox");
    EventBus.$off("showEditBox");
    EventBus.$off("showGenerateCountStickers");
  },
  mounted() {
    this.warehouse_id = this.getWarehouseId();
    this.getProductAttributes();
    this.fetchData();
    this.fetchEmployee();
  },
  methods: {
    async fetchEmployee() {
      await this.$store.dispatch("getEmployees");
    },
    onFilter() {
      this.filter.page = 1;
      this.$nextTick(() => {
        this.fetchBox();
      });
    },
    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.$nextTick(() => {
        this.fetchBox();
      });
    },
    setDefaultFilter() {
      return {
        style: "",
        color: "",
        size: "",
        keyword: "",
        type: "product_style",
        limit: 25,
        page: 1,
      };
    },
    setRouteParam() {
      const params = this.filter;
      this.$router.replace({ name: "box", query: params });
    },
    async getProductAttributes() {
      const { data } = await getProductAttributes();
      this.styleOptions = data || [];
    },
    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);
      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }
      filter.page = +filter.page || 1;
      return filter;
    },
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchBox();
      });
    },
    resetColorSize() {
      this.filter.color = '';
      this.filter.size = '';
    },
    createBox() {
      EventBus.$emit("showCreateBox");
    },
    importBox() {
      EventBus.$emit("showImportBox");
    },
    generateBox() {
      EventBus.$emit("showGenerateBox");
    },
    generateCountStickers() {
      EventBus.$emit("showGenerateCountStickers");
    },
    async fetchData() {
      this.filter = this.getRouteParam();
      this.fetchBox();
    },
    async fetchBox() {
      this.isLoading = true;
      this.setRouteParam();
      const res = await list(this.filter);
      this.isLoading = false;
      const data = res.data || [];
      this.total = data.total;
      this.items = data.data;
    },
    editBox(item) {
      EventBus.$emit("showEditBox", item);
    },
    async deleteBox(item) {
      await destroy(item.id);
      this.notification(this.$t('Delete box successfully.'));
      this.fetchData();
    },
    selectOption(type, evt){
      const value = evt.target.value || "";
      if (!value) return;
      if (type === 'style') {
        for (const key in this.styleOptions) {
          if (key.toLowerCase() === value.toLowerCase()) {
            this.filter.style = key;
            this.$refs.productStyle.blur();
            return;
          }
        }
      } else if (type === 'color') {
        for (let color of this.colorOptions) {
          if (value.toLowerCase() === color.toLowerCase()) {
            this.filter.color = color;
            this.$refs.productColor.blur();
            return;
          }
        }
      } else {
        for (let size of this.sizeOptions) {
          if (value.toLowerCase() === size.toLowerCase()) {
            this.filter.size = size;
            this.$refs.productSize.blur();
            return;
          }
        }
      }
    },
    exportBox() {
      return (window.location.href = this.buildLinkDownload);
    },
  },
};
