export const mutations = {
  setBrands(state, brands) {
    state.brands = brands;
  },
  addBrand(state, brand) {
    brand = {
      ...brand,
      nameCache: brand.name,
    };
    state.brands.push(brand);
  },
  updateBrand(state, brand) {
    state.brands.forEach((item) => {
      if (item.id === brand.id) {
        item.name = brand.name;
        item.nameCache = brand.name;
      }
    });
  },
  deleteBrand(state, id) {
    const index = state.brands.findIndex((item) => item.id === id);
    state.brands.splice(index, 1);
  },
  resetDataBrands(state) {
    state.brands = state.brands.map((item) => {
      return {
        ...item,
        name: item.nameCache,
        is_edit: false,
      };
    });
  },
};
