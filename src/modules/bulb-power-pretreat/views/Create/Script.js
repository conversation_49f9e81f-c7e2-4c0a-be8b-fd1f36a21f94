import {
  createBulbPowerPretreat,
  fetchAllColors,
  fetchAllStyles,
  getColorsByStyle,
} from '@/api/bulbPowerPretreat';
import { clone, isEmpty, isNil } from 'ramda';
import { debounce } from '@/utilities/helper.js';
import { Back } from '@element-plus/icons-vue';

export default {
  name: 'CreateBulbPowerPretreat',
  components: { Back },
  mixins: [],
  data() {
    return {
      isLoading: false,
      data: null,
      value: 50,
      optionBulb: [
        { text: '0%', value: 0 },
        { text: '25%', value: 25 },
        { text: '50%', value: 50 },
        { text: '75%', value: 75 },
        { text: '100%', value: 100 },
      ],
      numRows: 6,
      numCols: 9,
      originalList: null,
      colorList: [],
      colorSku: '',
      colorFilterKey: '',
      styleList: [],
      styleSku: '',
      styleFilterKey: '',
      hasErrorStyleSelect: false,
      hasErrorColorSelect: false,
      colorListByStyle: [],
    };
  },
  computed: {
    colorsFiltered() {
      if (this.colorFilterKey) {
        return this.colorListByStyle.filter((item) => {
          return String(item.name)
            .toLowerCase()
            .includes(this.colorFilterKey.toLowerCase());
        });
      } else {
        return this.colorListByStyle;
      }
    },

    stylesFiltered() {
      if (this.styleFilterKey) {
        return this.styleList.filter((item) => {
          return String(item.name)
            .toLowerCase()
            .includes(this.styleFilterKey.toLowerCase());
        });
      } else {
        return this.styleList;
      }
    },

    disableSelectColor() {
      return isEmpty(this.styleSku) || isNil(this.styleSku);
    },
  },
  watch: {
    value(newValue) {
      for (let i = 0; i < this.numRows; i++) {
        for (let j = 0; j < this.numCols; j++) {
          if (this.data?.[i]?.[j]?.active === true) {
            this.data[i][j].value = newValue;
          }
        }
      }
    },
    styleSku(value) {
      if (value) this.hasErrorStyleSelect = false;
    },
    colorSku(value) {
      if (value) this.hasErrorColorSelect = false;
    },
  },
  created() {
    this.setDefaultData();
    this.fetchAllProductStyles();
    this.fetchAllProductColors();
  },
  mounted() {},
  methods: {
    debounceFilterColors: debounce(async function (query) {
      this.colorFilterKey = query;
    }, 100),
    debounceFilterStyles: debounce(async function (query) {
      this.styleFilterKey = query;
    }, 100),
    async selectStyle() {
      this.colorSku = '';
      const res = await getColorsByStyle({
        style_sku: this.styleSku,
      });
      const resColors = res.data || [];
      if (resColors.length == 0) {
        this.notification('No color found for this style!', 'error');
      }
      this.colorListByStyle = this.colorList.filter((item) =>
        resColors.includes(item.name)
      );
    },
    pushPretreatBulbPower() {
      return this.$router.push({
        name: 'pretreat_bulb_power',
        query: { activeTab: 'sku_setting' },
      });
    },
    formatTooltip(value) {
      return `${value}%`;
    },
    async fetchAllProductColors() {
      const { data } = await fetchAllColors();
      this.colorList = data;
    },
    async fetchAllProductStyles() {
      const { data } = await fetchAllStyles();
      this.styleList = data;
    },
    selectAll() {
      for (let i = 0; i < this.numRows; i++) {
        for (let j = 0; j < this.numCols; j++) {
          this.data[i][j].active = true;
        }
      }
    },
    reset() {
      this.originalList.forEach((item, index) => {
        let row = Math.floor(index / this.numCols);
        this.data[row][index - row * this.numCols].value = item;
      });
    },
    setDefaultData() {
      this.data = new Array(this.numRows);
      for (let i = 0; i < this.numRows; i++) {
        this.data[i] = new Array(this.numCols);
        for (let j = 0; j < this.numCols; j++) {
          this.data[i][j] = {
            value: 50,
            active: false,
          };
        }
      }
    },
    async onSubmit() {
      if (isNil(this.styleSku) || isEmpty(this.styleSku)) {
        this.notification('Please select style name.', 'error');
        this.hasErrorStyleSelect = true;
        return;
      }
      if (isNil(this.colorSku) || isEmpty(this.colorSku)) {
        this.notification('Please select color name.', 'error');
        this.hasErrorColorSelect = true;
        return;
      }
      this.isLoading = true;
      let params = [];
      for (let i = 0; i < this.numRows; i++) {
        for (let j = 0; j < this.numCols; j++) {
          params.push(this.data[i][j].value);
        }
      }
      try {
        const response = await createBulbPowerPretreat({
          color_sku: this.colorSku,
          style_sku: this.styleSku,
          bulb_setting_data: params,
        });
        if (response.status === 200) {
          this.notification(response.data.message, 'success');
          // this.fetchBulbPower();
          this.deSelectAll();
          this.pushPretreatBulbPower();
        }
      } catch (e) {
        const data = e.response.data;
        let msg = '';
        if (data.errors) {
          const firstKey = Object.keys(data.errors)[0];
          msg = data.errors[firstKey][0];
        } else {
          msg = data.message;
        }

        this.notification(msg, 'error');
      }
      this.isLoading = false;
    },
    deSelectAll() {
      for (let i = 0; i < this.numRows; i++) {
        for (let j = 0; j < this.numCols; j++) {
          this.data[i][j].active = false;
        }
      }
    },
    settingActive(value) {
      if (value == this.value) {
        for (let i = 0; i < this.numRows; i++) {
          for (let j = 0; j < this.numCols; j++) {
            if (this.data?.[i]?.[j]?.active === true) {
              this.data[i][j].value = value;
            }
          }
        }
      } else {
        this.value = value;
      }
    },

    updateListSetting(indexRow, indexCol) {
      this.data[indexRow][indexCol].active =
        !this.data[indexRow][indexCol].active;
    },
    generateBackground(value) {
      if (value >= 75) {
        return 'bg-[#FF0000]';
      }
      if (value >= 50) {
        return 'bg-[#FFA500]';
      }
      if (value >= 25) {
        return 'bg-[#FFFF00]';
      }
    },
  },
};
