<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head-left mb-4">
      <h1>{{ $t('Printing Bulb Power') }}</h1>
    </div>
    <el-tabs type="card" class="mb-4" v-model="activeTab">
      <!-- Default setting -->
      <el-tab-pane label="Default setting" name="default">
        <div v-loading="isLoading">
          <div class="top-head">
            <div class="top-head-left  ml-2">
              <h1>{{ $t('Default setting') }}</h1>
            </div>
            <div class="top-head-right">
              <el-button type="primary" @click="onSubmit">
                {{ $t('Save') }}
              </el-button>
            </div>
          </div>
          <div class="table-content w-full">
            <div class="align-middle border-2 rounded-md m-auto w-1/2 px-4 py-4 mb-4">
              <div class="flex">
                <el-button @click="selectAll()" class="flex-1">Select All</el-button>
                <el-button @click="deSelectAll()" class="flex-1">De-Select All</el-button>
                <el-button @click="reset()" class="flex-1">Reset</el-button>
              </div>
              <div class="flex my-1">
                <icon :data="iconPower1" class="my-auto !h-[20px] !w-[20px] mr-[15px]"/>
                <div class="slider-demo-block w-full">
                  <el-slider :format-tooltip="formatTooltip" class="" v-model="value" />
                </div>
                <icon :data="iconPower2" class="my-auto !h-[20px] !w-[20px] ml-[15px] mr-[0px]"/>
              </div>
              <div class="flex">
                <el-button v-for="(item, index) in optionBulb" :key="index" @click="settingActive(item.value)" class="flex-1">{{ item.text }}</el-button>
              </div>
            </div>
            <div class="border-[1px] mx-2">
              <div class="flex w-full" v-for="(itemRow, indexRow) in numRows">
                <div @click="updateListSetting(indexRow, indexCol)" :class="[data?.[indexRow]?.[indexCol]?.active ? 'bg-[#ADC6F3]' : '']" class="border-[1px] w-[120px] flex-1 cursor-pointer" v-for="(itemCol, indexCol) in numCols">
                  <p class="text-center">{{ indexRow * numCols + itemCol }}</p>
                  <div :class="[ generateBackground(data?.[indexRow]?.[indexCol]?.value) ]" class="border-slate-700 w-[60px] h-[60px] border rounded-full m-auto flex justify-items-center items-center">
                    <div class="w-[30px] h-[30px] border rounded-full m-auto border-slate-700">
                    </div>
                  </div>
                  <p class="text-center">{{ data?.[indexRow]?.[indexCol]?.value }}%</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- Setting for each SKU -->
      <el-tab-pane label="Setting for each SKU" name="sku_setting">
        <div class="top-head mb-4">
          <div class="top-head-left">
            <h1>{{ $t('Setting for each SKU') }}</h1>
          </div>
          <div class="top-head-right">
            <el-button type="primary" @click="pushToCreateScreen">
              <span class="icon-margin-right"> <icon :data="iconAdd" /></span
              >{{ $t('Create') }}
            </el-button>
          </div>
        </div>
        <div class="table-content">
          <div class="filter-top mb-4">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-input
                  :placeholder="$t('Style name or SKU')"
                  v-model="filter.style_name_or_sku"
                  clearable
                  @clear="onFilter"
                  @keyup.enter="onFilter"
                />
              </el-col>
              <el-col :span="6">
                <el-input
                  :placeholder="$t('Color name or SKU')"
                  v-model="filter.color_name_or_sku"
                  clearable
                  @clear="onFilter"
                  @keyup.enter="onFilter"
                />
              </el-col>
              <el-col :span="6">
                <div class="btn-filter">
                  <el-button type="primary" @click="onFilter">
                    <span class="icon-margin-right">
                      <icon :data="iconFilter" />
                      </span>
                      {{ $t('Filter') }}
                  </el-button>
                  <el-button @click="resetFilter">
                    <span class="icon-margin-right">{{ $t('Reset') }}</span>
                  </el-button>
                </div>
              </el-col>
            </el-row>
          </div>
          <el-table
            border
            stripe
            size="small"
            :data="items"
            :max-height="maxHeight"
            v-loading="isLoading"
            element-loading-text="Loading..."
          >
            <el-table-column
              prop="id"
              :label="$t('ID')"
              min-width="50"
            ></el-table-column>
            <el-table-column
              prop="style.name"
              :label="$t('Style Name')"
              min-width="150"
            ></el-table-column>
            <el-table-column
              prop="style.sku"
              :label="$t('Style SKU')"
              min-width="150"
            ></el-table-column>
            <el-table-column
              prop="color.name"
              :label="$t('Color Name')"
              min-width="150"
            ></el-table-column>
            <el-table-column
              prop="color.sku"
              :label="$t('Color SKU')"
              min-width="150"
            ></el-table-column>
            <el-table-column
              prop="user.username"
              :label="$t('User')"
              min-width="100"
            ></el-table-column>

            <el-table-column :label="$t('Date')" min-width="150">
              <template #default="scope">
                {{ formatDate(scope.row.created_at, false, 'YYYY-MM-DD HH:mm:ss') }}
              </template>
            </el-table-column>

            <el-table-column
              :label="$t('Active status')"
              min-width="100"
            >
              <template #default="scope">
                <el-switch
                  v-model="scope.row.is_active"
                  class="mt-2"
                  style="margin-left: 24px"
                  @click="changeStatusBulbSetting(scope.row)"
                />
              </template>

            </el-table-column>

            <el-table-column
              prop="action"
              :label="$t('Action')"
              fixed="right"
              width="100"
            >
              <template #default="scope">
                <el-button
                  class="el-link-edit"
                  type="primary"
                  size="small"
                  @click="pushToUpdateScreen(scope.row)"
                >
                  Edit
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="bottom">
            <div class="total">
              {{ $t('Total:') }} {{ items.length ? formatNumber(total) : 0 }}
            </div>
            <el-pagination
              :disabled="isLoading"
              background
              layout="prev, pager, next"
              :page-size="filter.limit"
              :total="total"
              @current-change="changePage"
              v-model:currentPage="filter.page"
            >
            </el-pagination>
            <div class="limit" :disabled="isLoading">
              <el-select
                v-model="filter.limit"
                :placeholder="$t('Select')"
                size="mini"
                @change="onFilter"
              >
                <el-option
                  v-for="item in limits"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
