import { getList, toggleBulbPowerStatus } from '@/api/bulbPowerPrinting';
import EventBus from '@/utilities/eventBus';
import { Check, Close } from '@element-plus/icons-vue';
import { getListPrinting, updatePrinting } from '@/api/bulbPower';
import { clone } from 'ramda';

export default {
  name: 'BulbPowerPrintingList',
  components: {
    Check,
    Close,
  },
  data() {
    return {
      items: [],
      isLoading: false,
      filter: this.setDefaultFilter(),
      isLoading: false,
      data: null,
      value: 50,
      optionBulb: [
        { text: '0%', value: 0 },
        { text: '25%', value: 25 },
        { text: '50%', value: 50 },
        { text: '75%', value: 75 },
        { text: '100%', value: 100 },
      ],
      numRows: 6,
      numCols: 9,
      originalList: null,
    };
  },
  watch: {
    value(newValue) {
      for (let i = 0; i < this.numRows; i++) {
        for (let j = 0; j < this.numCols; j++) {
          if (this.data?.[i]?.[j]?.active === true) {
            this.data[i][j].value = newValue;
          }
        }
      }
    },
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 245);
    },
    activeTab() {
      if (this.$route.query?.activeTab)
        return this.$route.query.activeTab;
      return 'default';
    }
  },
  mounted() {
    this.filter = this.getRouteParam();
    this.getBulbPowerPrinting();
    this.setDefaultData();
    this.fetchBulbPower();
  },
  methods: {
    async changeStatusBulbSetting(row) {
      try {
        await toggleBulbPowerStatus({
          id: row.id,
          is_active: row.is_active,
        });

        this.notification('Change active status successfully!');
      } catch (e) {
        this.notification(
          'Change active status fail. Please check again!',
          'error'
        );
      }
    },
    setDefaultFilter() {
      return {
        limit: 25,
        page: 1,
        color_name_or_sku: '',
        style_name_or_sku: '',
      };
    },
    pushToCreateScreen() {
      return this.$router.push({ name: 'create_printing_bulb_power' });
    },
    pushToUpdateScreen(row) {
      return this.$router.push({
        name: 'update_printing_bulb_power',
        params: { id: row.id },
      });
    },
    async getBulbPowerPrinting() {
      this.isLoading = true;
      this.setRouteParam();
      const { data } = await getList(this.filter);
      this.items = data.data;
      if (this.items) {
        this.items.forEach((element) => {
          element.is_active = element.is_active == 1 ? true : false;
        });
      }
      this.total = data.total;
      this.isLoading = false;
    },
    changePage(page) {
      this.filter.page = page;
      this.getBulbPowerPrinting();
    },
    onFilter() {
      this.filter.page = 1;
      this.getBulbPowerPrinting();
    },
    resetFilter() {
      this.filter = this.setDefaultFilter();
      this.getBulbPowerPrinting();
    },

    formatTooltip(value) {
      return `${value}%`;
    },
    selectAll() {
      for (let i = 0; i < this.numRows; i++) {
        for (let j = 0; j < this.numCols; j++) {
          this.data[i][j].active = true;
        }
      }
    },
    reset() {
      this.originalList.forEach((item, index) => {
        let row = Math.floor(index / this.numCols);
        this.data[row][index - row * this.numCols].value = item;
      });
    },
    setDefaultData() {
      this.data = new Array(this.numRows);
      for (let i = 0; i < this.numRows; i++) {
        this.data[i] = new Array(this.numCols);
        for (let j = 0; j < this.numCols; j++) {
          this.data[i][j] = {
            value: 100,
            active: false,
          };
        }
      }
    },
    async onSubmit() {
      this.isLoading = true;
      let params = [];
      for (let i = 0; i < this.numRows; i++) {
        for (let j = 0; j < this.numCols; j++) {
          params.push(this.data[i][j].value);
        }
      }
      try {
        const response = await updatePrinting({ bulbPowers: params });
        if (response.status === 200) {
          this.notification(response.data.message, 'success');
          this.fetchBulbPower();
          this.deSelectAll();
        }
      } catch (e) {
        this.notification(e, 'error');
      }
      this.isLoading = false;
    },
    deSelectAll() {
      for (let i = 0; i < this.numRows; i++) {
        for (let j = 0; j < this.numCols; j++) {
          this.data[i][j].active = false;
        }
      }
    },
    settingActive(value) {
      if (value == this.value) {
        console.log('value', value, this.value);
        for (let i = 0; i < this.numRows; i++) {
          for (let j = 0; j < this.numCols; j++) {
            if (this.data?.[i]?.[j]?.active === true) {
              this.data[i][j].value = value;
            }
          }
        }
      } else {
        this.value = value;
      }
    },

    updateListSetting(indexRow, indexCol) {
      this.data[indexRow][indexCol].active =
        !this.data[indexRow][indexCol].active;
    },
    generateBackground(value) {
      if (value >= 75) {
        return 'bg-[#FF0000]';
      }
      if (value >= 50) {
        return 'bg-[#FFA500]';
      }
      if (value >= 25) {
        return 'bg-[#FFFF00]';
      }
    },
    async fetchBulbPower() {
      this.isLoading = true;
      try {
        const response = await getListPrinting();
        if (response.data.data) {
          this.originalList = clone(response.data.data?.bulbPowers);
          this.originalList.forEach((item, index) => {
            let row = Math.floor(index / this.numCols);
            this.data[row][index - row * this.numCols].value = item;
          });
        }
      } catch (e) {
        this.notification(
          e.response.data.message ?? 'The given data was invalid.',
          'error'
        );
      }
      this.isLoading = false;
    },
  },
};
