<!-- <style src="./Style.scss" lang="scss" scoped></style> -->
<script src="./Script.js"></script>
<template>
  <div v-loading="isLoading" class="mb-10">
    <div class="top-head mb-4">
      <div class="top-head-left ml-2">
        <h1>{{ $t('Update Printing Bulb Power') }}</h1>
      </div>
      <div class="top-head-right mr-1">
        <el-button type="primary" @click="pushPrintingBulbPower" plain>
          <Back width="12" /> {{ $t('Printing bulb power list') }}
        </el-button>
      </div>
    </div>
    <div class="table-content w-full">
      <div class="flex justify-center">
        <div class="border-2 rounded-md w-1/6 px-4 py-4 mb-4 mr-7">
        Color name: <span class="font-bold" :style="{ color: '#337ecc'}">{{ colorSetting?.color?.name }}</span>
        <br/>
        Color SKU: <span class="font-bold" :style="{ color: '#337ecc'}">{{ colorSetting?.color?.sku }}</span>
        <br/>
        Color code: <span class="font-bold" :style="{ color: '#337ecc'}">{{ colorSetting?.color?.color_code }}</span>
        <br />
        Style name: <span class="font-bold" :style="{ color: '#337ecc'}">{{ colorSetting?.style?.name }}</span>
        <br/>
        Style SKU: <span class="font-bold" :style="{ color: '#337ecc'}">{{ colorSetting?.style?.sku }}</span>
      </div>
      <div class="border-2 rounded-md w-1/2 px-4 py-4 mb-4">
        <div class="flex">
          <el-button @click="selectAll()" class="flex-1">Select All</el-button>
          <el-button @click="deSelectAll()" class="flex-1">De-Select All</el-button>
          <el-button @click="reset()" class="flex-1">Reset</el-button>
        </div>
        <div class="flex my-1">
          <icon
            :data="iconPower1"
            class="my-auto !h-[20px] !w-[20px] mr-[15px]"
          />
          <div class="slider-demo-block w-full">
            <el-slider
              :format-tooltip="formatTooltip"
              class=""
              v-model="value"
            />
          </div>
          <icon
            :data="iconPower2"
            class="my-auto !h-[20px] !w-[20px] ml-[15px] mr-[0px]"
          />
        </div>
        <div class="flex">
          <el-button
            v-for="(item, index) in optionBulb"
            :key="index"
            @click="settingActive(item.value)"
            class="flex-1"
            >{{ item.text }}</el-button
          >
        </div>
      </div>
      </div>

      <div class="border-[1px] mx-2">
        <div class="flex w-full" v-for="(itemRow, indexRow) in numRows">
          <div
            @click="updateListSetting(indexRow, indexCol)"
            :class="[
              data?.[indexRow]?.[indexCol]?.active ? 'bg-[#ADC6F3]' : '',
            ]"
            class="border-[1px] w-[120px] flex-1 cursor-pointer"
            v-for="(itemCol, indexCol) in numCols"
          >
            <p class="text-center">{{ indexRow * numCols + itemCol }}</p>
            <div
              :class="[generateBackground(data?.[indexRow]?.[indexCol]?.value)]"
              class="border-slate-700 w-[60px] h-[60px] border rounded-full m-auto flex justify-items-center items-center"
            >
              <div
                class="w-[30px] h-[30px] border rounded-full m-auto border-slate-700"
              ></div>
            </div>
            <p class="text-center">
              {{ data?.[indexRow]?.[indexCol]?.value }}%
            </p>
          </div>
        </div>
      </div>

      <div class="top-head-right mr-2 mt-3 flex justify-end">
        <el-button type="primary" @click="onSubmit">
          {{ $t('Update') }}
        </el-button>
      </div>
    </div>
  </div>
</template>
