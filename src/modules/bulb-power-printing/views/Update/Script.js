import {
  updateBulbPowerPrinting,
  getDetailBulbPowerPrinting,
} from '@/api/bulbPowerPrinting';
import { fetchAllColors } from '@/api/bulbPowerPrinting';
import { clone } from 'ramda';
import { debounce } from '@/utilities/helper.js';
import {
  Back
} from '@element-plus/icons-vue';

export default {
  name: 'UpdateBulbPowerPrinting',
  components: { Back },
  mixins: [],
  data() {
    return {
      isLoading: false,
      data: null,
      value: 50,
      optionBulb: [
        { text: '0%', value: 0 },
        { text: '25%', value: 25 },
        { text: '50%', value: 50 },
        { text: '75%', value: 75 },
        { text: '100%', value: 100 },
      ],
      numRows: 6,
      numCols: 9,
      originalList: null,
      colorList: [],
      colorSku: '',
      colorFilterKey: '',
      colorSetting: {},
    };
  },
  computed: {
    colorsFiltered() {
      if (this.colorFilterKey) {
        return this.colorList.filter((item) => {
          return String(item.name)
            .toLowerCase()
            .includes(this.colorFilterKey.toLowerCase());
        });
      } else {
        return this.colorList;
      }
    },
    bulbSettingId() {
      return this.$route.params?.id || null;
    },

  },
  watch: {
    value(newValue) {
      for (let i = 0; i < this.numRows; i++) {
        for (let j = 0; j < this.numCols; j++) {
          if (this.data?.[i]?.[j]?.active === true) {
            this.data[i][j].value = newValue;
          }
        }
      }
    },
  },
  created() {
    this.setDefaultData();
    this.fetchBulbPower();
    this.fetchAllProductColors();
  },
  mounted() {},
  methods: {
    debounceFilterColors: debounce(async function (query) {
      this.colorFilterKey = query;
    }, 100),

    pushPrintingBulbPower() {
      return this.$router.push({
        name: 'printing_bulb_power',
        query: {
          activeTab: 'sku_setting',
        },
      });
    },

    async fetchBulbPower() {
      this.isLoading = true;
      const res = await getDetailBulbPowerPrinting(this.bulbSettingId);
      if (res.data.data) {
        this.originalList = clone(JSON.parse(res.data.data));
        this.originalList.forEach((item, index) => {
          let row = Math.floor(index / this.numCols);
          this.data[row][index - row * this.numCols].value = item;
        });
        this.colorSetting = res.data;
        this.originalList = JSON.parse(res.data.data);
      }
      this.isLoading = false;
    },
    formatTooltip(value) {
      return `${value}%`;
    },
    async fetchAllProductColors() {
      const { data } = await fetchAllColors();
      this.colorList = data;
    },
    selectAll() {
      for (let i = 0; i < this.numRows; i++) {
        for (let j = 0; j < this.numCols; j++) {
          this.data[i][j].active = true;
        }
      }
    },
    reset() {
      this.originalList.forEach((item, index) => {
        let row = Math.floor(index / this.numCols);
        this.data[row][index - row * this.numCols].value = item;
      });
    },
    setDefaultData() {
      this.data = new Array(this.numRows);
      for (let i = 0; i < this.numRows; i++) {
        this.data[i] = new Array(this.numCols);
        for (let j = 0; j < this.numCols; j++) {
          this.data[i][j] = {
            value: 50,
            active: false,
          };
        }
      }
    },
    async onSubmit() {
      this.isLoading = true;
      let params = [];
      for (let i = 0; i < this.numRows; i++) {
        for (let j = 0; j < this.numCols; j++) {
          params.push(this.data[i][j].value);
        }
      }
      try {
        const response = await updateBulbPowerPrinting({
          id: this.bulbSettingId,
          color_sku: this.colorSetting?.color?.sku,
          style_sku: this.colorSetting?.style?.sku,
          bulb_setting_data: params,
        });
        if (response.status === 200) {
          this.notification(response.data.message, 'success');
          this.deSelectAll();
          this.pushPrintingBulbPower();
        }
      } catch (e) {
        const data = e.response.data;
        let msg = '';
        if (data.errors) {
          const firstKey = Object.keys(data.errors)[0];
          msg = data.errors[firstKey][0];
        } else {
          msg = data.message;
        }

        this.notification(msg, 'error');
      }
      this.isLoading = false;
    },
    deSelectAll() {
      for (let i = 0; i < this.numRows; i++) {
        for (let j = 0; j < this.numCols; j++) {
          this.data[i][j].active = false;
        }
      }
    },
    settingActive(value) {
      if (value == this.value) {
        for (let i = 0; i < this.numRows; i++) {
          for (let j = 0; j < this.numCols; j++) {
            if (this.data?.[i]?.[j]?.active === true) {
              this.data[i][j].value = value;
            }
          }
        }
      } else {
        this.value = value;
      }
    },

    updateListSetting(indexRow, indexCol) {
      this.data[indexRow][indexCol].active =
        !this.data[indexRow][indexCol].active;
    },
    generateBackground(value) {
      if (value >= 75) {
        return 'bg-[#FF0000]';
      }
      if (value >= 50) {
        return 'bg-[#FFA500]';
      }
      if (value >= 25) {
        return 'bg-[#FFFF00]';
      }
    },
  },
};
