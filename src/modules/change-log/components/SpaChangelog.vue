<template>
  <div>
    <div class="flex flex-row-reverse mb-5">
      <div>
        <el-button v-if="changeEditSpa" class="mr-5" type="primary" @click="changeEdit">Edit</el-button>
        <div v-else>
          <el-button class="mr-5" @click="changeEditExit">Exit</el-button>
          <el-button class="mr-5" type="primary" @click="updateDataChangeLog(false)">Save</el-button>
          <el-button class="mr-5" type="primary" @click="updateDataChangeLog(true)">Save and Notify</el-button>
        </div>
      </div>
    </div>
    <div :class="[changeEditSpa ? 'pointer-events-none' : '']" >
      <QuillEditor v-model:content="dataSpa" theme="snow" toolbar="full"  ref="quillEditor" contentType="html"/>
    </div>
  </div>
</template>

<script>
import {equals} from 'ramda';
import {addAndUpdate, get} from "@/api/changelog"
import {QuillEditor} from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css';

export default {
  name: 'SpaChangelog',
  props: {
  },
  components: {
    QuillEditor,
  },
  mixins: [],
  data() {
    return {
      isLoading: false,
      dataSpa: null,
      changeEditSpa: true
    };
  },
  emits: ['handleFilter'],
  computed: {
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },
    maxHeight() {
      return parseInt(window.innerHeight - 280);
    },
  },
  mounted() {
    this.fetchDataSpa();
  },
  methods: {
    async fetchDataSpa() {
      const data = await get({source: 'spa'});
      this.dataSpa =  data.data.data || ''
    },
    async updateDataChangeLog(noti){
      try {
        const param = {
          source: 'spa',
          data: this.dataSpa,
          is_noti: noti
        }
        await addAndUpdate(param);
        this.notification('save change log SPA success', 'success');
      } catch (e) {
        let message = 'save change log fail.'
        this.notification(message, 'error');
      } finally {
        this.changeEditSpa = true
      }
    },
    async changeEdit(){
      this.changeEditSpa = !this.changeEditSpa
    },
    changeEditExit() {
      this.changeEditSpa = !this.changeEditSpa
      this.fetchDataSpa()
    }
  },
};
</script>
