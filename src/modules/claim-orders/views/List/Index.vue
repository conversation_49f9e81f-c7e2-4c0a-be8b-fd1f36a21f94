<style src="./Style.scss" lang="scss"></style>
<script src="./Script.js"></script>
<template>
    <div>
        <div class="top-head">
            <div class="top-head-left">
                <h1>{{ $t('Claims') }}</h1>
            </div>
            <div class="top-head-right">
            <span class="mr-3">{{ $t('Total Claims: ') }}{{ totalTicket }}</span>
            <span class="mr-3">{{ $t('Approved: ') }}{{ totalResolved }}</span>
            <span class="mr-3">{{ $t('In Review: ') }}{{ totalInProgress }}</span>
            <span class="mr-3">{{ $t('Rejected: ') }}{{ totalRejected }}</span>
            <span class="mr-3">{{ $t('Closed: ') }}{{ totalClosed }}</span>
            <span class="mr-3 text-red-500"
            >{{ $t('New: ') }}{{ totalUnresolved }}</span
            >
      </div>

        </div>
        <div class="table-content">
            <div class="filter">
                <div class="label">{{ $t('Filter by:') }}</div>
                <div class="filter-item ml-2">
                    <el-dropdown ref="ticket_number" trigger="click" class="el-dropdown-filter-item" :class="{ 'is-active': hasChangeFilterByItem('ticket_number') }">
                        <span class="el-dropdown-link">
                            <template v-if="hasChangeFilterByItem('ticket_number')">
                                <el-tooltip effect="dark" :content="$t('Claim Order')" placement="top-start">
                                    <span>{{ filter.ticket_number }}</span>
                                </el-tooltip>
                            </template>
                            <template v-else>{{ $t('Claim Order') }}</template>
                            <span class="icon">
                                <icon :data="iconChevronDown" />
                            </span>
                        </span>
                        <template #dropdown>
                            <div class="el-dropdown-menu-filter-item">
                                <el-input :placeholder="$t('Enter search Ticket Number')" class="search" v-model="filter.ticket_number" @keydown.enter="onFilter('ticket_number')" clearable @clear="clearFilterItem('claim_order')" />
                            </div>
                        </template>
                    </el-dropdown>
                </div>

                <div class="filter-item ml-2">
                    <el-dropdown ref="order_number" trigger="click" class="el-dropdown-filter-item" :class="{ 'is-active': hasChangeFilterByItem('order_number') }">
                        <span class="el-dropdown-link">
                            <template v-if="hasChangeFilterByItem('order_number')">
                                <el-tooltip effect="dark" :content="$t('Order Number')" placement="top-start">
                                    <span>{{ filter.order_number }}</span>
                                </el-tooltip>
                            </template>
                            <template v-else>{{ $t('Order Number') }}</template>
                            <span class="icon">
                                <icon :data="iconChevronDown" />
                            </span>
                        </span>
                        <template #dropdown>
                            <div class="el-dropdown-menu-filter-item">
                                <el-input :placeholder="$t('Enter search Order Number')" class="search" v-model="filter.order_number" @keydown.enter="onFilter('order_number')" clearable @clear="clearFilterItem('order_number')" />
                            </div>
                        </template>
                    </el-dropdown>
                </div>

              <div class="filter-item ml-2">
                <el-dropdown ref="order_number" trigger="click" class="el-dropdown-filter-item" :class="{ 'is-active': hasChangeFilterByItem('ref_number') }">
                        <span class="el-dropdown-link">
                            <template v-if="hasChangeFilterByItem('ref_number')">
                                <el-tooltip effect="dark" :content="$t('Ref Number')" placement="top-start">
                                    <span>{{ filter.ref_number }}</span>
                                </el-tooltip>
                            </template>
                            <template v-else>{{ $t('Ref Number') }}</template>
                            <span class="icon">
                                <icon :data="iconChevronDown" />
                            </span>
                        </span>
                  <template #dropdown>
                    <div class="el-dropdown-menu-filter-item">
                      <el-input :placeholder="$t('Enter search Ref Number')" class="search" v-model="filter.ref_number" @keydown.enter="onFilter('ref_number')" clearable @clear="clearFilterItem('ref_number')" />
                    </div>
                  </template>
                </el-dropdown>
              </div>

                <div class="filter-item ml-2">
                    <el-dropdown trigger="click" class="el-dropdown-filter-item" :class="{ 'is-active': hasChangeFilterByItem('store') }">
                        <span class="el-dropdown-link">
                            <template v-if="hasChangeFilterByItem('store')">
                                <el-tooltip effect="dark" :content="$t('Store Name')" placement="top-start">
                                    <span>{{ filter.store }}</span>
                                </el-tooltip>
                            </template>
                            <template v-else>{{ $t(' Store Name ') }}</template>
                            <span class="icon">
                                <icon :data="iconChevronDown" />
                            </span>
                        </span>
                        <template #dropdown>
                            <div class="el-dropdown-menu-filter-item">
                                <el-input :placeholder="$t('Enter Store Name')" class="search" v-model="filter.store" @keydown.enter="onFilter('store')" clearable @clear="clearFilterItem('store')" />
                            </div>
                        </template>
                    </el-dropdown>
                </div>

                <div class="filter-item ml-2">
                    <el-dropdown trigger="click" class="el-dropdown-filter-item" :class="{ 'is-active': hasChangeFilterByItem('email') }">
                        <span class="el-dropdown-link">
                            <template v-if="hasChangeFilterByItem('email')">
                                <el-tooltip effect="dark" :content="$t('Email')" placement="top-start">
                                    <span>{{ filter.email }}</span>
                                </el-tooltip>
                            </template>
                            <template v-else>{{ $t(' Email ') }}</template>
                            <span class="icon">
                                <icon :data="iconChevronDown" />
                            </span>
                        </span>
                        <template #dropdown>
                            <div class="el-dropdown-menu-filter-item">
                                <el-input :placeholder="$t('Enter search Email')" class="search" v-model="filter.email" @keydown.enter="onFilter('email')" clearable @clear="clearFilterItem('email')" />
                            </div>
                        </template>
                    </el-dropdown>
                </div>

                <div class="filter-item ml-2">
                    <el-dropdown trigger="click" class="el-dropdown-filter-item" :class="{ 'is-active': hasChangeFilterByItem('status') }">
                        <span class="el-dropdown-link">
                            <template v-if="hasChangeFilterByItem('status')">
                                <el-tooltip effect="dark" :content="$t('Status')" placement="top-start">
                                    <span>{{ getStatus(filter.status) }}</span>
                                </el-tooltip>
                            </template>
                            <template v-else>{{ $t(' Status ') }}</template>
                            <span class="icon">
                                <icon :data="iconChevronDown" />
                            </span>
                        </span>
                        <template #dropdown>
                            <div class="el-dropdown-menu-filter-item">
                                <el-select filterable v-model="filter.status" :placeholder="$t('Select Status')" @change="onFilter">
                                    <el-option v-for="item in status" :key="item.value" :label="item.label" :value="String(item.value)">
                                    </el-option>
                                </el-select>
                            </div>
                        </template>
                    </el-dropdown>
                </div>

                <div class="filter-item ml-2">
                    <el-dropdown trigger="click" class="el-dropdown-filter-item" :class="{ 'is-active': hasChangeFilterByItem('issue') }">
                        <span class="el-dropdown-link">
                            <template v-if="hasChangeFilterByItem('issue')">
                                <el-tooltip effect="dark" :content="$t('issue')" placement="top-start">
                                    <span v-if="filter.issue === 1">My Order Lost In Transit</span>
                                    <span v-else="filter.issue === 2">My Order Need Some Care</span>
                                </el-tooltip>
                            </template>
                            <template v-else>{{ $t(' Issue ') }}</template>
                            <span class="icon">
                                <icon :data="iconChevronDown" />
                            </span>
                        </span>
                        <template #dropdown>
                            <div class="el-dropdown-menu-filter-item">
                                <el-select filterable v-model="filter.issue" :placeholder="$t('Select Issue')" @change="onFilter">
                                    <el-option v-for="item in type" :key="item.value" :label="item.label" :value="item.value">
                                    </el-option>
                                </el-select>
                            </div>
                        </template>
                    </el-dropdown>
                </div>

                <div class="filter-item ml-2">
                    <el-dropdown trigger="click" class="el-dropdown-filter-item" :class="{ 'is-active': hasChangeFilterByItem('resolution') }">
                        <span class="el-dropdown-link">
                            <template v-if="hasChangeFilterByItem('resolution')">
                                <el-tooltip effect="dark" :content="$t('Resolution')" placement="top-start">
                                    <span v-if="filter.resolution === 'no_need'">No Need</span>
                                    <span v-else-if="filter.resolution === 'refund'">Request Refund</span>
                                    <span v-else-if="filter.resolution === 'replacement'">Request Replacement</span>
                                </el-tooltip>
                            </template>
                            <template v-else>{{ $t(' Resolution ') }}</template>
                            <span class="icon">
                                <icon :data="iconChevronDown" />
                            </span>
                        </span>
                        <template #dropdown>
                            <div class="el-dropdown-menu-filter-item">
                                <el-select filterable v-model="filter.resolution" :placeholder="$t('Select Resolution')" @change="onFilter">
                                    <el-option v-for="item in resolution " :key="item.value" :label="item.label" :value="String(item.value)">
                                    </el-option>
                                </el-select>
                            </div>
                        </template>
                    </el-dropdown>
                </div>

                <div class="filter-item ml-2">
                    <el-dropdown trigger="click" class="el-dropdown-filter-item" :class="{ 'is-active': date && date.length }">
                        <span class="el-dropdown-link">
                            <template v-if="date && date.length">
                                <el-tooltip effect="dark" :content="$t('Issued Date')" placement="top-start">
                                    <span> {{ templateDateRange(date[0], date[1]) }}</span>
                                </el-tooltip>
                            </template>
                            <template v-else> {{ $t('Issued Date') }}</template>
                            <span class="icon">
                                <icon :data="iconChevronDown" />
                            </span>
                        </span>
                        <template #dropdown>
                            <div class="el-dropdown-menu-filter-item">
                                <el-date-picker format="YYYY-MM-DD" v-model="date" type="daterange" range-separator="To" :start-placeholder="$t('Start Date')" :end-placeholder="$t('End Date')" @change="onChangeDate">
                                </el-date-picker>
                            </div>
                        </template>
                    </el-dropdown>
                </div>
                <div class="filter-item" v-if="hasFilter">
                    <el-link type="danger" @click="onClearFilter" :underline="false">
                        {{ $t('Clear') }}
                    </el-link>
                </div>
            </div>

            <el-table stripe border size="small" :data="items" style="width: 100%" @sort-change="sortTable" :max-height="maxHeight" v-loading="isLoading" element-loading-text="Loading..." @row-click="selectedRow">
                <el-table-column class-name="cursor-pointer" prop="ticket_number" :label="$t('Ticket Number')" min-width="120">
                    <template class="text-cyan-400 hover:" #default="scope">
                        <el-link :underline="false" type="primary">
                            {{ scope.row.ticket_number }}
                        </el-link>
                    </template>
                </el-table-column>

                <el-table-column prop="order_number" :label="$t('Order Number')" min-width="120">
                    <template #default="scope">
                        {{ scope.row.sale_order?.order_number }}
                    </template>
                </el-table-column>

                <el-table-column prop="ref_number" :label="$t('Reference Number')" min-width="120">
                  <template #default="scope">
                    {{ scope.row.sale_order?.external_number }}
                  </template>
                </el-table-column>

                <el-table-column prop="store_name" class-name="text-center" :label="$t('Store Name')" min-width="120">
                    <template #default="scope">
                        {{ scope.row.store.name }}
                    </template>
                </el-table-column>

                <el-table-column prop="contract_email" class-name="text-center" :label="$t('Contract Email')" min-width="120">
                    <template #default="scope">
                        {{ scope.row.customer_email }}
                    </template>
                </el-table-column>

                <el-table-column prop="type" class-name="text-center" :label="$t('Issue')">
                    <template #default="scope">
                        {{ getOrdeTypeByValue(scope.row.type, type) }}
                    </template>
                </el-table-column>

                <el-table-column prop="resolution" :label="$t('Preferred Resolution')" min-width="140">
                    <template #default="scope">
                        {{ getOrdeResolutionByValue(scope.row.solution, resolution) }}
                    </template>
                </el-table-column>

                <el-table-column prop="status" :label="$t('Status')">
                    <template #default="scope">
                        <el-tag 
                            type="info" 
                            :class="getClassStatus(scope.row.status)" 
                            class="rounded-xl text-center !text-white justify-center items-center" 
                            round 
                            size="small"
                        >
                            {{ getOrderStatusByValue(scope.row.status, status) }}
                        </el-tag>
                    </template>
                </el-table-column>

                <el-table-column prop="employee_name" :label="$t('Resolved by')" min-width="120">
                    <template #default="scope">
                        {{ scope.row?.resolve_by?.name }}
                    </template>
                </el-table-column>

                <el-table-column prop="assign_to" :label="$t('Assign')">
                    <template #default="scope">
                        {{ scope.row?.assigne?.name }}
                    </template>
                </el-table-column>

                <el-table-column prop="created_at" sortable="custom" :label="$t('Issued Date')" min-width="120">
                    <template #default="scope">
                        {{
                        convertTime(scope.row.created_at, 'YYYY-MM-DD HH:mm:ss')
                        }}
                    </template>
                </el-table-column>
                <el-table-column prop="age" :label="$t('Age')">
                    <template #default="scope">
                        {{ scope.row?.age ?? '' }}
                    </template>
                </el-table-column>
            </el-table>
            <div class="bottom">
                <div class="total">
                    {{ $t('Total:') }} {{ items.length ? formatNumber(total) : 0 }}
                </div>
                <el-pagination :disabled="isLoading" background layout="prev, pager, next" :page-size="filter.limit" :total="total" @current-change="changePage" v-model:currentPage="filter.page">
                </el-pagination>
                <div class="limit" :disabled="isLoading">
                    <el-select v-model="filter.limit" :placeholder="$t('Select')" size="mini" @change="onFilter">
                        <el-option v-for="item in limits" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </div>
            </div>
        </div>
        <el-dialog v-loading="loading_detail" v-model="showDetail" @close="resetData" :show-close="false" custom-class=" el-dialog dialog-detail el-dialog-large">
            <template #title>
                <!-- login -->
                <div>
                    <div class="mb-2">
                        <div v-if="employee" class="bg-gray-50 p-3 border rounded">
                            <div class="flex justify-between">
                                <b style="font-size: 18px" class="mr-2">{{ $t('Hi') }} {{ employee.name + ',' }}
                                    {{ $t(' Have a nice day!') }}</b>
                                <el-link type="danger" class="ml-3" @click="resetEmployee" :underline="false">{{ $t('Logout') }}
                                </el-link>
                            </div>
                        </div>
                    </div>
                    <div v-if="!employee" class="mb-1 required-icon">
                        Scan Employee ID
                    </div>
                    <div class="m-3" v-show="!employee" :error="employeeError" @keyup.enter="getScanCodeEmloyee">
                        <el-input :placeholder="$t('Employee ID')" class="el-form-item-scan-employee max-w-fit" ref="employeeCode" v-model="codeEmployee" />
                    </div>
                </div>
                <!-- end login -->
                <!-- header -->
                <div class="flex justify-between">
                    <span class="font-bold text-xl word-break-custom">Claim: (#{{
              ticketSelected.ticket_number
            }})</span>
                </div>
            </template>
            <!-- body -->
            <!-- form -->
            <div v-if="ticketSelected" class="border border-solid border-gray-300 flex">
                <div class="w-6/12">
                    <div class="flex items-center content-between p-2">
                        <span class="font-bold flex-[0_0_80px]">
                            {{ $t('Issue') }}:
                        </span>
                        <div class="flex-1">
                            <span class="text-center w-52 !border-purple-400">{{ getOrdeTypeByValue(ticketSelected.type, type) }}</span>
                        </div>
                    </div>
                    <div class="mt-2 ticket-item p-2">
                        <div class="flex items-center content-between">
                            <span class="font-bold flex-[0_0_80px]">{{ $t('Status') }}:</span>
                            <el-select filterable v-model="ticketStatus" :placeholder="$t('Status')" :disabled="
                  isLoadingAssignTo || ticketSelected.status === 'approve' || !employee || ticketSelected.status === 'closed' || ticketSelected.status === 'reject'
                " @change="changeStatus" class="w-52" clearable>
                                <el-option v-for="item in ticket_status" :label="item.label" :value="item.value" />
                            </el-select>
                        </div>
                        <div class="flex items-center content-between my-3">
                            <div class="font-semibold flex-[0_0_80px]">
                                {{ $t('Assign') }}:
                            </div>
                            <div class="flex-1">
                                <el-select filterable v-model="assignToId" :placeholder="$t('Assign to')" :disabled="
                      isLoadingAssignTo || ticketSelected.status === 'approve' || !employee || ticketSelected.status === 'reject' || ticketSelected.status === 'closed'
                    " @change="assignTo" class="w-52" clearable>
                                    <el-option v-for="item in techEmployees" :key="item.id" :label="item.name" :value="item.id" />
                                </el-select>
                            </div>
                        </div>
                        <div class="flex items-center content-between">
                            <span class="font-bold flex-[0_0_80px]">
                                {{ $t('Date') }}:
                            </span>
                            <div class="flex-1">
                                <span class="text-center w-52 !border-purple-400">{{
                    convertUtcToLocalTime(
                      ticketSelected.created_at,
                      'YYYY-MM-DD HH:mm:ss'
                    )
                  }}</span>
                            </div>
                        </div>
                    </div>
                    <!-- claim detail -->
                    <div class="flex items-center content-between p-2">
                        <span class="font-bold">
                            {{ $t('Claim  Detail') }}:
                        </span>
                    </div>
                    <div class="border-2 border-grey-200 ml-2">
                        <div class="mt-2 p-2">
                            <p class="word-break-custom">
                                <span class="font-bold">{{ $t('Store') }}: </span>{{ ticketSelected?.store.name ?? '' }}
                            </p>
                        </div>
                      <div class="mt-2 p-2">
                        <p class="word-break-custom">
                          <span class="font-bold">{{ $t('Reference number') }}: </span>{{ ticketSelected?.sale_order?.external_number ?? '' }}
                        </p>
                      </div>
                        <div class="mt-2 p-2">
                            <p class="word-break-custom">
                                <span class="font-bold">{{ $t('Order Number') }}: </span>{{ ticketSelected?.sale_order?.order_number ?? '' }}
                            </p>
                        </div>
                        <div class="mt-2 p-2">
                            <p class="word-break-custom">
                                <span class="font-bold">{{ $t('Contract email') }}: </span>{{ ticketSelected.customer_email }}
                            </p>
                        </div>
                        <div class="mt-2 p-2">
                            <p class="word-break-custom">
                                <span class="font-bold">{{ $t('Preferred Resolution') }}: </span>{{ getOrdeResolutionByValue(ticketSelected.solution, resolution) }}
                            </p>
                        </div>
                    </div>
                    <div class="mt-4 ticket-item p-2">
                        <p class="font-bold">{{ $t('Additional detail') }}:</p>
                        <p class="word-break-custom">
                            {{ ticketSelected.additional_details }}
                        </p>
                    </div>

                    <div class="mt-2 ticket-item p-2">
                        <p class="font-bold">{{ $t('Attachments') }}:</p>
                        <div @click="download(file.link_url)" class="hover:bg-gray-100 rounded cursor-pointer" v-for="file in ticketSelected?.images">
                            <el-link :underline="false" type="primary">
                                {{ getFileName(file.link_url) }}
                            </el-link>
                        </div>
                    </div>
                </div>

                <!-- history log -->
                <div class="ml-2 w-6/12 flex flex-col" v-loading="isLoading">
                    <div class="mt-2 p-2 flex flex-col flex-grow max-h-[500px] overflow-auto">
                        <el-scrollbar>
                        <!-- :max-height="400" -->
                        <el-timeline class="timeline timeline-custom">
                            <el-timeline-item
                            v-for="(item, index) in comments"
                            :key="index"
                            :type="'primary'"
                            :timestamp="item.created_at"
                            :hide-timestamp="true"
                            >
                            <div class="el-timeline-item__number">{{ index + 1 }}</div>
                            <div
                                class="el-timeline-item__content mt-1"
                                style="white-space: pre-line"
                                v-html="item.note"
                            ></div>
                            <div class="el-timeline-item__timestamp is-bottom">
                            <span class="user mr-3">
                                <icon class="relative" style="top: -2px" :data="iconUser2" />
                                    {{ item.employee?.name }}
                                </span>
                                {{ convertUtcToLocalTime(item.created_at, 'YYYY-MM-DD HH:mm:ss')}}
                            </div>
                            </el-timeline-item>
                        </el-timeline>

                        </el-scrollbar>
                    </div>
                    <div class="mt-2 p-2 flex flex-col">
                        <div class="relative w-full flex-grow">
                            <div class="flex-grow"></div>
                            <el-input v-if="employee" :placeholder="$t('Comment')" class="w-full" ref="employeeCode" v-model="comment" type="textarea"></el-input>
                            <el-button type="primary" class="absolute bottom-2 right-1 !p-1 text-xs" :disabled="!comment" @click="sendComment">
                                Comment
                            </el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- endform -->
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showDetail = false">Close</el-button>
                    <!-- <el-button v-if="ticketSelected.status != 'resolved'" type="primary" @click="
              showDetail = false;
              showDialogUpdateStatus = true;
            ">Resolve</el-button> -->
                </span>
            </template>
        </el-dialog>

        <el-dialog v-model="showDialogUpdateStatus" custom-class="el-dialog-custom el-dialog-box" @close="resetData" width="35%" draggable>
            <template #title>
                <span class="font-bold text-xl">Change To {{ getOrderStatusByValue(ticketStatus, status) }}</span>
            </template>
            <div>
                <div>
                    <div class="mb-2">
                        <div v-if="employee" class="bg-gray-50 p-3 border rounded">
                            <div class="flex justify-between">
                                <b style="font-size: 18px" class="mr-2">{{ $t('Hi') }} {{ employee.name + ',' }}
                                    {{ $t(' Have a nice day!') }}</b>
                                <el-link type="danger" class="ml-3" @click="resetEmployee" :underline="false">{{ $t('Logout') }}
                                </el-link>
                            </div>
                        </div>
                    </div>
                    <div v-if="ticketSelected && ticketSelected.long_description" class="mb-2">
                        <div class="font-semibold">{{ $t('Description') }}:</div>
                        <div style="white-space: pre-line" v-html="ticketSelected.long_description"></div>
                    </div>
                    <div v-if="!employee" class="mb-1 required-icon">
                        Scan Employee ID
                    </div>
                    <div v-show="!employee" :error="employeeError" @keyup.enter="getScanCodeEmloyee">
                        <el-input :placeholder="$t('Employee ID')" class="el-form-item-scan-employee max-w-fit" ref="employeeCode" v-model="codeEmployee" />
                    </div>
                </div>
                <el-form status-icon ref="formData" :model="data" :rules="dataRules" @submit.prevent="onSubmit" label-width="130px" :label-position="'top'">
                    <div class="justify-space-between layout-default mt-3">
                        <div class="w-full">
                            <el-form-item :label="$t('Claim Feedback')" prop="feedback">
                            <el-input type="textarea" :placeholder="$t('Type or paste your content here!')" v-model="data.feedback" :class="{
                      'border border-red-400 rounded':
                        errorValidator && errorValidator.feedback,
                    }"></el-input>
                            </el-form-item>
                        </div>
                        <div
                        class="text-danger text-[12px]"
                        v-if="errorValidator && errorValidator.feedback"
                    >
                        {{ errorValidator.feedback[0] }}
                    </div>
                    </div>
                    <div>
                        <p>{{ $t('Attachments') }}</p>
                        <div class="justify-space-between layout-default custom-upload-file">
                            <el-upload ref="fileUpload" class="upload-demo" drag multiple :auto-upload="false" :on-change="handleChangeImage" :before-upload="beforeUpload" :data="listUpload" :accept="'image/jpeg,image/png'" :max-size="5 * 1024 * 1024">
                                <el-icon class="el-icon--upload">
                                    <upload-filled />
                                </el-icon>
                                <div class="el-upload__text">
                                    Drop file here or <em>click to upload</em>
                                </div>
                            </el-upload>
                        </div>
                    </div>
                    <div class="text-danger text-[12px]" v-if="fileSelected.length == 0">
                        {{ requiredMessage }}
                    </div>
                    <div v-else-if="errorAttachment.length > 0">
                        <div v-for="error in errorAttachment" class="text-danger text-[12px]">
                            <p>{{ $t(error) }}</p>
                        </div>
                    </div>

                    <div class="text-danger text-[12px]" v-if="errorValidator && errorValidator.note">
                        {{ errorValidator.note[0] }}
                    </div>

                </el-form>
            </div>
            <template #footer>
                <el-button type="primary" @click="onSubmit" :disabled="!employee" :loading="isLoading">Submit
                </el-button>
            </template>
        </el-dialog>

        <!-- <div v-if="renderCreatePopup">
            <CreateInternalTicket @createdTicket="fetchData" :status="status" :urgency="urgency" @forceRender="forceRender" />
        </div> -->
    </div>
</template>

<style lang="scss">
.timeline-custom {
  padding-left: 30px;
  .el-timeline-item {
    .el-timeline-item__number {
      top: -1px;
      left: -30px;
      position: absolute;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background-color: var(--el-color-primary);
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
    }
  }
}
.word-break-custom {
        word-break: break-word;
    }

    .el-dialog__body {
        @apply py-2;
    }

    .timeline-custom {
  padding-left: 30px;
  .el-timeline-item {
    .el-timeline-item__number {
      top: -1px;
      left: -30px;
      position: absolute;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background-color: var(--el-color-primary);
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
    }
  }
}

</style>
