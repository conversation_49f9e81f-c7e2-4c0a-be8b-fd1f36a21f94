import {
  sentEmail,
  listClaims,
  saveComment,
  assignUser,
  getComment,
  getCount,
  getCountStatus,
} from '@/api/claimOrders.js';

import { mapGetters } from 'vuex';
import { equals, isEmpty } from 'ramda';
import filterMixin from '@/mixins/filter';
import dateMixin from '@/mixins/date.js';
import focusMixin from '@/mixins/helpers.js';
import moment from 'moment';
import {
  employeeLogoutTimeChecking,
  employeeTimeChecking,
} from '@/api/employee.js';
import EventBus from '@/utilities/eventBus.js';
import CreateInternalTicket from '@/modules/internal-ticket/views/Create/Index.vue';
import { API_URL } from '@/utilities/constants';

export default {
  name: 'Ticket',
  components: {
    CreateInternalTicket,
  },
  mixins: [filterMixin, dateMixin, focusMixin],
  data() {
    return {
      renderCreatePopup: true,
      items: [],
      comments: [],
      filter: this.setDefaultFilter(),
      isLoading: false,
      date: '',
      openDropdownTag: false,
      showDetail: false,
      showDialogUpdateStatus: false,
      showDialogStatusDetail: false,
      openDialogCreate: false,
      assignDialog: false,
      isEdit: false,
      ticketSelected: null,
      employee: null,
      time_checking_id: null,
      statusUpdate: null,
      codeEmployee: '',
      employeeError: '',
      feedback: '',
      id_time_checking: '',
      totalResolved: 0,
      totalInProgress: 0,
      totalUnresolved: 0,
      totalTicket: 0,
      total: 0,
      totalClosed: 0,
      totalRejected: 0,
      errorValidator: {},
      countTicket: {},
      note: '',
      data: {},
      files: [],
      listUpload: [],
      fileSelected: [],
      loading_detail: false,
      dataRules: {},
      job_type: 'resolved_claim_order',
      issue: [
          {
            label: 'Coloring Issue',
            value: '1',
          },
          {
            label: 'Missing Print',
            value: '2',
          },
          {
            label: 'Wrong Print',
            value: '3',
          },
          {
            label: 'Wrong Garment',
            value: '4',
          },
          {
            label: 'Wrong Items Send',
            value: '5',
          },
          {
            label: 'Washed Out Garment',
            value: '6',
          },
          {
            label: 'Washed Out Garment',
            value: '7',
          },
        ],        
        resolution: [
          {
            label: 'Request Refund',
            value: 'refund',
          },
          {
            label: 'Request Replacement',
            value: 'replacement',
          },
          {
            label: 'No Need',
            value: 'no_need',
          },
        ],        
        type: [
          {
              label: 'My Order Lost In Transit', 
              value: '1'
          },
          {
              label: 'My Order Need Some Care', 
              value: '2'
          },
        ],
        status: [
        {
          label: 'New',
          value: 'new',
        },
        {
          label: 'In Review',
          value: 'in_review',
        },
        {
          label: 'Approved',
          value: 'approve',
        },
        {
          label: 'Rejected',
          value: 'rejected',
        },
        {
          label: 'Closed',
          value: 'closed',
        },
      ],
      assignToId: '',
      ticketStatus: '',
      ticket_status: [],
      isLoadingAssignTo: false,
      comment: '',
    };
  },
  destroyed() {
    EventBus.$off('showCreateInternalTicket');
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 220);
    },
    ...mapGetters({
      getEmployees: 'getEmployees',
    }),
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },
    techEmployees() {
      return this.getEmployees.filter(
        (item) =>
          item.department && item.department.toLowerCase() === 'support'
      );
    },
    dataRulesComputed() {
      return {
        feedback: this.ticketSelected.solution === 'no_need' ? [] : [this.commonRule()],
      };
    }  
  },
  created() {
    this.fetchData();
    this.fetchEmployee();
    EventBus.$emit('showCreateInternalTicket');
  },
  methods: {
    forceRender() {
      this.renderCreatePopup = false;
      setTimeout(() => {
        this.renderCreatePopup = true;
      }, 100);
    },
    getInfoTechEmployee(code) {
      if (!code) return;
      const employee = this.techEmployees.find((item) => item.code === code);
      return employee?.name || '';
    },
    async fetchEmployee() {
      await this.$store.dispatch('getEmployees');
    },
    async assignTo() {
      this.isLoadingAssignTo = true;
      let params = {
        claim_order_id: this.ticketSelected.id,
        employee_id: this.employee.id,
        employee_assign_id: this.assignToId
      };
      try {
        this.loading_detail = true;
        await assignUser(params);
        this.loading_detail = false;
        this.notification(this.$t('Assign successfully', 'success'));
        this.showDetail = false;
        this.isLoading = true;
        this.fetchData();
        this.fetchEmployee();    
        this.isLoading = false;
      } catch (e) {}
      this.isLoadingAssignTo = false;
    },
    checkDisableStatus(status) {
      if (this.statusUpdate == 'resolved') return true;
      if (this.statusUpdate == 'in_progress') {
        return !['unresolved', 'in_progress'].includes(status);
      }
      return ['unresolved'].includes(status);
    },
    openAssignDialog() {
      if (this.ticketSelected.status != 'unresolved') {
        return;
      }
      this.assignDialog = true;
    },
    closeAssignDialog() {
      this.resetEmployee();
    },
    sortTable(data) {
      let sortColumn = '';
      let sortBy = '';
      if (data.prop && data.order) {
        sortColumn = data.prop;
        if (data.order === 'ascending') {
          sortBy = 'ASC';
        } else if (data.order === 'descending') {
          sortBy = 'DESC';
        }
      }
      this.filter.sort_column = sortColumn;
      this.filter.sort_by = sortBy;
      this.$nextTick(() => {
        this.fetchTickets();
      });
    },
    download(filePath) {
      return window.open(`${filePath}`, '_blank');
    },
    getFileName(path) {
      let filePaths = path.split('/');
      return filePaths[filePaths.length - 1] ?? '';
    },
    commonRule() {
      return {
        required: true,
        validator: (rule, value, callback) => {
          if (value && /\S/.test(value)) { // Check if value contains non-whitespace characters
            callback(); // Valid
          } else {
            callback(new Error(this.$t('This field cannot be left blank.'))); // Invalid
          }
    
          // Additional check for not being empty
          if (!value || value.trim().length === 0) {
            callback(new Error(this.$t('This field cannot be empty.'))); // Invalid
          } else {
            callback(); // Valid
          }
        },
        trigger: 'blur', // Trigger validation on blur
      };
    },
    async fetchData() {
      this.filter = this.getRouteParam();
      this.fetchTickets();
      this.getCountTickets();
    },
    onFilter(item = '') {
      this.filter.page = 1;
      this.$nextTick(() => {
        this.fetchTickets();
        if (item && this.$refs[item]) {
          this.$refs[item].handleClose();
        }
      });
    },
    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.date = '';
      this.$nextTick(() => {
        this.fetchTickets();
      });
    },
    onChangeDate() {
      if (this.date && this.date.length) {
        const startDate = new Date(this.date[0]);
        this.filter.start_date = startDate.getFullYear() + '-' + ('0' + (startDate.getMonth() + 1)).slice(-2) + '-' + ('0' + startDate.getDate()).slice(-2);
        
        // Format end_date
        const endDate = new Date(this.date[1]);
        this.filter.end_date = endDate.getFullYear() + '-' + ('0' + (endDate.getMonth() + 1)).slice(-2) + '-' + ('0' + endDate.getDate()).slice(-2);
        } else {
        this.filter.start_date = '';
        this.filter.end_date = '';
      }

      this.onFilter();
    },
    setDefaultFilter() {
      let params = {
        limit: 25,
        page: 1,
        store: '',
        ticket_number: '',
        issue: '',
        status: '',
        resolution: '',
        start_date: '',
        end_date: '',
        email: '',
        order_number: '',
        ref_number: ''
      };
      return params;
    },
    setRouteParam() {
      const params = this.filter;
      this.$router.replace({ name: 'claim_orders', query: params });
    },
    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);
      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }
      if (filter.start_date && filter.end_date) {
        this.date = [filter.start_date, filter.end_date];
      }
      filter.page = +filter.page || 1;
      return filter;
    },
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchTickets();
      });
    },
    async fetchTickets() {
      this.isLoading = true;
      this.setRouteParam();
      // const res = await list(this.filter);
      const res = await listClaims(this.filter);
      this.isLoading = false;
      const data = res.data || [];
      this.total = data.total || 0;
      this.items = data.data || [];
    },
    async getCountTickets() {
      this.isLoading = true;
      const res = await getCountStatus();
      this.countTicket = res.data;
      this.totalResolved =
        res.data.find((item) => item.status == 'approve')?.quantity ?? 0;
      this.totalUnresolved =
        res.data.find((item) => item.status == 'new')?.quantity ?? 0;
      this.totalInProgress =
        res.data.find((item) => item.status == 'in_review')?.quantity ?? 0;
      this.totalClosed =
        res.data.find((item) => item.status == 'closed')?.quantity ?? 0;
      this.totalRejected =
        res.data.find((item) => item.status == 'rejected')?.quantity ?? 0;

      this.totalTicket =
        this.totalResolved + this.totalUnresolved + this.totalInProgress + this.totalClosed + this.totalRejected;
    },
    hasChangeFilterByItem(name) {
      const query = this.$route.query;
      if (query[name]) {
        return true;
      }
      return false;
    },
    clearFilterItem(item) {
      this.filter[item] = '';
      if (this.$refs[item]) {
        this.$refs[item].handleClose();
      }
      this.onFilter();
    },

    getUrgency(value) {
      const selectItem = this.issue.find(
        (item) => item.value.toLowerCase() === value.toLowerCase()
      );
      return (selectItem && selectItem.label) || '';
    },
    getStatus(value) {
      const selectItem = this.status.find(
        (item) => item.value.toLowerCase() === value.toLowerCase()
      );
      return (selectItem && selectItem.label) || '';
    },
    selectedRow(row, column) {
      if (column.property == 'ticket_number') {
        this.showDetail = true;
        this.ticketSelected = row;
        this.statusUpdate = row.status;
        this.assignToId = this.ticketSelected.assign || '';
        this.ticketStatus = this.ticketSelected.status || '';
        this.fetchComment();
        this.getFilteredStatus(row.solution)
        this.dataRules = {
          feedback: this.ticketSelected.solution === 'no_need' ? [] : [this.commonRule()],
        };
    
      }
    },
    async resetData() {
      this.errorValidator = {};
      this.data = {};
      this.isEdit = false;
      this.showDetail = false,
      this.showDialogUpdateStatus =  false,
      this.showDialogStatusDetail = false,
      this.openDialogCreate = false,
      this.assignDialog = false,

      await this.resetEmployee();
    },
    formatTime(time, format = 'MMM DD, YYYY') {
      return moment(time).format(format);
    },
    async getScanCodeEmloyee() {
      try {
        this.isLoading = true;
        const res = await employeeTimeChecking({
          code: Number(this.codeEmployee),
          job_type: this.job_type,
        });
        if (!res.data.data) {
          this.codeEmployee = '';
          this.employeeError = 'Scan employee code error, please scan again.';
          this.notification(
            'Scan employee code error, please scan again.',
            'error'
          );
          this.focusByElClassScanEmployee();
          return;
        }
        this.employee = res.data.data;
        this.time_checking_id = res.data.id_time_checking;
        this.focusByElClass();
        this.isLoading = false;
      } catch (e) {
        const data = e.response.data;
        let message = this.$t('Not found');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.isLoading = false;
        this.notification(message, 'error');
      }
    },
    async resetEmployee() {
      await employeeLogoutTimeChecking(this.time_checking_id);
      this.employee = null;
      this.employeeError = '';
      this.codeEmployee = '';
      this.time_checking_id = null;
      this.time = 0;
      this.focusByElClassScanEmployee();
    },
    async onSubmit() {
      const isValid = await this.$refs['formData'].validate();
      if (!isValid) return;
      this.isLoading = true;
      let formData = new FormData();
      formData.append('sale_order_claim_support_id', this.ticketSelected.id);
      formData.append('employee_id', this.employee.id);
      formData.append('status', this.ticketStatus);
      formData.append('feedback', this.data?.feedback ?? '');
        for (let i = 0; i < this.listUpload.length; i++) {
          let file = this.listUpload[i].raw;
          formData.append('file[' + i + ']', file);
        }

      if (!isValid) return;
      this.isLoading = true;
      try {
        await sentEmail(formData);
        await this.fetchTickets();
        await this.getCountTickets();
        this.data = {};
        this.$refs['formData'].resetFields();
        this.notification(this.$t('Update status successfully', 'success'));
        this.showDialogUpdateStatus = false;
        this.showDetail = false;
        this.$refs.fileUpload.clearFiles(); // Reset file input
        this.fetchData();
      } catch (e) {
        this.errorValidator = e.response.data.errors;
      }
      this.isLoading = false;

    },
    create() {
      EventBus.$emit('showCreateInternalTicket');
    },
    getClassStatus(status) {
        switch (status) {
            case 'new':
                return '!bg-[#F68D11]';
            case 'in_review':
                return '!bg-[#6E57F9]';
            case 'approve':
                return '!bg-[#6FC140]';
            case 'rejected':
                return '!bg-[#F9101E]';
            case 'closed':
                return '!bg-[#000000]';
            default:
                return ''; // Default class if status doesn't match any case
        }
    },
      getOrderStatusByValue(data, status) {
      const selectItem = status.find((item) => item.value === data);
      return (selectItem && selectItem.label) || '';
    },
    getOrdeIssueByValue(data, issue) {
      const selectItem = issue.find((item) => item.value === data);
      return (selectItem && selectItem.label) || '';
    },
    getOrdeTypeByValue(data, type) {
      const selectItem = type.find((item) => item.value === data);
      return (selectItem && selectItem.label) || '';
    },
    getOrdeResolutionByValue(data, resolution) {
      const selectItem = resolution.find((item) => item.value === data);
      return (selectItem && selectItem.label) || '';
    },
    async sendComment() {
      this.isLoading = true;
      let params = {
        employee_id: this.employee.id,
        claim_order_id: this.ticketSelected.id,
        action: 'comment',
        note: this.comment,
      };
      try {
        await saveComment(params);
        await this.fetchComment();
        this.notification(this.$t('Comment successfully', 'success'));
        this.comment = '';
      } catch (e) {
      }
      this.isLoading = false;
    },
    async fetchComment() {
      this.isLoading = true;
      let params = {
        claim_order_id: this.ticketSelected.id,
      };
      try {
        const res = await getComment(params);
        this.comments = res.data;
      } catch (e) {}
      this.isLoading = false;
    },
    getFilteredStatus(resolution) {
      if (resolution === 'no_need') {
          this.ticket_status = this.status.filter(status => status.value === 'in_review' || status.value === 'closed');
      } else if (resolution === 'replacement' || resolution === 'refund') {
          this.ticket_status = this.status.filter(status => status.value === 'in_review' || status.value === 'approve' || status.value === 'rejected');
      } else {
          this.ticket_status = this.status;
      }
    },
    beforeUpload(file) {
      // Validate file type and size before uploading
      const isJpgOrPngOrPdf = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'application/pdf';
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isJpgOrPngOrPdf) {
        this.$message.error('Please upload JPG, PNG, or PDF format only.');
      }
      if (!isLt5M) {
        this.$message.error('File size must be less than 5MB.');
      }

      return isJpgOrPngOrPdf && isLt5M;
    },

    handleChangeImage(file, listFile) {
      this.listUpload = listFile;
    },
    async changeStatus() {
      if(this.ticketStatus === 'in_review') {
        let params = {
          employee_id: this.employee.id,
          sale_order_claim_support_id: this.ticketSelected.id,
          status: this.ticketStatus,
        };
        this.loading_detail = true;
        await sentEmail(params);
        this.loading_detail = false;
        this.notification(this.$t('Update status successfully', 'success'));
        this.showDetail = false;
        this.fetchData();
      } else {
        this.showDialogUpdateStatus = true;
      }
    },
  },
  // watch: {
  //   ticketSelected: {
  //     handler(newVal) {
  //       this.dataRules = this.dataRulesComputed;
  //     },
  //     deep: true
  //   }
  // }
  
}
