import { list } from "@/api/claim.js";
import { mapGetters } from "vuex";
import { equals } from "ramda";
import filterMixin from "@/mixins/filter";
import dateMixin from "@/mixins/date.js";
import { API_URL } from "@/utilities/constants";
import warehouseMixin from "@/mixins/warehouse";

export default {
  name: "Claim",
  components: {},
  mixins: [filterMixin, dateMixin, warehouseMixin],
  data() {
    return {
      items: [],
      filter: this.setDefaultFilter(),
      isLoading: false,
      date: "",
      openDropdownTag: false,
      types: [
        {
          label: "All",
          value: "",
        },
        {
          label: "CS",
          value: "CS",
        },
        {
          label: "QC",
          value: "QC",
        },
        {
          label: "MP",
          value: "MP",
        },
      ],
      reasonClass: [
        {
          name: "defective item",
          class: "text-warning",
        },
        {
          name: "wrong style",
          class: "text-warning",
        },
        {
          name: "wrong size",
          class: "text-warning",
        },
        {
          name: "wrong color",
          class: "text-warning",
        },
        {
          name: "missing pretreat",
          class: "text-danger",
        },
        {
          name: "heat press stain",
          class: "text-danger",
        },
        {
          name: "wrong print",
          class: "text-success",
        },
        {
          name: "wrong location print",
          class: "text-success",
        },
        {
          name: "faded print",
          class: "text-success",
        },
        {
          name: "lost in pulling",
          class: "text-success",
        },
        {
          name: "lost in pretreat",
          class: "text-success",
        },
        {
          name: "lost in printing",
          class: "text-success",
        },
        {
          name: "lost in qc",
          class: "text-success",
        }
      ],
    };
  },
  beforeUnmount() {},
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 220);
    },
    ...mapGetters([]),
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },
    buildLinkDownload() {
      let link = `${API_URL}/claim/export`;

      let params = {
        date_start: "",
        date_end: "",
        keyword: this.filter.keyword,
        type: this.filter.type,
        warehouse_id: this.userWarehouseId
      };

      if (this.date && this.date.length) {
        params.date_start = this.formatDate(this.date[0], false);
        params.date_end = this.formatDate(this.date[1], false);
      }

      params = new URLSearchParams(params);

      return `${link}?${params.toString()}`;
    },
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    exportExcel() {
      return (window.location.href = this.buildLinkDownload);
    },
    async fetchData() {
      this.filter = this.getRouteParam();
      this.fetchClaim();
    },
    onFilter(item = "") {
      this.filter.page = 1;
      this.$nextTick(() => {
        this.fetchClaim();
        if (item && this.$refs[item]) {
          this.$refs[item].handleClose();
        }
      });
    },
    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.date = "";
      this.$nextTick(() => {
        this.fetchClaim();
      });
    },
    onChangeDate() {
      if (this.date && this.date.length) {
        this.filter.date_start = this.formatDate(this.date[0], false);
        this.filter.date_end = this.formatDate(this.date[1], false);
      } else {
        this.filter.date_start = "";
        this.filter.date_end = "";
      }

      this.onFilter();
    },
    setDefaultFilter() {
      let params = {
        limit: 25,
        page: 1,
        keyword: "",
        date_start: "",
        date_end: "",
        type: "",
      };

      return params;
    },
    setRouteParam() {
      const params = this.filter;
      this.$router.replace({ name: "claim", query: params });
    },
    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);
      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }
      if (filter.date_start && filter.date_end) {
        this.date = [filter.date_start, filter.date_end];
      }
      filter.page = +filter.page || 1;
      return filter;
    },
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchClaim();
      });
    },
    async fetchClaim() {
      this.isLoading = true;
      this.setRouteParam();
      const res = await list(this.filter);
      this.isLoading = false;
      const data = res.data || [];
      this.total = data.total || 1;
      this.items = data.data || [];
    },
    hasChangeFilterByItem(name) {
      const query = this.$route.query;
      if (query[name]) {
        return true;
      }
      return false;
    },
    sortTable(data) {
      let sortColumn = "";
      let sortBy = "";
      if (data.prop && data.order) {
        sortColumn = data.prop;
        if (data.order === "ascending") {
          sortBy = "ASC";
        } else if (data.order === "descending") {
          sortBy = "DESC";
        }
      }
      this.filter.sort_column = sortColumn;
      this.filter.sort_by = sortBy;
      this.$nextTick(() => {
        this.fetchClaim();
      });
    },
    clearFilterItem(item) {
      this.filter[item] = "";
      if (this.$refs[item]) {
        this.$refs[item].handleClose();
      }
      this.onFilter();
    },

    getType(value) {
      const selectItem = this.types.find((item) => item.value.toLowerCase() === value.toLowerCase());
      return (selectItem && selectItem.label) || "";
    },
    handleOpenTag(isShow) {
      this.openDropdownTag = isShow;
    },
    tableCellClassName(data) {
      if (
        data.column &&
        data.column.property &&
        data.column.property == "reason"
      ) {
        if (data?.row?.reason) {
          const reasonItem = this.reasonClass.find(
            (item) => item.name === data.row.reason
          );
          return reasonItem?.class || "";
        }
      }
    },
  },
};
