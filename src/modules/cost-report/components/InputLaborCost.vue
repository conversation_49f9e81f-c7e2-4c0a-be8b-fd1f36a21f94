<template>
  <div>
    <el-dialog
        v-model="openDialogInputLaborCost"
        width="30%"
        title="Input Labor Cost"
        :close-on-click-modal="false"
        custom-class="el-dialog-labor-cost el-dialog-custom el-dialog-custom2"
        @close="onClose"
    >
      <template #default>
        <div class="flex justify-end mb-3">
          <div>Step {{ step }} of 2</div>
        </div>
        <div v-show="step === 1">
          <div class="flex items-center border-t pt-5">
            <span class="">Year</span>
            <div class="w-4/6 ml-auto">
              <el-date-picker
                  :clearable="false"
                  v-if="step === 1"
                  v-model="data.year"
                  type="year"
                  format="YYYY"
                  value-format="YYYY"
                  @change="changeYear"
                  placeholder="Select Year"
                  size="large"
              />
            </div>
          </div>
          <div v-if="validationErrors.carrier_code" class="w-4/6 ml-auto text-red-600 mt-1">
            {{ validationErrors.carrier_code[0] }}
          </div>
          <div class="flex items-center mt-5">
            <span class="">Pay Period Begin</span>
            <div class="w-4/6 ml-auto">
              <el-select @change="changeDate" v-model="data.pay_period_begin" class="" placeholder="Select Period"
                         size="large">
                <el-option v-for="(dateBegin, i) in periodBegins" :key="i" :label="dateBegin" :value="dateBegin"/>
              </el-select>
            </div>
          </div>
          <div v-if="validationErrors.tracking_number" class="w-4/6 ml-auto text-red-600 mt-1">
            {{ validationErrors.tracking_number[0] }}
          </div>
          <div class="flex items-center mt-5">
            <span class="">Pay Period End</span>
            <div class="w-4/6 ml-auto">
              <el-input v-model="data.pay_period_end" size="large" disabled=""/>
            </div>
          </div>
          <div v-if="validationErrors.ship_date" class="w-4/6 ml-auto text-red-600 mt-1">
            {{ validationErrors.ship_date[0] }}
          </div>
          <div class="flex items-center mt-5">
            <span class="">Total Labor Cost</span>
            <div class="w-4/6 ml-auto">
              <el-input size="large" placeholder="Cost" v-model="data.cost">
                <template #prefix>
                  <span class="items-center font-bold text-slate-900">$</span>
                </template>
              </el-input>
            </div>
          </div>
          <div v-if="validationErrors.price" class="w-4/6 ml-auto text-red-600 mt-1">
            {{ validationErrors.price[0] }}
          </div>
        </div>

        <div v-show="step === 2">
          <ScanEmployee
              @setEmployee="setEmployee"
              :jobType="'cost_report'"
              class="mb-3"
          />
          <el-input
              class="el-input-password"
              placeholder="Password"
              autocomplete="new-password"
              type="password"
              v-model="inputPassword"
              :disabled="passwordValid"
          >
          </el-input>
        </div>

        <div class="mt-3 flex justify-end">
          <template v-if="step === 1">
            <el-button @click="onCancel">Reset</el-button>
            <el-button
                @click="next"
                type="primary"
                :disabled="isDisabled"
            >Next
            </el-button
            >
          </template>
          <template v-else>
            <el-button @click="back">Back</el-button>
            <el-button
                @click="onSubmit"
                type="primary"
                :disabled="isDisabled || !passwordValid"
            >Confirm
            </el-button
            >
          </template>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import EventBus from '@/utilities/eventBus.js';
import ScanEmployee from '@/components/ScanEmployee.vue';
import moment from 'moment';
import {addLabor, fetchLabor} from '@/api/costReport.js';
import {isEmpty} from 'ramda';
import {makeid} from '@/utilities/helper.js';
import {toRaw, isProxy} from 'vue';

export default {
  name: 'InputLaborCost',
  components: {ScanEmployee},
  data() {
    return {
      openDialogInputLaborCost: false,
      isLoading: false,
      step: 1,
      items: this.setDefaultData(),
      affectedAt: '',
      employee: {},
      inputPassword: '',
      currentCostReport: [],
      randomKey: makeid(8),
      validationErrors: {},
      data: {
        pay_period_begin: '01/09/2023',
        pay_period_end: '01/22/2023',
        year: '2023',
        cost: '',
      },
      periodBegins: [],
      originYear: '2023',
      originPeriodBegin: '01/09/2023'
    };
  },
  created() {
    EventBus.$on('showInputLaborCost', async () => {
      this.items = this.setDefaultData();
      this.inputPassword = '';
      this.affectedAt = '';
      this.employee = {};
      this.step = 1;
      this.openDialogInputLaborCost = true;
      this.getListPeriodBegin();
      await this.getDetailLabor();
    });
  },
  watch: {
    'data.pay_period_begin': {
      handler() {
        this.data.pay_period_end = moment(this.data.pay_period_begin, "MM/DD/YYYY").add(13, 'days').format("MM/DD/YYYY");
      },
      deep: true
    },
    'data.year': {
      handler() {
        this.getListPeriodBegin();
      },
      deep: true
    }
  },
  computed: {
    passwordValid() {
      if (!this.inputPassword) {
        return false;
      }
      const passwordValid = 'SM' + moment().format('MMDD');
      if (this.inputPassword === passwordValid) {
        return true;
      }
      return false;
    },
    isDisabled() {
      if (this.isLoading) {
        return true;
      }
      if (this.step === 1 && !this.data.cost) {
        return true;
      }
      if (this.step === 2 && !Object.keys(this.employee)?.length) {
        return true;
      }
      return false;
    },
    isScanEmployee() {
      return !!Object.keys(this.employee).length;
    },
  },
  methods: {
    async getDetailLabor() {
      const format = 'YYYY-MM-DD';
      const params = {
        pay_period_begin: moment(this.data.pay_period_begin).format(format),
        pay_period_end: moment(this.data.pay_period_end).format(format),
      }
      const {data} = await fetchLabor(params);
      this.data.cost = data.data[0]?.cost ?? 0;
    },
    getListPeriodBegin() {
      this.periodBegins = [];
      let dateFormat = "MM/DD/YYYY";
      let subYear = parseInt(this.data.year) - parseInt(this.originYear);
      let absSubYear = Math.abs(subYear);
      let periodOfYear = 27;
      let startBegin = subYear >= 0
          ? moment(this.originPeriodBegin, dateFormat).add(14 * absSubYear * periodOfYear, 'days').format(dateFormat)
          : moment(this.originPeriodBegin, dateFormat).subtract(14 * absSubYear * periodOfYear, 'days').format(dateFormat);

      let checkDiff = moment(startBegin, dateFormat).diff(moment(`01/01/${this.data.year}`, dateFormat), 'days');
      if (Math.abs(checkDiff) >= 14) {
        startBegin = moment(startBegin, dateFormat).subtract(Math.ceil(checkDiff / 14) * 14, 'days').format(dateFormat);
      }
      for (let i = 0; i <= periodOfYear; i++) {
        let yearBegin = moment(startBegin, dateFormat).add(14 * i, 'days').format("YYYY");

        if (parseInt(yearBegin) > parseInt(this.data.year)) {
          break;
        }

        if (parseInt(yearBegin) == parseInt(this.data.year)) {
          this.periodBegins.push(moment(startBegin, dateFormat).add(14 * i, 'days').format(dateFormat));
        }
      }
      this.data.pay_period_begin = isProxy(this.periodBegins) ? toRaw(this.periodBegins)[0] : this.periodBegins[0];
    },
    async changeDate(date) {
      const format = 'YYYY-MM-DD';
      this.isLoading = true;
      const params = {
        pay_period_begin: moment(date).format(format),
        pay_period_end: moment(date, "MM/DD/YYYY").add(13, 'days').format(format),
      }

      const {data} = await fetchLabor(params);
      this.data.cost = data.data[0]?.cost ?? 0;
      this.isLoading = false;
    },
    setEmployee(employee) {
      this.employee = employee;
      if (this.isScanEmployee) {
        this.focusByElClass('el-input-password');
      }
    },
    async onSubmit() {
      this.isLoading = true;
      try {
        delete this.data.year;
        this.data.employee_id = this.employee.id;
        const format = 'YYYY-MM-DD';
        this.data.pay_period_begin = moment(this.data.pay_period_begin).format(format);
        this.data.pay_period_end = moment(this.data.pay_period_end).format(format);
        await addLabor(this.data);
        this.openDialogInputLaborCost = false;
        this.$emit('refresh');
      } catch (e) {
        console.log(e);
        let data = e.response.data;
        let message = data?.message || this.$t('Input cost error.');
        if (!isEmpty(data) && data?.errors) {
          data = data?.errors;
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.notification(message, 'error');
      } finally {
        this.isLoading = false;
      }
    },
    onClose() {
      EventBus.$emit('logoutScanEmployee');
    },
    onCancel() {
      this.items = this.setDefaultData();
      this.affectedAt = '';
    },
    next() {
      this.step++;
      if (!this.isScanEmployee) {
        EventBus.$emit('autoFocusScanEmployee');
        return;
      }
      this.focusByElClass('el-input-password');
    },
    back() {
      this.step--;
    },
    setDefaultData() {
      return [
        {
          pay_period_begin: '01/09/2023',
          pay_period_end: ''
        },
      ];
    },
  },
};
</script>

<style lang="scss">
.el-dialog-labor-cost {
  .el-date-editor {
    width: 100%;
  }

  .el-input__prefix-inner {
    align-items: center;
  }

  .el-select--large {
    width: 100%;
  }
}
</style>
