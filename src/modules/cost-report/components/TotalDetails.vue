<template>
  <div>
    <el-dialog
        v-model="openDialogTotalDetail"
        :title="buildTitle"
        custom-class="el-dialog-custom el-dialog-800"
    >
      <template #default>
        <table class="w-full">
          <thead>
          <tr>
            <th class="border px-2 py-1 text-center"></th>
            <th class="border px-2 py-1 text-center" colspan="2">
              Total blank cost ($)
            </th>
            <th class="border px-2 py-1 text-center">Total unit</th>
            <th class="border px-2 py-1 text-center">Cost per unit ($)</th>
          </tr>
          </thead>
          <tbody>
          <tr>
            <td class="border px-2 py-1">1. Blank cost</td>
            <td class="border px-2 py-1 text-center" colspan="2">
              {{ formatNumberFloat(data?.blank?.total) || 0 }}
            </td>
            <td class="border px-2 py-1 text-center">
              {{ formatNumberFloat(data?.blank?.total_unit || 0) }}
            </td>
            <td class="border px-2 py-1 text-center">
              {{ formatNumberFloat(data?.blank?.cost_per_unit) || 0 }}
            </td>
          </tr>
          </tbody>
          <thead>
          <tr>
            <th class="border px-2 py-1 text-center"></th>
            <th class="border px-2 py-1 text-center" colspan="2">
              Average consumption per unit (cc)
            </th>
            <th class="border px-2 py-1 text-center">Cost per cc ($)</th>
            <th class="border px-2 py-1 text-center">Cost per unit ($)</th>
          </tr>
          </thead>
          <tbody>

          <tr>
            <td class="border px-2 py-1">2. White Ink Cost</td>
            <td class="border px-2 py-1 text-center" colspan="2">
              {{ formatNumberFloat(data?.white_ink_cc?.avg_cc) || 0 }}
            </td>
            <td class="border px-2 py-1 text-center">
              {{ formatNumberFloat(data?.white_ink_cc?.cost_per_cc || 0) }}
            </td>
            <td class="border px-2 py-1 text-center">
              {{ formatNumberFloat(data?.white_ink_cc?.cost_per_unit) || 0 }}
            </td>
          </tr>
          <tr>
            <td class="border px-2 py-1">3. Color Ink Cost</td>
            <td class="border px-2 py-1 text-center" colspan="2">
              {{ formatNumberFloat(data?.color_ink_cc?.avg_cc) || 0 }}
            </td>
            <td class="border px-2 py-1 text-center">
              {{ formatNumberFloat(data?.color_ink_cc?.cost_per_cc) || 0 }}
            </td>
            <td class="border px-2 py-1 text-center">
              {{ formatNumberFloat(data?.color_ink_cc?.cost_per_unit) || 0 }}
            </td>
          </tr>
          </tbody>

          <thead>
          <tr>
            <th class="border px-2 py-1 text-center"></th>
            <th class="border px-2 py-1 text-center" colspan="2">
              Total pretreat cost ($)
            </th>
            <th class="border px-2 py-1 text-center">Total unit</th>
            <th class="border px-2 py-1 text-center">Cost per unit ($)</th>
          </tr>
          </thead>
          <tbody>
          <tr>
            <td class="border px-2 py-1">4. Pretreat Cost</td>
            <td class="border px-2 py-1 text-center" colspan="2">
              {{ formatNumberFloat(data?.pretreat?.total) || 0 }}
            </td>
            <td class="border px-2 py-1 text-center">
              {{ formatNumber(data?.pretreat?.total_unit) || 0 }}
            </td>
            <td class="border px-2 py-1 text-center">
              {{ formatNumberFloat(data?.pretreat?.cost_per_unit) || 0 }}
            </td>
          </tr>
          </tbody>

          <thead>
          <tr>
            <th class="border px-2 py-1 text-center"></th>
            <th class="border px-2 py-1 text-center">Cost per bag ($)</th>
            <th class="border px-2 py-1 text-center">Total unit</th>
            <th class="border px-2 py-1 text-center">Total order</th>
            <th class="border px-2 py-1 text-center">Cost per unit ($)</th>
          </tr>
          </thead>
          <tbody>
          <tr>
            <td class="border px-2 py-1">5. Bag Cost</td>
            <td class="border px-2 py-1 text-center">
              {{ formatNumberFloat(data?.bag?.cost_per) || 0 }}
            </td>
            <td class="border px-2 py-1 text-center">
              {{ formatNumber(data?.bag?.total_unit) || 0 }}
            </td>
            <td class="border px-2 py-1 text-center">
              {{ formatNumber(data?.bag?.total_order) || 0 }}
            </td>
            <td class="border px-2 py-1 text-center">
              {{ formatNumberFloat(data?.bag?.cost_per_unit) || 0 }}
            </td>
          </tr>
          </tbody>
          <thead>
          <tr>
            <th class="border px-2 py-1 text-center"></th>
            <th class="border px-2 py-1 text-center" colspan="3">
              Labor cost
            </th>
            <th class="border px-2 py-1 text-center">Cost per unit ($)</th>
          </tr>
          </thead>
          <tbody>
          <tr>
            <td class="border px-2 py-1">6. Labor Cost</td>
            <td class="border px-2 py-1 text-center" colspan="3">
              {{ formatNumberFloat(data?.labor?.total) || 0 }}
            </td>
            <td class="border px-2 py-1 text-center">
              {{ formatNumberFloat(data?.labor?.cost_per_unit) || 0 }}
            </td>
          </tr>
          </tbody>
          <thead>
          <tr>
            <th class="border px-2 py-1 text-center"></th>
            <th class="border px-2 py-1 text-center" colspan="2">
              Total gas & electricbill ($)
            </th>
            <th class="border px-2 py-1 text-center">Total unit</th>
            <th class="border px-2 py-1 text-center">Cost per unit ($)</th>
          </tr>
          </thead>
          <tbody>
          <tr>
            <td class="border px-2 py-1">7. Utility Cost</td>
            <td class="border px-2 py-1 text-center" colspan="2">
              {{ formatNumberFloat(data?.utility?.total) || 0 }}
            </td>
            <td class="border px-2 py-1 text-center">
              {{ formatNumber(data?.utility?.total_unit) || 0 }}
            </td>
            <td class="border px-2 py-1 text-center">
              {{ formatNumberFloat(data?.utility?.cost_per_unit) || 0 }}
            </td>
          </tr>
          </tbody>

          <thead>
          <tr>
            <th class="border px-2 py-1 text-center"></th>
            <th class="border px-2 py-1 text-center" colspan="2">
              Total shipping cost ($)
            </th>
            <th class="border px-2 py-1 text-center">Total unit</th>
            <th class="border px-2 py-1 text-center">Cost per unit ($)</th>
          </tr>
          </thead>
          <tbody>
          <tr>
            <td class="border px-2 py-1">8. Shipping Cost</td>
            <td class="border px-2 py-1 text-center" colspan="2">
              {{ formatNumberFloat(data?.shipping?.total_shipping) || 0 }}
            </td>
            <td class="border px-2 py-1 text-center">
              {{ formatNumber(data?.shipping?.total_unit) || 0 }}
            </td>
            <td class="border px-2 py-1 text-center">
              {{ formatNumberFloat(data?.shipping?.cost_per_unit) || 0 }}
            </td>
          </tr>
          <tr>
            <td class="px-2 pt-4 text-lg font-semibold" colspan="4">Total</td>
            <td class="px-2 pt-4 text-right text-xl">
              {{ totalCostPerUnit }}
            </td>
          </tr>
          </tbody>
        </table>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import EventBus from '@/utilities/eventBus.js';
import formatNumberMixin from '@/mixins/formatNumber.js';

export default {
  name: 'TotalDetails',
  mixins: [formatNumberMixin],
  components: {},
  data() {
    return {
      openDialogTotalDetail: false,
      data: {},
      productType: '',
      month: '',
    };
  },
  created() {
    EventBus.$on('showTotalDetails', (data, productType, month) => {
      console.log(data);
      this.productType = productType;
      this.month = month;
      this.data = data;
      this.openDialogTotalDetail = true;
    });
  },
  computed: {
    totalCostPerUnit() {
      let total = 0;
      for (let i = 0; i < Object.keys(this.data).length; i++) {
        const key = Object.keys(this.data)[i];
        const item = this.data[key];
        let costPerUnit = 0;
        if (item.cost_per_unit) {
          costPerUnit = item.cost_per_unit;
        }
        total += costPerUnit;
      }
      return this.formatNumberFloat(total);
    },
    buildTitle() {
      return `Total cost (${this.productType} - ${this.month})`;
    },
  },
};
</script>
