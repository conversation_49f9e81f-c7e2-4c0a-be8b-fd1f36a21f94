<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ $t('History') }}</h1>
      </div>
      <div class="top-head-right">
        <router-link
          class="el-button el-button--primary el-button--default"
          :to="{ name: 'cost_report' }"
        >
          {{ $t('Cost Report') }}
        </router-link>
      </div>
    </div>

    <el-tabs v-model="activeName" >
      <el-tab-pane label="Production Costs" name="first">
        <div class="table-content">
        <div class="filter">
          <div class="label">{{ $t('Filter by:') }}</div>
          <div class="filter-item">
            <el-date-picker
                format="YYYY-MM"
                value-format="YYYY-MM"
                v-model="filter.affected_at"
                type="month"
                placeholder="Pick a month"
                @change="onChangeDate"
            />
          </div>
          <div class="filter-item">
            <el-select
                filterable
                v-model="filter.cost_type"
                :placeholder="'Cost Type'"
                collapse-tags
                @change="onFilter"
                clearable
                multiple
                @clear="onFilter"
            >
              <el-option
                  v-for="item in costType"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
              />
            </el-select>
          </div>
          <div class="filter-item">
            <el-select
                filterable
                v-model="filter.employee_id"
                :placeholder="$t('Select employee')"
                @change="onFilter"
                clearable
                @clear="onFilter"
            >
              <el-option
                  v-for="item in employees"
                  :key="item.id"
                  :label="item.name"
                  :value="String(item.id)"
              >
              </el-option>
            </el-select>
          </div>
          <div class="filter-item" v-if="hasFilter">
            <el-link type="danger" @click="onClearFilter" :underline="false">
              {{ $t('Clear') }}
            </el-link>
          </div>
        </div>

        <el-table
            stripe
            border
            size="small"
            :data="items"
            style="width: 100%"
            :max-height="maxHeight"
            v-loading="isLoading"
            element-loading-text="Loading..."
            @sort-change="sortTable"
            :cell-class-name="tableCellClassName"
        >
          <el-table-column prop="style" :label="$t('Style')">
            <template #default="scope">
              {{ scope.row.product_type?.name }}
            </template>
          </el-table-column>
          <el-table-column prop="cost_type" :label="$t('Cost Type')">
            <template #default="scope">
              {{ getCostTypeName(scope.row.type) }}
            </template>
          </el-table-column>
          <el-table-column prop="value" :label="$t('Value ($)')">
            <template #default="scope">
              {{ formatNumberFloat(scope.row.value) }}
            </template>
          </el-table-column>
          <el-table-column prop="month_affected" :label="$t('Month affected')">
            <template #default="scope">
              {{ buildMonthName(scope.row.affected_month_at - 1) }}
              {{ scope.row.affected_year_at }}
            </template>
          </el-table-column>
          <el-table-column prop="update_date" :label="$t('Update date')">
            <template #default="scope">
              {{ formatDate(scope.row.updated_at, false, 'YYYY-MM-DD') }}
            </template>
          </el-table-column>
          <el-table-column prop="employee" :label="$t('Employee')">
            <template #default="scope">
              {{ scope.row.employee?.name }}
            </template>
          </el-table-column>
        </el-table>
        <div class="bottom">
          <div class="total">
            {{ $t('Total:') }} {{ items.length ? formatNumber(total) : 0 }}
          </div>
          <el-pagination
              :disabled="isLoading"
              background
              layout="prev, pager, next"
              :page-size="filter.limit"
              :total="total"
              @current-change="changePage"
              v-model:currentPage="filter.page"
          >
          </el-pagination>
          <div class="limit" :disabled="isLoading">
            <el-select
                v-model="filter.limit"
                :placeholder="$t('Select')"
                size="mini"
                @change="onFilter"
            >
              <el-option
                  v-for="item in limits"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
      </el-tab-pane>
      <el-tab-pane label="Labor cost" name="second">
        <div class="table-content">
          <div class="filter">
            <div class="label">{{ $t('Filter by:') }}</div>
            <div class="filter-item">
              <el-date-picker
                  v-model="filterLabor.year"
                  type="year"
                  format="YYYY"
                  value-format="YYYY"
                  placeholder="Select Year"
                  @change="onChangeDateLabor"
                  clearable
              />

            </div>
            <div class="filter-item">
              <el-select @change="changeDate" @clear="changeDate" v-model="filterLabor.pay_period_begin" class="" placeholder="Select Period" clearable>
                <el-option v-for="(dateBegin, i) in periodBegins" :key="i" :label="dateBegin" :value="dateBegin"/>
              </el-select>
            </div>
            <div class="filter-item">
              <el-select
                  filterable
                  v-model="filterLabor.employee_id"
                  :placeholder="$t('Select employee')"
                  @change="onFilterEmployeeLabor"
                  clearable
                  @clear="onFilterEmployeeLabor"
              >
                <el-option
                    v-for="item in employees"
                    :key="item.id"
                    :label="item.name"
                    :value="String(item.id)"
                >
                </el-option>
              </el-select>
            </div>
            <div class="filter-item" v-if="hasFilterLabor">
              <el-link type="danger" @click="onClearFilterLabor" :underline="false">
                {{ $t('Clear') }}
              </el-link>
            </div>
          </div>

          <el-table
              stripe
              border
              size="small"
              :data="labors"
              style="width: 100%"
              :max-height="maxHeight"
              v-loading="isLoading"
              element-loading-text="Loading..."
          >
            <el-table-column prop="cost_type" :label="$t('Cost Type')">
              <template #default="scope">
                Labor Cost
              </template>
            </el-table-column>
            <el-table-column prop="value" :label="$t('Value ($)')">
              <template #default="scope">
                {{ formatNumberFloat(scope.row.cost) }}
              </template>
            </el-table-column>
            <el-table-column prop="begin" :label="$t('Pay period begin')">
              <template #default="scope">
                {{ formatDate(scope.row.pay_period_begin, false, 'YYYY-MM-DD') }}
              </template>
            </el-table-column>
            <el-table-column prop="end" :label="$t('Pay period end')">
              <template #default="scope">
                {{ formatDate(scope.row.pay_period_end, false, 'YYYY-MM-DD') }}
              </template>
            </el-table-column>
            <el-table-column prop="year" :label="$t('Year')">
              <template #default="scope">
                {{ formatDate(scope.row.pay_period_begin, false, 'YYYY') }}
              </template>
            </el-table-column>
            <el-table-column prop="employee" :label="$t('Employee')">
              <template #default="scope">
                {{ scope.row.employee?.name }}
              </template>
            </el-table-column>
          </el-table>
          <div class="bottom">
            <div class="total">
              {{ $t('Total:') }} {{ labors.length ? formatNumber(totalLabors) : 0 }}
            </div>
            <el-pagination
                :disabled="isLoading"
                background
                layout="prev, pager, next"
                :page-size="filterLabor.limit"
                :total="totalLabors"
                @current-change="changePageLabor"
                v-model:currentPage="filterLabor.page"
            >
            </el-pagination>
            <div class="limit" :disabled="isLoading">
              <el-select
                  v-model="filterLabor.limit"
                  :placeholder="$t('Select')"
                  size="mini"
                  @change="onFilter"
              >
                <el-option
                    v-for="item in limits"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
