<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head mb-4">
      <div class="top-head-left">
        <h1>{{ $t('Country Of Origin') }}</h1>
      </div>
    </div>
    <div class="table-content">
      <div class="filter-top">
        <!--              create modal login-->
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input :placeholder="$t('Search country/code')" v-model="filter.keyword" @keyup.enter="onFilter"/>
          </el-col>
          <el-col :span="6">
            <el-select
                filterable
                class="w-full"
                v-model="filter.has_numeric_code"
                @change="onFilter"
            >
              <el-option
                  v-for="item in categoryOptions"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"

              >
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="6">
            <div class="btn-filter mt-1">
              <el-link type="danger" @click="resetFilter" :underline="false">
                {{ $t('Clear') }}
              </el-link>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-table border stripe size="small" :data="countries" :max-height="maxHeight" v-loading="isLoading" element-loading-text="Loading...">
        <el-table-column prop="name" :label="$t('Name')" min-width="100"></el-table-column>
        <el-table-column prop="iso2" :label="$t('IOS 2 code')" min-width="200"></el-table-column>
        <el-table-column :label="$t('Numeric code')" min-width="200">
          <template v-slot="scope">
            <div v-if="scope.row.code">
              <el-tag v-for="code in scope.row.code" :key="code" class="ml-2">{{ code }}</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="action" :label="$t('Action')" fixed="right" width="85">
          <template #default="scope">
            <el-link class="el-link-edit" :underline="false" type="primary" @click="updateSupply(scope.row)">
              <icon :data="iconEdit"/>
            </el-link>
          </template>
        </el-table-column>
      </el-table>

      <div class="bottom">
        <div class="total">{{ $t('Total:') }} {{ countries.length ? formatNumber(total) : 0 }}</div>
        <el-pagination :disabled="isLoading" background layout="prev, pager, next" :page-size="filter.limit"
                       :total="total" @current-change="changePage" v-model:currentPage="filter.page">
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select v-model="filter.limit" :placeholder="$t('Select')" size="mini" @change="onFilter">
            <el-option v-for="item in limits" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
  </div>
      <update-country
          @refresh="listCountries"
      />
</template>