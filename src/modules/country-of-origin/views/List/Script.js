import {countries} from '@/api/default.js';
import EventBus from "@/utilities/eventBus.js";
import UpdateCountry from "@/modules/country-of-origin/views/Update/Index.vue";

export default {
    name: "Country List",
    components: {
        UpdateCountry
    },
    data() {
        return {
            countries: [],
            isLoading: false,
            filter: this.setDefaultFilter(),
            categoryOptions: [
                {
                    name: "Without numeric code",
                    value: 0
                },
                {
                    name: "With numeric code",
                    value: 1
                },
            ]
        }
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 300);
        },
    },
    mounted() {
        this.listCountries();
    },
    created() {
        EventBus.$on("fetchCountry", () => {
            this.listCountries();
        });
    },
    methods: {
        setDefaultFilter() {
            return {
                limit: 25,
                page: 1,
                keyword: "",
                has_paginator: 1,
            };
        },
        async listCountries() {
            this.isLoading = true;
            this.setRouteParam();
            const res = await countries(this.filter);
            this.countries = res.data.data;
            if(this.countries.length){
                this.countries.forEach((item) => {
                    if (item.code !== null) {
                        item.code = item.code.split(",").map((code) => parseInt(code));
                    }
                });
            }
            this.total = res.data.total;
            this.isLoading = false;
        },
        changePage(page) {
            this.filter.page = page;
            this.listCountries();
        },
        onFilter() {
            this.filter.page = 1;
            this.listCountries();
        },
        updateSupply(item) {
            EventBus.$emit("showUpdateCountry", item);
        },
        resetFilter() {
            this.filter = this.setDefaultFilter()
            this.listCountries();
        },
    }
}