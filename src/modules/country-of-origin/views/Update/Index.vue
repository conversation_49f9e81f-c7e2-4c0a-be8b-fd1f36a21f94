<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <el-dialog v-model="dialogVisible"
             destroy-on-close
             :title="$t('Update Country')"
             custom-class="el-dialog-custom"
             @close="closeModal"
             :destroy-on-close="true">
    <el-form status-icon :model="data" @submit.prevent="onSubmit" :label-position="'top'">
      <el-form-item :label="$t('Name')">
        <el-input v-model="data.name" disabled></el-input>
      </el-form-item>
      <el-form-item :label="$t('IOS 2 code')">
        <el-input v-model="data.iso2" disabled></el-input>
      </el-form-item>
      <el-form-item :label="$t('Numeric Code')">
        <el-tag
            v-for="code in selectedCodes"
            :key="code"
            class="mx-1"
            closable
            :disable-transitions="false"
            @close="handleClose(code)"
        >
          {{ code }}
        </el-tag>
        <div>
          <el-input
              v-if="inputVisible"
              ref="InputRef"
              v-model="codes"
              class="ml-1 w-1/5"
              size="small"
              @keyup.enter="handleInputConfirm"
              @blur="closeInput"
          />
          <el-button v-else class="button-new-tag ml-1" size="small" @click="showInput">
            + New Code
          </el-button>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="onSubmit" :disabled="isLoading" :loading="isLoading">{{ $t('Update')}}
      </el-button>
    </template>
  </el-dialog>
</template>