import EventBus from "@/utilities/eventBus";
import {update, validateCode} from '@/api/default.js';

export default {
    name: "UpdateCountry",
    props: {
        categoryOptions: {type: Array, default: () => []},
        unitOptions: {type: Array, default: () => []},
    },
    data() {
        return {
            dialogVisible: false,
            data: this.setDefaultData(),
            serverErrors: [],
            isLoading: false,
            countryId: "",
            codes: '',
            selectedCodes: [],
            inputVisible : false
        }
    },
    mounted() {
    },
    created() {
        EventBus.$on("showUpdateCountry", (item) => {
            this.countryId = item.id;
            this.data = Object.assign(this.data, item);
            if(this.data.code){
                this.selectedCodes = this.data.code
            }
            this.serverErrors = [];
            this.dialogVisible = true;
        });
    },
    methods: {
        closeModal() {
            this.selectedCodes = [];
            this.code = '';
            EventBus.$emit("fetchCountry");
        },
        handleClose (code){
            const index = this.selectedCodes.indexOf(code);
            if (index !== -1) {
                this.selectedCodes.splice(index, 1);
            }
        },
        closeInput(){
            this.inputVisible = false;
        },
        async handleInputConfirm() {
            if (!isNaN(this.codes.trim())) {
                if (this.codes && !this.selectedCodes.includes(this.codes.trim())) {
                    try {
                        await validateCode({id: this.countryId, code: this.codes.trim()});
                        this.selectedCodes.push(this.codes.trim());
                    } catch (error) {
                        this.notification("This code is already exists.", "error");
                    } finally {
                        this.codes = '';
                        this.inputVisible = false;
                    }
                }else{
                    this.codes = '';
                    this.inputVisible = false;
                    this.notification("This code is already exists.", "error");
                }
            } else {
                this.notification("Please enter a numeric value!", "error");
                this.codes = '';
                this.inputVisible = false;
            }
        },
        showInput(){
            this.inputVisible = true
            this.$nextTick(() => {
                this.$refs.InputRef.focus();
            });
        },
        isError(field) {
            return !!this.serverErrors[field];
        },
        getErrorMessage(field) {
            return this.serverErrors[field][0];
        },
        setDefaultData() {
            return {
                category_id: "",
                unit_id: "",
                brand_id: "",
                name: "",
                sku: ""
            };
        },
        async onSubmit() {
            if (this.isLoading) return;
            console.log(this.selectedCodes, this.countryId)
            this.isLoading = true;
            try {
                const res = await update({id: this.countryId, code: this.selectedCodes});
                this.dialogVisible = false;
                this.notification("Update country successfully.");
                this.$emit("refresh");
            } catch (e) {
                this.serverErrors = e.response.data.errors;
                this.notification("Failed to update country", "error");
            } finally {
                this.isLoading = false;
            }
        },
    }
}
