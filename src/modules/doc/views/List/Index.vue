<style src="./Style.scss" lang="scss" scoped>

</style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ buildPageTitle }}</h1>
      </div>
      <div class="top-head-right">
        <el-popover v-if="!hasAccept" placement="bottom" :width="200" trigger="click" :visible="visible">
          <div class="mb-3 relative">
            <div class="close absolute right-0 cursor-pointer hover:!text-[#1a73e8]"
              :style="{ color: 'var(--el-color-info)' }" @click="visible = false;">
              <icon :data="iconClose" />
            </div>
            <div class="el-form-item__label mr-0">
              {{ $t('Enter Password') }}
            </div>
            <el-input ref="elInputPass" autocomplete="new-password" type="password" size="small" v-model="password"
              @keyup.enter="acceptChangeDoc">
            </el-input>
          </div>
          <template #reference>
            <el-button type="primary" @click="visible = !visible; $refs.elInputPass.focus()">{{
                $t('Manage Doc')
            }}</el-button>
          </template>
        </el-popover>
        <template v-else>
          <el-button type="primary" plain @click="showDocCategoryManager">
            <span class="icon-margin-right">
            </span>{{ $t("Submenu") }}
          </el-button>
          <el-button type="primary" @click="createDoc">
            <span class="icon-margin-right">
              <icon :data="iconAdd" />
            </span>{{ $t("Create") }}
          </el-button>
        </template>

      </div>
    </div>
    <div class="table-content" v-loading="isLoading">
      <el-tabs v-model="currentTab" class="el-tab-doc mb-0">
        <el-tab-pane v-for="lang in languages" :key="lang.key"
          :label="lang.name + ' (' + getCountItems(data?.[lang.key]) + ')'" :name="lang.key">
          <div class="filter !mt-1 !h-auto">
            <div class="filter-item">
              <el-input name="searchName" :placeholder="$t('Enter search name')" class="search" v-model="filter.name"
                @keyup.enter="onFilter()" clearable @clear="onClearFilter()" />
            </div>
            <div class="filter-item">
              <el-button type="primary" @click="onFilter">
                <span class="icon-margin-right">
                  <icon :data="iconFilter" />
                </span>{{ $t("Filter") }}
              </el-button>
            </div>
            <div class="filter-item" v-if="hasFilter">
              <el-button @click="onClearFilter">
                <span class="icon-margin-right">{{ $t("Reset") }}</span>
              </el-button>
            </div>
          </div>
          <div v-if="data?.[lang.key]?.items?.length" class="grid grid-cols-4 xl:grid-cols-6 2xl:grid-cols-8 gap-4">
            <el-tooltip v-for="item in data[lang.key].items" :key="item.id" effect="dark" :content="lastUpdate(item)"
              placement="top-start">
              <div @click="editDoc(item)"
                class="border rounded p-3 cursor-pointer relative flex flex-col items-center hover:border-[#1a73e8]">

                <div class="min-h-[68px] flex items-center">
                  <template v-if="hasAccept">
                    <el-button size="small" class="!w-8 !h-8 absolute right-3 top-3" @click.stop="viewLinkDoc(item)">
                      <icon :data="iconLink" class="!mr-0 !w-5 !h-5 !top-0 !max-h-5"></icon>
                    </el-button>
                    <el-popconfirm :title="'Are you sure to delete this ' + item.name + '?'"
                      @confirm.stop="deleteDoc(item)">
                      <template #reference>
                        <el-button size="small" @click.stop type="danger"
                          class="!w-8 !h-8 absolute right-3 top-12 ml-0">
                          <icon :data="iconDelete" class="!mr-0 !w-4 !h-4 !top-0 !max-h-4"></icon>
                        </el-button>
                      </template>
                    </el-popconfirm>
                  </template>

                  <icon class="!w-12 !h-12" :data="iconDocument"></icon>
                </div>
                <div class="text-center">
                  {{ item.name }}
                </div>
              </div>
            </el-tooltip>
          </div>
          <div v-else>
            {{ $t("No records.") }}
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <CreateDoc @refresh="fetchData" />
    <EditDoc @refresh="fetchData" />
    <DocCategoryManager @refresh="fetchData" />
  </div>
</template>
