<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
      v-model="openDialogBrand"
      :title="$t('Brand Management')"
      custom-class="el-dialog-custom el-dialog-brand"
      @close="closeBrandList"
      :close-on-click-modal="false"
    >
      <template #default>
        <div class="add-brand">
          <el-form
            status-icon
            ref="addBrandForm"
            :model="addData"
            :rules="brandRules"
            @submit.prevent="addBrand('addBrandForm')"
          >
            <el-form-item
              prop="name"
              @keyup.enter="addBrand('addBrandForm')"
              :class="{
                'is-error': addBrandError && addBrandError['name'],
              }"
            >
              <el-input v-model="addData.name"></el-input>
              <div
                v-if="addBrandError && addBrandError['name']"
                class="el-form-item__error"
              >
                {{ addBrandError["name"][0] }}
              </div>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="addBrand('addBrandForm')"
                :disabled="isLoadingAdd"
                :loading="isLoadingAdd"
                >{{$t('Add')}}</el-button
              >
            </el-form-item>
          </el-form>
        </div>

        <div class="list-brand">
          <h1 class="title">{{$t('List brand')}}</h1>
          <template v-if="getBrands && getBrands.length">
            <div class="item" v-for="(item, index) in getBrands" :key="index">
              <div class="item-input">
                <div v-if="item.is_edit">
                  <el-form
                    status-icon
                    @submit.prevent="updateBrand('addBrandForm')"
                  >
                    <el-form-item
                      :class="{
                        'is-error':
                          !item.name ||
                          (editBrandError && editBrandError['name']),
                      }"
                    >
                      <el-input
                        class="item-input-edit-brand"
                        v-model="item.name"
                        :id="'inputEdit' + item.id"
                      ></el-input>
                      <div v-if="!item.name" class="el-form-item__error">
                        {{$t('This field cannot be left blank.')}}
                      </div>
                      <div
                        v-if="editBrandError && editBrandError['name']"
                        class="el-form-item__error"
                      >
                        {{ editBrandError["name"][0] }}
                      </div>
                    </el-form-item>
                  </el-form>

                  <div class="item-input-group-btn">
                    <el-button
                      size="mini"
                      type="success"
                      @click="updateBrand(item)"
                      :disabled="isLoadingEdit['id'] === item.id"
                      :loading="isLoadingEdit['id'] === item.id"
                      circle
                      ><icon :data="iconCheck"
                    /></el-button>
                    <el-button
                      size="mini"
                      type="info"
                      @click="clearEditBrand(item)"
                      circle
                      ><icon :data="iconClose"
                    /></el-button>
                  </div>
                </div>
                <div v-else @click="editBrand(item)">
                  {{ item.name }}
                </div>
              </div>
              <div class="btn-group" v-if="!item.is_edit">
                <el-button size="mini" type="primary" @click="editBrand(item)"
                  ><icon :data="iconEdit"
                /></el-button>
                <el-popconfirm
                  :title="$t('Are you sure to delete this?')"
                  @confirm="deleteBrand(item)"
                >
                  <template #reference>
                    <el-button
                      size="mini"
                      type="danger"
                      :disabled="isLoadingDelete['id'] === item.id"
                      :loading="isLoadingDelete['id'] === item.id"
                      ><icon :data="iconDelete"
                    /></el-button>
                  </template>
                </el-popconfirm>
              </div>
            </div>
          </template>
          <template v-else>{{$t('No records.')}}  </template>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
