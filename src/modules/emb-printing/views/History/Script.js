import EventBus from "@/utilities/eventBus.js";
import { mapGetters } from "vuex";
import { add, destroy, update } from "@/api/brand.js";
export default {
  name: "Brand",
  data() {
    return {
      openDialogBrand: false,
      addData: {
        name: "",
      },
      brandRules: {
        name: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: "blur",
          },
        ],
      },
      isLoadingAdd: false,
      isLoadingEdit: {},
      isLoadingDelete: {},
      addBrandError: {},
      editBrandError: {},
    };
  },
  computed: {
    ...mapGetters(["getBrands"]),
  },
  watch: {},
  created() {
    EventBus.$on("showBrandManagement", () => {
      this.openDialogBrand = true;
    });
  },
  mounted() {
    this.fetchBrands();
  },
  methods: {
    fetchBrands() {
      this.$store.dispatch("getBrands");
    },
    resetAddData() {
      this.$refs.addBrandForm.resetFields();
      this.addData.name = "";
      this.addBrandError = {};
      this.editBrandError = {};
      this.$store.commit("resetDataBrands");
    },
    closeBrandList() {
      this.resetAddData();
    },
    async addBrand(formName) {
      const isValid = await this.$refs[formName].validate();
      if (!isValid) {
        return;
      }
      this.isLoadingAdd = true;
      try {
        const res = await add(this.addData);
        this.isLoadingAdd = false;
        const data = (res.data && res.data.data) || undefined;
        if (data) {
          this.notification(this.$t("Add brand successfully."));
          this.$store.commit("addBrand", data);
          this.resetAddData();
        }
      } catch (e) {
        this.isLoadingAdd = false;
        this.addBrandError = e.response.data;
        this.notification(this.$t("Add brand error."), "error");
      }
    },
    async deleteBrand(item) {
      this.isLoadingDelete = {
        id: item.id,
      };
      await destroy(item.id);
      this.isLoadingDelete = {};
      this.$store.commit("deleteBrand", item.id);
      this.notification(this.$t("Delete brand successfully."));
    },
    editBrand(item) {
      item.is_edit = true;
      this.$nextTick(() => {
        const refInput = "inputEdit" + item.id;
        document.getElementById(refInput).focus();
      });
    },
    async updateBrand(item) {
      if (!item.name) {
        return;
      }
      this.isLoadingEdit = {
        id: item.id,
      };
      try {
        await update(item);
        this.isLoadingEdit = {};
        this.notification(this.$t("Update brand successfully."));
        this.$store.commit("updateBrand", item);
        item.is_edit = false;
      } catch (e) {
        this.isLoadingEdit = {};
        this.editBrandError = e.response.data;
        this.notification(this.$t("Update brand error."), "error");
      }
    },
    clearEditBrand(item) {
      item.is_edit = false;
      item.name = item.nameCache;
    },
  },
};
