<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div class="border-b pb-4 flex flex-row items-center">
    <div class="mr-5 "><h3>{{ $t("Print EMB WIP") }}</h3></div>
<!--    <div >-->
<!--      <el-checkbox v-for="item in priorityStores" :key="item.id" @change="triggerCount(item.id)" class="!mr-2" v-model="isSelectStore" :label="item.code || storeNA" border >-->
<!--        {{ item.code || storeNA }}({{ item.total }})</el-checkbox>-->
<!--    </div>-->

    <div class="text-right	grow ">{{ $t("Total Pending: ") }}<span
        class="font-semibold	">{{ new Intl.NumberFormat().format(warehouse.total) }}</span></div>
  </div>
  <div class="flex flex-row">
    <div class="basis-1/5 border-r">

      <div v-if="styles.length == 0">
        <el-empty :description="$t('no data')"></el-empty>
      </div>
      <el-scrollbar style="height: calc(100vh - 150px);">
        <div v-for="item in styles" :key="item.name" @click="setStyle(item)"
             class="scrollbar-item bg-gradient-to-r hover:from-cyan-300  hover:to-blue-400 my-3 mr-3 p-3"
             :class="[
                {'border-4 border-blue-500/75': item.product_style_sku == filterPrint.style_sku},
                (item.totalPC == 0) ? 'from-gray-200 to-gray-500' : 'from-cyan-200 to-blue-300'
              ]"
             :style="(item.totalPC == 0) ? 'pointer-events: none' : ''">
          <div class="name">{{ item.name }}</div>
          <div class="sub">
            {{ $t("Pending") }} <strong>{{ item.total }}</strong><br> {{ $t("Last Printed: ") }}
            {{ item.printed_at ? utcToLocalTime(item.printed_at).format('lll') : 'not yet' }}
          </div>
        </div>
      </el-scrollbar>
    </div>
    <div class="basis-1/5 border-r">
      <div class="item-xqc m-3 p-3 bg-gradient-to-r from-rose-300 to-red-500 hover:from-rose-400	hover:to-red-600"
           @click="setXQC()"
           :class="{'border-4 border-red-500/75': filterPrint.is_xqc == 1}">
        <div class="name">{{ $t("XQC") }}</div>
        <div class="sub">
          {{ $t("Pending Label: ") }}<strong>{{ new Intl.NumberFormat().format(xqc.total) }}</strong><br>
          {{ $t("Last Printed: ") }}
          {{ xqc.printed_at ? utcToLocalTime(xqc.printed_at).format('lll') : 'not yet' }}
        </div>
      </div>

      <div class="item-xqc m-3 p-3 bg-gradient-to-r from-orange-400 to-orange-600 hover:from-orange-500	hover:to-orange-700"
           @click="setEps()"
           :class="{'border-4 border-orange-700/75': filterPrint.is_eps == 1}">
        <div class="name">{{ $t("EXPRESS") }}</div>
        <div class="sub">
          {{ $t("Pending Label: ") }}<strong>{{ new Intl.NumberFormat().format(eps.total) }}</strong><br>
          {{ $t("Last Printed: ") }}
          {{ eps.printed_at ? utcToLocalTime(eps.printed_at).format('lll') : 'not yet' }}
        </div>
      </div>


      <div v-if="account" @click="setAll()" :class="{'border-4 border-yellow-500/75': filterPrint.all == 1}"
           class="item-xqc m-3 p-3 bg-gradient-to-r from-amber-200 to-orange-400	 hover:from-amber-300	hover:to-orange-500 ">
        <div class="name">{{ $t('ALL') }}</div>
        <div class="sub" v-if="account != null && store != null">
          {{ $t("Pending Label: ") }}<strong>{{ new Intl.NumberFormat().format(store.total) }}</strong><br>
          {{ $t("Last Printed: ") }}
          {{ store.printed_at ? utcToLocalTime(store.printed_at).format('lll') : 'not yet' }}
        </div>
        <div class="sub" v-else-if="account !=null">
          {{ $t("Pending Label: ") }}<strong>{{ new Intl.NumberFormat().format(account.total) }}</strong><br>
          {{ $t("Last Printed: ") }}
          {{ account.printed_at ? utcToLocalTime(account.printed_at).format('lll') : 'not yet' }}
        </div>
      </div>

      <div
          class="item-xqc m-3 p-3 bg-gradient-to-r from-fuchsia-200 to-pink-400 hover:from-fuchsia-300	hover:to-pink-500"
          @click="setReprint()"
          :class="{'border-4 border-pink-500/75': filterPrint.is_reprint == 1}"
      >
        <div class="name">{{ $t('RE-PRINT') }}</div>
        <div class="sub">
          {{ $t("Pending Label: ") }}<strong>{{ new Intl.NumberFormat().format(reprint.total) }}</strong><br>
          {{ $t("Last Printed: ") }}
          {{ reprint.printed_at ? utcToLocalTime(reprint.printed_at).format('lll') : 'not yet' }}
        </div>
      </div>

      <div
          class="item-xqc m-3 p-3 bg-gradient-to-r from-yellow-200 to-yellow-400 hover:from-yellow-300	hover:to-yellow-500"
          @click="setManualProcess()"
          :class="{'border-4 border-yellow-500/75': filterPrint.is_manual == 1}"
      >
        <div class="name">{{ $t('MANUAL') }}</div>
        <div class="sub">
          {{$t("Pending Label: ")}}<strong>{{ new Intl.NumberFormat().format(manualProcess.total) }}</strong><br> {{$t("Last Printed: ")}}
          {{ manualProcess.printed_at ? utcToLocalTime(manualProcess.printed_at).format('lll') : 'not yet' }}
        </div>
      </div>

      <div
          class="item-xqc m-3 p-3 bg-gradient-to-r from-green-300 to-green-500 hover:from-green-400 hover:to-green-600"
          @click="setReroute()"
          :class="{'border-4 border-green-500/75': filterPrint.is_reroute == 1}"
      >
        <div class="name">{{ $t('REROUTE') }}</div>
        <div class="sub">
          {{$t("Pending Label: ")}}<strong>{{ new Intl.NumberFormat().format(reroute.total) }}</strong><br> {{$t("Last Printed: ")}}
          {{ reroute.printed_at ? utcToLocalTime(reroute.printed_at).format('lll') : 'not yet' }}
        </div>
      </div>
      <div
          class="item-xqc m-3 p-3 bg-gradient-to-r from-violet-200 to-violet-400 hover:from-violet-300 hover:to-violet-500"
          @click="setFba()"
          :class="{'border-4 border-violet-500/75': filterPrint.is_fba == 1}"
      >
        <div class="name">{{ $t('FBA') }}</div>
        <div class="sub">
          {{$t("Pending Label: ")}}<strong>{{ new Intl.NumberFormat().format(fba.total) }}</strong><br> {{$t("Last Printed: ")}}
          {{ fba.printed_at ? utcToLocalTime(fba.printed_at).format('lll') : 'not yet' }}
        </div>
      </div>

    </div>
    <div class="basis-3/4">

      <div class="p-3">
        <el-input-number class="mr-3" style="width: 150px" v-model="filterPrint.limit" :min="0" :max="maxAllowPrint"/>
        <el-button @click="confirmPrintBtn" type="primary">{{ $t("Confirm") }}</el-button>
      </div>

      <el-dialog
          v-model="dialogVisible"
          :title="$t('Assign To Staff')"
          width="30%"


      >
        <div>
          <el-input
              class="m-0"
              v-model="filterPrint.employee_number"
              size="large"
              :placeholder="$t('Enter The Staff Number')"
          ></el-input>
        </div>

        <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t("Cancel") }}</el-button>
        <el-button type="primary" :disabled="clickConfirm" @click="confirmStaff()"
        >{{ $t("Confirm") }}</el-button
        >
      </span>
        </template>
      </el-dialog>


      <el-tabs v-model="activeTab" class="ml-3">
        <el-tab-pane :label="$t('Printing')" name="printing">
          <el-table stripe :data="pendingList.data" style="width: 100%">

            <el-table-column prop="id" :label="$t('ID')" width="120"/>
            <el-table-column :label="$t('Created at')" width="180">
              <template #default="scope">
                {{ utcToLocalTime(scope.row.created_at).format('lll') }}
              </template>
            </el-table-column>
            <el-table-column :label="$t('Filter')">
              <template #default="scope">
                <el-tag v-if="scope.row.is_xqc == 1" class="mr-2 mb-1" size="small" type="danger">
                  {{ $t("Xqc") }}
                </el-tag>

                <el-tag v-if="scope.row.is_eps == 1" class="mr-2 mb-1" size="small" type="danger">
                  {{ $t("Express") }}
                </el-tag>

                <el-tag v-if="+scope.row.is_reprint == 1" class="mr-2 mb-1" size="small" type="danger">
                  {{ $t("Reprint") }}
                </el-tag>

                <el-tag v-if="scope.row.is_manual == 1" class="mr-2 mb-1" size="small" type="warning">
                  {{$t("Manual")}}
                </el-tag>

                <el-tag v-if="scope.row.is_reroute == 1" class="mr-2 mb-1" size="small" type="warning">
                  {{$t("Reroute")}}
                </el-tag>

                <el-tag v-if="scope.row.is_fba == 1" class="mr-2 mb-1" size="small" type="warning">
                  {{$t("FBA")}}
                </el-tag>

                <el-tag v-if="scope.row.account_name" class="mr-2 mb-1" size="small" type="success">
                  {{ scope.row.account_name }}
                </el-tag>

                <el-tag v-if="scope.row.store_name" class="mr-2 mb-1" size="small" type="warning">
                  {{ scope.row.store_code || storeNA }}
                </el-tag>

                <el-tag v-if="scope.row.style_name" size="small mb-2 mr-2" type="primary">
                  {{ scope.row.style_name }}
                </el-tag>

                <el-tag v-if="scope.row.color_name" size="small mb-2" type="success">
                  {{ scope.row.color_name }}
                </el-tag>

              </template>
            </el-table-column>

            <el-table-column :label="$t('Staff')" min-width="100">
              <template #default="scope">
                <span class="break-normal">
                  {{ scope.row.employee_name }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="quantity" :label="$t('Quantity')" width="100"/>

            <el-table-column :label="$t('Status')" width="160">
              <template #default="scope">
                <el-tag v-if="+scope.row.convert_status == 1" type="success">{{ $t("Completed") }}</el-tag>
                <template v-else>
                  <template v-if="scope.row.convert_percent > 0">
                    <el-progress :percentage="scope.row.percentage"/>
                  </template>
                  <template v-else>
                    <el-tag v-if="+scope.row.convert_status == 2" type="danger">{{ $t("Error") }}</el-tag>
                    <el-tag v-else type="warning">{{ $t("Pending") }}</el-tag>
                  </template>

                </template>


              </template>
            </el-table-column>
            <el-table-column width="100" :label="$t('Action')">
              <template #default="scope">
                <template v-if="scope.row.print_status == 0">
                  <el-button size="small" :disabled="scope.row.convert_status !== 1 || scope.row.color_sku != null"
                             @click="printPDF(scope.row)"
                             type="primary">{{ $t("Print") }}
                  </el-button>
                </template>
                <template v-if="scope.row.print_status == 1">
                  <el-button size="small" :disabled="scope.row.convert_status !== 1 || scope.row.color_sku != null"
                             @click="printPDF(scope.row)"
                             type="info">{{ $t("Printed") }}
                  </el-button>
                </template>
              </template>
            </el-table-column>

          </el-table>
        </el-tab-pane>
        <el-tab-pane :label="$t('History')" name="history">
          <el-table stripe :data="printedList.data" style="width: 100%">

            <el-table-column prop="id" :label="$t('ID')" width="120"/>
            <el-table-column :label="$t('Created at')" width="180">
              <template #default="scope">
                {{ utcToLocalTime(scope.row.created_at).format('lll') }}
              </template>
            </el-table-column>
            <el-table-column :label="$t('Filter')">
              <template #default="scope">
                <el-tag v-if="scope.row.is_xqc == 1" class="mr-2 mb-1" size="small" type="danger">
                  {{ $t("Xqc") }}
                </el-tag>

                <el-tag v-if="scope.row.is_eps == 1" class="mr-2 mb-1" size="small" type="danger">
                  {{ $t("Express") }}
                </el-tag>

                <el-tag v-if="+scope.row.is_reprint == 1" class="mr-2 mb-1" size="small" type="danger">
                  {{ $t("Reprint") }}
                </el-tag>

                <el-tag v-if="scope.row.is_manual == 1" class="mr-2 mb-1" size="small" type="warning">
                  {{$t("Manual")}}
                </el-tag>

                <el-tag v-if="scope.row.is_reroute == 1" class="mr-2 mb-1" size="small" type="warning">
                  {{$t("Reroute")}}
                </el-tag>

                <el-tag v-if="scope.row.is_fba == 1" class="mr-2 mb-1" size="small" type="warning">
                  {{$t("FBA")}}
                </el-tag>
                <el-tag v-if="scope.row.account_name" class="mr-2 mb-1" size="small" type="success">
                  {{ scope.row.account_name }}
                </el-tag>

                <el-tag v-if="scope.row.store_name" class="mr-2 mb-1" size="small" type="warning">
                  {{ scope.row.store_code || storeNA }}
                </el-tag>

                <el-tag v-if="scope.row.style_name" size="small mb-2 mr-2" type="primary">
                  {{ scope.row.style_name }}
                </el-tag>

                <el-tag v-if="scope.row.color_name" size="small mb-2" type="success">
                  {{ scope.row.color_name }}
                </el-tag>

              </template>
            </el-table-column>

            <el-table-column prop="employee_name" :label="$t('Staff')"/>
            <el-table-column prop="quantity" :label="$t('Quantity')" width="100"/>

            <el-table-column :label="$t('Status')" width="160">
              <template #default="scope">
                <el-tag v-if="+scope.row.convert_status == 1" type="success">{{ $t("Completed") }}</el-tag>
                <template v-else>
                  <template v-if="scope.row.convert_percent > 0">
                    <el-progress :percentage="scope.row.percentage"/>
                  </template>
                  <template v-else>
                    <el-tag v-if="+scope.row.convert_status == 2" type="danger">{{ $t("Error") }}</el-tag>
                    <el-tag v-else type="warning">{{ $t("Pending") }}</el-tag>
                  </template>

                </template>


              </template>
            </el-table-column>
            <el-table-column width="100" :label="$t('Action')">
              <template #default="scope">
                <template v-if="scope.row.print_status == 0">
                  <el-button size="small" :disabled="scope.row.convert_status !== 1 || scope.row.color_sku != null"
                             @click="printPDF(scope.row)"
                             type="primary">{{ $t("Print") }}
                  </el-button>


                </template>
                <template v-if="scope.row.print_status == 1">
                  <el-button size="small" :disabled="scope.row.convert_status !== 1"
                             @click="rePrintedBtn(scope.row)"
                             type="info">{{ $t("Reprint") }}
                  </el-button>
                </template>
              </template>
            </el-table-column>

          </el-table>


          <div class="mt-3 flex items-center">
            <div class="flex">
              <el-input v-model="filterBarcodePrinted.keyword" :placeholder="$t('Search SKU')">
                <template #append>
                  <el-button @click="searchBarcodePrinted">{{ $t("Search") }}</el-button>
                </template>
              </el-input>

            </div>
            <div class="ml-5">
              <el-pagination
                  background
                  :page-size="filterBarcodePrinted.limit"
                  :pager-count="10"
                  layout="prev, pager, next"
                  :total="printedList.total"
                  @current-change="changePage"
              >
              </el-pagination>
            </div>
          </div>
        </el-tab-pane>

      </el-tabs>

      <el-dialog
          v-model="dialogVisiblePrint"
          @close="closeModalPrint"
          :title="$t('Print WIP')"
          width="30%"
          top="5vh"
      >

        <iframe class="m-0 p-0" :src="pdf + '#toolbar=0'" style="height: 450px; width: 100%"></iframe>
        <div class="mt-3">
          <el-button v-if="!(itemSelected.printed_at || itemSelected.print_status == 1)" type="primary" @click="printWIP()">{{ $t("Print") }}
          </el-button>
          <div :disabled="isLoading" v-if="(itemSelected.printed_at || itemSelected.print_status == 1)">
            <h2 class="mt-2">{{ $t("Enter the password to unlock reprint") }}</h2>
            <el-input :placeholder="$t('Password')" class="mt-2" v-model="password"></el-input>
            <el-button :disabled="password != passwordReprint || isLoading" class="mt-2" type="primary" @click="reprintWIP()">{{ $t("Reprint") }}
            </el-button>
          </div>
        </div><h2 class="mt-2">{{ $t("Confirm Printed") }}</h2>
        <el-input :placeholder="$t('Scan first label / Quét mã đầu tiên')" class="mt-2"
                  v-model="skuPrinted.start"></el-input>
        <el-input :placeholder="$t('Scan end label / Quét mã sau cùng')" class="mt-2"
                  v-model="skuPrinted.end"></el-input>
        <div class="mt-3">
          <el-button :disabled="!itemSelected.printed_at" type="primary" @click="confirmPrintedBtn()">{{ $t("Confirm") }}
          </el-button>
        </div>
      </el-dialog>

      <el-dialog
          v-model="dialogVisibleRePrint"
          :title="$t('RePrint WIP')"
          width="30%"
          top="5vh"
          :before-close="handleCloseReprint"
      >
        <div v-if="lockRePrintStatus" class="bg-slate-200" style="height: 350px"></div>
        <iframe v-else class="m-0 p-0" :src="pdf" style="height: 450px; width: 100%"></iframe>
        <h2 class="mt-2">{{ $t("Enter the password to unlock print") }}</h2>
        <el-input :placeholder="$t('Password')" class="mt-2"
                  v-model="password"></el-input>
        <div class="mt-3">
          <el-button type="primary" @click="unLockReprint()">{{ $t("Unlock") }}
          </el-button>
        </div>
      </el-dialog>

    </div>
  </div>
</template>
