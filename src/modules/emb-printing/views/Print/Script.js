import {countBarcode, printBarcode, confirmPrinted, fetchBarcode, countPendingPriorityStore} from "@/api/barcodeEMB.js";
import {getByCode} from "@/api/employee.js";
import {ElMessageBox, ElMessage} from 'element-plus'
import moment from "moment-timezone";
import {STORAGE_URL, S3_URL, NOT_APPLICABLE} from "@/utilities/constants";
import date from "@/mixins/date";
import warehouseMixin from '@/mixins/warehouse';
import {printBarcodeWIP, reprintBarcodeWIP} from "@/api/barcode";

export default {
    name: "Barcode",
    mixins: [date, warehouseMixin],
    data() {
        return {
            styles: [],
            stores: [],
            accounts: [],
            priorityStores: [],
            employees: {},
            warehouse: {},
            xqc: 0,
            eps: {},
            maxAllowPrint: 0,
            reprint: {},
            manualProcess: {},
            reroute: {},
            fba: {},
            insert: {},
            barcode: {},
            filter: {
                account_id: null,
                store_id: null,

            },
            filterPrint: {
                style_sku: null,
                is_xqc: null,
                all: 0,
                employee_id: null,
                limit: 0,
                employee_number: null,
                is_reprint: null,
                is_manual: null,
                is_reroute: null,
                is_fba: null,
                is_eps: null,
            },

            filterBarcodePrinted: {
                limit: 11,
                page: 0,
                keyword: ''
            },
            printedList: {},
            pendingList: {},
            isLoading: false,
            dialogVisible: false,
            preview_id: 0,

            activeTab: 'printing',
            pdf: '',
            dialogVisiblePrint: false,
            dialogVisibleRePrint: false,
            password: null,

            skuPrinted: {
                id: null,
                start: null,
                end: null
            },
            refreshCount: null,
            refreshPrinting: null,
            refreshPrinted: null,
            lockRePrintStatus: true,
            passwordReprint: '',
            clickConfirm: false,
            onThisPage: true,
            maxItems: 3000,
            storeNA: NOT_APPLICABLE,
            warehouse_id: null,
            itemSelected: {},
        };
    },
    mounted() {
        //  this.updateCount();
        //  this.updatePrintedList();
        //  this.updatePrintingList();
        // this.countPendingPriorityStore();
        let today = new Date();
        let dd = String(today.getDate()).padStart(2, '0');
        this.passwordReprint = dd;
        this.countBarcode();
        this.fetchPrintedList();
        this.fetchPendingList();
        this.warehouse_id = this.getWarehouseId();
    },

    computed: {
        account: function () {
            if (this.filter.account_id) {
                console.log('account', this.filter.account_id)
                const res = this.accounts.filter(i => i.id == this.filter.account_id)[0]
                return res
            }
            return null
        },
        store: function () {
            if (this.filter.store_id) {
                const res = this.stores.filter(i => i.id == this.filter.store_id)[0]
                return res
            }
            return null
        }
    },

    watch: {
        dialogVisiblePrint: {
            handler: function (value) {
                if (!value) {
                    const iframes = document.querySelectorAll('.pdf-iframe');
                    iframes.forEach((iframe) => {
                        URL.revokeObjectURL(iframe.src);
                        document.body.removeChild(iframe);
                    });
                }
            },
            deep: true,
        },
        'filter.account_id': {
            async handler() {
                this.resetFilterPrint();
                this.countBarcode();
                this.filter.store_id = null;

            }
        },
        'filter.store_id': {
            async handler() {
                this.resetFilterPrint();
                this.countBarcode();

            }
        },
        activeTab: {
            async handler() {
                this.filterBarcodePrinted.keyword = '';
            }
        }
    },

    methods: {
        async printWIP() {
            try {
                if (!this.itemSelected?.id) {
                    return;
                }
                this.isLoading = true;
                await printBarcodeWIP(this.itemSelected.id);
                await this.printPdfFromBlob(this.pdf);
                this.itemSelected.printed_at = moment().format('YYYY-MM-DD hh:mm:ss');
            } catch (e) {
                this.notification(e?.response?.data?.message ?? 'This WIP has been printed.', "error");

            } finally {
                this.isLoading = false;
            }
        },
        async reprintWIP() {
            try {
                if (!this.itemSelected?.id) {
                    return;
                }
                this.isLoading = true;
                await reprintBarcodeWIP(this.itemSelected.id);
                await this.printPdfFromBlob(this.pdf);
                this.itemSelected.printed_at = moment().format('YYYY-MM-DD hh:mm:ss');
            } catch (e) {
                this.notification(e?.response?.data?.message ?? 'Error', "error");

            } finally {
                this.isLoading = false;
            }
        },
        async searchBarcodePrinted() {
            this.filterBarcodePrinted.page = 1
            console.log('search')
            await this.fetchPrintedList()
        },
        changePage(page) {
            this.filterBarcodePrinted.page = page;
            console.log('change page', page)
            this.$nextTick(() => {
                this.fetchPrintedList();
            });
        },


        printPDF(item) {
            if (item.color_sku != null) {
                return false;
            }
            this.dialogVisiblePrint = true;
            this.skuPrinted.id = item.id
            this.itemSelected = item
            // this.pdf = `${S3_URL}/barcode/${item.id}.pdf?v=` + Math.floor(Math.random() * 1000);
            this.pdf = item.wip_url;
        },
        setAll() {
            if (this.store != null && this.account != null) {
                this.filterPrint.limit = this.store.total
                this.maxAllowPrint = this.store.total
            } else {
                this.filterPrint.limit = this.account.total
                this.maxAllowPrint = this.account.total
            }

            this.filterPrint.style_sku = null;
            this.filterPrint.is_xqc = null;
            this.filterPrint.is_reprint = null;
            this.filterPrint.is_manual = null;
            this.filterPrint.is_reroute = null;
            this.filterPrint.is_fba = null;
            this.filterPrint.all = 1;
            this.filterPrint.is_eps = null;
        },
        setXQC() {
            this.filterPrint.limit = this.xqc.total;
            this.maxAllowPrint = this.xqc.total > this.maxItems ? this.maxItems : this.xqc.total;
            this.filterPrint.style_sku = null;
            this.filterPrint.is_xqc = 1;
            this.filterPrint.is_reprint = null;
            this.filterPrint.is_manual = null;
            this.filterPrint.is_reroute = null;
            this.filterPrint.is_fba = null;
            this.filterPrint.all = 0;
            this.filterPrint.is_eps = null;
        },
        setEps() {
            this.filterPrint.limit = this.eps.total;
            this.maxAllowPrint = this.eps.total > this.maxItems ? this.maxItems : this.eps.total;
            this.filterPrint.style_sku = null;
            this.filterPrint.is_xqc = null;
            this.filterPrint.is_reprint = null;
            this.filterPrint.is_manual = null;
            this.filterPrint.is_reroute = null;
            this.filterPrint.is_fba = null;
            this.filterPrint.all = 0;
            this.filterPrint.is_eps = 1;
        },
        setReprint() {
            this.filterPrint.limit = this.reprint.total;
            this.maxAllowPrint = this.reprint.total > this.maxItems ? this.maxItems : this.reprint.total;
            this.filterPrint.style_sku = null;
            this.filterPrint.is_xqc = null;
            this.filterPrint.is_reprint = 1;
            this.filterPrint.is_manual = null;
            this.filterPrint.is_reroute = null;
            this.filterPrint.is_fba = null;
            this.filterPrint.all = 0;
            this.filterPrint.is_eps = null;
        },
        setManualProcess() {
            this.filterPrint.limit = this.manualProcess.total;
            this.maxAllowPrint = this.manualProcess.total > this.maxItems ? this.maxItems : this.manualProcess.total;
            this.filterPrint.style_sku = null;
            this.filterPrint.is_xqc = null;
            this.filterPrint.is_reprint = null;
            this.filterPrint.is_manual = 1;
            this.filterPrint.is_reroute = null;
            this.filterPrint.is_fba = null;
            this.filterPrint.all = 0;
            this.filterPrint.is_eps = null;
        },
        setStyle(item) {
            this.filterPrint.style_sku = item.product_style_sku;
            this.filterPrint.limit = item.total;
            this.maxAllowPrint = item.totalPC > this.maxItems ? this.maxItems : item.totalPC;
            this.filterPrint.is_xqc = null;
            this.filterPrint.is_reprint = null;
            this.filterPrint.is_manual = null;
            this.filterPrint.is_reroute = null;
            this.filterPrint.is_fba = null;
            this.filterPrint.all = 0;
            this.filterPrint.is_eps = null;
        },
        setReroute() {
            this.filterPrint.limit = this.reroute.total;
            this.maxAllowPrint = this.reroute.total > this.maxItems ? this.maxItems : this.reroute.total;
            this.filterPrint.style_sku = null;
            this.filterPrint.is_xqc = null;
            this.filterPrint.is_reprint = null;
            this.filterPrint.is_manual = null;
            this.filterPrint.all = 0;
            this.filterPrint.is_reroute = 1;
            this.filterPrint.is_fba = null;
            this.filterPrint.is_eps = null;
        },
        setFba() {
            this.filterPrint.limit = this.fba.total;
            this.maxAllowPrint = this.fba.total > this.maxItems ? this.maxItems : this.fba.total;
            this.filterPrint.style_sku = null;
            this.filterPrint.is_xqc = null;
            this.filterPrint.is_reprint = null;
            this.filterPrint.is_manual = null;
            this.filterPrint.all = 0;
            this.filterPrint.is_reroute = null;
            this.filterPrint.is_fba = 1;
            this.filterPrint.is_eps = null;
        },
        resetFilterPrint() {
            this.filterPrint.style_sku = null;
            this.filterPrint.is_xqc = null;
            this.filterPrint.is_reprint = null;
            this.filterPrint.is_manual = null;
            this.filterPrint.is_reroute = null;
            this.filterPrint.is_fba = null;
            this.filterPrint.all = 0;
            this.filterPrint.is_eps = null;
        },

        async triggerCount(store_id) {
            if (this.isSelectStore) {
                this.filter.store_id = store_id;
            } else {
                this.filter.store_id = null;
            }
        },
        async countPendingPriorityStore() {
            const res = await countPendingPriorityStore()
            this.priorityStores = res.data || [];
        },
        async countBarcode() {
            console.log('call count bar code')
            const res = await countBarcode(this.filter);
            const data = res.data || [];
            this.styles = data.styles;
            // this.stores = data.stores;
            // this.priorityStores = data.priority_stores;
            this.xqc = data.xqc;
            this.eps = data.eps;
            this.reprint = data.reprint;
            this.manualProcess = data.manual;
            this.warehouse = data.warehouse;
            this.reroute = data.reroute;
            this.fba = data.fba;
            this.insert = data.insert;
            clearTimeout(this.refreshCount);
            this.refreshCount = setTimeout(() => {
                if (this.onThisPage) {
                    this.countBarcode()
                    // this.countPendingPriorityStore();
                }
            }, 60 * 1000)
        },

        async fetchPrintedList() {
            const filter = {...this.filterBarcodePrinted, ...{print_status: 1}}

            fetchBarcode(filter).then(res => {
                let data = res.data
                data.data.forEach(item => {
                    item.percentage = Math.round((item.convert_percent / item.quantity) * 100, 0);
                });
                this.printedList = data;
            })
        },

        async fetchPendingList() {
            const filter = {...this.filterBarcodePrinted, ...{pending: 1}}
            filter.page = 0;
            const res = await fetchBarcode(filter)
            const data = res.data
            data.data.forEach(item => {
                item.percentage = Math.round((item.convert_percent / item.quantity) * 100, 0);
            });
            this.pendingList = data;
            clearTimeout(this.refreshPrinting);
            let x = setTimeout(() => {
                if (this.onThisPage) {
                    this.fetchPendingList()
                }
            }, 5 * 1000)
            console.log('time',x);
            this.refreshPrinting = x;

        },

        async confirmStaff() {
            this.clickConfirm = true;


            if (!this.filterPrint.employee_number) {
                ElMessage({
                    type: 'warning',
                    message: this.$t('Please enter the staff number to confirm the printed label'),
                })
                this.clickConfirm = false;
                return false
            }

            const res = await getByCode(this.filterPrint.employee_number)

            if (!res.data.data) {
                ElMessage({
                    type: 'error',
                    message: this.$t('The staff number does not exist'),
                })
                this.clickConfirm = false;
                return false
            }

            const params = {...this.filter, ...this.filterPrint};

            params.employee_id = res.data.data.id

            printBarcode(params).then(res => {
                this.dialogVisible = false
                this.fetchPendingList();
                this.fetchPrintedList();
                this.resetFilterPrint();
                this.countBarcode();
                setTimeout(() =>  this.clickConfirm = false, 1000);
            })
        },



        confirmPrintBtn() {
            console.log(this.filterPrint)
            if (this.filterPrint.style_sku == null && this.filterPrint.is_xqc == null && this.filterPrint.all == 0 && this.filterPrint.is_reprint == null && this.filterPrint.is_manual == null && this.filterPrint.is_reroute == null && this.filterPrint.is_fba == null && this.filterPrint.is_eps == null) {
                ElMessage({
                    type: 'warning',
                    message: this.$t('Please select the option to print'),
                })
                return false
            }
            if (this.filterPrint.limit == 0) {
                ElMessage({
                    type: 'warning',
                    message: this.$t('Limit must be greater than zero'),
                })
                return false
            }

            this.dialogVisible = true;
        },

        confirmPrintedBtn() {
            if (this.skuPrinted.start == '' || this.skuPrinted.end == '') {
                ElMessage({
                    type: 'warning',
                    message: this.$t('Please scan the first SKU and the last SKU'),
                })
                return false
            }

            confirmPrinted(this.skuPrinted.id, this.skuPrinted).then((res) => {
                if (res.data.status == true) {
                    ElMessage({
                        type: 'success',
                        message: this.$t('Successful print confirmation'),
                    })
                    this.fetchPendingList()
                    this.fetchPrintedList()
                    this.dialogVisiblePrint = false
                    this.skuPrinted = {
                        id: null,
                        start: null,
                        end: null
                    }
                } else {
                    ElMessage({
                        type: 'error ',
                        message: this.$t('The first or the last SKU is incorrect'),
                    })
                }
            })
        },

        rePrintedBtn(item) {
            let today = new Date();
            let dd = String(today.getDate()).padStart(2, '0');
            this.dialogVisibleRePrint = true;
            this.passwordReprint = dd;
            // this.pdf = `${S3_URL}/barcode/${item.id}.pdf?v=` + Math.floor(Math.random() * 1000);
            this.pdf = item.wip_url;
        },
        unLockReprint() {
            if (this.passwordReprint != this.password) {
                ElMessage({
                    type: 'error',
                    message: this.$t('The password is incorrect'),
                })
            } else {
                this.lockRePrintStatus = false;
            }
        },
        handleCloseReprint() {
            this.lockRePrintStatus = true
            this.dialogVisibleRePrint = false
            this.password = null;
        },
        closeModalPrint() {
            this.password = null;
            this.dialogVisiblePrint = false;
            this.skuPrinted = {
                id: null,
                start: null,
                end: null
            }
        },

        moment(date) {
            return moment(date)
        },


    },

    beforeUnmount() {
        console.log('clear all refresh')
        clearTimeout(this.refreshCount);
        clearTimeout(this.refreshPrinting);
        //  clearTimeout(this.refreshPrinted);
    },
    unmounted() {
        console.log('unmounted')
        clearTimeout(this.refreshCount);
        clearTimeout(this.refreshPrinting);
    },
    beforeRouteLeave(to, from, next) {
        this.onThisPage = false;
        next();
    },
};
