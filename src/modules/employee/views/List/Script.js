import { mapGetters } from "vuex";
import { equals } from "ramda";

export default {
  name: "Employee",
  components: {
  },
  data() {
    return {
      items: [],
      isLoading: false,
      filter: this.setDefaultFilter(),
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 188);
    },
    ...mapGetters(["getEmployees"]),
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      return filter;
    },
    setRouteParam() {
      const params = this.filter;
      this.$router.replace({
        name: "employee",
        query: params,
      });
    },
    onFilter() {
      this.fetchEmployee();
    },
    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.$nextTick(() => {
        this.fetchEmployee();
      });
    },
    setDefaultFilter() {
      return {
        name: "",
      };
    },
    async fetchData() {
      this.filter = this.getRouteParam();
      this.fetchEmployee();
    },
    async fetchEmployee() {
      this.isLoading = true;
      this.setRouteParam();
      await this.$store.dispatch("getEmployees", this.filter);
      this.isLoading = false;
    },
  },
};
