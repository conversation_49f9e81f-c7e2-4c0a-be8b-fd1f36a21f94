<template>
    <div>
<!--        <div class="flex mb-3 items-center">-->
<!--            <div class="mr-3">-->
<!--                <el-input :placeholder="$t('Search Tracking Number')" v-model="filter.tracking_number"-->
<!--                    @keydown.enter="onFilter" clearable @clear="onFilter" />-->
<!--            </div>-->
<!--            <div class="mr-3">-->
<!--                <el-date-picker class="!w-64" :disabled-date="disabledDate" format="YYYY-MM-DD" v-model="date"-->
<!--                    type="daterange" range-separator="To" :start-placeholder="$t('Start date')"-->
<!--                    :end-placeholder="$t('End date')" @change="onChangeDate">-->
<!--                </el-date-picker>-->
<!--            </div>-->
<!--            <div class="mr-3">-->
<!--                <el-select v-model="filter.staff_id" @change="onFilter" filterable :placeholder="$t('Staff')"-->
<!--                    clearable @clear="onFilter">-->
<!--                    <el-option v-for="item in employees" :key="item.id" :label="item.name" :value="item.id" />-->
<!--                </el-select>-->
<!--            </div>-->
<!--            <div v-if="hasFilter">-->
<!--                <el-link type="danger" @click="onClearFilter" :underline="false">-->
<!--                    {{ $t("Clear") }}-->
<!--                </el-link>-->
<!--            </div>-->
<!--        </div>-->
        <div class="table-content">
            <el-table stripe border size="small" :data="items" :max-height="maxHeight" v-loading="isLoading"
                element-loading-text="Loading...">
              <el-table-column prop="box_id" :label="$t('Number')" :resizable="false">
                <template #default="scope">
                  {{ scope.row.number_report }}
                </template>
              </el-table-column>
              <el-table-column prop="item" :label="$t('Quantity')" >
                <template #default="scope">
                  {{ scope.row.shipment_exportation_trackings_count }}
                </template>
              </el-table-column>
              <el-table-column prop="created_at" :label="$t('Created at')" >
                <template #default="scope">
                  {{ formatDate(scope.row?.created_at, false)}}
                </template>
              </el-table-column>
              <el-table-column prop="staff" :label="$t('Staff')" >
                <template #default="scope">
                  {{ scope.row?.employee?.name }}
                </template>
              </el-table-column>
              <el-table-column prop="action" :label="$t('Action')" width="97">
                <template #default="scope">
                  <template v-if="scope.row.status === 'download'">
                    <el-button class="w-20" :disabled="isLoading"
                               @click="downloadExportation(scope.row)" size="small" type="success">Download</el-button>
                  </template>
                </template>
              </el-table-column>
            </el-table>
            <div class="bottom">
                <div class="total">{{ $t('Total:') }} {{ items.length ? formatNumber(total) : 0 }}</div>
                <el-pagination :disabled="isLoading" background layout="prev, pager, next" :page-size="filter.limit"
                    :total="total" @current-change="changePage" v-model:currentPage="filter.page">
                </el-pagination>
                <div class="limit" :disabled="isLoading">
                    <el-select class="w-36" v-model="filter.limit" :placeholder="$t('Select')" size="mini"
                        @change="onFilter">
                        <el-option v-for="item in limits" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </div>
            </div>
        </div>
    </div>
</template>
  
<script>
import EventBus from '@/utilities/eventBus.js';
import { mapGetters } from 'vuex';
import { equals } from "ramda";
import {fetchExportationHistory} from "@/api/exportation.js";
import {API_URL} from "@/utilities/constants";

export default {
    name: 'History',
    props: {
        employee: {
            type: Object
        }
    },
    mixins: [],
    data() {
        return {
            items: [],
            isLoading: false,
            filter: this.setDefaultFilter(),
            date: '',
            staffList: [],
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            exportation_id_export: '',
        };
    },
    computed: {
        maxHeight() {
          return this.employee?.id > 0 ? parseInt(window.innerHeight - 420) : parseInt(window.innerHeight - 284)
        },
        hasFilter() {
            const defaultFilter = this.setDefaultFilter();
            return !equals(defaultFilter, this.filter);
        },
        ...mapGetters({
            employees: "getEmployees"
        }),
        buildLinkDownloadExportation() {
          let link = `${API_URL}/shipment-exportation/download`
          let params = {
            exportation_id: this.exportation_id_export,
          };
          params = new URLSearchParams(params);
          return `${link}?${params.toString()}`;
        }
    },
    mounted() {
        this.fetchData();
        this.fetchEmployee();
    },
    created() {
        EventBus.$on("onRefreshHistoryExportation", () => {
            this.fetchData();
        });
    },
    methods: {
        async fetchEmployee() {
            await this.$store.dispatch("getEmployees");
        },
        onClearFilter() {
            this.filter = this.setDefaultFilter();
            this.date = "";
            this.$nextTick(() => {
                this.fetchData();
            });
        },
        onChangeDate() {
            if (this.date && this.date.length) {
                this.filter.start_date = this.formatDate(this.date[0], false);
                this.filter.end_date = this.formatDate(this.date[1], false);
            } else {
                this.filter.start_date = "";
                this.filter.end_date = "";
            }
            this.onFilter();
        },
        setDefaultFilter() {
            return {
                limit: 25,
                page: 1,
                start_date: '',
                end_date: '',
                staff_id: '',
                tracking_number: ''
            };
        },
        changePage(page) {
            this.filter.page = page;
            this.$nextTick(() => {
                this.fetchData();
            });
        },
        onFilter() {
            this.filter.page = 1;
            this.$nextTick(() => {
                this.fetchData();
            });
        },
        async fetchData() {
            this.isLoading = true;
            let params = Object.assign({}, this.filter);
            params.timezone = this.timezone;
            const res = await fetchExportationHistory(params);
            const data = res?.data || {}
            this.items = data?.data || [];
            this.total = data?.total || 0;
            this.isLoading = false;
        },

      async downloadExportation(row) {
          this.exportation_id_export = row.id;
          window.location.href = await this.buildLinkDownloadExportation;
        },
    },
};

</script>
  