<template>
    <div>
        <div class="flex mb-3 items-center justify-end">
            <div class="mr-2 text-lg">
                {{ $t('Total:') }} {{ total > 0 ? formatNumber(total) : 0 }}
            </div>
            <div>
                <el-button type="primary" :disabled="total <= 0 || isLoadingGenerate" @click="generateExportationById">
                    {{ $t("Generate") }}
                </el-button>
            </div>
        </div>
<!--        <div class="mb-3">-->
<!--            <el-input class="!w-56" :placeholder="$t('Search Tracking Number')" v-model="filter.tracking_number"-->
<!--                @keydown.enter="onFilter" clearable @clear="onFilter" />-->
<!--        </div>-->
        <div class="table-content">
            <el-table v-if="total > 0 && employee?.id " stripe border size="small" :data="items" :max-height="maxHeight" v-loading="isLoading"
                element-loading-text="Loading...">
                <el-table-column prop="item" :label="$t('Tracking number')">
                    <template #default="scope">
                        {{ scope.row.tracking_number }}
                    </template>
                </el-table-column>
                <el-table-column prop="item" :label="$t('Item quantity')">
                    <template #default="scope">
                        {{ scope.row.quantity }}
                    </template>
                </el-table-column>
                <el-table-column prop="item" :label="$t('Part number')">
                    <template #default="scope">
                        {{ scope.row.part_number }}
                    </template>
                </el-table-column>
                <el-table-column prop="item" :label="$t('Country of origin')">
                    <template #default="scope">
                        {{ scope.row.part_number_country }}
                    </template>
                </el-table-column>
                <el-table-column prop="action" :label="$t('Action')" width="56">
                    <template #default="scope">
                        <el-popconfirm :title="
                            'Are you sure to delete this?'
                        " @confirm="removeTrackingNumber(scope.row)">
                            <template #reference>
                                <el-link :underline="false" type="danger">
                                    <icon :data="iconDelete" />
                                </el-link>
                            </template>
                        </el-popconfirm>
                    </template>
                </el-table-column>
            </el-table>
          <div v-else class="grid place-content-center h-96">
            <div class="text-center mb-3">
              <icon :data="iconScan" width="100" height="100"
                    style="fill: none !important; stroke: currentColor !important; color: #8E9296;"/>
            </div>
            <div style="color: #8E9296;" class="text-xl">{{ $t('Scan tracking number to view data') }}</div>
          </div>
            <div class="bottom" v-if="total > 0">
                <div class="total opacity-0">{{ $t('Total:') }} {{ total > 0 ? total : 0 }}</div>
                <el-pagination :disabled="isLoading" background layout="prev, pager, next" :page-size="filter.limit"
                    :total="total" @current-change="changePage" v-model:currentPage="filter.page">
                </el-pagination>
                <div class="limit" :disabled="isLoading">
                    <el-select class="w-36" v-model="filter.limit" :placeholder="$t('Select')" size="mini"
                        @change="onFilter">
                        <el-option v-for="item in limits" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </div>
            </div>
        </div>
    </div>
</template>
  
<script>

import filterMixin from "@/mixins/filter";
import EventBus from '@/utilities/eventBus.js';
import {generateExportation, listTracking, removeTrackingNumber} from '@/api/exportation.js';
import { isEmpty } from 'ramda';
export default {
    name: 'Tracking',
    components: {},
    mixins: [filterMixin],
    props: {
        employee: {
            type: Object
        }
    },
    data() {
        return {
            items: [],
            isLoading: false,
            filter: this.setDefaultFilter(),
            isLoadingGenerate: false,
        };
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 470);
        },
        maxHeightPrintPDF() {
            return parseInt(window.innerHeight - 300);
        }
    },
    created() {
        EventBus.$on("scanTrackingNumberExportation", (data) => {
            this.filter = this.setDefaultFilter();
            this.filter.exportation_id = data.exportation_id;
            this.fetchData();
        });
    },
    methods: {
        async generateExportationById() {
            if (!this.employee?.id) {
                this.notification("Please Scan Employee Code.", 'error');
                return;
            }
            this.isLoadingGenerate = true;
            try {
                const params = {
                  exportation_id: this.filter.exportation_id,
                  employee_id: this.employee?.id,
                }
                await generateExportation(params);
                this.$emit('refreshGenerateSuccess');
                this.notification('Successfully generated an exportation report.');
            } catch (e) {
                let data = e.response?.data;
                let message = data?.message || this.$t('Generate manifest url error.');
                if (!isEmpty(data) && data?.errors) {
                    data = data?.errors;
                    const keyFirstData = Object.keys(data)[0];
                    const firstData = data[keyFirstData];
                    message = firstData[0];
                }
                this.notification(message, 'error');
            } finally {
                this.isLoadingGenerate = false;
            }
        },
        async removeTrackingNumber(item) {
            try {
                await removeTrackingNumber(item.id);
                this.notification(this.$t('Remove tracking number successfully.'));
                this.filter.tracking_number = '';
                this.filter.page = 1;
                this.fetchData();
                this.total--;
                EventBus.$emit("onRefreshPendingExportation");
            } catch (e) {
                let data = e.response?.data;
                let message = data?.message || this.$t('Remove tracking number error.');
                if (!isEmpty(data) && data?.errors) {
                    data = data.errors;
                    const keyFirstData = Object.keys(data)[0];
                    const firstData = data[keyFirstData];
                    message = firstData[0];
                }
                this.notification(message, 'error');
            }
        },
        setDefaultFilter() {
            return {
                limit: 25,
                page: 1,
                tracking_number: "",
                exportation_id: "",
            };
        },
        changePage(page) {
            this.filter.page = page;
            this.$nextTick(() => {
                this.fetchData();
            });
        },
        onFilter() {
            this.filter.page = 1;
            this.$nextTick(() => {
                this.fetchData();
            });
        },
        async fetchData() {
            this.items = [];
            if (!this.filter?.exportation_id) {
                return;
            }
            this.isLoading = true;
            const res = await listTracking(this.filter);
            const data = res?.data || {}
            this.items = data.data || [];
            this.total = data?.total || 0;
          console.log(this.total, "data total");
          this.isLoading = false;
        },
    },
};

</script>
  