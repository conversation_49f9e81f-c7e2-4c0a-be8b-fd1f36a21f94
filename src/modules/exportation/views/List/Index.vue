<style src="./Style.scss" lang="scss" scoped>

</style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ $t('Exportation Report') }}</h1>
      </div>
    </div>
    <div class="mt-3">
      <div class="grid grid-cols-2 gap-8 mb-3">
<!--        <div>-->
          <div class="2xl:w-1/2 xl:w-2/3">
            <ScanEmployee @setEmployee="setEmployee" :jobType="'exportation'" />
            <div v-if="isScanEmployee">
              <el-input class="mt-3 barcode-report-number"
                        :disabled="data.exportation_id ? true : false"
                        :placeholder="$t('Exportation report number')" v-model="data.number_report"
                @keyup.enter="focusByElClass('tracking-number')" @input="changeBoxID" />
              <el-input class="mt-3 tracking-number" :placeholder="$t('Scan Tracking Number')"
                v-model="data.tracking_number" @keyup.enter="scanTrackingNumber" />
            </div>
          </div>
<!--          <div class="mt-5" v-if="isScanEmployee">-->
<!--            <Tracking :employee="employee" @refreshGenerateSuccess="confirmGenerateSuccess" />-->
<!--          </div>-->
        </div>
        <div>
          <el-tabs v-model="currentTab">
            <el-tab-pane label="In progress" name="in_progress">
              <Tracking :employee="employee" @refreshGenerateSuccess="confirmGenerateSuccess" />
            </el-tab-pane>
            <el-tab-pane label="Pending" name="pending">
              <Pending :employee="employee" @continueExportation="continueExportation" />
            </el-tab-pane>
            <el-tab-pane label="History" name="history">
              <History :employee="employee" />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
<!--  </div>-->
</template>
