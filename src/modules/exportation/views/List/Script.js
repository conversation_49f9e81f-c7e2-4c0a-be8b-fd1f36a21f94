import { scanTrackingNumber } from '@/api/exportation.js';
import EventBus from '@/utilities/eventBus.js';
import { isEmpty } from 'ramda';
import ScanEmployee from '@/components/ScanEmployee.vue';
import Pending from '@/modules/exportation/components/Pending.vue';
import History from '@/modules/exportation/components/History.vue';
import Tracking from '@/modules/exportation/components/Tracking.vue';

export default {
  name: 'Manifest',
  components: { ScanEmployee, Pending, History, Tracking },
  data() {

    return {
      isLoading: false,
      employee: {},
      listShippingCarrier: [],
      shippingCarrier: '',
      trackingNumber: '',
      currentTab: 'in_progress',
      data: this.setDefaultData(),
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 284);
    },
    isScanEmployee() {
      return !!Object.keys(this.employee).length;
    },
  },
  watch: {
    isScanEmployee: {
      handler() {
        if (!this.isScanEmployee) {
          this.data = this.setDefaultData();
        }
      },
      deep: true,
    },
  },
  created() {
  },
  beforeUnmount() {
    EventBus.$off('scanTrackingNumberExportation');
    EventBus.$off('onRefreshPendingExportation');
    EventBus.$off('onRefreshHistoryExportation');
  },
  mounted() {

  },
  methods: {
    continueExportation(data) {
      this.data.exportation_id = data.id;
      this.data.number_report = data.number_report;
      this.currentTab = 'in_progress';
      EventBus.$emit('scanTrackingNumberExportation', this.data);
    },
    setDefaultData() {
      return {
        tracking_number: '',
        exportation_id: '',
        number_report: '',
      };
    },
    async scanTrackingNumber() {
      if (!this.employee?.id) {
        this.notification('Employee is required.', 'error');
        return;
      }

      if (!this.data?.number_report) {
        this.focusByElClass('barcode-report-number');
        this.notification('Exportation report number is required.', 'error');
        return;
      }

      if (!this.data?.tracking_number) {
        this.focusByElClass('tracking-number');
        this.notification('Tracking number is required.', 'error');
        return;
      }

      this.isLoading = true;
      try {
        const params = {
          employee_id: this.employee.id,
          tracking_number: this.data.tracking_number,
          exportation_id: this.data.exportation_id,
          number_report: this.data.exportation_id ? '' : this.data.number_report,
        };
        const res = await scanTrackingNumber(params);
        this.notification(this.$t('Scan tracking number successfully.'));
        this.data.exportation_id = res.data?.data?.exportation_id;
        if (this.data.exportation_id) {
          EventBus.$emit('scanTrackingNumberExportation', this.data);
          EventBus.$emit('onRefreshPendingExportation');
        }
      } catch (e) {
        let data = e.response?.data;
        let message = data?.message || this.$t('Scan tracking number error.');
        if (!isEmpty(data) && data?.errors) {
          data = data.errors;
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.notification(message, 'error');
      } finally {
        this.data.tracking_number = '';
        this.isLoading = false;
        this.focusByElClass('tracking-number');
      }
    },
    setEmployee(employee) {
      this.employee = employee;
      if (this.data.number_report) {
        this.focusByElClass('tracking-number');
        return;
      }
      this.focusByElClass('barcode-report-number');
    },
    confirmGenerateSuccess() {
      this.data = this.setDefaultData();
      EventBus.$emit('scanTrackingNumberExportation', this.data);
      EventBus.$emit('onRefreshPendingExportation');
      EventBus.$emit('onRefreshHistoryExportation');
      this.currentTab = 'history';
    },
  },
};
