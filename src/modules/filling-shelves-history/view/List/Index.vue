<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ $t('Filling Shelves History') }}</h1>
      </div>
    </div>
    <div class="table-content">
      <div class="filter-top my-4">
        <el-row :gutter="20">
          <el-col :span="3">
            <el-date-picker
              v-model="filter.date"
              type="daterange"
              format="YYYY-MM-DD"
              :start-placeholder="$t('Start date')"
              :end-placeholder="$t('End date')"
              style="width: 100%"
              @change="onChangeDate"
            >
            </el-date-picker>
          </el-col>
          <el-col :span="3">
            <el-input
              :placeholder="$t('Work Order ID')"
              v-model="filter.work_order_id"
              @keyup.enter="onFilter"
            />
          </el-col>
          <el-col :span="3">
            <el-input
              :placeholder="$t('Box ID')"
              v-model="filter.box_id"
              @keyup.enter="onFilter"
            />
          </el-col>
          <el-col :span="3">
            <el-select
              v-model="filter.status"
              :placeholder="$t('Status')"
              class="f-width"
            >
              <el-option key="1" label="Completed" value="completed">
              </el-option>
              <el-option key="2" label="Incompleted" value="incompleted">
              </el-option>
              <el-option
                key="3"
                label="Partial Completed"
                value="partial_completed"
              >
              </el-option>
              <el-option key="4" label="Cancelled" value="cancelled">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-input
              :placeholder="$t('Employee')"
              v-model="filter.employee"
              @keyup.enter="onFilter"
            />
          </el-col>
          <el-col :span="5">
            <div class="btn-filter">
              <el-button type="primary" @click="onFilter">
                <span class="icon-margin-right">
                  <icon :data="iconFilter" /></span
                >{{ $t('Filter') }}
              </el-button>
              <el-button @click="resetFilter">
                <span class="icon-margin-right">{{ $t('Reset') }}</span>
              </el-button>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-table
        border
        stripe
        size="small"
        :data="items"
        :max-height="maxHeight"
        v-loading="isLoading"
        element-loading-text="Loading..."
      >
        <el-table-column
          prop="work_order_id"
          :label="$t('Work Order ID')"
          min-width="100"
          align="center"
        />
        <el-table-column :label="$t('Box ID')" prop="box_number" align="center"/>
        <el-table-column :label="$t('Status')" min-width="150" align="center">
          <template #default="scope">
            <div>
              <el-tag
                type="info"
                :class="getClassStatus(scope.row.work_order)"
                class="rounded-xl"
                effect="dark"
                round
                size="small"
              >
                {{ getStatus(scope.row.work_order) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('Employee')" min-width="150" align="center">
          <template #default="scope">
            {{ scope.row.employee.name }}
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('Date Created')"
          min-width="200"
          align="center"
        >
          <template #default="scope">
            {{ listViewDateFormat(scope.row.created_at) }}
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom">
        <div class="total">
          {{ $t('Total:') }} {{ items.length ? formatNumber(total) : 0 }}
        </div>
        <el-pagination
          :disabled="isLoading"
          background
          layout="prev, pager, next"
          :page-size="filter.limit"
          :total="total"
          @current-change="changePage"
          v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
            v-model="filter.limit"
            :placeholder="$t('Select')"
            size="mini"
            @change="onFilter"
          >
            <el-option
              v-for="item in limits"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
  </div>
</template>
