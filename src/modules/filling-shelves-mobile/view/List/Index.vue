<style src="./Style.scss" lang="scss" scoped>
</style>
<script src="./Script.js"></script>
<template>
  <div class="content">
    <HeaderMobile :name="$t('Filling Shelves')"></HeaderMobile>
    <div class="content-body">
      <div class="add-inventory-addition flex-col">
        <el-form :label-position="'top'" @submit.prevent="onSubmit()">
          <div class="bg-gray-50 p-3 border rounded mb-3">
            <el-form-item v-show="!Object.keys(employee).length" :error="employeeError" :label="$t('Employee ID')"
              required>
              <el-input v-model="employeeID" @change="scanEmployeeID" class="el-form-item-employee"></el-input>
            </el-form-item>
            <div v-if="Object.keys(employee).length">
              <div class="flex justify-between">
                <b class="text-base">Hi {{ employee.name }}, Have a nice day!</b>
                <el-link type="danger" @click="resetEmployee" :underline="false">{{ $t('Logout') }}</el-link>
              </div>
              <div class="text-lg text-fuchsia-500">
                <IncrementTimer />
              </div>
            </div>
          </div>
          <el-form-item :label="$t('BoxID')" prop="boxId" class="el-form-item-box-id d-flex">
            <el-input class="flex-1" v-model="boxId" @change="onSubmit">
            </el-input>
          </el-form-item>
          <el-form-item :label="$t('SKU')" prop="sku" class="el-form-item-sku d-flex">
            <el-input class="flex-1" v-model="sku" @change="onSubmit">
            </el-input>
          </el-form-item>
        </el-form>
        <div class="mt-4 w-full border p-3" v-if="employee?.id">
          <div>
            <div class="text-lg">{{ $t('Manual SKU select') }}</div>
            <div class="text-[12px] text-red-500">{{ $t('In case you can\'t scan product SKU please select manual.') }}
            </div>
          </div>
          <el-form class="mt-2 w-full">
            <el-form-item prop="style">
              <el-select v-model="filter.style" @change="changeStyle" filterable :placeholder="$t('Select Style')"
                size="large" clearable @clear="onClearFilter">
                <el-option v-for="item in styles" :key="item.style" :label="item.style" :value="item.style">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="color">
              <el-select v-model="filter.color" filterable :placeholder="$t('Select Color')" size="large" clearable>
                <el-option v-for="item in colors" :key="item" :label="item" :value="item">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="size">
              <el-select v-model="filter.size" filterable :placeholder="$t('Select Size')" size="large" clearable>
                <el-option v-for="item in sizes" :key="item" :label="item" :value="item">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="flex">
              <el-button v-if="canFilter" @click="filterProductSku" type="primary" size="large">Submit</el-button>
              <el-link  v-if="hasFilter" type="danger" @click="onClearFilter" :underline="false" class="ml-2">
                {{ $t("Clear") }}
              </el-link>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>
