import { employeeLogoutTimeChecking, employeeTimeChecking } from "@/api/employee.js";
import { getProductAttributes, getProductByParams } from "@/api/product.js";
import { store } from "@/api/fillingShelves.js";
import IncrementTimer from "@/components/IncrementTimer.vue";
import HeaderMobile from "@/components/HeaderMobile.vue";
import { equals, isEmpty } from "ramda";

export default {
    name: "Filling Shelves Mobile",
    components: {
        IncrementTimer,
        HeaderMobile
    },
    mixins: [],
    computed: {
        hasFilter() {
            const defaultFilter = this.setDefaultFilter();
            return !equals(defaultFilter, this.filter);
        },
        canFilter() {
            return this.filter.style && this.filter.size && this.filter.color;
        }
    },
    data() {
        return {
            employeeID: '',
            employee: {},
            employeeError: '',
            time_checking_id: null,
            job_type: 'filling_shelves',
            boxId: null,
            sku: null,
            styles: [],
            colors: [],
            sizes: [],
            filter: this.setDefaultFilter(),
        };
    },
    mounted() {
        this.fetchProductAttributes();
    },
    methods: {
        async resetEmployee() {
            const res = await employeeLogoutTimeChecking(this.id_time_checking);
            this.employee = {};
            this.employeeError = '';
            this.employeeID = '';
            this.id_time_checking = null
        },
        async scanEmployeeID() {
            if (this.id_time_checking) {
                return true;
            }
            if (!this.employeeID) {
                this.employeeError = "Employee ID field cannot be left blank.";
                return false;
            }

            const res = await employeeTimeChecking({
                code: Number(this.employeeID),
                job_type: this.job_type
            })

            if (!res.data.data) {
                this.employeeError = "Can't find your employee ID, please scan again";
                return false;
            }
            this.employeeError = "";
            this.employee = res.data.data;
            this.time_checking_id = res.data.id_time_checking;
            this.focusByElClass();
            return true;
        },
        changeStyle() {
            const item = this.styles.find((item) => item.style === this.filter.style);
            this.filter.size = "";
            this.filter.color = "";
            this.colors = item?.colors || [];
            this.sizes = item?.sizes || [];
        },
        async fetchProductAttributes() {
            const res = await getProductAttributes();
            const data = res.data || {};
            this.styles = Object.values(data);
        },
        async onSubmit() {
            try {
                if (this.employee != null && this.boxId != null && this.sku != null) {
                    const data = {
                        employee_id: this.employee.id,
                        box_barcode: this.boxId,
                        sku: this.sku,
                        time_checking_id: this.time_checking_id
                    };
                    await store(data);

                    this.notification(this.$t('Create filling shelves successfully!'), "success");
                    
                    this.boxId = null;
                    this.sku = null;
                    this.focusByElClass();
                } else if (this.boxId === null) {
                    this.focusByElClass();
                } else {
                    this.focusByElClass("el-form-item-sku");
                }
            } catch (e) {
                const data = e.response.data;
                let message = this.$t('Create filling shelves error.');
                if (!isEmpty(data)) {
                    message =  this.$t(data.message);
                }
                this.notification(message, "error");
            }

        },
        focusByElClass(elClass = "el-form-item-box-id") {
            this.$nextTick(() => {
                const el = document.getElementsByClassName(elClass);
                if (!el || !el.length) {
                    return;
                }
                const firtsElError = (el && el[0]) || undefined;
                const input = firtsElError.querySelector("input");
                document.body.scrollTop = (input.offsetTop - 120) | 0;
                input.focus();
            });
        },
        async filterProductSku() {
            const res = await getProductByParams(this.filter);

            const product = res.data ? res.data : {};
            if (!!product && product.hasOwnProperty('sku') && !!product.sku) {
                this.sku = product.sku;
                this.onSubmit();
            } else {
                this.notification(this.$t('SKU not found.'), "error");
            }
        },
        onClearFilter() {
            this.filter = this.setDefaultFilter();
            this.sku = null
        },
        setDefaultFilter() {
            let params = {
                style: "",
                size: "",
                color: "",
            };
            return params;
        },
    },
};
