import { index, store } from "@/api/fillingShelves.js";

export const actions = {
  async getListFillingShelves(context, params) {
    try {
      const res = await index(params);
      const items = res?.data.data || [];
      context.commit("setFillingShelves", items);
      return items;
    } catch (e) {
      return e;
    }
  },
  async storeFillingShelves(context, params) {
    try {
      const res = await store(params);
      context.commit('setApiStatus', 200);
      context.commit('setSuccessMessages', res.data.message);
      context.commit("storeFillingShelves", res.data.data);
      return res;
    } catch (e) {
      if (e?.response?.status === 400) {
        context.commit('setErrorMessages', e.response.data.message);
        context.commit('setApiStatus', 400)
      } else if (e?.response?.status === 422) {
        context.commit('setValidationError', e.response.data);
        context.commit('setApiStatus', 422)
      }
      else if (e?.response?.status === 404) {
        context.commit('setValidationError', e.response.data);
        context.commit('setApiStatus', 404)
      }
      return e;
    }
  }
};
