export const mutations = {
  setFillingShelves(state, fillingShelves) {
    state.fillingShelves = fillingShelves;
  },
  storeFillingShelves(state, payload) {
    state.fillingShelves.push(payload);
  },
  setApiStatus(state, status) {
    state.apiStatus = status;
  },
  setErrorMessages(state, messages) {
    state.errorMessages = messages;
  },
  setSuccessMessages(state, messages) {
    state.successMessages = messages;
  },
  setValidationError(state, errorMessages) {
    state.validationError = errorMessages;
  }
};
