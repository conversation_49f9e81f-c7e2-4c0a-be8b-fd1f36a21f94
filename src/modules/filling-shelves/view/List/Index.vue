<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left flex justify-between">
        <h1>{{ $t('Filling Shelves') }}</h1>
        <router-link :to="{ name: 'filling-shelves-history' }">
          <el-button type="primary" size="large"
            >Filling Shelves History</el-button>
        </router-link>
      </div>
    </div>
    <div class="top-head table-content">
      <div class="top-head-left">
        <div class="scan-employee">
          <el-form>
            <div
              v-if="employee"
              class="bg-gray-50 p-3 border rounded"
              style="width: 400px"
            >
              <div class="flex justify-between">
                <b style="font-size: 18px" class="mr-2"
                  >{{ $t('Hi') }} {{ employee.name + ',' }}
                  {{ $t(' Have a nice day!') }}</b
                >
                <el-link
                  type="danger"
                  class="ml-3"
                  @click="resetEmployee"
                  :underline="false"
                  >{{ $t('Logout') }}</el-link
                >
              </div>
              <div class="text-lg text-fuchsia-500">
                <IncrementTimer />
              </div>
            </div>
          </el-form>
          <el-form
            v-show="!employee"
            :error="employeeError"
            @submit.prevent.native="getScanCodeEmloyee"
          >
            <el-input
              :placeholder="$t('Scan code employee')"
              class="mt-2 el-form-item-scan-employee border-stone-500"
              style="width: 400px"
              size="large"
              ref="employeeCode"
              v-model="codeEmployee"
            />
          </el-form>
        </div>
        <div class="scan-product flex">
          <el-form @submit.prevent.native="getScanBoxId(boxId)">
            <el-input
              :placeholder="$t('Scan Box ID')"
              class="mt-2 el-form-item-tracking-number border-stone-500"
              style="width: 400px"
              size="large"
              ref="boxIdControl"
              v-model="boxId"
            />
            <div
              class="mt-2 text-danger"
              v-if="validationError && validationError.box_barcode"
            >
              {{ validationError.box_barcode[0] }}
            </div>
            <div
              class="mt-2 text-danger"
              v-if="validationError && validationError.message"
            >
              {{ validationError.message }}
            </div>
          </el-form>
          <el-form @submit.prevent.native="getScanSku(sku)">
            <el-input
              :placeholder="$t('Scan Product SKU')"
              class="mt-2 ml-2 el-form-item-tracking-sku border-stone-500"
              style="width: 400px"
              size="large"
              ref="skuControl"
              v-model="sku"
            />
            <div
              class="mt-2 ml-2 text-danger"
              v-if="validationError && validationError.sku"
            >
              {{ validationError.sku[0] }}
            </div>
          </el-form>
        </div>

        <div class="mt-4 w-full" v-if="employee?.id">
          <div>
            <div class="text-lg">{{ $t('Manual SKU select') }}</div>
            <div class="text-[12px] text-red-500">
              {{
                $t("In case you can't scan product SKU please select manual.")
              }}
            </div>
          </div>
          <el-form class="flex mt-2 w-full">
            <el-form-item prop="style" class="mr-2">
              <el-select
                v-model="filter.style"
                @change="changeStyle"
                filterable
                :placeholder="$t('Select Style')"
                size="large"
                clearable
                @clear="onClearFilter"
              >
                <el-option
                  v-for="item in styles"
                  :key="item.style"
                  :label="item.style"
                  :value="item.style"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="color" class="mr-2">
              <el-select
                v-model="filter.color"
                filterable
                :placeholder="$t('Select Color')"
                size="large"
                clearable
              >
                <el-option
                  v-for="item in colors"
                  :key="item"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="size">
              <el-select
                v-model="filter.size"
                filterable
                :placeholder="$t('Select Size')"
                size="large"
                clearable
              >
                <el-option
                  v-for="item in sizes"
                  :key="item"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-if="canFilter" class="ml-2">
              <el-button @click="filterProductSku" type="primary" size="large"
                >Submit</el-button
              >
            </el-form-item>
            <el-form-item v-if="hasFilter" class="ml-2">
              <el-link type="danger" @click="onClearFilter" :underline="false">
                {{ $t('Clear') }}
              </el-link>
            </el-form-item>
          </el-form>
        </div>
        <div class="border mt-8">
          <el-table
            :data="listItem"
            stripe
            style="width: 100%"
            row-key="id"
            v-loading="isLoading"
            element-loading-text="Loading..."
            :max-height="maxHeight"
          >
            <el-table-column
              prop="box_number"
              label-class-name="box"
              :label="$t('Box ID')"
            >
            </el-table-column>
            <el-table-column prop="product.style" :label="$t('Style')">
            </el-table-column>
            <el-table-column prop="product.color" :label="$t('Color')">
            </el-table-column>
            <el-table-column :label="$t('Size')" prop="product.size">
            </el-table-column>
            <el-table-column prop="product_sku" :label="$t('Product SKU')">
            </el-table-column>
          </el-table>
        </div>
        <div class="mt-2 w-full" v-if="employee?.id">
          <div class="text-sm">
            {{
              $t(
                'Note: After clicking "Submit" button and it is success, data will show on the table.'
              )
            }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
