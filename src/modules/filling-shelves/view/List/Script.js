import {
  employeeLogoutTimeChecking,
  employeeTimeChecking,
} from "@/api/employee.js";
import IncrementTimer from "@/components/IncrementTimer.vue";
import { mapGetters } from "vuex";
import { getProductAttributes } from "@/api/product.js";
import { equals, isEmpty } from "ramda";

export default {
  name: "FillingShelves",
  components: {
    IncrementTimer,
  },
  mixins: [],
  data() {
    return {
      codeEmployee: "",
      employee: null,
      employeeError: "",
      time_checking_id: null,
      job_type: "filling_shelves",
      listItem: [],
      boxId: null,
      sku: null,
      styles: [],
      colors: [],
      sizes: [],
      filter: this.setDefaultFilter(),
    };
  },
  computed: {
    ...mapGetters([
      "getListFillingShelves",
      "apiStatus",
      "errorMessages",
      "successMessages",
      "validationError",
      "getAllProducts",
    ]),
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },
    canFilter() {
      return this.filter.style && this.filter.size && this.filter.color;
    }
  },
  mounted() {
    this.getProductList();
    this.fetchProductAttributes();
  },
  methods: {
    filterProductSku() {
      const product = this.getSkufromFilter()
      if (!!product && product.hasOwnProperty('sku') && !!product.sku) {
        this.getScanSku(product.sku)
      }
    },
    getSkufromFilter() {
      return this.getAllProducts.find(product => {
        return this.filter.style == product.style &&
          this.filter.size == product.size &&
          this.filter.color == product.color
        })
    },
    async getProductList() {
      await this.$store.dispatch("getAllProducts");
    },
    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.sku = null
    },
    setDefaultFilter() {
      let params = {
        style: "",
        size: "",
        color: "",
      };
      return params;
    },
    changeStyle() {
      const item = this.styles.find((item) => item.style === this.filter.style);
      this.filter.size = "";
      this.filter.color = "";
      this.colors = item?.colors || [];
      this.sizes = item?.sizes || [];
    },
    async fetchProductAttributes() {
      const res = await getProductAttributes();
      const data = res.data || {};
      this.styles = Object.values(data);
    },
    maxHeight() {
      return parseInt(window.innerHeight - 230);
    },
    async fetchFillingShelves() {
      this.isLoading = true;
      this.listItem = await this.$store.dispatch("getListFillingShelves", {
        employee_id: this.employee.id,
        ...this.filter,
      });
      this.isLoading = false;
    },
    getScanBoxId(value) {
      this.$store.commit("setValidationError", null);
      this.boxId = value;
      if (this.sku) {
        this.getScanSku(this.sku)
      } else {
        this.focusByElClassScanSku();
      }
    },
    async getScanSku(value) {
      this.$store.commit("setValidationError", null);
      this.sku = value;
      if (this.employee != null && this.boxId != null && this.sku != null) {
        await this.$store.dispatch("storeFillingShelves", {
          employee_id: this.employee.id,
          box_barcode: this.boxId,
          sku: this.sku,
          time_checking_id: this.time_checking_id,
        });
        if (this.apiStatus === 200) {
          this.listItem = this.getListFillingShelves;
          this.filter = this.setDefaultFilter();
          this.notification(this.successMessages, "success");
          this.boxId = null;
          this.sku = null;
        }
        if (this.apiStatus === 400) {
          this.notification(this.errorMessages, "error");
          this.boxId = null;
          this.sku = null;
        }
        if (this.apiStatus === 422 || this.apiStatus === 404) {
          this.sku = null;
        }
        this.focusByElClass();
      }
    },
    focusByElClass(elClass = "el-form-item-tracking-number") {
      this.$nextTick(() => {
        const el = document.getElementsByClassName(elClass);
        if (!el || !el.length) {
          return;
        }
        const firtsElError = (el && el[0]) || undefined;
        const input = firtsElError.querySelector("input");
        document.body.scrollTop = (input.offsetTop - 120) | 0;
        input.focus();
      });
    },
    focusByElClassScanEmployee(elClass = "el-form-item-scan-employee") {
      this.$nextTick(() => {
        const el = document.getElementsByClassName(elClass);
        if (!el || !el.length) {
          return;
        }
        const firtsElError = (el && el[0]) || undefined;
        const input = firtsElError.querySelector("input");
        document.body.scrollTop = (input.offsetTop - 120) | 0;
        input.focus();
      });
    },
    focusByElClassScanSku(elClass = "el-form-item-tracking-sku") {
      this.$nextTick(() => {
        const el = document.getElementsByClassName(elClass);
        if (!el || !el.length) {
          return;
        }
        const firtsElError = (el && el[0]) || undefined;
        const input = firtsElError.querySelector("input");
        document.body.scrollTop = (input.offsetTop - 120) | 0;
        input.focus();
      });
    },
    async getScanCodeEmloyee() {
      try {
        this.isLoading = true;
        const res = await employeeTimeChecking({
          code: Number(this.codeEmployee),
          job_type: this.job_type,
        });
        if (!res.data.data) {
          this.codeEmployee = "";
          this.employeeError = "Scan employee code error, please scan again.";
          this.notification(
            "Scan employee code error, please scan again.",
            "error"
          );
          this.focusByElClassScanEmployee();
          return;
        }
        this.employee = res.data.data;
        this.time_checking_id = res.data.id_time_checking;
        this.fetchFillingShelves();
        this.focusByElClass();
        this.isLoading = false;
      } catch (e) {
        const data = e.response.data;
        let message = this.$t("Not found");
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.isLoading = false;
        this.notification(message, "error");
      }
    },
    async resetEmployee() {
      await employeeLogoutTimeChecking(this.time_checking_id);
      this.employee = null;
      this.employeeError = "";
      this.codeEmployee = "";
      this.time_checking_id = null;
      this.time = 0;
      this.focusByElClassScanEmployee();
      this.listItem = [];
    },
  },
};
