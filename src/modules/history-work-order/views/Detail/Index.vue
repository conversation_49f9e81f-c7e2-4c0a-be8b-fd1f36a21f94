<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
    <el-dialog v-model="dialogVisible" destroy-on-close custom-class="el-dialog-custom" :destroy-on-close="true">
        <el-row class="row-bg row-detail" justify="space-between">
            <el-col :span="8">
                <span class="label font-semibold">Work Order ID: </span>
                <span>{{ data.id }}</span>
            </el-col>
            <el-col :span="8" align="right">
                <span class="label font-semibold">Date Create: </span>
                <span>{{ formatDate(data.created_at, false) }}</span>
            </el-col>
        </el-row>
        <el-row class="row-bg" justify="space-between">
            <el-col :span="8">
                <span class="label font-semibold">Employee:</span>
                <span>{{ data.name }}</span>
            </el-col>
        </el-row>
        <el-row class="row-bg" justify="space-between">
            <el-col :span="8">
                <span class="label font-semibold">Box Count:</span>
                <span>{{ data.itemCompletedCount }}/{{ data.itemsCount }}</span>
            </el-col>
        </el-row>
        <el-table border stripe size="small" :data="data.items" :max-height="maxHeight" element-loading-text="Loading..." style="margin: 20px 0px 50px 0px;">
            <el-table-column :label="$t('Work Order ID')" min-width="100">
                <template #default="scope">
                    <strong :class="{'line-through text-red-600': scope.row.is_replaced == 1,'text-green-600': scope.row.status== 'completed'}">
                        {{ scope.row.id }}
                    </strong>
                </template>
            </el-table-column>
            <el-table-column :label="$t('Box ID')" min-width="100">
                <template #default="scope">
                    <strong :class="{ 'line-through text-red-600': scope.row.is_replaced == 1,  'text-green-600': scope.row.status== 'completed'}">
                        {{ scope.row.box_number }}
                    </strong>
                </template>
            </el-table-column>
            <el-table-column :label="$t('Location')" min-width="150">
                <template #default="scope">
                    <strong :class="{'line-through text-red-600': scope.row.is_replaced == 1,'text-green-600': scope.row.status== 'completed'}">
                        {{scope.row.location_number }}
                    </strong>
                </template>
            </el-table-column>
            <el-table-column :label="$t('Box Scan Time')" min-width="150">
                <template #default="scope">
                    <strong :class="{'line-through text-red-600': scope.row.is_replaced == 1,'text-green-600': scope.row.status== 'completed'}">
                        {{ scope.row.status == 'completed' ? listViewDateFormat(scope.row.product.updated_at) : '' }}
                    </strong>
                </template>
            </el-table-column>
            <el-table-column :label="$t('Style')" min-width="100">
                <template #default="scope">
                    <strong :class="{'line-through text-red-600': scope.row.is_replaced == 1,'text-green-600': scope.row.status== 'completed'}">
                        {{ scope.row.product.style ?? '' }}
                    </strong>
                </template>
            </el-table-column>
            <el-table-column :label="$t('Color')" min-width="100">
                <template #default="scope">
                    <strong :class="{'line-through text-red-600': scope.row.is_replaced == 1,'text-green-600': scope.row.status== 'completed'}">
                        {{ scope.row.product.color ?? '' }}
                    </strong>
                </template>
            </el-table-column>
            <el-table-column :label="$t('Size')" min-width="100">
                <template #default="scope">
                    <strong :class="{'line-through text-red-600': scope.row.is_replaced == 1,'text-green-600': scope.row.status== 'completed'}">
                        {{ scope.row.product.size ?? '' }}
                    </strong>
                </template>
            </el-table-column>
        </el-table>
    </el-dialog>
</template>