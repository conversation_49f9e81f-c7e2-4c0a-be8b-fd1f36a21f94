import EventBus from "@/utilities/eventBus";
import { create } from "@/api/productStyle";

export default {
    name: "DetailWorkOrder",
    data() {
        return {
            dialogVisible: false,
            data: {},
        }
    },
    created() {
        EventBus.$on("showDetailHistoryOrder", (item) => {
            this.data = item;
            this.dialogVisible = true;
        });
    }
}
