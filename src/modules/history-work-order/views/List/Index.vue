<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
    <div>
        <div class="top-head mb-4">
            <div class="top-head-left">
                <h1>{{ $t('Work Order History') }}</h1>
            </div>
        </div>
        <div class="table-content">
            <div class="filter-top">
                <el-row :gutter="20">
                    <el-col :span="4">
                        <el-date-picker v-model="filter.date" type="daterange" format="YYYY-MM-DD" :start-placeholder="$t('Start date')" :end-placeholder="$t('End date')" style="width: 100%" @change="onChangeDate">
                        </el-date-picker>
                    </el-col>
                    <el-col :span="4">
                        <el-input :placeholder="$t('Work order id/Employee')" v-model="filter.woi" @keyup.enter="onFilter" />
                    </el-col>
                    <el-col :span="4">
                        <el-select v-model="filter.fs" :placeholder="$t('Filling Shelves')" class="f-width">
                            <el-option key="1" label="Yes" value="1"> </el-option>
                            <el-option key="2" label="No" value="2"> </el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <el-select v-model="filter.status" :placeholder="$t('Status')" class="f-width">
                            <el-option key="1" label="Completed" value="completed"> </el-option>
                            <el-option key="2" label="Incompleted" value="incompleted"> </el-option>
                            <el-option key="3" label="Partial Completed" value="partial_completed"> </el-option>
                            <el-option key="4" label="Cancelled" value="cancelled"> </el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="6">
                        <div class="btn-filter">
                            <el-button type="primary" @click="onFilter">
                                <span class="icon-margin-right">
                                    <icon :data="iconFilter" /></span>{{ $t("Filter") }}
                            </el-button>
                            <el-button @click="resetFilter">
                                <span class="icon-margin-right">{{ $t("Reset") }}</span>
                            </el-button>
                        </div>
                    </el-col>
                </el-row>
            </div>
            <el-table border stripe size="small" :data="items" :max-height="maxHeight" v-loading="isLoading" element-loading-text="Loading..." @row-click="handleRowClick">
                <el-table-column prop="id" :label="$t('Work Order ID')" min-width="100"></el-table-column>
                <el-table-column prop="name" :label="$t('Employee')" min-width="200"></el-table-column>
                <el-table-column :label="$t('Filling Shelves')" min-width="150">
                    <template #default="scope">
                        {{ getFillingStatus(scope.row) }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('Create Time')" min-width="200">
                    <template #default="scope">
                        {{ listViewDateFormat(scope.row.created_at) }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('Time Complete')" min-width="120">
                    <template #default="scope">
                        {{ getDuringTime(scope.row) }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('Box Count')" min-width="100">
                    <template #default="scope">
                        {{ getBox(scope.row) }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('Status')" min-width="150">
                    <template #default="scope">
                        <div>
                          <el-tag
                            type="info"
                            :class="getClassStatus(scope.row)"
                            class="rounded-xl"
                            effect="dark"
                            round
                            size="small"
                          >
                            {{ getStatus(scope.row) }}
                          </el-tag>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <div class="bottom">
                <div class="total">{{$t('Total:')}} {{ items.length ? formatNumber(total) : 0 }}</div>
                <el-pagination :disabled="isLoading" background layout="prev, pager, next" :page-size="filter.limit" :total="total" @current-change="changePage" v-model:currentPage="filter.page">
                </el-pagination>
                <div class="limit" :disabled="isLoading">
                    <el-select v-model="filter.limit" :placeholder="$t('Select')" size="mini" @change="onFilter">
                        <el-option v-for="item in limits" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </div>
            </div>
        </div>
    </div>
    <detail-work-order />
</template>