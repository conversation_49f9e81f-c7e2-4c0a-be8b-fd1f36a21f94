import { getHistory } from "@/api/workOrder.js";
import EventBus from "@/utilities/eventBus";
import DetailWorkOrder from "@/modules/history-work-order/views/Detail/Index.vue";

export default {
    name: "HistoryWorkOrder",
    components: {
        DetailWorkOrder,
    },
    data() {
        return {
            items: [],
            isLoading: false,
            filter: this.setDefaultFilter(),
        }
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 189);
        }
    },
    mounted() {
        this.filter = this.getRouteParam();
        this.getData();
    },
    methods: {
        setDefaultDate() {
          const today = new Date();
          const priorDate = new Date(new Date().setDate(today.getDate() - 30));
          return (
            (today && priorDate && [
                this.formatDate(priorDate, false),
                this.formatDate(today, false),
              ]) || ""
          );
        },
        setDefaultFilter() {
            return {
                date: this.setDefaultDate(),
                woi: '',
                fs: '',
                status: '',
                limit: 10,
                page: 1,
            };
        },
        getFillingStatus(item) {
            let status = 'Yes';
            if (item.fillingShelvesCount < item.itemCompletedCount) {
                status = 'No';
            }
            return status;
        },
        onChangeDate() {
            if (this.filter.date && this.filter.date.length) {
                this.filter.date[0] = this.formatDate(this.filter.date[0], false);
                this.filter.date[1] = this.formatDate(this.filter.date[1], false);
            }
        },
        getDuringTime(item) {
            let timeCreate = new Date(item.created_at).getTime();
            let timeComplete = new Date(item.completed_at).getTime();
            if (item.status === 'completed' && timeComplete && timeComplete > timeCreate) {
                return this.duringDay((timeComplete - timeCreate) / 1000)
            }
            return '';
        },
        duringDay(time){
            console.log(time)
            let hour = Math.floor(time / 3600);
            let minute = Math.floor((time % 3600) / 60);
            let second = Math.floor((time % 3600) % 60);
            hour = (hour > 0) ? (hour + 'h ') : '';
            minute = minute === 0 ? '1m' : (minute + 'm ');
            return hour + minute;
        },
        getBox(item) {
            return item.itemCompletedCount + '/' + item.itemsCount;
        },
        getStatus(item) {
            let status = 'Completed';
            switch(item.status) {
                case 'completed':
                    status = 'Completed';
                    break;
                case 'incompleted':
                    status = 'Incompleted';
                    break;
                case 'partial_completed':
                    status = 'Partial Completed';
                    break;
                default:
                    status = 'Cancelled'
            }
            return status;
        },
        getClassStatus(item) {
            let classStatus = "";
            switch (item.status) {
                case "completed":
                  classStatus = `bg-emerald-500`;
                  break;
                case "incompleted":
                  classStatus = `bg-indigo-500`;
                  break;
                case "partial_completed":
                  classStatus = `bg-orange-400`;
                  break;
                default:
                    classStatus = `bg-red-400`;
              }
              return classStatus;
        },
        changePage(page) {
            this.filter.page = page;
            this.getData();
        },
        onFilter() {
            this.filter.page = 1;
            this.getData();
        },
        resetFilter(){
            this.filter = this.setDefaultFilter()
            this.getData()
        },
        async getData() {
            this.isLoading = true;
            this.setRouteParam();
            const { data } = await getHistory(this.filter);
            this.items = data.data;
            this.total = data.total;
            this.isLoading = false;
        },
        handleRowClick(row) {
            EventBus.$emit("showDetailHistoryOrder", row);
        }
    }
}