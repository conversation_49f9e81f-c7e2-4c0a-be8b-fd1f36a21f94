<template>
    <el-dialog v-model="visible" :title="$t('Create ICC profile')" width="500px" destroy-on-close @closed="resetForm">
        <el-form :model="form" ref="formRef" :rules="rules" label-position="top">
            <el-form-item label="Product style" prop="product_style">
                <el-select v-model="form.product_style" placeholder="Product style" remote
                :remote-method="debounceFilterStyles" filterable>
                    <el-option v-for="item in stylesFiltered" :key="item.id" :label="item.name" :value="item.name" />
                </el-select>
            </el-form-item>

            <el-form-item label="WHITE - ICC profile" prop="white">
                <el-upload class="el-upload-drag-full mb-1 upload-box" ref="whiteUpload" name="white" limit="1"
                    accept=".icm" drag :auto-upload="false" :before-upload="() => false"
                    :on-change="(file) => handleChange(file, 'white')">
                    <el-icon class="el-icon--upload">
                        <icon :data="iconUploadCloud" />
                    </el-icon>
                    <div class="el-upload__text">
                        <em>{{ $t('click to upload') }}</em> or drag and drop
                        <p>File supported: .icm</p>
                    </div>
                </el-upload>
            </el-form-item>

            <el-form-item label="BLACK - ICC profile" prop="black">
                <el-upload class="el-upload-drag-full mb-1 upload-box" ref="blackUpload" name="black" limit="1"
                    accept=".icm" drag :auto-upload="false" :before-upload="() => false"
                    :on-change="(file) => handleChange(file, 'black')">
                    <el-icon class="el-icon--upload">
                        <icon :data="iconUploadCloud" />
                    </el-icon>
                    <div class="el-upload__text">
                        <em>{{ $t('click to upload') }}</em> or drag and drop
                        <p>File supported: .icm</p>
                    </div>
                </el-upload>
            </el-form-item>


            <el-form-item label="COLORED - ICC profile" prop="colored">
                <el-upload class="el-upload-drag-full mb-1 upload-box" ref="coloredUpload" name="colored" limit="1"
                    accept=".icm" drag :auto-upload="false" :before-upload="() => false"
                    :on-change="(file) => handleChange(file, 'colored')">
                    <el-icon class="el-icon--upload">
                        <icon :data="iconUploadCloud" />
                    </el-icon>
                    <div class="el-upload__text">
                        <em>{{ $t('click to upload') }}</em> or drag and drop
                        <p>File supported: .icm</p>
                    </div>
                </el-upload>
            </el-form-item>
        </el-form>

        <template #footer>
            <el-button @click="visible = false">Cancel</el-button>
            <el-button type="primary" :loading="isSubmitting" @click="handleSubmit" :disabled="uploading">Create</el-button>
        </template>
    </el-dialog>
</template>

<script>
import EventBus from "@/utilities/eventBus";
import { create, upload, uploadS3 } from "@/api/iccProfile.js";
import { debounce } from '@/utilities/helper.js';
import { is } from "ramda";

export default {
    name: "CreateICCProfile",
    data() {
        return {
            visible: false,
            isSubmitting: false,
            uploading: false,
            form: {
                product_style: "",
                white_name: "",
                white_url: "",
                black_name: "",
                black_url: "",
                colored_name: "",
                colored_url: "",
            },
            rules: {
                product_style: [{ required: true, message: "Please select a product style", trigger: "blur" }],
                white_url: [{ required: true, message: "Please upload white ICC profile", trigger: "change" }],
                black_url: [{ required: true, message: "Please upload black ICC profile", trigger: "change" }],
                black_url: [{ required: true, message: "Please upload colored ICC profile", trigger: "change" }],
            },
            whiteFileList: [],
            blackFileList: [],
            coloredFileList: [],
            styleFilterKey: '',
        };
    },
    props: {
        productStyles: {
            type: Array,
            default: () => []
        }
    },
    computed: {
        stylesFiltered() {
            if (this.styleFilterKey) {
                return this.productStyles.filter((item) => {
                return String(item.name)
                    .toLowerCase()
                    .includes(this.styleFilterKey.toLowerCase());
                });
            } else {
                return this.productStyles;
            }
        },
    },
    created() {
        console.log('CreateICCProfile mounted, listening EventBus');
        EventBus.$on("showCreateICCProfile", this.showModal);
    },
    beforeUnmount() {
        EventBus.$off("showCreateICCProfile", this.showModal);
    },
    methods: {
        showModal() {
            console.log("Received showCreateICCProfile event");
            this.visible = true;
        },
        debounceFilterStyles: debounce(async function (query) {
            this.styleFilterKey = query;
        }, 100),
        async handleChange(file, type) {
            this.uploading = true;
            this[`${type}FileList`] = [file];
            try {
                const params = {
                    file_name: file.name,
                }
                const { data } = await upload(params);
                await uploadS3(data.signed_url, file.raw);
                this.form[`${type}_name`] = data.file_name;
                this.form[`${type}_url`] = data.final_url;
            } catch (e) {
                console.log(e.response);
                
                this.notification(`Failed to upload ${type} ICC profile`, "error");
            } finally {
                this.uploading = false;
            }
        },
        resetForm() {
            this.$refs.formRef?.resetFields();
            this.form = {
                product_style: "",
                white: null,
                black: null,
                colored: null,
            };
            this.whiteFileList = [];
            this.blackFileList = [];
            this.coloredFileList = [];
            this.styleFilterKey = '';
        },
        async handleSubmit() {
            this.$refs.formRef.validate(async (valid) => {
                if (!valid) return;
                this.isSubmitting = true;
                try {
                    const formData = new FormData();
                    formData.append("product_style", this.form.product_style);
                    formData.append("white_name", this.form.white_name);
                    formData.append("white_url", this.form.white_url);
                    formData.append("black_name", this.form.black_name);
                    formData.append("black_url", this.form.black_url);
                    formData.append("colored_name", this.form.colored_name);
                    formData.append("colored_url", this.form.colored_url);

                    await create(formData);
                    this.notification("Created successfully", "success");
                    this.visible = false;
                    this.$emit("refresh");
                } catch (e) {
                    const errors = e.response?.data;
                    if (errors) {
                        const messages = Object.values(errors).flat().join(", ");
                        this.notification(messages, "error");
                    }
                } finally {
                    this.isSubmitting = false;
                }
            });
        },
    },
};
</script>

<style scoped>
.upload-box {
    width: 100%;
}
</style>
