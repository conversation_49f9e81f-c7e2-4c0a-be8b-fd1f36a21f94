<template>
    <div>
        <div class="top-head mb-4">
            <div class="top-head-left">
                <h1>{{ $t('Product Style ICC Profile') }}</h1>
            </div>
            <div class="top-head-right">
                <el-button type="primary" @click="addICCProfile">
                    <span class="icon-margin-right">
                        <icon :data="iconAdd" />
                    </span>{{ $t('Create') }}</el-button>
            </div>
        </div>
        <div class="flex gap-2 mb-4">
            <el-select v-model="filter.product_style" placeholder="Product style" remote
            :remote-method="debounceFilterStyles" filterable>
                <el-option v-for="item in stylesFiltered" :key="item.id" :label="item.name" :value="item.name" />
            </el-select>

            <!-- <el-date-picker v-model="filter.date" type="daterange" placeholder="Created date" clearable /> -->

            <el-button @click="onFilter" type="primary">Apply filter</el-button>
            <el-button @click="onClear" v-if="hasFilter" plain>Clear</el-button>
        </div>

        <el-table :data="items" stripe border style="width: 100%" :max-height="500" v-loading="loading">
            <el-table-column prop="product_style" label="Product style" min-width="150" />
            <el-table-column prop="white" label="White" min-width="150">
                <template #default="scope">
                    <p :href="scope.row.white" target="_blank">{{ getFileName(scope.row.white_name) }}</p>
                </template>
            </el-table-column>
            <el-table-column prop="black" label="Black" min-width="150">
                <template #default="scope">
                    <p :href="scope.row.black" target="_blank">{{ getFileName(scope.row.black_name) }}</p>
                </template>
            </el-table-column>
            <el-table-column prop="coloured" label="Coloured" min-width="150">
                <template #default="scope">
                    <p :href="scope.row.coloured" target="_blank">{{ getFileName(scope.row.colored_name) }}</p>
                </template>
            </el-table-column>
            <el-table-column prop="created_at" :label="$t('Created Date')" min-width="200">
                <template #default="scope">
                    {{ formatTime(scope.row.created_at) }}
                </template>
            </el-table-column>
        </el-table>

        <div class="flex justify-between items-center mt-4">
            <div class="total">{{ $t('Total:') }} {{ items.length ? formatNumber(total) : 0 }}</div>
            <el-pagination background layout="prev, pager, next" :current-page="page" :page-size="10" :total="total"
                @current-change="changePage" />
            <div class="limit" :disabled="isLoading">
                <el-select v-model="filter.limit" :placeholder="$t('Select')" size="mini" @change="onFilter">
                    <el-option v-for="item in limits" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </div>
        </div>
    </div>
    <CreateICCProfile :productStyles="productStyles" @refresh="getICCProfileList" />
</template>

<script>
import { list } from "@/api/iccProfile.js";
import EventBus from "@/utilities/eventBus";
import { fetchAll } from '@/api/productStyle';
import CreateICCProfile from '../../components/Create.vue';
import { debounce } from '@/utilities/helper.js';

export default {
    name: "ICCProfileList",
    data() {
        return {
            items: [],
            total: 0,
            isLoading: false,
            filter: this.setDefaultFilter(),
            productStyles: [],
            styleFilterKey: '',
        };
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 189);
        },
        stylesFiltered() {
            if (this.styleFilterKey) {
                return this.productStyles.filter((item) => {
                return String(item.name)
                    .toLowerCase()
                    .includes(this.styleFilterKey.toLowerCase());
                });
            } else {
                return this.productStyles;
            }
        },
        hasFilter() {
            const current = { ...this.filter };
            const defaultFilter = this.setDefaultFilter();
            delete current.page;
            delete current.limit;
            delete defaultFilter.page;
            delete defaultFilter.limit;

            return JSON.stringify(current) !== JSON.stringify(defaultFilter);
        },
    },
    components: {
        CreateICCProfile,
    },
   
    mounted() {
        this.getICCProfileList();
        this.fetchProductStyles();
    },
    methods: {
        setDefaultFilter() {
            return {
                limit: 10,
                page: 1,
                product_style: null,
            };
        },
        debounceFilterStyles: debounce(async function (query) {
            this.styleFilterKey = query;
        }, 100),
        async getICCProfileList() {
            this.isLoading = true;
            const { data } = await list(this.filter);
            this.items = data.data;
            this.total = data.total;
            this.isLoading = false;
        },
        changePage(page) {
            this.filter.page = page;
            this.getICCProfileList();
        },
        onFilter() {
            this.setRouteParam();
            this.filter.page = 1;
            this.getICCProfileList();
        },
        onClear() {
            this.filter = this.setDefaultFilter();
            this.onFilter();
        },
        addICCProfile() {
           console.log('Emitting showCreateICCProfile');
            EventBus.$emit("showCreateICCProfile");
        },
        onUpdate(item) {
            EventBus.$emit("showUpdateICCProfile", item);
        },
        setRouteParam() {
            this.$router.replace({ query: { ...this.filter } });
        },
        getRouteParam() {
            return {
                ...this.setDefaultFilter(),
                ...this.$route.query,
            };
        },
        getFileName(url) {
            return url?.split("/").pop() || "";
        },
        async fetchProductStyles() {
            const { data } = await fetchAll();
            this.productStyles = data || [];
        },
    }
};

</script>