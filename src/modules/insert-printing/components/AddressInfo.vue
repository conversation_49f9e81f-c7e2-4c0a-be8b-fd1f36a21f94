<template>
  <div>
    <div v-if="address?.country === 'US'">
      <div class="font-semibold">{{ address.name || '' }}</div>
      {{ address.street1 || ''
      }}{{ address?.street2 ? ', ' + address.street2 : ''
      }}{{ address.city ? ', ' + address.city : ''
      }}{{ address.state ? ', ' + address.state : ''
      }}{{ address.zip ? ', ' + address.zip : ''
      }}{{ address.country ? ', ' + address.country : '' }}
    </div>
    <div v-else>
      <div class="font-semibold">{{ address.name || '' }}</div>
      <span class="text-error">
        {{ address.street1 || ''
        }}{{ address?.street2 ? ', ' + address.street2 : ''
        }}{{ address.city ? ', ' + address.city : ''
        }}{{ address.state ? ', ' + address.state : ''
        }}{{ address.zip ? ', ' + address.zip : ''
        }}{{ address.country ? ', ' + address.country : '' }}
      </span>
    </div>
    <div v-if="address.email">
      <span v-if="address?.country === 'US'">{{ address.email || '' }}</span>
      <span v-else class="text-error">{{ address.email || '' }}</span>
    </div>
    <div v-if="address.phone">
      <span v-if="address?.country === 'US'">{{ address.phone || '' }}</span>
      <span v-else class="text-error">{{ address.phone || '' }}</span>
    </div>
    <div>
      <div class="mt-2 flex justify-between text-xs" v-if="address">
        <div v-if="address?.verified_status === 3" class="text-danger">
          <icon
            class="text-danger"
            style="position: relative; top: -1px"
            :data="iconError"
          />
          {{ address?.verified_message || $t('Address Error') }}
        </div>
        <div v-else-if="address?.verified_status === 2" class="text-warning">
          <icon
            class="text-warning"
            style="position: relative; top: -3px"
            :data="iconWarning"
          />
          {{ address?.verified_message || $t('Address Problem') }}
        </div>
        <div v-else-if="address?.verified_status === 0">
          <!-- {{ $t("Unknown") }} -->
        </div>
        <div v-else class="text-success">
          <template v-if="address?.country === 'US'">
            <icon
              class="text-success"
              style="position: relative; top: -3px; width: 20px; height: 20px"
              :data="iconHome"
            />
          </template>
          <template v-else>
            <icon
              class="text-success"
              style="position: relative; top: -2px; width: 20px; height: 20px"
              :data="iconLanguage"
            />
          </template>
          {{ $t('Address Validated') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { verifyAddressByAddressId } from '@/api/saleOrder';

export default {
  name: 'AddressInfo',
  components: {},
  props: {
    saleOrder: {
      type: Object,
    },
    address: {
      type: Object,
    },
  },
  data() {
    return {
      isLoadingVefifyAddress: false,
    };
  },
  computed: {
    isVerifyAddress() {
      return !![0, 2, 3].includes(this?.address.verified_status);
    },
  },
  methods: {
    async verifyAddress() {
      this.isLoadingVefifyAddress = true;
      try {
        this.isLoadingVefifyAddress = false;
        await verifyAddressByAddressId(this.address.address_id);
        this.$emit('onUpdate');
        this.notification(this.$t('Verify address successfully.'));
      } catch (e) {
        this.isLoadingVefifyAddress = false;
        this.notification(
          e.response?.data?.message || 'Verify address error.',
          'error'
        );
      }
    },
  },
};
</script>

<style scoped></style>
