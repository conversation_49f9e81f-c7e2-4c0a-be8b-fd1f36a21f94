<template>
  <div class="border rounded p-3 mb-3" style="min-width: 350px">
    <div
      v-if="saleOrder.store"
      class="border-b pb-2 mb-2 uppercase font-semibold text-2xl flex items-center justify-between"
    >
      <div>
        <icon
          class="text-indigo-500"
          style="position: relative; top: -2px"
          :data="iconStore"
        />
        {{ saleOrder.store.code || 'N/A' }}
      </div>
      <div v-if="buildTags.length" class="flex items-center">
        <template v-if="buildTags.length < 2">
          <el-tag
            v-for="item in buildTags"
            :key="item.id"
            type="info"
            class="rounded py-1 px-2 mr-1"
            effect="dark"
            round
            size="small"
            :color="item.color"
          >
            {{ item.name }}
          </el-tag>
        </template>
        <template v-else>
          <el-tooltip
            v-for="(item, index) in buildTags"
            :key="item.id"
            class="box-item"
            effect="dark"
            :content="item.name"
            placement="top-start"
          >
            <el-tag
              type="info"
              class="!rounded-full !p-0 !w-2.5 h-5 cursor-pointer"
              :class="{
                'mr-2': index !== buildTags.length - 1,
                'mr-0': index === buildTags.length - 1,
              }"
              effect="dark"
              round
              size="small"
              :color="item.color"
            >
            </el-tag>
          </el-tooltip>
        </template>
      </div>
    </div>
    <div v-if="saleOrder.internal_note" class="mb-3">
      <el-alert
        class="font-semi w-auto"
        :closable="false"
        :title="saleOrder.internal_note"
        type="error"
      />
    </div>
    <div class="flex items-center mb-1">
      <div class="font-semibold flex-[0_0_112px] w-28">{{ $t('Order:') }}</div>
      <div class="text-lg mr-2">{{ saleOrder.order_number || '' }}</div>
      <LabelChip
        class="mr-2"
        :orderType="saleOrder.order_type"
        v-if="
          saleOrder.order_type &&
          (saleOrder.order_type === 2 || saleOrder.order_type === 3)
        "
      />
      <span
        class="flex gap-2 mr-2"
        v-if="saleOrder.order_insert && saleOrder.order_insert.length > 0"
      >
        <SignTag
          v-for="item in saleOrder.order_insert"
          :key="item.id"
          :type="item.type"
          :data="item"
        />
      </span>
      <div class="mr-2">
        <el-tag
          type="info"
          class="rounded-xl mr-2 bg-sky-500"
          effect="dark"
          round
          size="small"
          v-if="saleOrder.is_manual === 1"
        >
          {{ $t('Manual Process') }}
        </el-tag>
        <el-tag
          type="info"
          class="rounded-xl mr-2 bg-rose-500"
          effect="dark"
          round
          size="small"
          v-if="saleOrder.is_test === 1"
        >
          {{ $t('Test') }}
        </el-tag>
        <el-tag
          type="info"
          class="rounded-xl mr-2 bg-sky-500"
          effect="dark"
          round
          size="small"
          v-if="saleOrder.is_xqc === 1"
        >
          {{ $t('XQC') }}
        </el-tag>
      </div>
    </div>
    <div class="flex text-gray-600 mb-1">
      <div class="flex-[0_0_112px] w-28">{{ $t('Reference:') }}</div>
      <div>{{ saleOrder.external_number || '' }}</div>
    </div>
    <div class="flex text-gray-600 mb-1">
      <div class="flex-[0_0_112px] w-28">{{ $t('Shipping:') }}</div>
      <div
        class="el-tag el-tag--info el-tag--small el-tag--dark rounded-xl capitalize text-white px-2"
        :class="{
          'bg-indigo-500': saleOrder.shipping_method == 'standard',
          'bg-emerald-500': saleOrder.shipping_method == 'express',
          'bg-orange-500': saleOrder.shipping_method == 'priority',
          '!text-black bg-yellow-500 ': ![
            'standard',
            'express',
            'priority',
          ].includes(saleOrder.shipping_method),
        }"
      >
        {{ saleOrder.shipping_method || '' }}
      </div>
    </div>

    <div class="flex">
      <div class="text-gray-600 flex-[0_0_112px] w-28">
        {{ $t('Return Address:') }}
      </div>
      <AddressInfo
        :saleOrder="saleOrder"
        :address="returnAddress"
        @onUpdate="$emit('refresh')"
      />
    </div>
    <div class="flex mb-1">
      <div class="text-gray-600 flex-[0_0_112px] w-28">
        {{ $t('Ship Address:') }}
      </div>
      <div class="text-gray-600" v-if="saleOrder">
        <AddressInfo
          :saleOrder="saleOrder"
          :address="address"
          @onUpdate="$emit('refresh')"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { clone, includes } from 'ramda';
import { countries, fetchStateByCountry } from '@/api/default.js';
import { verifyAddressByAddressId } from '@/api/saleOrder';
import AddressForm from '@/modules/sale-order/components/AddressForm.vue';
import AddressInfo from '@/modules/labeling/components/AddressInfo.vue';
import { mapGetters } from 'vuex';
import LabelChip from '@/components/LabelChip.vue';
import SignTag from '@/components/SignTag.vue';

export default {
  name: 'AddressOrder',
  components: {
    AddressForm,
    AddressInfo,
    LabelChip,
    SignTag,
  },
  props: {
    saleOrder: {
      type: Object,
    },
  },
  data() {
    return {
      isLoadingComponent: false,
      isLoading: false,
      isLoadingVefifyAddress: false,
      address: {
        order_id: '',
        name: '',
        email: '',
        company: '',
        phone: '',
        street1: '',
        street2: '',
        city: '',
        state: '',
        zip: '',
        residential: 0,
        verified_status: 0,
        verified_message: '',
      },
      openDialogEditAddress: false,
      addressCached: {},
      countries: [],
      states: [],
      currentState: '',
      currentCountry: '',
      isValidateAddress: false,
      validateAddress: '',
      returnAddress: {},
      returnAddressCached: {},
      sourceTag: 'sale_order',
    };
  },
  computed: {
    isVerifyAddress() {
      return !![0, 2, 3].includes(this?.address.verified_status);
    },
    ...mapGetters(['getTags', 'getUsStates']),
    buildTags() {
      let tags = [];
      let saleOrderTag = this.saleOrder?.tag;
      if (!saleOrderTag) {
        return tags;
      }
      saleOrderTag = saleOrderTag.split(',');
      for (let i = 0; i < saleOrderTag.length; i++) {
        const item = saleOrderTag[i];
        const tagItem = this.getTags.find((tag) => +tag.id === +item);
        if (!tagItem) continue;
        tags.push(tagItem);
      }
      return tags;
    },
  },
  beforeUnmount() {},
  created() {},
  watch: {
    saleOrder: {
      handler() {
        this.getInfoAddress();
        this.mapDataInfoAddress();
      },
      deep: true,
    },
  },
  mounted() {
    this.fetchData();
    this.fetchTag();
  },
  methods: {
    async fetchTag() {
      await this.$store.dispatch('getTags', { source: this.sourceTag });
    },
    onUpdate() {
      this.$emit('refresh');
      this.openDialogEditAddress = false;
    },
    async fetchCountries() {
      const res = await countries();
      this.countries = res.data || [];
    },
    async fetchStateByCountry(id) {
      if (this.getUsStates?.length) {
        this.states = this.getUsStates;
        return;
      }
      const res = await fetchStateByCountry(id);
      this.states = res.data || [];
    },
    async changeCountry() {
      this.address.state_id = '';
      this.address.state = '';
      await this.fetchStateByCountry(this.address.country_id);
    },
    async fetchData() {
      this.isLoadingComponent = true;
      await this.fetchCountries();
      this.getInfoAddress();
      this.isLoadingComponent = false;
    },
    async getInfoAddress() {
      const saleOrder = clone(this.saleOrder);
      const addressSaleOrder = saleOrder.address_sale_order || [];
      const toAddress =
        (addressSaleOrder &&
          addressSaleOrder.length &&
          addressSaleOrder.find(
            (item) => item.type_address === 'to_address'
          )) ||
        {};
      const address = clone(toAddress);
      this.address = {
        ...this.address,
        ...address,
        order_id: saleOrder.id,
        address_id: address?.id,
        id: saleOrder.id,
      };
      this.addressCached = clone(this.address);
      // return address
      const returnAddress =
        (addressSaleOrder &&
          addressSaleOrder.length &&
          addressSaleOrder.find(
            (item) => item.type_address === 'return_address'
          )) ||
        {};
      this.returnAddress = {
        ...returnAddress,
        order_id: saleOrder.id,
        address_id: returnAddress?.id,
        id: saleOrder.id,
        default: returnAddress.default || false,
      };
      this.returnAddressCached = clone(this.returnAddress);
    },
    showValidateAddress() {
      this.isValidateAddress = !this.isValidateAddress;
      this.validateAddress = '';
      this.$nextTick(() => {
        if (this?.$refs?.validateAddress) {
          this.$refs.validateAddress.focus();
        }
      });
    },
    async verifyAddress() {
      this.isLoadingVefifyAddress = true;
      try {
        this.isLoadingVefifyAddress = false;
        await verifyAddressByAddressId(this.address.address_id);
        this.$emit('refresh');
        this.notification(this.$t('Verify address successfully.'));
      } catch (e) {
        this.isLoadingVefifyAddress = false;
        this.notification(
          e.response?.data?.message || 'Verify address error.',
          'error'
        );
      }
    },
    async mapDataInfoAddress() {
      const country = this.countries.find(
        (item) => item.iso2 === this.address.country
      );
      if (country && country.id) {
        this.address.country_id = country && country.id;
        await this.fetchStateByCountry(this.address.country_id);
        const state = this.states.find(
          (item) => item.iso2 === this.address.state
        );
        this.address.state_id = state && state.id;
      }
    },
  },
};
</script>

<style scoped></style>
