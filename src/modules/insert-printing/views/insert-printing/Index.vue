<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head mb-3">
      <div class="top-head-left"><h1>{{ $t('Insert Printing') }}</h1></div>
    </div>
    <div class="flex">
      <div style="min-width: 300px;" class="w-1/4">
        <el-form class="mb-3" ref="scanLabel">
          <div>
            <el-form-item v-show="!Object.keys(employee).length" :error="employeeError">
              <el-input size="large" v-model.trim="employeeID" @keyup.enter="scanEmployeeID"
                        :placeholder="$t('Scan Employee ID')"></el-input>
            </el-form-item>
            <div v-if="Object.keys(employee).length" class="bg-gray-50 p-3 border rounded mb-3">
              <div class="flex justify-between">
                <b class="text-base">Hi {{ employee.name }}, Happy Producing!</b>
                <el-link type="danger" style="min-width: 60px;" @click="resetEmployee" :underline="false">{{ $t('Logout') }}</el-link>
              </div>
              <div class="text-lg text-indigo-500">
                <IncrementTimer/>
              </div>
            </div>
          </div>
          <div class="scan-sku mb-3">
            <el-form-item :error="labelIdError">
              <el-input
                  :placeholder="$t('Scan Label ID')"
                  class="mt-2 el-form-item-tracking-number border-stone-500	w-full"
                  size="large"
                  ref="scanLabelId"
                  v-model="label_id"
                  @keyup.enter="getScanLabel()"
              />
            </el-form-item>
          </div>
          <div v-if="saleOrderBarcode">
            <div class="flex flex-wrap justify-center w-full" v-if="saleOrderBarcode?.iconProduct">
              <div v-for="(item, index) in saleOrderBarcode?.iconProduct">
                <el-image
                    lazy="true"
                    class="mr-1"
                    style="width: 70px; height: 70px;"
                    :src="getImageUrl(item)"
                    :fit="fit"
                ></el-image>
              </div>
            </div>
          </div>
        </el-form>
      </div>
      <div class="w-2/4" v-if="saleOrderBarcode">
        <div class="px-4">
          <address-order v-loading="isLoading" v-if="saleOrderBarcode?.order" @refresh="getScanLabelWithFlag"
                         :saleOrder="saleOrderBarcode?.order"></address-order>
          <div v-loading="isLoading" class="border rounded">
            <div class="card-header"></div>
            <div>
              <div v-if="saleOrderImage">
                <template v-for="itemOrder in saleOrderImage">
                <div class="flex items-center px-3 pt-3 justify-between">
                  <div class="mr-2 grow"><strong>Insert file</strong></div>
                  <div class="grow" v-if="saleOrderBarcode?.printed_at">
                    <strong>{{ $t("Printed: ") + formatTime(saleOrderBarcode?.printed_at)}}</strong>
                  </div>
                  <div class="flex">
                    <el-select v-model="printer" class="mr-3 print-select" placeholder="Select Printer" >
                      <el-option
                          v-for="item in printers"
                          :key="item.name"
                          :label="item.displayName"
                          :value="item.name"
                      />
                    </el-select>
                    <el-button @click="printLabelInsert(itemOrder)" type="primary" v-if="saleOrderBarcode?.printed_at"><span><icon :data="iconPrinterLine"/></span> Reprint </el-button>
                    <el-button @click="printLabelInsert(itemOrder)" type="primary" v-else><span><icon :data="iconPrinterLine"/></span> Print </el-button>
                  </div>
                </div>
<!--                  :style="{height: ((maxHeight / 2)) + 'px'}"-->
                  <iframe v-if="itemOrder.image_ext == 'pdf'" class="m-0 p-2  w-full"
                          style="height: 700px"
                          ref="fragment" id="myIFrame" @load="loadedIframe"
                          :src="itemOrder.image_url"></iframe>
                  <img v-else class="w-auto h-auto ml-auto mr-auto" :src="itemOrder.image_url">
                </template>
              </div>
              <div v-else>
                <el-empty :description="$t('There Are No Label Information')">
                  <template #image>

                  </template>
                </el-empty>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="!saleOrderBarcode" class="border rounded ml-4 w-full">
        <el-empty :description="$t('Please scan Label ID')">
          <template #image>
            <img src="@/assets/images/scan-label-id.png">
          </template>
        </el-empty>
      </div>
    </div>
  </div>
</template>
