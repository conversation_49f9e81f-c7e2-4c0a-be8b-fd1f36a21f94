import {employeeLogoutTimeChecking, employeeTimeChecking} from "@/api/employee.js";
import {getOrderByLabelId} from "@/api/saleOrder.js";
import IncrementTimer from "@/components/IncrementTimer.vue";
import AddressOrder from "../../components/AddressOrder.vue";
import EventBus from "@/utilities/eventBus.js";
import {API_URL, STORAGE_URL} from "@/utilities/constants";
import {getListPrinters, print} from "@/api/label.js";
import {getlabel, logPrintedSuccess} from "@/api/insertPrinting.js";
import dateMixin from "@/mixins/date";

export default {
    name: "LabelShipping",
    components: {
        IncrementTimer,
        AddressOrder,
    },
    mixins: [dateMixin],
    data() {
        return {
            isLoading: false,
            codeEmployee: "",
            label_id: '',
            labelIdError: '',
            employeeID: '',
            employee: {},
            employeeError: '',
            job_type: "insert_printing",
            id_time_checking: null,
            dataRules: {
                label_id: [
                    {
                        required: true,
                        message: this.$t('This field cannot be left blank.'),
                        trigger: "change",
                    },
                ],
            },
            saleOrderBarcode: null,
            saleOrderImage: '',
            shipmentId: '',
            printers: [],
            printer: null,
            reprint: '',
            labelFlagId: '',
            typeFilePrinter: null,
            dialogPrePrintLabel: false,
            urlStorage: STORAGE_URL,
        };
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight);
        }
    },
    beforeUnmount() {
    },
    mounted() {
        this.listPrinters()
        this.loadLabel()
        // this.fetchStateByUsId();
    },
    async created() {
        this.printer = this.getOldPrinterIsUsed();
    },
    methods: {
        getOldPrinterIsUsed() {
            return (
                window.localStorage.getItem(`printer_insert`) || null
            );
        },
        getImageUrl(item){
            return this.urlStorage + '/IconProduct/' + item
        },
        async fetchStateByUsId() {
            await this.$store.dispatch("fetchStateByUs")
        },
        async loadLabel() {
            this.printer = window.localStorage.getItem('printer_insert');
        },
        async listPrinters() {
            getListPrinters().then((res) => {
                if (res.data?.status == 'Success') {
                    this.printers = res.data.data
                }
            })
        },
        async scanEmployeeID() {
            if (this.id_time_checking) {
                return true;
            }
            if (!this.employeeID) {
                this.employeeError = "Employee ID field cannot be left blank.";
                return false;
            }
            const res = await employeeTimeChecking({
                code: Number(this.employeeID),
                job_type: this.job_type
            })
            if (!res.data.data) {
                this.employeeError = "Can't find your employee ID, please scan again";
                return false;
            }
            this.employeeError = "";
            this.employee = res.data.data;
            this.id_time_checking = res.data.id_time_checking
            this.$refs.scanLabelId.focus();
            return true;
        },
        async resetEmployee() {
            const res = await employeeLogoutTimeChecking(this.id_time_checking)
            this.employee = {}
            this.employeeError = ""
            this.codeEmployee = ""
            this.id_time_checking = null
            this.employeeID = ''
            this.saleOrderBarcode = null
            this.label_id = ''
            this.saleOrderImage = ''
        },
        async getScanLabel() {
            this.typeFilePrinter = null;
            if (!this.scanEmployeeID()) return;
            if (!this.label_id) {
                this.labelIdError = "Employee ID field cannot be left blank.";
                return;
            }
            this.isLoading = true;
            this.labelIdError = "";
            this.saleOrderBarcode = {}
            this.saleOrderImage = ''
            this.shipmentId = ''
            this.labelFlagId = ''
            try {
                const res = await getlabel({
                    label_id: this.label_id,
                    time_tracking_id: this.id_time_checking
                })
                this.saleOrderBarcode = res.data;
                this.isLoading = false;
                this.labelFlagId = this.label_id;
                this.label_id = ''
                this.saleOrderImage = this.saleOrderBarcode?.sale_order_item_image
               await this.printLabelInsert(this.saleOrderImage[0])
            } catch (e) {
                this.saleOrderBarcode = null
                const data = e.response.data;
                this.isLoading = false;
                this.label_id = ''
                let message = data?.message || this.$t('Something went wrong. Try again.');
                this.notification(message, "error");
                this.$refs.scanLabelId.focus();
            }
        },
        PrintDiv() {
            var contents = document.getElementById('myIFrame').innerHTML;
            var frame1 = document.createElement('iframe');
            frame1.name = "frame1";
            frame1.style.position = "absolute";
            frame1.style.top = "-1000000px";
            document.body.appendChild(frame1);
            var frameDoc = frame1.contentWindow ? frame1.contentWindow : frame1.contentDocument.document ? frame1.contentDocument.document : frame1.contentDocument;
            frameDoc.document.open();
            frameDoc.document.write(`<html><head><title>'Label shipment'</title><style>@media print {
    @page {
        size: 6in 4in landscape;
        margin: 0in 0in 0in 0in;
    }
    img {
    width: 4in;
    height: 6in;
    }
    #printable {
        display: block;
        rotation:90deg;
    }
}</style>`);
            frameDoc.document.write('</head><body>');
            frameDoc.document.write(contents);
            frameDoc.document.write('</body></html>');
            frameDoc.document.close();
            setTimeout(function () {
                window.frames["frame1"].focus();
                window.frames["frame1"].print();
                document.body.removeChild(frame1);
            }, 500);
        },
        resetDataScanLabel(){
            this.saleOrderBarcode =  null,
            this.saleOrderImage =  '',
            this.shipmentId =  '',
            this.reprint =  '',
            this.shipmentRefundId =  null
            this.label_id = ''
            this.$refs.scanLabelId.focus();
        },
        focusByElClassInputPass(elClass = "print-select") {
            this.$nextTick(() => {
                const el = document.getElementsByClassName(elClass);
                if (!el || !el.length) {
                    return;
                }
                const firtsElError = (el && el[0]) || undefined;
                const input = firtsElError.querySelector("select");
                document.body.scrollTop = (input.offsetTop - 120) | 0;
                input.focus();
            });
        },
        focusToScanLabel(){
            this.$refs.scanLabelId.focus();
        },
        async printLabelInsert(item) {
            this.isLoading = true;
            if (!this.printer) {
                this.isLoading = false;
                return this.notification(this.$t('Please select Printer'), 'error');
            }
            try {
                const url = item.url_s3;
                const nameFile = item.id + '.pdf';
                const res = await print({
                    url: url,
                    file: nameFile,
                    printer: this.printer,
                    paper: item?.product?.size,
                });
                if (res && res?.data?.status === 'Success') {
                    const barcodePrinted = await this.logPrintedSuccess(this.saleOrderBarcode.label_id);
                    this.saleOrderBarcode?.printed_at ? this.notification(this.$t('Reprint Success'), 'success') : this.notification(this.$t('Print Success'), 'success');
                    this.saleOrderBarcode.printed_at = barcodePrinted?.data?.data.printed_at;
                } else {
                    throw new Error('Print Failed');
                }
                window.localStorage.setItem(`printer_insert`,this.printer);
            } catch (e) {
                this.notification(this.$t('Print Failed'), 'error');
            } finally {
                this.isLoading = false;
                this.focusToScanLabel();
            }
        },
        handlePrint() {
            this.printLabel();
        },
        async logPrintedSuccess(labelId) {
            try {
               return await logPrintedSuccess({
                    label_id: labelId,
                    time_tracking_id: this.id_time_checking,
                    employee_id : this.employee?.id
                });
            } catch (e) {
                this.notification(this.$t('Log insert printing failed'), 'error');
            }
        }
    }
};
