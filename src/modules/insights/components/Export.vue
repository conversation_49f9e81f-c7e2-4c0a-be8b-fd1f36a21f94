<template>
  <div>
    <el-dialog
      v-model="openInvoicingExport"
      :title="$t('Export Sale Items By Print Side')"
      custom-class="el-dialog-export el-dialog-custom "
      :close-on-click-modal="false"
    >
      <template #default>
        <div>
          <div class="mb-1">
            {{ $t('Select date before click export') }}
          </div>
          <div class="flex">
            <div class="mr-2">
              <el-select
                filterable
                v-model="storeId"
                :placeholder="$t('Select store')"
              >
                <el-option
                  v-for="item in getStores"
                  :key="item.id"
                  :label="item.name"
                  :value="String(item.id)"
                >
                </el-option>
              </el-select>
            </div>
            <div class="mr-2">
              <el-date-picker
                class="label-filter"
                format="YYYY-MM-DD"
                v-model="orderDate"
                @change="onChangeDate('orderDate')"
                type="daterange"
                range-separator="To"
                :start-placeholder="$t('Start date')"
                :end-placeholder="$t('End date')"
              >
              </el-date-picker>
            </div>
            <div>
              <el-button
                :disabled="!orderDate"
                type="primary"
                @click="exportToExcel"
              >
                <span class="icon-margin-right">
                  <icon :data="iconExport" /> </span
                >{{ $t('Export') }}
              </el-button>
            </div>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>

  <div class="export-overview">
    <el-dialog
      v-model="openDialogExport"
      :title="modalTitle"
      custom-class="el-dialog-custom el-dialog-box"
      width="35%"
      @close="onClose"
    >
      <template #default>
        <div class="pt-3 pb-3">
          <el-form-item
            v-show="!isExportActive"
            :label="$t('Password')"
            required
          >
            <el-input
              type="password"
              v-model="passwordExport"
              @keyup.enter="checkPasswordExport"
            ></el-input>
          </el-form-item>

          <div v-if="isExportActive">
            <div
              class="block"
              v-if="
                typeExport == 'inventory' || typeExport == 'supply_inventory'
              "
            >
              <el-date-picker
                v-model="date"
                @change="onChangeDate('date')"
                type="month"
                class="custom-date-picker label-filter"
                format="YYYY-MM"
                placeholder="Select date"
                :disabled-date="disabledMonth"
              />
            </div>
            <div class="block" v-else>
              <el-date-picker
                v-model="date"
                @change="onChangeDate('date')"
                type="daterange"
                class="custom-date-picker label-filter"
                format="YYYY-MM-DD"
                start-placeholder="Start date"
                end-placeholder="End date"
                range-separator="To"
                :disabled-date="disabledDate"
              />
            </div>
            <el-progress
              v-if="loadingExport"
              :show-text="false"
              class="mt-3"
              :percentage="50"
              :indeterminate="true"
              :text-inside="true"
            />
          </div>
        </div>
      </template>

      <template #footer class="mt-3">
        <span class="dialog-footer">
          <el-button
            type="primary"
            :disabled="!date || loadingExport"
            @click="exportData()"
            >Export</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>

  <div>
    <el-dialog
      v-model="openInsertOrderExport"
      :title="$t('Export Insert Order')"
      custom-class="el-dialog-export el-dialog-custom "
      :close-on-click-modal="false"
    >
      <template #default>
        <div>
          <div class="mb-1">
            {{ $t('Select date before click export') }}
          </div>
          <div class="flex">
            <div class="mr-2">
              <el-select
                filterable
                v-model="storeId"
                multiple
                :placeholder="$t('Select store')"
              >
                <el-option
                  v-for="item in getStores"
                  :key="item.id"
                  :label="item.name"
                  :value="String(item.id)"
                >
                </el-option>
              </el-select>
            </div>
            <div class="mr-2">
              <el-date-picker
                class="label-filter"
                format="YYYY-MM-DD"
                v-model="orderDate"
                @change="onChangeDate('orderDate')"
                type="daterange"
                range-separator="To"
                :start-placeholder="$t('Start date')"
                :end-placeholder="$t('End date')"
              >
              </el-date-picker>
            </div>
            <div>
              <el-button
                :disabled="!orderDate"
                type="primary"
                @click="exportInsert"
              >
                <span class="icon-margin-right">
                  <icon :data="iconExport" /> </span
                >{{ $t('Export') }}
              </el-button>
            </div>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>

  <div>
    <el-dialog
      v-model="openIPViolationOrderExport"
      :title="$t('Export IP Violation Order')"
      custom-class="el-dialog-export el-dialog-custom "
      :close-on-click-modal="false"
      @close="onCloseIPViolationOrder"
    >
      <template #default>
        <div>
          <div class="mb-1">
            {{ $t('Select date before click export') }}
          </div>
          <div class="flex">
            <div class="mr-2">
              <el-select
                filterable
                v-model="storeId"
                multiple
                :placeholder="$t('Select store')"
              >
                <el-option
                  v-for="item in getStores"
                  :key="item.id"
                  :label="item.name"
                  :value="String(item.id)"
                >
                </el-option>
              </el-select>
            </div>
            <div class="mr-2">
              <el-date-picker
                class="label-filter"
                format="YYYY-MM-DD"
                v-model="orderDate"
                @change="onChangeDate('orderDate')"
                type="daterange"
                range-separator="To"
                :start-placeholder="$t('Start date')"
                :end-placeholder="$t('End date')"
              >
              </el-date-picker>
            </div>
            <div>
              <el-button
                :disabled="!orderDate"
                type="primary"
                @click="exportIPViolationOrder"
              >
                <span class="icon-margin-right">
                  <icon :data="iconExport" /> </span
                >{{ $t('Export') }}
              </el-button>
            </div>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>

  <div>
    <el-dialog
      v-model="openSaleReportI"
      :title="$t('Export Sale Report I')"
      custom-class="el-dialog-export el-dialog-custom "
      :close-on-click-modal="false"
      @close="onCloseSaleReportI"
    >
      <template #default>
        <div>
          <div class="mb-1">
            {{ $t('Select date before click export') }}
          </div>
          <div class="flex">
            <div class="mr-2">
              <el-select
                filterable
                v-model="storeId"
                multiple
                :placeholder="$t('All')"
              >
                <el-option
                  v-for="item in getStores"
                  :key="item.id"
                  :label="item.name"
                  :value="String(item.id)"
                >
                </el-option>
              </el-select>
            </div>
            <div class="mr-2">
              <el-date-picker
                class="label-filter"
                format="YYYY-MM-DD"
                v-model="orderDate"
                @change="onChangeDate('orderDate')"
                type="daterange"
                range-separator="To"
                :start-placeholder="$t('Start date')"
                :end-placeholder="$t('End date')"
              >
              </el-date-picker>
            </div>
            <div>
              <el-button
                :disabled="!orderDate"
                type="primary"
                @click="exportSaleReportI"
              >
                <span class="icon-margin-right">
                  <icon :data="iconExport" /> </span
                >{{ $t('Export') }}
              </el-button>
            </div>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>

  <div>
    <el-dialog
      v-model="openQuantityReport"
      :title="modalTitle"
      custom-class="el-dialog-custom el-dialog-box"
      :width="isExportActive ? '' : '35%'"
      @close="onCloseQuantityReport"
      :close-on-click-modal="false"
    >
      <template #default>
        <div class="pt-3 pb-3" v-show="!isExportActive">
          <el-form-item :label="$t('Password')" required>
            <el-input
              type="password"
              v-model="passwordExport"
              @keyup.enter="checkPasswordExport"
            ></el-input>
          </el-form-item>
        </div>
        <div v-show="isExportActive">
          <div class="mb-1">
            {{ $t('Select date before click export') }}
          </div>
          <div class="flex">
            <div class="mr-2">
              <el-select
                filterable
                v-model="warehouseId"
                multiple
                :placeholder="$t('Select Warehouse')"
              >
                <el-option
                  v-for="item in getUserWarehouses"
                  :key="item.id"
                  :label="item.name"
                  :value="String(item.id)"
                >
                </el-option>
              </el-select>
            </div>
            <div class="mr-2">
              <el-date-picker
                class="label-filter"
                format="YYYY-MM"
                v-model="orderDate"
                type="monthrange"
                :disabled-date="disableFutureDates"
                range-separator="To"
                :start-placeholder="$t('Start date')"
                :end-placeholder="$t('End date')"
              >
              </el-date-picker>
            </div>
            <div>
              <el-button
                :disabled="!orderDate"
                type="primary"
                @click="exportDataPrintQuantity"
              >
                <span class="icon-margin-right">
                  <icon :data="iconExport" /> </span
                >{{ $t('Export') }}
              </el-button>
            </div>
          </div>
          <el-progress
            v-if="loadingExport"
            :show-text="false"
            class="mt-3"
            :percentage="50"
            :indeterminate="true"
            :text-inside="true"
          />
        </div>
      </template>
    </el-dialog>
  </div>
  <el-dialog
    v-model="openShippedSkuReport"
    :title="$t('Export Shipped SKUs Report')"
    custom-class="el-dialog-export el-dialog-custom "
    :close-on-click-modal="false"
  >
    <template #default>
      <div>
        <div class="mb-1">
          {{ $t('Select date before click export') }}
        </div>
        <div class="flex">
          <div class="mr-2">
            <el-select
              filterable
              v-model="storeId"
              :placeholder="$t('Select store')"
            >
              <el-option
                v-for="item in getStores"
                :key="item.id"
                :label="item.name"
                :value="String(item.id)"
              >
              </el-option>
            </el-select>
          </div>
          <div class="mr-2">
            <el-date-picker
              class="label-filter"
              format="YYYY-MM-DD"
              v-model="date"
              @change="onChangeDate('date')"
              type="daterange"
              range-separator="To"
              :start-placeholder="$t('Start date')"
              :end-placeholder="$t('End date')"
            >
            </el-date-picker>
          </div>
          <div>
            <el-button
              :disabled="!date || !storeId"
              type="primary"
              @click="submitShippedSkuReport"
            >
              <span class="icon-margin-right">
                <icon :data="iconExport" /> </span
              >{{ $t('Export') }}
            </el-button>
          </div>
        </div>

        <h3 class="table-title my-4">Reports</h3>

        <el-table
          border
          stripe size="small" :data="listShippedSkuReport"
          style="width: 100%"
          :max-height="maxHeight"
          v-loading="isLoading"
          element-loading-text="Loading..."
        >
          <el-table-column  prop="first_box" :label="$t('Store')" min-width="40">
            <template #default="scope">
                {{ scope.row.store_id }}
            </template>
          </el-table-column>
          <el-table-column prop="last_box" :label="$t('From - To')" min-width="100">
            <template #default="scope">
                {{ scope.row.date }}
            </template>
          </el-table-column>
          <el-table-column prop="created_at" :label="$t('Created At')" >
          <template #default="scope">
            {{ convertToPST(scope.row.created_at) }}
          </template>
          </el-table-column>
          <el-table-column prop="status" :label="$t('status')" min-width="50">
            <template #default="scope">
              <span  
                class="mx-auto block text-center"
                :class="{
                  'bg-green-500 text-white': scope.row.status === 1,  // Green for Completed
                  'bg-orange-400 text-white': scope.row.status === 0, // Orange for Generating
                  'rounded': true  // Padding and border (rounded corners)
                }"
              >
                {{ scope.row.status === 1 ? 'Completed' : 'Generating' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="Action">
            <template #default="scope">
              <div class="flex justify-center">
                <a :href="scope.row.link_url" download style="text-decoration: none;">
                  <el-button type="primary" size="small" class="text-xs px-3 py-1">
                    Download
                  </el-button>
                </a>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="dialog-footer mt-4">
          <div class="bottom">
            <div class="total">{{$t('Total:')}} {{ listShippedSkuReport.length ? formatNumber(total) : 0 }}</div>
            <div class="flex justify-center items-center"> <!-- Flex container to center content -->
              <el-pagination
                :disabled="isLoading"
                background
                layout="prev, pager, next"
                :page-size="filter.limit"
                :total="total"
                @current-change="changePage"
                v-model:currentPage="filter.page"
              >
              </el-pagination>
            </div>
          </div>
        </div>

    </template>

  </el-dialog>
</template>

<script>
import EventBus from '@/utilities/eventBus.js';
import { mapGetters } from 'vuex';
import dateMixin from '@/mixins/date.js';
import { API_URL } from '@/utilities/constants';
import moment from 'moment';
import {
  report,
  reportPurchaseByVendor,
  reportSaleByCustomer,
} from '@/api/inventory';
import { exportSupplyReport } from '@/api/supplyInventory';
import warehouseMixin from '@/mixins/warehouse';
import { generateShippedSKUInvoice, getListInvoiceTemp } from '@/api/invoice';

export default {
  name: 'InvoicingExport',
  mixins: [dateMixin, warehouseMixin],
  props: {},
  computed: {
    ...mapGetters(['getStores']),
    buildLinkDownload() {
      let link = `${API_URL}/sale-order-item/export`;
      let params = {
        store_id: this.storeId,
        order_date_start: '',
        order_date_end: '',
      };

      if (this.orderDate && this.orderDate.length) {
        params.order_date_start = this.formatDate(this.orderDate[0], false);
        params.order_date_end = this.formatDate(this.orderDate[1], false);
      }

      params = new URLSearchParams(params);

      return `${link}?${params.toString()}`;
    },

    buildLinkDownloadInsert() {
      let link = `${API_URL}/order-insert/export`;
      let params = {
        store_id: this.storeId,
        start_date: '',
        end_date: '',
      };

      if (this.orderDate && this.orderDate.length) {
        params.start_date = this.formatDate(this.orderDate[0], false);
        params.end_date = this.formatDate(this.orderDate[1], false);
      }

      params = new URLSearchParams(params);

      return `${link}?${params.toString()}`;
    },

    buildLinkDownloadIPViolationOrder() {
      let link = `${API_URL}/sale-order/ip-violation-export`;
      let params = {
        store_ids: this.storeId,
        start_date: '',
        end_date: '',
      };

      if (this.orderDate && this.orderDate.length) {
        params.start_date = this.formatDate(this.orderDate[0], false);
        params.end_date = this.formatDate(this.orderDate[1], false);
      }

      params = new URLSearchParams(params);

      return `${link}?${params.toString()}`;
    },

    buildLinkDownloadSaleReportI() {
      let link = `${API_URL}/sale-order/sales-report`;
      let params = {
        store_ids: this.storeId,
        start_date: '',
        end_date: '',
      };

      if (this.orderDate && this.orderDate.length) {
        params.start_date = this.formatDate(this.orderDate[0], false);
        params.end_date = this.formatDate(this.orderDate[1], false);
      }

      params = new URLSearchParams(params);

      return `${link}?${params.toString()}`;
    },
  },

  data() {
    return {
      orderDate: '',
      storeId: '',
      openInvoicingExport: false,
      isLoading: false,
      openDialogExport: false,
      isExportActive: false,
      passwordExport: null,
      date: null,
      openInsertOrderExport: false,
      openIPViolationOrderExport: false,
      openSaleReportI: false,
      openQuantityReport: false,
      openShippedSkuReport: false,
      warehouseId: null,
      loadingExport: false,
      percentage: 0,
      fileExports: this.setDefaultExports(),
      modalTitle: '',
      listShippedSkuReport: [],
      filter: this.setDefaultFilter(),
      total: 0,
      typeExports: [
        {
          label: 'Inventory Report',
          value: 'inventory',
        },
        {
          label: 'Monthly purchase by style and vendor',
          value: 'purchase_by_vendor',
        },
        {
          label: 'Monthly sale by style and customer',
          value: 'purchase_by_customer',
        },
      ],
      typeExport: 'inventory',
    };
  },

  created() {
    EventBus.$on('invoicingExport', () => {
      this.storeId = '';
      this.orderDate = '';
      this.openInvoicingExport = true;
    });
    EventBus.$on('openInventoryExport', () => {
      this.openDialogExport = true;
      this.typeExport = 'inventory';
      this.modalTitle = 'Export Inventory FIFO Report by Month';
    });
    EventBus.$on('openSupplyInventoryExport', () => {
      this.openDialogExport = true;
      this.typeExport = 'supply_inventory';
      this.modalTitle = 'Export Supply Inventory Report';
    });
    EventBus.$on('openMonthlyExport', () => {
      this.openDialogExport = true;
      this.typeExport = 'purchase_by_vendor';
      this.modalTitle = 'Export Monthly Purchase by Style and Vendor Report';
    });
    EventBus.$on('openSalesExport', () => {
      this.openDialogExport = true;
      this.typeExport = 'purchase_by_customer';
      this.modalTitle = 'Export Monthly Sales by Style and Customer Report';
    });
    EventBus.$on('openInsertOrderExport', () => {
      this.openInsertOrderExport = true;
    });
    EventBus.$on('openIPViolationOrderExport', () => {
      this.openIPViolationOrderExport = true;
    });
    EventBus.$on('openSaleReportI', () => {
      this.openSaleReportI = true;
      this.storeId = '';
      this.orderDate = '';
    });
    EventBus.$on('openQuantityReport', () => {
      this.openQuantityReport = true;
      this.modalTitle = 'Export Print Quantity Report';
      this.passwordExport = '';
    });
    EventBus.$on('openShippedSkuReport', () => {
      this.openShippedSkuReport = true;
      this.storeId = '';
      this.orderDate = '';
      this.listShippedSkuReport = [];
      this.fetchShippedSkuReport();
    });
  },

  methods: {
    setDefaultFilter() {
      return {
        limit: 10,
        page: 1,
      };
    },
    onChangeDate(model) {
      //fix date change to right format
      if (Array.isArray(this[model])) {
        this[model] = this[model].map(d => {
          // Nếu là đối tượng có field `$d` (ví dụ như dayjs object)
          if (d && typeof d === 'object' && '$d' in d) {
            return d.$d;
          }
          return d; // Giữ nguyên nếu đã là Date
      });
  }
    
  },
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchShippedSkuReport();
      });
    },
    exportToExcel() {
      return (window.location.href = this.buildLinkDownload);
    },

    exportInsert() {
      return (window.location.href = this.buildLinkDownloadInsert);
    },

    exportIPViolationOrder() {
      return (window.location.href = this.buildLinkDownloadIPViolationOrder);
    },

    exportSaleReportI() {
      return (window.location.href = this.buildLinkDownloadSaleReportI);
    },

    async submitShippedSkuReport() {
      this.isLoading = true;
      try {
        let params = {
          store_id: this.storeId,
          start_date: moment(this.date[0]).format('YYYY-MM-DD'),
          end_date: moment(this.date[1]).format('YYYY-MM-DD'),
        };
        // let fileName = 'production_invoice_' + Date.now() + '.xlsx';
        await generateShippedSKUInvoice(params);

        await new Promise(resolve => setTimeout(resolve, 3000));

        this.fetchShippedSkuReport();
        // var blob = new Blob([response.data], {
        //   type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        // });
        // const url = window.URL.createObjectURL(blob);
        // const link = document.createElement('a');
        // link.href = url;
        // link.setAttribute('download', fileName);
        // document.body.appendChild(link);
        // link.click();
        // link.remove();
        this.isLoading = false;
      } catch (error) {
        this.isLoading = false;
        this.loadingExport = false;
        console.error(error.response.status);
        if(error.response.status === 429) {
          this.notification(
          this.$t('Please wait for at least 1 minute before making another request..'),
          'error'
        );

        } else {
          this.notification(
          this.$t('Something went wrong, please try again.'),
          'error'
        );
        }
      }
    },
    async fetchShippedSkuReport() {
      this.isLoading = true;

      const response = await getListInvoiceTemp(this.filter);
      const data = response.data.data || [];
      const total = response.data.total || 0;
      this.listShippedSkuReport = data;
      this.total = total;

      this.isLoading = false;
    },

    setDefaultExports() {
      let result = [];
      const months = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
      ];
      let d = new Date();

      for (let i = 1; i <= 1; i++) {
        d.setDate(0);
        let dateString =
          d.getFullYear() +
          '-' +
          (d.getMonth() < 9 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) +
          '-' +
          d.getDate();
        console.log(dateString);
        result.push({
          label: months[d.getMonth()] + ', ' + d.getFullYear(),
          value: '2022-01-01-' + dateString + '-fifo-',
        });
      }

      return result;
    },

    checkPasswordExport() {
      let d = new Date();
      let date = d.getDate();
      let month = d.getMonth() + 1;
      date =
        (month < 10 ? '0' + month : month.toString()) +
        (date < 10 ? '0' + date : date.toString());

      if (this.passwordExport.toUpperCase() == 'S' + date) {
        this.isExportActive = true;
      } else {
        console.log('S' + date);
        this.notification(this.$t('Password is incorrect!'), 'error');
      }
    },

    disabledDate(time) {
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      return (
        time.getTime() > yesterday.getTime() ||
        time.getTime() < new Date('2022-01-01 00:00:00').getTime()
      );
    },

    disabledMonth(time) {
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setMonth(yesterday.getMonth() - 1);

      return (
        time.getTime() > yesterday.getTime() ||
        time.getTime() < new Date('2022-01-01 00:00:00').getTime()
      );
    },

    onClose() {
      this.openDialogExport = false;
      this.isExportActive = false;
      this.loadingExport = false;
      this.passwordExport = null;
      this.date = null;
    },

    onCloseIPViolationOrder() {
      this.openIPViolationOrderExport = false;
      this.isExportActive = false;
      this.loadingExport = false;
      this.orderDate = null;
      this.storeId = null;
    },

    onCloseSaleReportI() {
      this.openSaleReportI = false;
      this.isExportActive = false;
      this.loadingExport = false;
      this.orderDate = null;
      this.storeId = null;
    },

    onCloseQuantityReport() {
      this.openQuantityReport = false;
      this.isExportActive = false;
      this.loadingExport = false;
      this.orderDate = null;
      this.warehouseId = null;
    },

    async exportData() {
      switch (this.typeExport) {
        case 'inventory':
          await this.exportDataInventory();
          break;
        case 'supply_inventory':
          await this.exportDataSupplyInventory();
          break;
        case 'purchase_by_vendor':
          await this.exportDataPurchaseByVendor();
          break;
        case 'purchase_by_customer':
          await this.exportDataPurchaseByCustomer();
          break;
        default:
          break;
      }
    },

    changeType() {
      this.date = null;
    },

    async exportDataInventory() {
      this.loadingExport = true;
      this.percentage = 0;
      let endDate = moment(this.date).endOf('month').format('YYYY-MM-DD');
      let startDate = moment(this.date).startOf('month').format('YYYY-MM-DD');
      let params = {
        end_date: endDate,
        start_date: startDate,
        warehouse_id: this.warehouseId,
        warehouse_code: this.userWarehouseCode,
      };
      const config = {
        responseType: 'arraybuffer',
        onDownloadProgress: (progressEvent) => {
          this.percentage = parseInt(
            Math.round((progressEvent.loaded / progressEvent.total) * 100)
          );
        },
      };

      try {
        const response = await report(params, config);
        var blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
        const dateTime = moment().tz('America/Los_Angeles').format('YYYYMMDD_HHmmss');
        const filename = `reversed_fifo_report_exported_${dateTime}.xlsx`;
        var downloadElement = document.createElement('a');
        var href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        downloadElement.download = filename;
        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);
      } catch (error) {
        console.log(error);
        this.notification('Export fail.', 'error');
      }

      this.loadingExport = false;
    },
    async exportDataSupplyInventory() {
      // Coding here
      this.loadingExport = true;
      this.percentage = 0;
      let yearMonth = moment(this.date).startOf('month').format('YYYY-MM');
      let monthName = moment(this.date).format('MMMM');
      let year = moment(this.date).year();
      let params = {
        year_month: yearMonth,
        warehouse_id: this.warehouseId,
        warehouse_code: this.userWarehouseCode,
      };
      const config = {
        responseType: 'arraybuffer',
        onDownloadProgress: (progressEvent) => {
          this.percentage = parseInt(
            Math.round((progressEvent.loaded / progressEvent.total) * 100)
          );
        },
      };
      try {
        const response = await exportSupplyReport(params, config);
        console.log(response);
        var blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
        var downloadElement = document.createElement('a');
        var href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        downloadElement.download = `${monthName}-${year}-SupplyInventory-${this.userWarehouseCode}.xlsx`;
        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);
      } catch (error) {
        this.notification('Export fail.', 'error');
      }

      this.loadingExport = false;
    },
    async exportDataPurchaseByVendor() {
      this.loadingExport = true;
      this.percentage = 0;
      let params = {
        start_date: moment(this.date[0]).format('YYYY-MM-DD'),
        end_date: moment(this.date[1]).format('YYYY-MM-DD'),
        warehouse_id: this.warehouseId,
        warehouse_code: this.userWarehouseCode,
      };
      const config = {
        responseType: 'arraybuffer',
        onDownloadProgress: (progressEvent) => {
          this.percentage = parseInt(
            Math.round((progressEvent.loaded / progressEvent.total) * 100)
          );
        },
      };

      try {
        const response = await reportPurchaseByVendor(params, config);
        var blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
        var downloadElement = document.createElement('a');
        var href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        downloadElement.download = `Monthly_Purchase_By_Style_Vendor_${params.start_date}_to_${params.end_date}_${this.userWarehouseCode}.xlsx`;
        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);
      } catch (error) {
        this.notification('Export fail.', 'error');
      }

      this.loadingExport = false;
    },

    async exportDataPurchaseByCustomer() {
      this.loadingExport = true;
      this.percentage = 0;
      console.log(this.date);
      let params = {
        start_date: moment(this.date[0]).format('YYYY-MM-DD'),
        end_date: moment(this.date[1]).format('YYYY-MM-DD'),
        warehouse_id: this.warehouseId,
        warehouse_code: this.userWarehouseCode,
      };
      console.log(params);
      
      const config = {
        responseType: 'arraybuffer',
        onDownloadProgress: (progressEvent) => {
          this.percentage = parseInt(
            Math.round((progressEvent.loaded / progressEvent.total) * 100)
          );
        },
      };

      try {
        const response = await reportSaleByCustomer(params, config);
        var blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
        var downloadElement = document.createElement('a');
        var href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        downloadElement.download = `Monthly_Sales_By_Style_and_Customer_${params.start_date}_to_${params.end_date}.xlsx`;
        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);
      } catch (error) {
        this.notification('Export fail.', 'error');
      }

      this.loadingExport = false;
    },

    async exportDataPrintQuantity() {
      this.loadingExport = true;
      let link = `${API_URL}/sale-order/quantity-print-area-report`;
      let params = {
        warehouse_ids: this.warehouseId,
        start_date: '',
        end_date: '',
      };

      if (this.orderDate && this.orderDate.length) {
        params.start_date = moment(this.orderDate[0]).format('YYYY-MM');
        params.end_date = moment(this.orderDate[1]).format('YYYY-MM');
      }

      params = new URLSearchParams(params);
      this.loadingExport = false;

      return (window.location.href = `${link}?${params.toString()}`);
    },

    disableFutureDates(date) {
      return date.getTime() > Date.now();
    },

    printPDF(item) {
      this.updatePrintStatus(item.id)
      this.dialogVisiblePrint = true;
      this.pdf = item.link_url;
    },
    convertToPST(date) {
      // Convert UTC+7 time to PST and format as "Nov 20, 2024 8:48 AM"
      return moment.tz(date, 'YYYY-MM-DD HH:mm:ss', 'Asia/Bangkok') // UTC+7
        .tz('America/Los_Angeles') // Convert to PST
        .format('MMM D, YYYY h:mm A'); // Format as "Nov 20, 2024 8:48 AM"
    }
  },
};
</script>

<style lang="scss">
.label-filter {
  width: -webkit-fill-available !important;
}
</style>
