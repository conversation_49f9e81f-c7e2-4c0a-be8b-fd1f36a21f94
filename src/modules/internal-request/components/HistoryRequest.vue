<template>
  <div class="w-full">

    <div class="">
      <el-tabs class="el-tab-filter" v-model="filter.tab" type="card">
        <el-tab-pane :label="item.name" :name="item.key" v-for="(item, index) in tabs" :key="index"
                     :disabled="filter.tab == item.key">
          <template #label>
                <span class="custom-tabs-label">
                    {{ item.name }}
                </span>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>
    <!-- Filter Block -->
    <el-row class="mt-6 gap-4 flex flex-nowrap w-full" align="middle">
      <span> Filter by: </span>
      <el-row style="width: 90%" class="flex gap-4">
        <el-col :span="4">
          <el-input
            placeholder="Scan SKU"
            v-model="filter.key_word"
            @input="handleChangeKeyWord"
          ></el-input>
        </el-col>
        <el-col :span="4">
          <el-input
            placeholder="Box ID"
            v-model="filter.box_id"
            @input="handleChangeKeyWord"
          ></el-input>
        </el-col>
        <el-col :span="4">
          <el-select
              placeholder="Status"
              multiple
              collapse-tags
              collapse-tags-tooltip
              v-model="filter.status"
              @change="handleFilter"
              class="w-full"
          >
            <el-option
                v-for="item in listStatus"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            placeholder="Employee"
            filterable
            v-model="filter.employee_id"
            @change="handleFilter"
            class="w-full"
          >
            <el-option
              v-for="item in getEmployees"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-col>
        <el-link type="danger" :underline="false" @click="resetFilter">
          {{ $t('Clear') }}
        </el-link>
      </el-row>
    </el-row>

    <!-- Table Block -->
    <el-row class="mt-3 w-full">
      <el-table
        border
        stripe
        size="small"
        :data="internalHistoryInfo?.data"
        :max-height="maxHeight"
        v-loading="isLoading"
        element-loading-text="Loading..."
        class="break-words"
      >
        <el-table-column
            prop="status"
            width="80"
            :label="$t('Status')"
        >
          <template #default="scope">
            <span
                :class="[
               scope.row.status == 'checked' ? 'text-[#207868]' : (scope.row.status == 'rejected' ? 'text-[#b1b2b7]' : 'text-[#F1C536]')
              ]"
            >
              {{ generateStatus(scope.row.status) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="action" :label="$t('Action')" width="100">
          <template #default="scope">
            <span class="text-[#207868]" v-if="scope.row.status == CHECKED"
            >{{ $t('Confirmed') }}
            </span>
            <el-button
                class="w-full"
                v-else-if="scope.row.status == UNCHECK"
                @click="openDialogConfirm(scope.row)"
                type="primary"
            >Confirm</el-button
            >
          </template>
        </el-table-column>
        <el-table-column
            prop="id"
            width="80"
            :label="$t('ID')"
        >
          <template #default="scope">
            <span>
              {{ scope.row.id_pad }}
            </span>

          </template>
        </el-table-column>
        <el-table-column
            prop="name"
            width="210"
            :label="$t('Name')"
        >
          <template #default="scope">
            {{ scope.row?.product?.name }}
          </template>
        </el-table-column>
        <el-table-column
            prop="created_at"
            width="100"
            :label="$t('Date')"
        >
          <template #default="scope">
            <el-tooltip
                class="box-item"
                effect="dark"
                :content="formatTime(scope.row.created_at, 'YYYY-MM-DD hh:mm:ss')"
                placement="top-start"
            >
              {{ this.formatTime(scope.row.created_at, 'YYYY-MM-DD') }}
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
            prop="sku"
            width="100"
            :label="$t('SKU')"
        >
          <template #default="scope">
            {{ scope.row?.product?.sku }}
          </template>
        </el-table-column>
        <el-table-column
            prop="barcode"
            width="100"
            :label="$t('Box ID')"
        >
          <template #default="scope">
            <span>
              {{ scope.row?.box?.barcode }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
            prop="pre_location"
            width="150"
            :label="$t('Pre Location')"
        >
          <template #default="scope">
            <span>
              {{ scope.row.status == CHECKED ? scope.row?.box_moving?.[1]?.pre_location?.barcode : scope.row?.box_moving?.[0]?.pre_location?.barcode}}
            </span>
          </template>
        </el-table-column>

        <el-table-column
            v-if="filter.tab == 'restricted'"
            prop="pre_location"
            width="150"
            :label="$t('New Location')"
        >
          <template #default="scope">
            <span>
              {{ scope.row.status == CHECKED ? scope.row?.box_moving?.[0]?.location?.barcode : ''}}
            </span>
          </template>
        </el-table-column>
        <el-table-column
            prop="request_by"
            width="150"
            :label="$t('Requested By')"
        >
          <template #default="scope">
            {{ scope.row?.employee_create?.name }}
          </template>
        </el-table-column>
        <el-table-column
            prop="pick_up_by"
            width="150"
            :label="$t('Picked Up By')"
        >
          <template #default="scope">
            <span>
              {{ scope.row?.employee_receive?.name }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
            prop="fulfill_by"
            width="150"
            :label="$t('Fulfilled By')"
        >
          <template #default="scope">
            <span>
              {{ scope.row?.employee_fulfill?.name }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
            prop="cancel_by"
            width="150"
            :label="$t('Rejected By')"
        >
          <template #default="scope">
            <span>
              {{ scope.row?.employee_reject?.name }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
            prop="confirm_by"
            width="150"
            :label="$t('Confirmed By')"
        >
          <template #default="scope">
            <span>
              {{ scope.row?.employee_confirm?.name }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </el-row>

    <!-- Paginate Block -->
    <el-row class="mt-6 w-full justify-between">
      <div class="total">
        {{ $t('Total:') }}
        {{ formatNumber(internalHistoryInfo?.total) }}
      </div>
      <el-pagination
        :disabled="isLoading"
        background
        layout="prev, pager, next"
        :page-size="filter.limit"
        :total="internalHistoryInfo?.total"
        @current-change="changePage"
        v-model:currentPage="filter.page"
      >
      </el-pagination>
      <div class="limit" :disabled="isLoading">
        <el-select
          v-model="filter.limit"
          :placeholder="$t('Select')"
          size="mini"
          @change="onFilter"
        >
          <el-option
            v-for="item in limits"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
    </el-row>
    <el-dialog
        v-model="dialogWarning"
        width="500px"
    >
      <div class="flex justify-center">
        <icon class="!text-red-700 !w-[40px] !h-[40px] mr-2" :data="iconError2"></icon>
        <span class=" text-center text-[25px]"> Location id not found</span>
      </div>
      <p class="text-center !break-words text-[17px]">By confirming a new Location ID will be created and the box will be marked as received.</p>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogWarning = false">Cancel</el-button>
          <el-button type="primary" @click="confirmRbt">
            Confirm
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="isOpenModal"
      :title="$t(`Confirm Request ${requestSelect?.id_pad}`)"
      custom-class="min-h-40 min-w-[400px] custom-dialog"
      @close="closeModal"
      destroy-on-close
      :close-on-click-modal="false"
      width="30%"
    >
      <template #default>
        <el-form
          :model="formData"
          :label-position="'top'"
          :rules="formRules"
          ref="confirmRequest"
          @keydown.enter.exact.prevent="event.preventDefault()"
        >
          <div class="w-full h-full flex flex-col rounded-sm">
            <el-form-item
              prop="employeeCode"
              :label="!employee ? 'Employee ID' : ''"
            >
              <div v-if="employee" class="bg-gray-50 p-3 border rounded w-full">
                <div class="flex justify-between">
                  <b style="font-size: 18px" class="mr-2"
                    >{{ $t('Hi') }} {{ employee.name + ',' }}
                    {{ $t(' Have a nice day!') }}</b
                  >
                  <el-link
                    type="danger"
                    class="ml-3"
                    @click="resetEmployee(job_type)"
                    :underline="false"
                    >{{ $t('Logout') }}
                  </el-link>
                </div>
                <div class="text-lg text-indigo-500">
<!--                  <IncrementTimer />-->
                </div>
              </div>
              <el-input
                v-else
                placeholder="Scan Employee Code"
                class="employee-input-scan"
                @keyup.enter="scanCodeEmployee(formData.employeeCode, job_type)"
                v-model="formData.employeeCode"
              >
              </el-input>
            </el-form-item>
            <el-form-item
                prop="box_id"
                label="Box ID"
            >
              <div class="w-full">
                  <el-input
                      placeholder="Box ID"
                      v-model="formData.box_id"
                      @keyup.enter="onSubmit()"
                  >
                  </el-input>
                <div
                    class="text-danger text-[12px]"
                    v-if="errorValidator && errorValidator.box_id"
                >
                  {{ errorValidator.box_id?.[0] }}
                </div>
              </div>
            </el-form-item>
            <div>Are you sure to confirm this request?</div>
          </div>
        </el-form>
      </template>
      <template #footer>
        <div class="flex justify-end">
          <el-button :disabled="!employee || isLoading" type="primary" @click="onSubmit()"
            >Confirm</el-button
          >
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script>
import { mapState, mapActions, mapGetters, mapMutations } from 'vuex';
import formatNumberMixin from '@/mixins/formatNumber.js';
import moment from 'moment';
import { debounce } from '@/utilities/helper.js';
import {
  employeeLogoutTimeChecking,
  employeeTimeChecking, getByCode,
} from '@/api/employee';
import { checkMovingShelvesLocation } from '@/api/location';
import { isEmpty } from 'ramda';
import IncrementTimer from '@/components/IncrementTimer.vue';
import {list, confirmRbtRequest} from "@/api/internalRequest";

export default {
  mixins: [formatNumberMixin],
  components: { IncrementTimer },
  computed: {
    ...mapState('internalRequest', [
      'internalModeView',
    ]),
    ...mapGetters(['getEmployees']),
    maxHeight() {
      return parseInt(window.innerHeight - 260);
    },
  },
  created() {
    this.fetchEmployee();
    this.setDefaultRangeTime();
    this.filter = this.getRouteParam();
    this.fetchData(this.filter);
  },
  data() {
    return {
      dialogWarning: false,
      internalHistoryInfo: {
        total: 0,
        data: [],
      },
      shortcuts: [
        {
          text: 'Today',
          value: () => {
            const date = new Date();
            return [this.formatDate(date), this.formatDate(date)];
          },
        },
        {
          text: 'Yesterday',
          value: () => {
            const date = new Date();
            date.setTime(date.getTime() - 3600 * 1000 * 24);
            return [this.formatDate(date), this.formatDate(date)];
          },
        },
        {
          text: 'Last 7 days',
          value: () => {
            const date = new Date();
            const endDate = new Date().setTime(
              new Date().getTime() - 3600 * 1000 * 24 * 6
            );
            return [this.formatDate(endDate), this.formatDate(date)];
          },
        },
      ],
      CHECKED: 'checked',
      UNCHECK: 'uncheck',
      isOpenModal: false,
      isLoading: false,
      requestSelect: null,
      job_type: 'confirm_internal_request',
      formData: {
        employeeCode: null,
        box_id: null,
      },
      formRules: {
        employeeCode: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: ['blur'],
          },
        ],
        box_id: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: ['blur'],
          },
        ],
      },
      employee: null,
      codeEmployee: '',
      filter: this.setDefaultFilter(),
      filterName: 'pulling_shelves',
      tabs: [
        {
          key: 'pulling_shelves',
          name: 'Pulling Shelves'
        },
        {
          key: 'restricted',
          name: 'Dark Pods'
        }
      ],
      listStatus: [
        {
          label: 'Uncheck',
          value: 'uncheck',
        },
        {
          label: 'Checked',
          value: 'checked',
        },
        {
          label: 'Rejected',
          value: 'rejected',
        }
      ],
      rangeTime: null,
      CREATE_TYPE: 'create',
      RECEIVE_TYPE: 'receive',
      FULFILL_TYPE: 'fulfill',
      CONFIRM_TYPE: 'confirm',
      errorValidator: null,
    };
  },
  watch: {
    'filter.tab': {
      handler: function (value) {
        this.fetchData(this.filter);
      },
      deep: true,
    },
  },
  methods: {
    ...mapActions('internalRequest', [
      'getInternalRequestInfo',
      'confirmRequest',
      'confirmRbtRequest',
    ]),
    ...mapMutations('internalRequest', ['resetInternalRequestInfo']),
    generateStatus (value) {
      let words = value.split('_');
      for (let i = 0; i < words.length; i++) {
        words[i] = words[i][0].toUpperCase() + words[i].slice(1);
      }
      return words.join(' ');
    },
    async confirmRbt() {
      try {
        let params = {
          id: this.requestSelect.id,
          employee_id: this.employee.code,
          box_id: this.formData.box_id,
        };
        const response = await this.confirmRbtRequest(params);

        if (response?.status == 'success' || response?.data?.status == 'success') {
          this.notification('Success', 'success');
          this.closeModal();
          return;
        } else if (response.response?.data?.errors) {
          this.errorValidator = response?.response?.data?.errors;
          let error = response?.response?.data?.errors?.employee_id?.[0]
          this.notification(
              this.$t(
                  error || 'The given data was invalid.'
              ),
              'error'
          );
        } else if(response.response?.data?.message) {
          this.notification(
              this.$t(
                  response.response.data.message || 'The given data was invalid.'
              ),
              'error'
          );
        }
      } catch (e) {
        this.notification(e, 'error');
      }
      this.dialogWarning = false;
      this.isLoading = false;
    },
    async onSubmit() {
      this.errorValidator = null;
      if (!this.employee) {
        this.notification('Please scan employee code first!', 'error');
        this.isLoading = false;
        return;
      }
        const isValid = await this.$refs['confirmRequest'].validate();

        if (!isValid) {
          this.notification('The given data was invalid.', 'error');
          this.isLoading = false;
          return;
        }
      this.isLoading = true;

      try {

        if (this.filter.tab == 'restricted') {
          await this.confirmRbt();
          this.isLoading = false;
          return;
        } else {
          let params = {
            id: this.requestSelect.id,
            employee_id: this.employee.id,
            box_id: this.formData.box_id,
          };
          const response = await this.confirmRequest(params);
          if (response?.data) {
            this.notification(response?.message, 'success');
            this.closeModal();
            return;
          } else if (response.response.data.errors) {
            this.errorValidator = response?.response?.data?.errors;
            let error = response?.response?.data?.errors?.employee_id?.[0]
            this.notification(
                this.$t(
                    error || 'The given data was invalid.'
                ),
                'error'
            );
          } else if(response.response.data.message) {
            this.notification(
                this.$t(
                    response.response.data.message || 'The given data was invalid.'
                ),
                'error'
            );
          }
        }
      } catch (e) {
        this.notification(e, 'error');
      }
      this.isLoading = false;

    },
    async resetEmployee() {
      this.employee = null;
      this.formData.employeeCode = '';
    },
    resetFormData() {
      this.formData = {
        employeeCode: null,
      };
    },
    async scanCodeEmployee(code, job_type) {
      try {
        this.isLoading = true;
        const res = await getByCode(code);
        if (!res.data.data) {
          this.codeEmployee = '';
          this.notification(
            'Scan employee code error, please scan again.',
            'error'
          );
          return;
        }
        this.employee = res.data.data;
        this.isLoading = false;
      } catch (e) {
        const data = e.response.data;
        let message = this.$t('Not found');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.isLoading = false;
        this.notification(message, 'error');
      }
    },
    async getInternalRequestInfo(params) {
      try {
        this.isLoading = true;
        const response = await list(params);
        this.internalHistoryInfo = response.data ?? [];
      } catch (e) {
        this.notification(e ?? 'The given data was invalid.', 'error');
      }
      this.isLoading = false;

    },
    //INIT DATA PAGE
    async fetchData(filters) {
      this.setRouteParam();
      await this.getInternalRequestInfo(filters);
    },
    async fetchEmployee() {
      await this.$store.dispatch('getEmployees');
    },
    async openDialogConfirm(item) {
      this.isOpenModal = true;
      this.requestSelect = item;
    },
    closeModal() {
      this.dialogWarning = false;
      this.isOpenModal = false;
      this.requestSelect = null;
      this.errorValidator = null;
      this.isLoading = false;
      this.formData = {
        employeeCode: null,
        box_id: null,
      };
      this.resetEmployee();
      this.fetchData(this.filter);
    },
    //HANDLE FILTER LOGIC

    setRouteParam() {
      const params = this.filter;
      this.$router.replace({ name: 'internal_request', query: params });
    },
    getRouteParam() {
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        tab_name: this.internalModeView ? 'pending' : 'history',
      };
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);
      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }
      if (filter.start_date && filter.end_date) {
        this.rangeTime = [];
      }
      filter.page = +filter.page || 1;
      return filter;
    },
    setDefaultRangeTime() {
      this.rangeTime = [];
    },
    setDefaultFilter() {
      let params = {
        limit: 25,
        page: 1,
        tab_name: 'history',
        key_word: '',
        box_id: '',
        employee_id: '',
        start_date: '',
        end_date: '',
        status: [],
        warehouse_id: this.$author.warehouseId() ?? '',
        tab: 'pulling_shelves'

      };
      return params;
    },
    handleChangeRangeTime() {
      if (this.rangeTime && this.rangeTime.length) {
        this.filter.start_date = this.formatDate(this.rangeTime[0], false);
        this.filter.end_date = this.formatDate(this.rangeTime[1], false);
      } else {
        this.filter.start_date = '';
        this.filter.end_date = '';
      }
      this.onFilter();
    },
    async onFilter() {
      await this.fetchData(this.filter);
    },
    resetFilter() {
      let tab = this.filter.tab;
      this.filter = this.setDefaultFilter();
      this.filter.tab = tab
      this.onFilter();
      this.rangeTime = null;
    },

    handleChangeKeyWord: debounce(async function () {
      await this.onFilter();
    }, 1000),

    async handleFilter() {
      await this.fetchData(this.filter);
    },
    generateEmployee(row, type = this.CREATE_TYPE) {
      let result = '';
      row.forEach((item) => {
        if (item?.pivot?.type == type) {
          result += `, ${item.name}`;
        }
      });
      return result.substring(2);
    },
    generatePreLocation(row) {
      let result = '';
      row.forEach((item) => {
        result += `, ${item?.box_moving?.[0]?.pre_location?.barcode}` ?? '';
      });
      return result.substring(2);
    },
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchData(this.filter);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.el-tab-filter ::v-deep .el-tabs__header .el-tabs__item.is-active {
  background-color: #1A73E8 !important;
  border-color: #1A73E8 !important;
}
.el-tab-filter ::v-deep .el-tabs__header .el-tabs__item.is-active .custom-tabs-label {
  color: #fff !important;
}

</style>
