<template>
  <div class="w-full">
    <!-- Header Block -->
    <div class="mt-3">
      <el-tabs v-model="tabValue" type="card" @tab-click="handleTabChange">
        <el-tab-pane name="all">
          <template #label>
            <span class="flex items-center">
              <icon />
              <span>ALL ({{ statisticInternalRequestInfo?.all ?? 0 }})</span>
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane name="tiktok">
          <template #label>
            <span class="flex items-center gap-1">
              <icon :data="iconTiktok" />
              <span>TIKTOK ({{ statisticInternalRequestInfo?.tiktok ?? 0 }})</span>
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane name="restricted">
          <template #label>
            <span class="flex items-center gap-1">
              <icon style="color: red;" :data="iconRestricted" />
              <span>DARK PODS ({{ statisticInternalRequestInfo?.restricted ?? 0 }})</span>
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane name="priority">
          <template #label>
            <span class="flex items-center gap-1">
              <icon style="color: #FFB636;" :data="iconStar" />
              <span> PRIORITY ({{ statisticInternalRequestInfo?.priority ?? 0 }}) </span>
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane name="normal">
          <template #label>
            <span class="flex items-center gap-1">
              <icon />
              <span> NORMAL ({{ statisticInternalRequestInfo?.normal ?? 0 }}) </span>
            </span>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>

    <el-row :gutter="20">
      <el-col :span="6">
        <div v-if="employee" class="bg-gray-50 p-3 border rounded" style="min-width: 320px">
          <div class="flex justify-between">
            <b style="font-size: 18px" class="mr-2">
              {{ $t('Hi') }} {{ employee.name + ',' }} {{ $t('Have a nice day!') }}
            </b>
            <el-link type="danger" class="ml-3 min-w-[50px]" style="word-break: break-word"
              @click="resetEmployee(job_type)" :underline="false">
              {{ $t('Logout') }}
            </el-link>
          </div>
          <div class="text-lg text-indigo-500">
            <IncrementTimer />
          </div>
        </div>
        <el-input v-else placeholder="Scan Employee Code" class="scan-employee-input-create"
          @keyup.enter="scanCodeEmployee(codeEmployee, job_type)" v-model="codeEmployee">
        </el-input>
      </el-col>
      <el-col :span="19">
        <el-row class="flex justify-end gap-6">
          <span class="text-[#D8102D] text-2xl font-bold">
            New: {{ statisticInternalRequestInfo?.new ?? 0 }}
          </span>
          <span class="text-[#FF7004] text-2xl font-bold">
            Picking Up: {{ statisticInternalRequestInfo?.picking_up ?? 0 }}
          </span>
          <span class="text-[#FFCC26] text-2xl font-bold">
            Uncheck: {{ statisticInternalRequestInfo?.uncheck ?? 0 }}
          </span>
        </el-row>
      </el-col>
    </el-row>

    <el-row class="mt-3" :gutter="12" align="middle">
      <el-col :span="3">
        <el-input-number placeholder="Box quantity" v-model="boxQuantity" type="number" :min="1" :max="5"
          class="scan-sku-input-create">
        </el-input-number>
      </el-col>
      <el-col :span="3">
        <el-input placeholder="Scan SKU or Label ID" v-model="scanInput" @keyup.enter="scanProductBySkuOrLabel"
          class="scan-sku-input-create">
        </el-input>
      </el-col>
      <el-col :span="1" align="middle">
        <span> OR </span>
      </el-col>
      <el-col :span="17">
        <div class="w-full relative overflow-x-auto overflow-y-hidden">
          <el-row :gutter="10" align="middle" class="!mx-0">
            <el-col :span="4">
              <el-select v-model="product.style" @change="selectStyle" filterable :placeholder="$t('Style')">
                <el-option v-for="item in styles" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-select v-model="product.color" filterable :placeholder="$t('Color')">
                <el-option v-for="item in colors" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-select v-model="product.size" filterable :placeholder="$t('Size')">
                <el-option v-for="item in sizes" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-col>
            <el-col :span="4" v-if="tabValue != 'restricted'">
              <el-select :disabled="tabValue != 'all'" v-model="destination" filterable :placeholder="$t('Destination')">
                <el-option v-for="type in destinations"  :label="type.label" :value="type.value" />
              </el-select>
            </el-col>
            <el-col :span="4" v-if="tabValue == 'all' || tabValue == 'restricted'">
              <el-select :disabled="requestTypes.length == 1" v-model="requestType" filterable :placeholder="$t('Request Type')">
                <el-option v-for="type in requestTypes"  :label="type.label" :value="type.value" :disabled="(!canSubmitTikTokRequest && type.value == typeTikTok) || (!canSubmitPriorityRequest && type.value == typePriority)" class="font-bold" />
              </el-select>
            </el-col>
            <el-col :span="4">
              <div class="flex items-center">
                <el-button type="primary" :disabled="!productScanned || !boxQuantity" @click="checkAvailableRequest">
                  {{ $t('Submit') }}
                </el-button>
                <el-button type="danger" @click="resetDataSendRequest">
                  {{ $t('Reset') }}
                </el-button>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-col>
    </el-row>

    <!-- Filter Block -->
    <el-row class="mt-6 gap-4 flex flex-nowrap w-full" align="middle">
      <span> Filter by: </span>
      <el-row style="width: 90%" class="flex gap-4">
        <el-date-picker v-model="rangeTime" type="daterange" start-placeholder="Start date" end-placeholder="End date"
          range-separator="To" @change="handleChangeRangeTime" :shortcuts="shortcuts" />
        <el-col class="flex-initial" :span="4">
          <el-input placeholder="SKU/Style/Color/Size" v-model="filter.key_word"
            @input="handleChangeKeyWord"></el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="filter.status" multiple placeholder="Status" @change="handleFilter" class="w-full">
            <el-option v-for="item in listStatus" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-col>
        <el-col class="!flex-initial" :span="5">
          <el-select placeholder="Employee" filterable v-model="filter.employee_id" @change="handleFilter">
            <el-option v-for="item in getEmployees" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-col>
        <el-col class="!flex-initial" :span="4">
          <el-input placeholder="Location" v-model="filter.location" @keyup.enter="handleFilter"></el-input>
        </el-col>
        <el-link type="danger" :underline="false" @click="resetFilter">
          {{ $t('Clear') }}
        </el-link>
      </el-row>
    </el-row>

    <!-- Table Block -->
    <el-row class="mt-3 w-full">
      <el-table border stripe size="small" :data="internalRequestInfo?.data" :max-height="maxHeight"
        v-loading="isLoading" element-loading-text="Loading..." class="break-words">
        <el-table-column prop="status" width="150" :label="$t('Status')">
          <template #default="scope">
            <span :class="scope.row.status == 'new' ? 'text-[#D8102D]' : 'text-[#FF7004]'">
              {{ generateStatus(scope.row.status) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="id" width="100" :label="$t('ID')">
          <template #default="scope">
            <div class="flex items-center space-x-2">
              <el-link :underline="false" type="primary" @click="showModal(scope.row)">
                {{ scope.row.id_pad }}
              </el-link>
              <icon v-if="scope?.row?.priority === 1" style="color: #FFB636; margin-bottom: 4px;" :data="iconStar" />
              <icon v-if="scope?.row?.priority === 3" :data="iconTiktok" class="custom-icon ml-1 mb-1" />
              <span class="el-dropdown-link flex items-center"
                v-if="scope?.row?.is_rbt == 1 && scope?.row?.priority === 2">
                <icon style="color: red; padding-bottom: 3px;" :data="iconRestricted" />
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="location" width="400" :label="$t('Location')">
          <template #default="scope">
            {{ generateLocation(scope.row?.locations ?? []) }}
          </template>
        </el-table-column>
        <el-table-column prop="Destination" width="400" :label="$t('Location')">
          <template #default="scope">
            <span class=" font-bold">{{ scope.row.is_rbt != 1 ? 'Pulling Shelves' : 'Dark Pods' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" width="230" :label="$t('Name')">
          <template #default="scope">
            {{ scope.row?.product?.name }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" width="100" :label="$t('Date')">
          <template #default="scope">
            <el-tooltip class="box-item" effect="dark"
              :content="formatTime(scope.row.created_at, 'YYYY-MM-DD hh:mm:ss')" placement="top-start">
              {{ this.formatTime(scope.row.created_at, 'YYYY-MM-DD') }}
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="sku" width="100" :label="$t('SKU')">
          <template #default="scope">
            {{ scope.row?.product?.sku }}
          </template>
        </el-table-column>
        <el-table-column prop="stock_level" width="100" :label="$t('Stock Level')">
          <template #default="scope">
            <span
              :class="scope.row?.stock_level == 'In stock' ? 'text-[#08B69F]' : (scope.row?.stock_level == 'Incoming' ? 'text-[#08B69F]' : 'text-[#D8102D]')">
              {{ scope.row?.stock_level }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="receive_by" width="200" :label="$t('Picking up By')">
          <template #default="scope">
            {{ scope.row?.employee_receive?.name }}
          </template>
        </el-table-column>
        <el-table-column prop="request_by" width="200" :label="$t('Requested By')">
          <template #default="scope">
            {{ scope.row?.employee_create?.name }}
          </template>
        </el-table-column>
        <el-table-column prop="action" :label="$t('Action')" width="70">
          <template #default="scope">
            <el-dropdown v-if="scope?.row?.priority && scope?.row?.priority != typeNormal" trigger="click">
              <span class="el-dropdown-link">
                <el-link :underline="false" type="primary" class="mr-2" @click="update(scope.row.id)">
                  <svg class="mt-[3px]" width="18" height="18" viewBox="0 0 18 18" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <rect y="9" width="12.7279" height="12.7279" rx="2" transform="rotate(-45 0 9)" fill="#FF8A00" />
                    <path
                      d="M4.45459 11.6495L5.68497 12.728L8.90936 9.87697L12.1338 12.728L13.3641 11.6495L8.90938 7.71057L4.45459 11.6495Z"
                      fill="white" />
                    <path
                      d="M4.45459 7.75745L5.68497 8.83601L8.90936 5.98495L12.1338 8.83601L13.3641 7.75745L8.90938 3.81848L4.45459 7.75745Z"
                      fill="white" />
                  </svg>
                </el-link>
              </span>
            </el-dropdown>

            <el-dropdown v-else trigger="click">
              <span class="el-dropdown-link">
                <el-link :underline="false" type="primary" class="mr-2">
                  <svg class="mt-[3px]" width="18" height="18" viewBox="0 0 18 18" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <rect y="9" width="12.7279" height="12.7279" rx="2" transform="rotate(-45 0 9)" fill="#3A3F4A" />
                    <path
                      d="M4.45459 11.6495L5.68497 12.728L8.90936 9.87697L12.1338 12.728L13.3641 11.6495L8.90938 7.71057L4.45459 11.6495Z"
                      fill="white" />
                    <path
                      d="M4.45459 7.75745L5.68497 8.83601L8.90936 5.98495L12.1338 8.83601L13.3641 7.75745L8.90938 3.81848L4.45459 7.75745Z"
                      fill="white" />
                  </svg>
                </el-link>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item class="clearfix"
                    @click="update(scope.row.id, typeTikTok, scope.row?.can_convert_to_tiktok)"
                    :disabled="!scope.row?.can_convert_to_tiktok">
                    <icon :data="iconTiktok" class="custom-icon mr-[4px] mb-0.5"
                      :style="{ color: scope.row?.can_convert_to_tiktok ? '#000000' : '#909399', display: 'inline' }" />
                    <span class="font-bold"
                      :class="scope.row?.can_convert_to_tiktok ? 'text-[#000000]' : 'text-[#909399]'">TikTok
                      request</span>
                  </el-dropdown-item>
                  <el-dropdown-item class="clearfix"
                    @click="update(scope.row.id, typePriority, scope.row?.can_convert_to_priority)"
                    :disabled="!scope.row?.can_convert_to_priority">
                    <icon class="mr-1.5 pb-[2px]" :data="iconStar"
                      :style="{ color: scope.row?.can_convert_to_priority ? '#FFB636' : '#909399', display: 'inline' }" />
                    <span class="font-bold"
                      :class="scope.row?.can_convert_to_priority ? 'text-[#FFB636]' : 'text-[#909399]'">Priority
                      request</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <el-link @click="openDialogRemove(scope?.row)" v-if="scope?.row?.status == 'new'" :underline="false"
              type="danger">
              <icon :data="iconDelete" />
            </el-link>
          </template>
        </el-table-column>
      </el-table>
    </el-row>

    <!-- Paginate Block -->
    <el-row class="mt-6 w-full justify-between">
      <div class="total">
        {{ $t('Total:') }}
        {{ formatNumber(internalRequestInfo?.total) }}
      </div>
      <el-pagination :disabled="isLoading" background layout="prev, pager, next" :page-size="filter.limit"
        :total="internalRequestInfo?.total" @current-change="changePage" v-model:currentPage="filter.page">
      </el-pagination>
      <div class="limit" :disabled="isLoading">
        <el-select v-model="filter.limit" :placeholder="$t('Select')" size="mini" @change="onFilter">
          <el-option v-for="item in limits" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </div>
    </el-row>

    <!-- Fulfill Request Modal -->
    <el-dialog v-model="isOpenModal" custom-class="min-h-40 min-w-[400px] custom-dialog" @close="closeModal"
      destroy-on-close :close-on-click-modal="false" width="30%">
      <template #title>
        <div class="flex">
          <span class="text-xl self-center mr-2">{{ $t(`Fulfill Request ${productSelect?.id_pad}`) }}</span>
          <CountdownTimer :time="time" />
        </div>
      </template>
      <template #default>
        <el-form :model="formData" :label-position="'top'" :rules="formRules" ref="fulfillRequest"
          @submit.prevent="onSubmit('fulfillRequest')">
          <div class="w-full h-full flex flex-col rounded-sm">
            <el-form-item prop="employeeCode" :label="!employeeProcess ? 'Employee ID' : ''">
              <div v-if="employeeProcess" class="bg-gray-50 p-3 border rounded w-full">
                <div class="flex justify-between">
                  <b style="font-size: 18px" class="mr-2">{{ $t('Hi') }} {{ employeeProcess.name + ',' }}
                    {{ $t(' Have a nice day!') }}</b>
                  <el-link type="danger" class="ml-3" @click="resetEmployee('create_box_moving')" :underline="false"
                    style="word-break: break-word">{{ $t('Logout') }}
                  </el-link>
                </div>
                <div class="text-lg text-indigo-500">
                  <IncrementTimer />
                </div>
              </div>
              <el-input v-else placeholder="Scan Employee Code" class="employee-input-scan"
                @keyup.enter="scanCodeEmployee(formData.employeeCode, 'create_box_moving')"
                v-model="formData.employeeCode">
              </el-input>
            </el-form-item>
          </div>
          <el-row :gutter="20" class="mt-4">
            <el-col :span="24">
              <el-form-item prop="barcode" :label="'Box ID'">
                <el-input v-model="formData.barcode" type="text" class="mt-2 barcode-scan-input" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <div class="flex justify-between">
          <span>Product Name: </span>
          <span class="font-semibold">{{ productSelect?.product?.name }}</span>
        </div>
        <div class="flex justify-between content-center">
          <span class="self-center">Location: </span>
          <div class="flex flex-wrap gap-2 my-1">
            <span v-for="item in productSelect?.locations" class="rounded-full bg-[#e0e0e0]	py-[2px] px-[8px]">
              {{ item }}
            </span>
          </div>
        </div>
      </template>
      <template #footer>
        <div class="flex justify-between">
          <div>
            <el-button class="!border-[#F56C6C] rounded-sm !text-[#F56C6C] !bg-[#ffffff]" :disabled="!employeeProcess"
              @click="openDialogMissing">Report missing box</el-button>
            <el-button class="!border-[#F56C6C] rounded-sm !text-[#F56C6C] !bg-[#ffffff]" :disabled="!employeeProcess"
              @click="cancelPickUp">Cancel pick up</el-button>
          </div>
          <div>
            <el-button @click="resetFormData">Reset</el-button>
            <el-button type="primary" :disabled="!employeeProcess"
              @click="onSubmit('fulfillRequest')">Confirm</el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="isOpenModalReceive" :title="$t(`Confirm receive request `) + productSelect?.id_pad"
      custom-class="min-h-40 min-w-[400px] custom-dialog" @close="closeModalReceive" destroy-on-close
      :close-on-click-modal="false" width="30%">
      <template #default>
        <el-form :model="formDataReceiveRequest" :label-position="'top'" :rules="formReceiveRules" ref="receiveRequest"
          @submit.prevent="receiveRequest()" @keydown.enter.exact.prevent="event.preventDefault()">
          <div class="w-full h-full flex flex-col rounded-sm">
            <el-form-item prop="employeeCode" :label="!employeeProcess ? 'Employee ID' : ''">
              <div v-if="employeeProcess" class="bg-gray-50 p-3 border rounded w-full">
                <div class="flex justify-between">
                  <b style="font-size: 18px" class="mr-2">{{ $t('Hi') }} {{ employeeProcess.name + ',' }}
                    {{ $t(' Have a nice day!') }}</b>
                  <el-link type="danger" class="ml-3" @click="resetEmployee(job_type_receive)"
                    style="word-break: break-word" :underline="false">{{ $t('Logout') }}
                  </el-link>
                </div>
                <div class="text-lg text-indigo-500">
                  <IncrementTimer />
                </div>
              </div>
              <el-input v-else placeholder="Scan Employee Code" class="employee-input-scan" @keyup.enter="
                scanCodeEmployee(formDataReceiveRequest.employeeCode, job_type_receive)
                " v-model="formDataReceiveRequest.employeeCode">
              </el-input>
            </el-form-item>
          </div>
        </el-form>
      </template>
      <template #footer>
        <div class="flex justify-end">
          <el-button type="primary" :disabled="!employeeProcess" @click="receiveRequest()">Confirm</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="dialogMissing" :title="$t('Report missing box')"
      custom-class="min-h-40 min-w-[400px] custom-dialog" @close="closeDialogMissing" destroy-on-close
      :close-on-click-modal="false" width="30%">
      <template #default>
        <el-form :model="formDataMissingBox" :label-position="'top'" ref="missingBox"
          @submit.prevent="reportMissingBox()" @keydown.enter.exact.prevent="event.preventDefault()">
          <div class="w-full h-full flex flex-col rounded-sm">
            <el-form-item prop="employeeCode" label="Location">
              <el-input placeholder="Scan Location" class="employee-input-scan" @keyup.enter="getBoxMissing()"
                v-model="barcode">
              </el-input>
            </el-form-item>
            <div class="flex justify-between content-center">
              <span class="self-center">Box ID: </span>
              <div class="flex flex-wrap gap-2 my-1">
                <span v-if="boxMissing.length > 0" v-for="item in boxMissing"
                  class="rounded-full bg-[#e0e0e0] py-[2px] px-[8px]">
                  {{ item.barcode }}
                </span>
                <span v-else> ------</span>
              </div>
            </div>
          </div>
        </el-form>
      </template>
      <template #footer>
        <div class="flex justify-end">
          <el-button type="primary" :disabled="!employeeProcess" @click="reportMissingBox()">Confirm</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="dialogConfirm" width="500px" align-center @close="dialogConfirm = false; available = null">
      <template #title>
        <div>
          <icon class="!text-red-700 mb-3 text-xs !w-[30px] !h-[30px] mr-2" :data="iconError2"></icon>
          <span class="text-[25px] font-bold">Existing Request Found</span>

        </div>
      </template>
      <p v-if="available?.incoming_stock == 0 && available?.total_box < boxQuantity" class="text-center mb-[5px]">{{
        $t('Box Available: ') + available?.total_box }}
      </p>
      <p v-if="available?.incoming_stock == 0 && available?.total_box < boxQuantity"
        class="whitespace-pre-line text-center set-font-family">
        {{ $t('Do you want to add new request for available box despite existing request? ') }}
      </p>
      <p v-else-if="available && available?.incoming_stock == 0 && available?.total_box >= boxQuantity"
        class="whitespace-pre-line text-center set-font-family text-[16px]">
        A previous request has been made for this SKU. Do you want to add new request?
      </p>
      <p v-else-if="available && available?.incoming_stock > 0"
        class="whitespace-pre-line text-center set-font-family text-[16px]">
        A previous request has been made for this SKU, and incoming stock is on its way. Do you want to add new request?
      </p>
      <div class="mt-[20px]">
        <div class="flex justify-between my-[2px]">
          <span class="text-slate-500	">Style: </span>
          <span class="">{{ product?.style }}</span>
        </div>
        <div class="flex justify-between my-[2px]">
          <span class="text-slate-500	">Color: </span>
          <span class="">{{ product?.color }}</span>
        </div>
        <div class="flex justify-between my-[2px]">
          <span class="text-slate-500	">Size: </span>
          <span class="">{{ product?.size }}</span>
        </div>
        <div class="flex justify-between mt-[2px]">
          <span class="text-slate-500	">Box quantity: </span>
          <span class="">{{ available?.count_pending }}</span>
        </div>
      </div>
      <div
          v-if="requestType == typeRestricted && (available?.rbt_exists_requests ?? []).length > 0"
          class="table-content"
      >
        <el-table
          :data="available.rbt_exists_requests"
          height="300"
          border
          stripe
          size="small"
          class="mt-2 text-black"
          style="width: 100%"
          header-row-class-name="table-header-bg"

        >
          <el-table-column prop="id" label="Internal Request ID">
            <template #default="scope">
              <span>{{ scope.row.id }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="Current Status">
            <template #default="scope">
              <span :class="scope.row.status == 'new' ? 'text-[#D8102D]' : (scope.row.status == 'uncheck' ? 'text-[#F1C536]' : 'text-[#FF7004]')">
                {{ generateStatus(scope.row.status) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="created" label="Creation date and time">
            <template #default="scope">
              <span>{{ convertTime(scope.row.created_at) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer flex-layout">
          <el-button class="w-1/2" @click="dialogConfirm = false; this.available = null">No</el-button>
          <el-button class="w-1/2" type="primary" @click="handleManualSendRequest">
            Yes
          </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="dialogError" width="20%" show-close="false" align-center
      custom-class="custom-dialog hidden-close" @close="dialogError = false">
      <svg class="m-auto mb-[15px] mt-[-15px]" width="80" height="80" viewBox="0 0 80 80" fill="none"
        xmlns="http://www.w3.org/2000/svg">
        <rect width="80" height="80" rx="40" fill="#FBEBEB" />
        <g opacity="0.5">
          <rect x="3.69238" y="3.69238" width="72.6154" height="72.6154" rx="36.3077" fill="#D73939" />
        </g>
        <rect x="6.76904" y="6.76904" width="66.4615" height="66.4615" rx="33.2308" fill="#D73939" />
        <path d="M53 26.9998L27 52.9998" stroke="white" stroke-width="5" stroke-linecap="round"
          stroke-linejoin="round" />
        <path d="M27 26.9998L53 52.9998" stroke="white" stroke-width="5" stroke-linecap="round"
          stroke-linejoin="round" />
      </svg>
      <p class="text-2xl text-[#D8102D] text-center mb-[5px]">{{ $t('Failed') }}</p>
      <pre class="whitespace-pre-line text-center set-font-family text-[16px]">{{ messageError }}</pre>
      <p v-if="available?.total_box == 0 && available?.incoming_stock == 0"
        class="whitespace-pre-line text-center set-font-family text-[16px]">
        This SKU is out of stock. Please reach out to the relevant department for further assistance.<br><br>
        SKU này đã hết hàng, Vui lòng liên hệ với bộ phận liên quan để được hỗ trợ.
      </p>
      <template #footer>
        <span class="dialog-footer">
          <el-button class="w-full" type="primary" @click="dialogError = false">Close</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="dialogIncoming" width="30%" align-center custom-class="custom-dialog hidden-close"
      @close="dialogIncoming = false">
      <svg class="m-auto mb-[15px] mt-[-15px]" width="80" height="80" viewBox="0 0 80 80" fill="none"
        xmlns="http://www.w3.org/2000/svg">
        <rect width="80" height="80" rx="40" fill="#F0F1FE" />
        <g opacity="0.5">
          <rect x="3.69238" y="3.69238" width="72.6154" height="72.6154" rx="36.3077" fill="#6874F9" />
        </g>
        <rect x="6.76904" y="6.76929" width="66.4615" height="66.4615" rx="33.2308" fill="#6874F9" />
        <path
          d="M40 42.6665H41.3333C42.8 42.6665 44 41.4665 44 39.9998V26.6665H32C30 26.6665 28.2533 27.7732 27.3467 29.3998"
          stroke="white" stroke-width="3.07692" stroke-linecap="round" stroke-linejoin="round" />
        <path
          d="M26.667 46.6665C26.667 48.8798 28.4537 50.6665 30.667 50.6665H32.0003C32.0003 49.1998 33.2003 47.9998 34.667 47.9998C36.1337 47.9998 37.3337 49.1998 37.3337 50.6665H42.667C42.667 49.1998 43.867 47.9998 45.3337 47.9998C46.8003 47.9998 48.0003 49.1998 48.0003 50.6665H49.3337C51.547 50.6665 53.3337 48.8798 53.3337 46.6665V42.6665H49.3337C48.6003 42.6665 48.0003 42.0665 48.0003 41.3332V37.3332C48.0003 36.5998 48.6003 35.9998 49.3337 35.9998H51.0536L48.7737 32.0132C48.2937 31.1865 47.4137 30.6665 46.4537 30.6665H44.0003V39.9998C44.0003 41.4665 42.8003 42.6665 41.3337 42.6665H40.0003"
          stroke="white" stroke-width="3.07692" stroke-linecap="round" stroke-linejoin="round" />
        <path
          d="M34.6667 53.3333C36.1394 53.3333 37.3333 52.1394 37.3333 50.6667C37.3333 49.1939 36.1394 48 34.6667 48C33.1939 48 32 49.1939 32 50.6667C32 52.1394 33.1939 53.3333 34.6667 53.3333Z"
          stroke="white" stroke-width="3.07692" stroke-linecap="round" stroke-linejoin="round" />
        <path
          d="M45.3337 53.3333C46.8064 53.3333 48.0003 52.1394 48.0003 50.6667C48.0003 49.1939 46.8064 48 45.3337 48C43.8609 48 42.667 49.1939 42.667 50.6667C42.667 52.1394 43.8609 53.3333 45.3337 53.3333Z"
          stroke="white" stroke-width="3.07692" stroke-linecap="round" stroke-linejoin="round" />
        <path
          d="M53.3333 40V42.6667H49.3333C48.6 42.6667 48 42.0667 48 41.3333V37.3333C48 36.6 48.6 36 49.3333 36H51.0533L53.3333 40Z"
          stroke="white" stroke-width="3.07692" stroke-linecap="round" stroke-linejoin="round" />
        <path d="M26.667 34.6665H34.667" stroke="white" stroke-width="3.07692" stroke-linecap="round"
          stroke-linejoin="round" />
        <path d="M26.667 38.6665H32.0003" stroke="white" stroke-width="3.07692" stroke-linecap="round"
          stroke-linejoin="round" />
        <path d="M26.667 42.6665H29.3337" stroke="white" stroke-width="3.07692" stroke-linecap="round"
          stroke-linejoin="round" />
      </svg>
      <p class="text-2xl text-[#6874F9] text-center mb-[10px]">{{ $t('Incoming stock') }}</p>
      <p v-if="available?.total_box" class="text-center mb-[5px]">{{ $t('Box Available: ') + available?.total_box }}</p>
      <p class="whitespace-pre-line text-center set-font-family text-[16px]">
        {{ $t('Would you like to create a pre-request for the incoming goods ? ') }}
      </p>
      <template #footer>
        <span class="dialog-footer flex-layout">
          <el-button class="w-1/2" @click="dialogIncoming = false; this.available = null">No</el-button>
          <el-button class="w-1/2" type="primary" @click="handleManualSendRequest">
            Yes
          </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="dialogWarning" width="30%" align-center custom-class="custom-dialog hidden-close"
      @close="dialogWarning = false">
      <svg class="m-auto mb-[15px] mt-[-15px]" width="80" height="80" viewBox="0 0 80 80" fill="none"
        xmlns="http://www.w3.org/2000/svg">
        <rect width="80" height="80" rx="40" fill="#FEF8F0" />
        <g opacity="0.5">
          <rect x="3.69238" y="3.69238" width="72.6154" height="72.6154" rx="36.3077" fill="#ED7C40" />
        </g>
        <rect x="6.76904" y="6.76929" width="66.4615" height="66.4615" rx="33.2308" fill="#ED7C40" />
        <path
          d="M57.271 49.5538L43.8175 26.1897C43.4278 25.5232 42.8704 24.9704 42.2008 24.5863C41.5312 24.2021 40.7726 24 40.0006 24C39.2286 24 38.4701 24.2021 37.8005 24.5863C37.1308 24.9704 36.5735 25.5232 36.1838 26.1897L22.7302 49.5538C22.3521 50.2005 22.1528 50.9361 22.1528 51.6853C22.1528 52.4344 22.3521 53.1701 22.7302 53.8168C23.1156 54.4865 23.6722 55.0417 24.3429 55.4253C25.0137 55.8089 25.7744 56.0072 26.5471 55.9998H53.4542C54.2263 56.0067 54.9864 55.8081 55.6566 55.4245C56.3267 55.0409 56.8829 54.4861 57.268 53.8168C57.6465 53.1703 57.8464 52.4348 57.8469 51.6857C57.8474 50.9366 57.6487 50.2008 57.271 49.5538ZM54.0711 51.9691C54.0085 52.0757 53.9183 52.1635 53.81 52.223C53.7017 52.2826 53.5793 52.3118 53.4557 52.3076H26.5471C26.4235 52.3118 26.3011 52.2826 26.1928 52.223C26.0845 52.1635 25.9943 52.0757 25.9317 51.9691C25.8786 51.8828 25.8505 51.7835 25.8505 51.6822C25.8505 51.5809 25.8786 51.4816 25.9317 51.3953L39.3853 28.0312C39.4519 27.9286 39.5431 27.8443 39.6505 27.786C39.758 27.7277 39.8783 27.6971 40.0006 27.6971C40.1229 27.6971 40.2433 27.7277 40.3507 27.786C40.4582 27.8443 40.5494 27.9286 40.616 28.0312L54.068 51.3953C54.1216 51.4813 54.1502 51.5805 54.1507 51.6818C54.1513 51.7831 54.1237 51.8825 54.0711 51.9691ZM38.1545 41.8462V36.9233C38.1545 36.4337 38.349 35.9641 38.6952 35.6179C39.0414 35.2717 39.511 35.0772 40.0006 35.0772C40.4903 35.0772 40.9598 35.2717 41.306 35.6179C41.6522 35.9641 41.8467 36.4337 41.8467 36.9233V41.8462C41.8467 42.3359 41.6522 42.8054 41.306 43.1516C40.9598 43.4979 40.4903 43.6924 40.0006 43.6924C39.511 43.6924 39.0414 43.4979 38.6952 43.1516C38.349 42.8054 38.1545 42.3359 38.1545 41.8462ZM42.4621 48C42.4621 48.4868 42.3178 48.9627 42.0473 49.3675C41.7768 49.7723 41.3924 50.0878 40.9426 50.2741C40.4928 50.4604 39.9979 50.5091 39.5204 50.4142C39.0429 50.3192 38.6043 50.0847 38.2601 49.7405C37.9159 49.3963 37.6814 48.9577 37.5864 48.4802C37.4915 48.0027 37.5402 47.5078 37.7265 47.058C37.9128 46.6082 38.2283 46.2238 38.6331 45.9533C39.0379 45.6828 39.5138 45.5385 40.0006 45.5385C40.6535 45.5385 41.2795 45.7978 41.7412 46.2594C42.2028 46.7211 42.4621 47.3471 42.4621 48Z"
          fill="white" />
      </svg>
      <p class="text-2xl text-[#6874F9] text-center mb-[10px]">{{ $t('Warning') }}</p>
      <p v-if="available?.total_box" class="text-center mb-[5px]">{{ $t('Box Available: ') + available?.total_box }}</p>
      <p class="whitespace-pre-line text-center set-font-family text-[16px]">
        {{ $t('Would you like to create a request for the available box(es) only ? ') }}
      </p>
      <template #footer>
        <span class="dialog-footer flex-layout">
          <el-button class="w-1/2" @click="dialogWarning = false; this.available = null">No</el-button>
          <el-button class="w-1/2" type="primary" @click="handleManualSendRequest">
            Yes
          </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="isOpenModalRemove" :title="$t(`Remove request `) + productSelect?.id_pad"
      custom-class="min-h-40 min-w-[400px] custom-dialog" @close="closeDialog" destroy-on-close
      :close-on-click-modal="false" width="30%">
      <template #default>
        <el-form :model="formDataRemoveRequest" :label-position="'top'" :rules="formReceiveRules" ref="receiveRequest"
          @submit.prevent="remove()" @keydown.enter.exact.prevent="event.preventDefault()">
          <div class="w-full h-full flex flex-col rounded-sm">
            <el-form-item prop="employeeCode" :label="!employeeProcess ? 'Employee ID' : ''">
              <div v-if="employeeProcess" class="bg-gray-50 p-3 border rounded w-full">
                <div class="flex justify-between">
                  <b style="font-size: 18px" class="mr-2">{{ $t('Hi') }} {{ employeeProcess.name + ',' }}
                    {{ $t(' Have a nice day!') }}</b>
                </div>
              </div>
              <el-input v-else placeholder="Scan Employee Code" class="employee-input-scan"
                @keyup.enter="getEmployeeByCode(formDataRemoveRequest.employeeCode)"
                v-model="formDataRemoveRequest.employeeCode">
              </el-input>
            </el-form-item>
          </div>
        </el-form>
      </template>

      <template #footer>
        <div class="flex justify-end">
          <el-button @click="closeDialog()">Close</el-button>
          <el-button type="primary" :disabled="!employeeProcess" @click="remove()">Confirm</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapActions, mapGetters } from 'vuex';
import formatNumberMixin from '@/mixins/formatNumber.js';
import warehouseMixin from '@/mixins/warehouse';
import dateMixin from '@/mixins/date';
import moment from 'moment';
import {
  getProductAttributes,
  getProductBySkuOrLabel,
  getProductByParams,
} from '@/api/product';
import { employeeTimeChecking, getByCode } from '@/api/employee.js';
import { checkAvailableRequest, getStatistic, list, reportMissingBox, cancelPickUp, allBoxMissing, checkValidType } from '@/api/internalRequest'
import { debounce } from '@/utilities/helper.js';
import { isEmpty } from 'ramda';
import IncrementTimer from '@/components/IncrementTimer.vue';
import CountdownTimer from '@/components/CountdownTimer.vue';
import mixinFocusByElClass from '@/mixins/focusByElClass';
import EventBus from "@/utilities/eventBus";
import {
  INTERNAL_NORMAL_REQUEST,
  INTERNAL_PRIORITY_REQUEST,
  INTERNAL_RESTRICTED_REQUEST,
  INTERNAL_TIKTOK_REQUEST, RBT_EMPLOYEE_CODE, SAN_JOSE_ID
} from '@/utilities/constants';

export default {
  components: {
    IncrementTimer,
    CountdownTimer,
  },

  mixins: [formatNumberMixin, mixinFocusByElClass, dateMixin, warehouseMixin],

  data() {
    return {
      internalRequestInfo: {
        total: 0,
        data: [],
      },
      boxMissing: [],
      available: null,
      time: 0,
      isLoading: false,
      dialogMissing: false,
      requestType: '',
      typeNormal: INTERNAL_NORMAL_REQUEST,
      typeRestricted: INTERNAL_RESTRICTED_REQUEST,
      typePriority: INTERNAL_PRIORITY_REQUEST,
      typeTikTok: INTERNAL_TIKTOK_REQUEST,
      rbtEmployeeCode: RBT_EMPLOYEE_CODE,
      canSubmitTikTokRequest: true,
      canSubmitPriorityRequest: true,
      statisticInternalRequestInfo: {
        new: 0,
        picking_up: 0,
        uncheck: 0,
      },
      shortcuts: [
        {
          text: 'Today',
          value: () => {
            const date = new Date();

            return [this.formatDate(date), this.formatDate(date)];
          },
        },
        {
          text: 'Yesterday',
          value: () => {
            const date = new Date();
            date.setTime(date.getTime() - 3600 * 1000 * 24);

            return [this.formatDate(date), this.formatDate(date)];
          },
        },
        {
          text: 'Last 7 days',
          value: () => {
            const date = new Date();
            const endDate = new Date().setTime(
              new Date().getTime() - 3600 * 1000 * 24 * 6
            );

            return [this.formatDate(endDate), this.formatDate(date)];
          },
        },
      ],
      filter: this.setDefaultFilter(),
      listStatus: [
        {
          label: 'New',
          value: 'new',
        },
        {
          label: 'Picking Up',
          value: 'picking_up',
        }
      ],
      product: {
        style: '',
        size: '',
        color: '',
      },
      dialogConfirm: false,
      dialogError: false,
      dialogIncoming: false,
      dialogWarning: false,
      isOpenModalRemove: false,
      messageError: "",
      scanInput: '',
      boxQuantity: 1,
      styles: [],
      sizes: [],
      colors: [],
      attributes: [],
      employee: null,
      employeeProcess: null,
      formDataReceiveRequest: {
        employeeCode: null,
      },
      formDataMissingBox: {
        location: null,
      },
      formDataRemoveRequest: {
        employeeCode: null,
      },
      codeEmployee: '',
      time_checking_id: null,
      process_time_checking_id: null,
      rangeTime: null,
      job_type: 'internal_request',
      job_type_accept: 'create_box_moving',
      job_type_receive: 'receive_internal_request',
      isOpenModal: false,
      isOpenModalReceive: false,
      productSelect: null,
      barcode: null,
      formData: {
        employeeCode: null,
        newLocation: 'Pulling Shelves',
        barcode: '',
      },
      formRules: {
        employeeCode: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: ['blur'],
          },
        ],
      },
      formReceiveRules: {
        employeeCode: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: ['blur'],
          },
        ]
      },
      productScanned: null,
      CREATE_TYPE: 'create',
      RECEIVE_TYPE: 'receive',
      tabValue: 'all',
      destination: 'pulling_shelves',
      destinations: [
        {
          label: "Pulling Shelves",
          value: "pulling_shelves",
        },
        {
          label: "Dark Pods",
          value: "dark_pods",
        }
      ],
      requestTypes: [
        {
          label: "TikTok",
          value: INTERNAL_TIKTOK_REQUEST,
        },
        {
          label: "Priority",
          value: INTERNAL_PRIORITY_REQUEST,
        }
      ]
    };
  },

  computed: {
    ...mapState('internalRequest', [
      'internalModeView',
    ]),

    ...mapGetters(['getEmployees']),

    maxHeight() {
      return parseInt(window.innerHeight - 350);
    },
  },

  watch: {
    tabValue (value) {
      if (value == 'restricted') {
        this.requestType = this.typeRestricted;
        this.requestTypes = [
          {
            label: "Dark Pods",
            value: this.typeRestricted,
          }
        ];
      } else {
        this.destination = 'pulling_shelves';
        this.destinations = [
          {
            label: "Pulling Shelves",
            value: "pulling_shelves",
          },
          {
            label: "Dark Pods",
            value: "dark_pods",
          }
        ]
        this.requestType = '';
        this.requestTypes = [
          {
            label: "TikTok",
            value: this.typeTikTok,
          },
          {
            label: "Priority",
            value: this.typePriority,
          }
        ];
      }
    },
    destination (value) {
      if (this.destination == 'dark_pods') {
        this.requestType = this.typeRestricted;
        this.requestTypes = [
          {
            label: "Dark Pods",
            value: this.typeRestricted,
          }
        ];

      } else if (this.destination == 'pulling_shelves') {
        this.requestType = '';
        this.requestTypes = [
          {
            label: "TikTok",
            value: this.typeTikTok,
          },
          {
            label: "Priority",
            value: this.typePriority,
          }
        ];
      } else {
        this.requestTypes = [];
      }
    },
    product: {
      handler() {
        if (!this.product.style || !this.product.color || !this.product.size) {
          return;
        }

        this.getProductInfoByParams();
      },
      deep: true,
    },

    productSelect: {
      handler(value) {
        if (value?.received_at) {
          let nowPST = moment.utc().tz('America/Los_Angeles').format('YYYY-MM-DD hh:mm:ss');
          let expriedAt = moment(value.expried_at).format('YYYY-MM-DD hh:mm:ss')
          this.time = moment(expriedAt).diff(moment(nowPST), 'seconds')
        }
      },
      deep: true,
    }
  },

  created() {
    EventBus.$on('expiredTime', () => { this.closeModal(); })
    this.fetchEmployee();
    this.fetchProductAttributes();
    this.setDefaultRangeTime();
    this.filter = this.getRouteParam();
    this.fetchData(this.filter);
  },

  mounted() {
    this.getStatisticInternalRequestInfo();
    this.focusByElClass('scan-employee-input-create');
  },

  methods: {
    ...mapActions('internalRequest', [
      'updatePriority',
      'deleteRequest',
      'createInternalRequest',
      'handleProcessInternalRequest',
      'confirmReceiveRequest',
      'createRbtRequest',
    ]),

    generateStatus(value) {
      let words = value.split('_');

      for (let i = 0; i < words.length; i++) {
        words[i] = words[i][0].toUpperCase() + words[i].slice(1);
      }

      return words.join(' ');
    },

    async getStatisticInternalRequestInfo() {
      try {
        let tabs = ['all', 'tiktok', 'restricted', 'priority', 'normal'];
        let request_type = tabs.includes(this.tabValue) ? this.tabValue : 'all'
        let params = {
          warehouse_id: this.$author.warehouseId() ?? null,
          request_type,
        }
        const response = await getStatistic(params);

        if (response.data) {
          this.statisticInternalRequestInfo = {
            ...this.statisticInternalRequestInfo,
            ...response.data,
          };
        }
      } catch (e) {
        this.notification(
          this.$t(e || 'The given data was invalid.'),
          'error'
        );
      }

      this.isLoading = false;
    },

    async update(id, internalRequestType = INTERNAL_NORMAL_REQUEST, canSubmit = true) {
      if (!canSubmit) {
        return;
      }

      const params = {
        requestId: id,
        type: internalRequestType
      }
      const response = await this.updatePriority(params)

      if (response?.data) {
        this.notification(this.$t('Update internal request successfully.'));
        await this.fetchData(this.filter);
      } else {
        this.notification(
          this.$t(response?.response?.data?.message || 'The given data was invalid.'),
          'error'
        );
      }
    },

    openDialogRemove(row) {
      this.productSelect = row;
      this.isOpenModalRemove = true
      this.focusByElClass('employee-input-scan');
    },

    closeDialog() {
      this.isOpenModalRemove = false;
      this.employeeProcess = null;
      this.formDataRemoveRequest.employeeCode = '';
      this.errors = [];
    },

    async remove() {
      this.isLoading = true;
      let id = this.productSelect.id;

      try {
        const response = await this.deleteRequest({
          id: id,
          employee_id: this.employeeProcess.id,
        })

        if (response.status) {
          this.notification(this.$t('Delete internal request successfully.'));
          await this.fetchData(this.filter);
          await this.getStatisticInternalRequestInfo();
        } else if (response.response.data.message) {
          this.notification(
            this.$t(response?.response?.data?.message || 'The given data was invalid.'),
            'error'
          );
        }
      } catch (error) {
        this.notification(
          this.$t(error || 'The given data was invalid.'),
          'error'
        );
      } finally {
        this.closeDialog()
        this.isLoading = false;
      }
    },

    //INIT DATA PAGE
    async fetchData(filters) {
      this.setRouteParam();
      await this.getInternalRequestInfo(filters);
    },

    async getInternalRequestInfo(params) {
      try {
        this.isLoading = true;
        const response = await list(params);
        this.internalRequestInfo = response.data ?? [];
      } catch (e) {
        if (e?.response?.data?.message) {
          this.notification(e.response.data.message, 'error');
        } else if ((typeof e) == "string") {
          this.notification(e, 'error');
        }
      }
      this.isLoading = false;
    },

    //HANDLE FILTER LOGIC
    setRouteParam() {
      const params = this.filter;
      params.request_type = this.tabValue
      this.$router.replace({ name: 'internal_request', query: params });
    },

    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      let tabs = ['all', 'tiktok', 'restricted', 'priority', 'normal'];
      this.tabValue = tabs.includes(routeQuery.request_type) ? routeQuery.request_type : 'all'
      filter = {
        ...filter,
        tab_name: this.internalModeView ? 'pending' : 'history',
      };
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);

      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }

      if (filter.start_date && filter.end_date) {
        this.rangeTime = [filter.start_date, filter.end_date];
      }

      filter.page = +filter.page || 1;

      return filter;
    },

    setDefaultRangeTime() {
      this.rangeTime = [];
    },

    setDefaultFilter() {
      let params = {
        limit: 25,
        page: 1,
        employee_id: '',
        key_word: '',
        start_date: '',
        end_date: '',
        status: [],
        tab_name: 'pending',
        warehouse_id: this.$author.warehouseId() ?? '',
        location: '',
      };
      this.rangeTime = [];

      return params;
    },

    handleChangeRangeTime() {
      if (this.rangeTime && this.rangeTime.length) {
        this.filter.start_date = this.formatDate(this.rangeTime[0], false);
        this.filter.end_date = this.formatDate(this.rangeTime[1], false);
      } else {
        this.filter.start_date = '';
        this.filter.end_date = '';
      }

      this.onFilter();
    },

    async onFilter() {
      await this.fetchData(this.filter);
    },

    resetFilter() {
      this.filter = this.setDefaultFilter();
      this.onFilter();
    },

    handleChangeKeyWord: debounce(async function () {
      await this.onFilter();
    }, 1000),

    async handleFilter() {
      await this.fetchData(this.filter);
    },

    //CREATE REQUEST MODAL LOGIC PROCESSING
    showModal(item) {
      this.productSelect = item;

      if (item.status == 'new') {
        this.isOpenModalReceive = true;
      } else {
        this.isOpenModal = true;
      }

      this.focusByElClass('employee-input-scan');
    },

    resetFormData() {
      this.formData = {
        employeeCode: null,
        barcode: '',
      };
    },

    closeModal() {
      this.resetEmployee(this.job_type_accept);
      this.resetFormData();
      this.productSelect = null;
      this.isOpenModal = false;
      this.errors = [];
    },

    closeModalReceive() {
      this.fetchData(this.filter);
      this.resetEmployee(this.job_type_receive);
      this.formDataReceiveRequest = {
        employeeCode: null,
      }
      this.productSelect = null;
      this.isOpenModalReceive = false;
      this.errors = [];

    },

    async receiveRequest() {
      const isValid = await this.$refs['receiveRequest'].validate();

      if (!isValid) {
        this.notification('The given data was invalid.', 'error');
       
        return;
      }

      this.isLoading = true;

      try {
        let params = {
          employee_id: this.employeeProcess.id,
          id: this.productSelect.id,
        }
        const response = await this.confirmReceiveRequest(params);

        if (response.data) {
          this.notification(this.$t(response.message));
          await this.fetchData(this.filter);
          await this.getStatisticInternalRequestInfo();
          this.closeModalReceive();
        } else if (response.response.data.errors) {
          let error = response?.response?.data?.errors?.employee_id?.[0]
          this.notification(
            this.$t(
              error || 'The given data was invalid.'
            ),
            'error'
          );
        } else if (response.response.data.message) {
          this.notification(
            this.$t(
              response.response.data.message || 'The given data was invalid.'
            ),
            'error'
          );
        }
      } catch (e) {
        this.notification(e, 'error');
      } finally {
        this.isLoading = false;
      }
    },

    async onSubmit(formName) {
      const isValid = await this.$refs[formName].validate();

      if (!isValid) {
        this.notification('The given data was invalid.', 'error');

        return;
      }

      this.isLoading = true;

      try {
        //TODO: Handle Create Fulfill Request
        const data = {
          requestID: this.productSelect.id,
          payload: {
            employee_id: this.employeeProcess.id,
            barcode: this.formData.barcode,
            id_time_checking: this.process_time_checking_id,
          },
        };
        const response = await this.handleProcessInternalRequest(data);

        if (response?.data) {
          this.notification(this.$t(`${response?.data?.message}`), 'success');
          this.closeModal();
          await this.fetchData(this.filter);
          await this.getStatisticInternalRequestInfo();
        } else if (response.response.data.errors) {
          this.errors = response.response.data.errors;
          this.formData.barcode = '';
          this.focusByElClass('barcode-scan-input');
          let message = '';
          let errorLength = Object.keys(this.errors).length;

          for (let i = 0; i <= errorLength; i++) {
            const key = Object.keys(this.errors)[i];

            if (!key) {
              continue;
            }

            let msgError = this.errors[key][0];
            message += msgError;
          }

          throw new Error(
            response?.data?.message ||
            message ||
            response?.response?.data?.message ||
            'The given data was invalid.'
          );
        } else if (response.response.data.message) {
          this.notification(
            this.$t(response.response.data.message || 'The given data was invalid.'),
            'error'
          );
        }
      } catch (e) {
        this.notification(e, 'error');
      } finally {
        this.isLoading = false;
      }
    },

    //SCAN EMPLOYEE CODE & LOGOUT LOGIC PROCESSING
    async resetEmployee(type) {
      switch (type) {
        case this.job_type:
          this.employee = null;
          this.codeEmployee = '';
          this.time_checking_id = null;
        case this.job_type_accept:
          this.employeeProcess = null;
          this.formData.employeeCode = '';
          this.process_time_checking_id = null;
        case 'create_box_moving':
          this.employeeProcess = null;
          this.formData.employeeCode = '';
          this.process_time_checking_id = null;
        case this.job_type_receive:
          this.employeeProcess = null;
          this.formDataReceiveRequest.employeeCode = '';
          this.process_time_checking_id = null;
      }
    },

    async getEmployeeByCode(code) {
      try {
        this.isLoading = true;
        const res = await getByCode(code);

        if (!res.data.data) {
          this.codeEmployee = '';
          this.notification(
            'Scan employee code error, please scan again.',
            'error'
          );
          this.focusByElClass('scan-employee-input-create');
          this.isLoading = false;

          return;
        }

        this.focusByElClass('scan-sku-input-create');
        this.employeeProcess = res.data.data;
        this.isLoading = false;
      } catch (e) {
        this.isLoading = false;
        this.notification(e, 'error');
      }
    },

    async scanCodeEmployee(code, job_type) {
      try {
        this.isLoading = true;

        if (job_type == 'create_box_moving') {
          var res = await employeeTimeChecking({
            code: code,
            job_type: job_type,
          });
        } else {
          var res = await getByCode(code);
        }

        switch (job_type) {
          case this.job_type:
            if (!res.data.data) {
              this.codeEmployee = '';
              this.notification(
                'Scan employee code error, please scan again.',
                'error'
              );
              this.focusByElClass('scan-employee-input-create');
              this.isLoading = false;

              return;
            }

            this.focusByElClass('scan-sku-input-create');
            this.employee = res.data.data;
            this.time_checking_id = res.data.id_time_checking;

            break;
          case this.job_type_accept:
            if (!res.data.data) {
              this.formData.employeeCode = '';
              this.notification(
                'Scan employee code error, please scan again.',
                'error'
              );
              this.focusByElClass('employee-input-scan');
              this.isLoading = false;

              return;
            }

            this.focusByElClass('barcode-scan-input');
            this.employeeProcess = res.data.data;
            this.process_time_checking_id = res.data.id_time_checking;

            break;
          case this.job_type_receive:
            if (!res.data.data) {
              this.formDataReceiveRequest.employeeCode = '';
              this.notification(
                'Scan employee code error, please scan again.',
                'error'
              );
              this.focusByElClass('employee-input-scan');
              this.isLoading = false;

              return;
            }

            this.focusByElClass('barcode-scan-input');
            this.employeeProcess = res.data.data;
            this.process_time_checking_id = res.data.id_time_checking;

            break;
          case 'create_box_moving':
            if (!res.data.data) {
              this.formData.employeeCode = '';
              this.notification(
                'Scan employee code error, please scan again.',
                'error'
              );
              this.focusByElClass('employee-input-scan');
              this.isLoading = false;

              return;
            }

            this.focusByElClass('barcode-scan-input');
            this.employeeProcess = res.data.data;
            this.process_time_checking_id = res.data.id_time_checking;

            break;
          default:
            break;
        }

        this.isLoading = false;
      } catch (e) {
        const data = e.response.data;
        let message = this.$t('Not found');

        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }

        this.isLoading = false;
        this.notification(message, 'error');
      } finally {
        this.isLoading = false;
      }
    },

    async checkAvailableRequest() {
      if (!this.employee) {
        this.notification('Please scan employee code first!', 'error');

        return;
      }

      if (!this.boxQuantity) {
        this.notification('Please enter box quantity!', 'error');

        return;
      }
      let params = {
        product_id: this.productScanned?.id,
        warehouse_id: this.employee?.warehouse_id
      }
      if (this.requestType == this.typeRestricted) {
        params.is_rbt = true;
      }

      const response = await checkAvailableRequest(params);

      if (response?.data) {
        this.available = response.data;

        if (this.available?.total_box == 0 && this.available?.incoming_stock == 0) {
          this.dialogError = true
        } else if (this.available?.exist_request) {
          this.dialogConfirm = true;
        } else if (response?.data?.total_box && response?.data?.total_box >= this.boxQuantity) {
          await this.handleManualSendRequest()
        } else if (this.available?.incoming_stock > 0) {
          this.dialogIncoming = true;
        } else if (this.available?.total_box > 0 && this.available.total_box < this.boxQuantity) {
          this.boxQuantity = this.available?.total_box;
          this.dialogWarning = true;
        }
      }

    },

    async handleCreateRequest()
    {
      if (this.$author.warehouseId() != SAN_JOSE_ID) {
        this.notification('Cannot create DARK PODS internal request for this warehouse.', 'error');
        return
      }
      let payload = {
        employee_id: this.employee?.code,
        sku: this.productScanned?.sku,
        box_quantity: this.boxQuantity
      }
      this.isLoading = true;
      try {
        const response = await this.createRbtRequest(payload);
        if (response?.data?.status == 'success') {
          this.notification(this.$t('Create internal request successfully.'));
          await this.fetchData(this.filter);
          await this.getStatisticInternalRequestInfo();
          this.focusByElClass('scan-sku-input-create');
        } else if (response.response.data.errors) {
          this.errors = response.response.data.errors;
          this.formData.barcode = '';
          let message = '';
          let errorLength = Object.keys(response.response.data.errors).length;
          for (let i = 0; i <= errorLength; i++) {
            const key = Object.keys(response.response.data.errors)[i];

            if (!key) {
              continue;
            }

            let msgError = response.response.data.errors?.[key]?.[0];
            message += msgError;
          }

          throw new Error(message);
        } else if (response.response.data.message) {
          this.notification(response.response.data.message, 'error');
        }
      } catch (error) {
        this.notification(error, 'error');

      } finally {
        this.dialogConfirm = false;
        this.dialogConfirm = false;
        this.dialogError = false;
        this.dialogIncoming = false;
        this.dialogWarning = false;
        this.isLoading = false;
      }
    },

    //MANUAL SEND REQUEST LOGIC
    async handleManualSendRequest() {
      if (!this.scanInput || !this.productScanned) {
        this.notification('SKU is required', 'error');

        return;
      }

      let boxQuantity = this.boxQuantity

      if (this.available?.incoming_stock == 0 && this.available?.total_box < this.boxQuantity) {
        boxQuantity = this.available?.total_box;
      }

      let priority = INTERNAL_NORMAL_REQUEST;

      if (this.tabValue == 'all') {
        priority = !isEmpty(this.requestType) ? this.requestType : INTERNAL_NORMAL_REQUEST;
      } else if (this.tabValue == 'tiktok') {
        priority = 2;
      } else if (this.tabValue == 'priority') {
        priority = 1;
      }
      if (this.requestType == this.typeRestricted || this.tabValue == 'restricted') {
        this.handleCreateRequest();
        return
      }
      //TODO: Handle Logic Send Request
      const payload = {
        product_id: this.productScanned?.id,
        employee_id: this.employee?.id,
        box_quantity: boxQuantity,
        priority,
      };
      this.isLoading = true;

      try {
        this.resetDataSendRequest();
        const response = await this.createInternalRequest(payload);

        if (response.status) {
          this.notification(this.$t('Create internal request successfully.'));
          await this.fetchData(this.filter);
          await this.getStatisticInternalRequestInfo();
          this.focusByElClass('scan-sku-input-create');
        } else if (response.response.data.errors) {
          this.errors = response.response.data.errors;
          this.formData.barcode = '';
          let message = '';
          let errorLength = Object.keys(response.response.data.errors).length;

          for (let i = 0; i <= errorLength; i++) {
            const key = Object.keys(response.response.data.errors)[i];

            if (!key) {
              continue;
            }

            let msgError = response.response.data.errors?.[key]?.[0];
            message += msgError;
          }

          throw new Error(message);
        } else if (response.response.data.message) {
          this.notification(response.response.data.message, 'error');
        }
      } catch (error) {
        this.notification(error, 'error');

      } finally {
        this.dialogConfirm = false;
        this.dialogConfirm = false;
        this.dialogError = false;
        this.dialogIncoming = false;
        this.dialogWarning = false;
        this.isLoading = false;
      }
    },

    resetDataSendRequest() {
      this.scanInput = '';
      this.productScanned = null;
      // this.requestType = '';
      // this.destination = 'pulling_shelves';
      this.product = {
        style: '',
        size: '',
        color: '',
      };
    },

    async getProductInfoByParams() {
      this.isLoading = true;
      // this.requestType = '';
      const resCheckValidType = await checkValidType(this.product);
      this.canSubmitTikTokRequest = resCheckValidType.data?.flag_create_tiktok;
      this.canSubmitPriorityRequest = resCheckValidType.data?.flag_create_priority;
      const res = await getProductByParams(this.product);
      const product = res.data ? res.data : {};
      this.isLoading = false;

      if (!product?.sku) {
        this.notification('Product not found', 'error');
        this.resetDataSendRequest();

        return;
      }

      this.scanInput = product.sku;
      this.productScanned = product;
    },
    async scanProductBySkuOrLabel() {
      if (!this.scanInput) {
        this.notification('SKU is required', 'error');
        this.focusByElClass('scan-sku-input-create');

        return;
      }

      this.isLoading = true;

      try {
        const res = await getProductBySkuOrLabel({
          keyword: this.scanInput,
        });
        const data = res?.data || {};
        this.productScanned = data;

        if (data.id) {
          this.product.style = data.style;
          this.selectStyle();
          this.product.color = data.color;
          this.product.size = data.size;
        }
      } catch (e) {
        const data = e.response.data;
        let message = data?.message || this.$t('Product not found');

        this.resetDataSendRequest();
        this.notification(message, 'error');
        this.focusByElClass('scan-sku-input-create');
      } finally {
        this.isLoading = false;
      }
    },

    async fetchProductAttributes() {
      const res = await getProductAttributes();
      const data = res.data || '';
      let styles = data && Object.keys(data);
      styles =
        styles.length &&       
        styles.map((item) => {
          return {
            label: item,
            value: item,
          };
        });

      this.styles = styles;
      this.attributes = data;
    },

    selectStyle() {
      this.product.size = '';
      this.product.color = '';

      if (!this.product.style) {
        this.colors = [];
        this.sizes = [];

        return;
      }

      const currentStyle = this.attributes[this.product.style];
      let colors = currentStyle.colors;
      colors =
        colors.length &&
        colors.map((item) => {
          return {
            label: item,
            value: item,
          };
        });
      let sizes = currentStyle.sizes;
      sizes =
        sizes.length &&
        sizes.map((item) => {
          return {
            label: item,
            value: item,
          };
        });
      this.colors = colors;
      this.sizes = sizes;
    },

    async fetchEmployee() {
      await this.$store.dispatch('getEmployees');
    },

    generateEmployee(row, type = this.CREATE_TYPE) {
      let result = '';

      row.forEach(item => {
        if (item?.pivot?.type == type) {
          result += `, ${item.name}`
        }
      })

      return result.substring(2);
    },

    generateLocation(row) {
      let result = '';

      row.forEach((item, index) => {
        result += `, ${item}`
      })

      return result.substring(2);
    },

    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchData(this.filter);
      });
    },

    openDialogMissing() {
      this.dialogMissing = true;
    },

    async reportMissingBox() {
      this.isLoading = true;

      try {
        let params = {
          internal_request_id: this.productSelect.id,
          product_id: this.productSelect.product_id,
          location_barcode: this.barcode,
          employee_id: this.employeeProcess.id
        }
        const response = await reportMissingBox(params);

        if (response.data.status) {
          this.notification(this.$t(response.data?.output?.message));
          await this.fetchData(this.filter);
          await this.getStatisticInternalRequestInfo();
          this.closeDialogMissing();
          this.closeModal();
        }
      } catch (e) {
        var message = '';

        if (e?.response?.data?.errors) {
          let errorLength = Object.keys(e?.response?.data?.errors).length;

          for (let i = 0; i <= errorLength; i++) {
            const key = Object.keys(e?.response?.data?.errors)[i];

            if (!key) {
              continue;
            }

            let msgError = e?.response?.data?.errors[key][0];
            message += msgError;
          }
        } else if (e?.response?.data.message) {
          message = e?.response?.data.message;
        }
        
        this.notification(message, 'error')
      } finally {
        this.isLoading = false
      }
    },

    async getBoxMissing() {
      this.boxMissing = [];

      try {
        this.isLoading = true;
        const response = await allBoxMissing({
          product_id: this.productSelect.product_id,
          location_barcode: this.barcode,
          employee_id: this.employeeProcess.warehouse_id,
          internal_request_id: this.productSelect.id,
        });
        this.boxMissing = response?.data?.output?.data ?? [];
      } catch (e) {
        let errors = e.response.data.errors;
        this.focusByElClass('barcode-scan-input');
        let message = '';
        let errorLength = Object.keys(errors).length;

        for (let i = 0; i <= errorLength; i++) {
          const key = Object.keys(errors)[i];

          if (!key) {
            continue;
          }

          let msgError = errors[key][0];
          message += msgError;
        }

        this.notification(message, 'error');
      }

      this.isLoading = false;
    },

    closeDialogMissing() {
      this.dialogMissing = false;
      this.barcode = null;
      this.boxMissing = [];
      this.errors = [];
    },

    async cancelPickUp() {
      this.isLoading = true;

      try {
        const response = await cancelPickUp(this.productSelect.id, {
          employee_id: this.employeeProcess.id
        })

        if (response?.data) {
          this.notification(this.$t(`${response?.data?.message}`), 'success');
          this.closeModal();
          await this.fetchData(this.filter);
          await this.getStatisticInternalRequestInfo();
        } else if (response.response.data.errors) {
          this.errors = response.response.data.errors;
          this.formData.barcode = '';
          this.focusByElClass('barcode-scan-input');
          let message = '';
          let errorLength = Object.keys(this.errors).length;

          for (let i = 0; i <= errorLength; i++) {
            const key = Object.keys(this.errors)[i];

            if (!key) {
              continue;
            }

            let msgError = this.errors[key][0];
            message += msgError;
          }

          throw new Error(
            response?.data?.message ||
            message ||
            response?.response?.data?.message ||
            'The given data was invalid.'
          );
        } else if (response.response.data.message) {
          this.notification(
            this.$t(response.response.data.message || 'The given data was invalid.'),
            'error'
          );
        }
      } catch (e) {
        var message = '';

        if (e?.response?.data?.errors) {
          let errorLength = Object.keys(e?.response?.data?.errors).length;

          for (let i = 0; i <= errorLength; i++) {
            const key = Object.keys(e?.response?.data?.errors)[i];

            if (!key) {
              continue;
            }

            let msgError = e?.response?.data?.errors[key][0];
            message += msgError;
          }
        } else if (e?.response?.data.message) {
          message = e?.response?.data.message;
        }

        this.notification(message, 'error')
      }

      this.isLoading = false;
    },

    handleTabChange() {
      this.getStatisticInternalRequestInfo();
      this.handleFilter();
    }
  },


};
</script>

<style lang="scss" scoped>
::v-deep .table-header-bg {
  .el-table__cell {
    background-color: #788896;
    color: #fff;
    border-color: #8896a4 !important;
  }
}

</style>
