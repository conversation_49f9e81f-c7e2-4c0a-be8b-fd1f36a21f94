import {
  list,
  create,
  getStatistic,
  updatePriority,
  deleteRequest,
  confirmRequest,
  confirmReceiveRequest,
  fulfillRequest,
  confirmRbtRequest,
  createRbtRequest
} from '@/api/internalRequest';

export const actions = {
  async getInternalRequestInfo({ commit, state }, params) {
    try {
      state.isLoading = true;
      const response = await list(params);
      if (response.data) {
        commit('setInternalRequestInfo', response.data);
      }
      state.isLoading = false;
      return response;
    } catch (e) {
      return e;
    }
  },

  async getStatisticInternalRequestInfo({ commit, state }, params) {
    try {
      const response = await getStatistic(params);
      if (response.data) {
        commit('setStatisticInternalRequestInfo', response.data);
      }
      state.isLoading = false;
      return response;
    } catch (e) {
      return e;
    }
  },

  async handleProcessInternalRequest({ commit, state }, data) {
    try {
      const response = await fulfillRequest(data.requestID, data.payload);
      return response;
    } catch (e) {
      return e;
    }
  },

  async createInternalRequest({ commit, state }, payload) {
    try {
      const response = await create(payload);
      return response?.data;
    } catch (e) {
      return e;
    }
  },

  async updatePriority({ commit, state }, params) {
    try {
      const response = await updatePriority(params);
      return response?.data;
    } catch (e) {
      return e;
    }
  },
  async deleteRequest({ commit, state }, params = {}) {
    try {
      const response = await deleteRequest(params);
      return response?.data;
    } catch (e) {
      return e;
    }
  },
  async confirmRequest({commit, state}, payload) {
    try {
      const response = await confirmRequest(payload.id, payload);
      return response?.data;
    } catch (e) {
      return e;
    }
  },
  async confirmRbtRequest({commit, state}, payload) {
    try {
      const response = await confirmRbtRequest(payload.id, payload);
      return response;
    } catch (e) {
      return e;
    }
  },

  async createRbtRequest({commit, state}, payload) {
    try {
      const response = await createRbtRequest(payload);
      return response;
    } catch (e) {
      return e;
    }
  },
  async confirmReceiveRequest({commit, state}, payload) {
    try {
      const response = await confirmReceiveRequest(payload.id, payload);
      return response?.data;
    } catch (e) {
      return e;
    }
  }
};
