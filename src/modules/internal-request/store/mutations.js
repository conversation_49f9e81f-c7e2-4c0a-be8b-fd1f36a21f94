export const mutations = {
  changeViewMode(state, {}) {
    state.internalModeView = !state.internalModeView;
  },
  setInternalRequestInfo(state, data) {
    state.internalRequestInfo = {
      ...state.internalRequestInfo,
      ...data,
    };
  },
  resetInternalRequestInfo(state, data) {
    state.isLoading = true;
    state.internalRequestInfo = {
      ...data,
    };
  },
  setStatisticInternalRequestInfo(state, data) {
    state.statisticInternalRequestInfo = {
      ...state.statisticInternalRequestInfo,
      ...data,
    };
  },
};
