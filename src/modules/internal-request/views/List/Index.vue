<template>
  <el-col class="w-full text-gray-700 max-h-[calc(100vh - 66px)]">
    <el-row justify="space-between" class="flex mt-3 h-full">
      <span class="text-xl font-bold">
        {{
          $t(
            `${
              internalModeView ? 'Internal Request' : 'Fulfill Request History'
            }`
          )
        }}
      </span>
      <span>
        <el-button :disabled="isLoading" type="primary" @click="changeViewMode">
          {{ $t(`${internalModeView ? 'History' : 'Internal Request'}`) }}
        </el-button>
      </span>
    </el-row>
    <InternalView v-if="internalModeView" />
    <HistoryView v-else />
  </el-col>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import InternalView from '../../components/InternalRequest.vue';
import HistoryView from '../../components/HistoryRequest.vue';
export default {
  components: {
    InternalView,
    HistoryView,
  },
  ...mapState('internalRequest', [
    'isLoading',
  ]),
  computed: {
    ...mapState('internalRequest', ['internalModeView']),
  },
  data() {
    return {};
  },
  methods: {
    ...mapMutations('internalRequest', ['changeViewMode']),
  },
};
</script>

<style lang="scss" scoped></style>
