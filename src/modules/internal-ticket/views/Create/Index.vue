<style src="./Style.scss" lang="scss"></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
      v-model="openDialogCreate"
      custom-class="el-dialog-custom"
      @close="resetData"
      :close-on-click-modal="false"
    >
      <template #title>
        <span class="font-bold text-xl">Create Ticket</span>
      </template>
      <template #default>
        <div>
          <el-form class="mb-2">
            <div v-if="employee" class="bg-gray-50 p-3 border rounded">
              <div class="flex justify-between">
                <b style="font-size: 18px" class="mr-2"
                  >{{ $t('Hi') }} {{ employee.name + ',' }}
                  {{ $t(' Have a nice day!') }}</b
                >
                <el-link
                  type="danger"
                  class="ml-3"
                  @click="resetEmployee"
                  :underline="false"
                  >{{ $t('Logout') }}</el-link
                >
              </div>
            </div>
          </el-form>
          <div v-if="!employee" class="mb-1 required-icon">
            Scan Employee ID
          </div>
          <el-form
            v-show="!employee"
            :error="employeeError"
            @submit.prevent.native="getScanCodeEmloyee"
          >
            <el-input
              :placeholder="$t('Employee ID')"
              class="el-form-item-scan-employee max-w-fit"
              ref="employeeCode"
              v-model="codeEmployee"
            />
          </el-form>
          <el-form
            status-icon
            ref="formData"
            :model="data"
            :rules="dataRules"
            @submit.prevent="createTicket"
            label-width="130px"
            :label-position="'top'"
          >
            <div class="justify-space-between layout-default mt-5">
              <div class="w-48">
                <el-form-item prop="urgency">
                  <el-select
                    filterable
                    class="w-full"
                    :class="{
                      'border border-red-400 rounded':
                        errorValidator && errorValidator.urgency,
                    }"
                    v-model="data.urgency"
                    :placeholder="$t('Select Urgency')"
                  >
                    <el-option
                      v-for="style in urgency"
                      :key="style.label"
                      :label="style.label"
                      :value="style.value"
                    />
                  </el-select>
                </el-form-item>
                <div
                  class="text-danger text-[12px]"
                  v-if="errorValidator && errorValidator.urgency"
                >
                  {{ errorValidator.urgency[0] }}
                </div>
              </div>
              <div class="w-48">
                <el-form-item prop="department_id">
                  <el-select
                    filterable
                    class="w-full"
                    v-model="data.department_id"
                    :class="{
                      'border border-red-400 rounded':
                        errorValidator && errorValidator.department_id,
                    }"
                    :placeholder="$t('Select Department')"
                  >
                    <el-option
                      v-for="style in departments"
                      :key="style.id"
                      :label="style.name"
                      :value="style.id"
                    />
                  </el-select>
                </el-form-item>
                <div
                  class="text-danger text-[12px]"
                  v-if="errorValidator && errorValidator.department"
                >
                  {{ errorValidator.department[0] }}
                </div>
              </div>
            </div>
            <el-form-item :label="$t('Ticket name')" prop="ticket_name">
              <el-input
                :placeholder="$t('Ticket Name')"
                v-model="data.ticket_name"
                :class="{
                  'border border-red-400 rounded':
                    errorValidator && errorValidator.ticket_name,
                }"
              ></el-input>
            </el-form-item>
            <div
              class="text-danger text-[12px]"
              v-if="errorValidator && errorValidator.ticket_name"
            >
              {{ errorValidator.ticket_name[0] }}
            </div>
            <div class="justify-space-between layout-default mt-3">
              <div class="w-48">
                <el-form-item :label="$t('Phone Number')" prop="phone_number">
                  <el-input
                    :placeholder="$t('Phone Number')"
                    v-model="data.phone_number"
                    :class="{
                      'border border-red-400 rounded':
                        errorValidator && errorValidator.phone_number,
                    }"
                  ></el-input>
                </el-form-item>
                <div
                  class="text-danger text-[12px]"
                  v-if="errorValidator && errorValidator.phone_number"
                >
                  {{ errorValidator.phone_number[0] }}
                </div>
              </div>
              <div class="w-48">
                <el-form-item :label="$t('Email')" prop="email">
                  <el-input
                    :placeholder="$t('Email')"
                    v-model="data.email"
                    :class="{
                      'border border-red-400 rounded':
                        errorValidator && errorValidator.email,
                    }"
                  ></el-input>
                </el-form-item>
                <div
                  class="text-danger text-[12px]"
                  v-if="errorValidator && errorValidator.email"
                >
                  {{ errorValidator.email[0] }}
                </div>
              </div>
            </div>

            <div class="justify-space-between layout-default mt-3">
              <div class="w-48">
                <el-form-item
                  :label="$t('Long Description')"
                  prop="long_description"
                >
                  <el-input
                    type="textarea"
                    rows="5"
                    :placeholder="$t('Long Description')"
                    v-model="data.long_description"
                    :class="{
                      'border border-red-400 rounded':
                        errorValidator && errorValidator.long_description,
                    }"
                  ></el-input>
                </el-form-item>
              </div>
              <div class="w-48">
                <el-form-item
                  :label="$t('List Order Number')"
                  prop="order_numbers"
                >
                  <el-input
                    type="textarea"
                    rows="5"
                    :placeholder="$t('List Order Number')"
                    v-model="data.order_numbers"
                    :class="{
                      'border border-red-400 rounded':
                        errorValidator && errorValidator.order_numbers,
                    }"
                  ></el-input>
                </el-form-item>
              </div>
            </div>

            <div
              class="text-danger text-[12px]"
              v-if="errorValidator && errorValidator.long_description"
            >
              {{ errorValidator.long_description[0] }}
            </div>
            <div>
              <p>{{ $t('Attachments') }}</p>
              <div
                class="justify-space-between layout-default custom-upload-file"
              >
                <el-upload
                  class="upload-demo"
                  drag
                  multiple
                  :auto-upload="false"
                  :on-change="handleChangeImage"
                  :data="listUpload"
                >
                  <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                  <div class="el-upload__text">
                    Drop file here or <em>click to upload</em>
                  </div>
                </el-upload>
                <!--                <el-form-item prop="file" class="w-1/3 position-item-form">-->
                <!--                  <label class="border-y border-x pb-5 cursor-pointer">-->
                <!--                    <el-input v-model="file" class="invisible" type="file" name="file" @change="handleFileUpload('file')" ref="file" accept=".doc,.docx,.xlsx,.pdf" ></el-input>-->
                <!--                    <span class="position-icon"><icon :data="iconFile" /></span>-->
                <!--                  </label>-->
                <!--                </el-form-item>-->
                <!--                <el-form-item prop="product_image" class="w-1/3 position-item-form">-->
                <!--                  <label class="border-y pb-5 cursor-pointer">-->
                <!--                    <el-input v-model="image" class="opacity-0 h-full w-full cursor-pointer" type="file" name="image" @change="handleFileUpload('image')" ref="image" accept="image/*"></el-input>-->
                <!--                    <span class="position-icon"><icon :data="iconImage" /></span>-->
                <!--                  </label>-->
                <!--                </el-form-item>-->
                <!--                <el-form-item prop="video" class="w-1/3 position-item-form">-->
                <!--                  <label class="border-y border-x pb-5 cursor-pointer">-->
                <!--                    <el-input  v-model="video" class="opacity-0 h-full w-full cursor-pointer" type="file" name="video" @change="handleFileUpload('video')" ref="video" accept="video/*"></el-input>-->
                <!--                    <span class="position-icon"><icon :data="iconVideo" /></span>-->
                <!--                  </label>-->
                <!--                </el-form-item>-->
              </div>
            </div>
            <div
              class="text-danger text-[12px]"
              v-if="fileSelected.length == 0"
            >
              {{ requiredMessage }}
            </div>
            <div v-else-if="errorAttachment.length > 0">
              <div
                v-for="error in errorAttachment"
                class="text-danger text-[12px]"
              >
                <p>{{ $t(error) }}</p>
              </div>
            </div>

            <div
              v-for="(name, index) in fileSelected"
              :key="index"
              class="relative hover:bg-gray-100 rounded"
            >
              <span class="ml-1"> {{ name }} </span
              ><icon
                @click="removeFile(name)"
                class="absolute right-1 cursor-pointer"
                :data="iconCancel"
              />
            </div>
          </el-form>
        </div>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            @click="createTicket"
            :disabled="isLoading || !employee"
            :loading="isLoading"
          >
            {{ $t('Submit') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
