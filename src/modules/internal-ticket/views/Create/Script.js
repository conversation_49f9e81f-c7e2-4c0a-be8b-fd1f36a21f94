import { create } from '@/api/internalTicket.js';
import EventBus from '@/utilities/eventBus.js';
import { STORAGE_URL } from '@/utilities/constants';
import { isEmpty } from 'ramda';
import focusMixin from '@/mixins/helpers.js';
import {
  employeeLogoutTimeChecking,
  employeeTimeChecking,
} from '@/api/employee.js';
import { list } from '@/api/department.js';
import key from '@element-plus/icons-vue/dist/es/key.mjs';

export default {
  name: 'CreateInternalTicket',
  mixins: [focusMixin],
  data() {
    return {
      openDialogCreate: false,
      employee: null,
      time_checking_id: null,
      codeEmployee: '',
      employeeError: '',
      job_type: 'create_ticket',
      departments: [],
      isLoading: false,
      hasErrorAttachment: false,
      errorValidator: {},
      images: [],
      files: [],
      videos: [],
      image: null,
      file: null,
      video: null,
      listUpload: [],
      fileSelected: [],
      data: {},
      urlStorage: STORAGE_URL,
      dataRules: {
        urgency: [this.commonRule()],
        department_id: [this.commonRule()],
        ticket_name: [this.commonRule()],
        long_description: [this.commonRule()],
        email: [this.commonRule()],
      },
      requiredMessage: '',
      mapKey: [],
      time: 0,
      errorAttachment: [],
    };
  },
  props: {
    status: {
      default: [],
      type: Array,
    },
    urgency: {
      default: [],
      type: Array,
    },
  },
  watch: {
    image(value) {
      this.data.image = value;
    },
    video(value) {
      this.data.video = value;
    },
    file(value) {
      this.data.file = value;
    },
    openDialogCreate() {
      if (!this.openDialogCreate) {
        this.listUpload = [];
        this.fileSelected = [];
        this.files = [];
        this.$emit('forceRender');
      }
    },
  },
  async created() {
    await this.fetchDepartments();
    EventBus.$on('showCreateInternalTicket', () => {
      this.openDialogCreate = true;
    });
  },
  methods: {
    validateImage(value, attr) {
      if (value) {
        this.errorValidator[attr][0] = 'Invalid value. Example: 16x16';
        return false;
      } else if (this.errorValidator.hasOwnProperty(attr)) {
        delete this.errorValidator[attr];
      }
      return true;
    },
    async fetchDepartments() {
      this.isLoading = true;
      try {
        const response = await list();
        this.departments = response.data;
      } catch (e) {
        this.notification('Department not found', 'error');
      }
      this.isLoading = false;
    },
    async resetEmployee() {
      await employeeLogoutTimeChecking(this.time_checking_id);
      this.employee = null;
      this.employeeError = '';
      this.codeEmployee = '';
      this.time = 0;
      this.time_checking_id = null;
      this.focusByElClassScanEmployee();
    },
    focusByElClassScanEmployee(elClass = 'el-form-item-scan-employee') {
      this.$nextTick(() => {
        const el = document.getElementsByClassName(elClass);
        if (!el || !el.length) {
          return;
        }
        const firtsElError = (el && el[0]) || undefined;
        const input = firtsElError.querySelector('input');
        document.body.scrollTop = (input.offsetTop - 120) | 0;
        input.focus();
      });
    },
    async getScanCodeEmloyee() {
      try {
        this.isLoading = true;
        const res = await employeeTimeChecking({
          code: Number(this.codeEmployee),
          job_type: this.job_type,
        });
        if (!res.data.data) {
          this.codeEmployee = '';
          this.employeeError = 'Scan employee code error, please scan again.';
          this.notification(
            'Scan employee code error, please scan again.',
            'error'
          );
          this.focusByElClassScanEmployee();
          return;
        }
        this.employee = res.data.data;
        this.time_checking_id = res.data.id_time_checking;
        this.focusByElClass();
        this.isLoading = false;
      } catch (e) {
        const data = e.response.data;
        let message = this.$t('Not found');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.isLoading = false;
        this.notification(message, 'error');
      }
    },
    handleFileUpload(type) {
      switch (type) {
        case 'file':
          this.files.push(this.$refs.file.input.files[0]);
          if (
            !this.fileSelected.includes(this.$refs.file.input.files[0].name)
          ) {
            this.fileSelected.push(this.$refs.file.input.files[0].name);
          }
          break;
        case 'image':
          this.images.push(this.$refs.image.input.files[0]);
          if (
            !this.fileSelected.includes(this.$refs.image.input.files[0].name)
          ) {
            this.fileSelected.push(this.$refs.image.input.files[0].name);
          }
          break;
        case 'video':
          this.videos.push(this.$refs.video.input.files[0]);
          if (
            !this.fileSelected.includes(this.$refs.video.input.files[0].name)
          ) {
            this.fileSelected.push(this.$refs.video.input.files[0].name);
          }
          break;
      }
    },
    commonRule() {
      return {
        required: true,
        message: this.$t('This field cannot be left blank.'),
        trigger: 'change',
      };
    },
    async resetData() {
      this.errorValidator = {};
      this.data = {};
      this.requiredMessage = '';
      this.images = [];
      this.videos = [];
      this.files = [];
      this.images = null;
      this.videos = null;
      this.files = null;
      this.listUpload = [];
      this.hasErrorAttachment = false;
      this.errorAttachment = [];
      await this.$refs['formData'].resetFields();
      await this.resetEmployee();
    },
    handleChangeImage(file, listFile) {
      this.listUpload = listFile;
    },
    async createTicket() {
      const isValid = await this.$refs['formData'].validate();
      if (!isValid) return;
      this.isLoading = true;
      let formData = new FormData();
      formData.append('urgency', this.data.urgency);
      formData.append('department_id', this.data.department_id);
      formData.append('ticket_name', this.data.ticket_name);
      formData.append('long_description', this.data.long_description);
      formData.append('employee_id', this.employee.id);
      formData.append('email', this.data.email ?? '');
      formData.append('phone_number', this.data?.phone_number ?? '');
      formData.append('order_numbers', this.data?.order_numbers ?? '');
      try {
        for (let i = 0; i < this.listUpload.length; i++) {
          let file = this.listUpload[i].raw;
          formData.append('file[' + i + ']', file);
        }
        await create(formData);
        const notification = this.$t(
          'Create internal ticket successfully',
          'success'
        );
        this.data = {};
        this.$refs['formData'].resetFields();
        this.notification(notification);
        this.$emit('createdTicket');
        this.openDialogCreate = false;
      } catch (e) {
        this.errorValidator = e.response.data.errors;
        this.mapKey = Object.keys(this.errorValidator);
        this.hasErrorAttachment = this.mapKey.some((item) => {
          return (
            item.indexOf('image') !== -1 ||
            item.indexOf('file') !== -1 ||
            item.indexOf('video') !== -1
          );
        });
        this.errorAttachment = [];
        for (const property in this.errorValidator) {
          if (property.indexOf('file') !== -1) {
            let name =
              this.errorValidator[property][0].match(/\s(.*)\.(\d)+\s/);
            let stringReplace = name[0].split('.')[0] + ' ';
            let error = this.errorValidator[property][0].replace(
              name[0],
              stringReplace
            );
            this.errorAttachment.push(error);
          }
        }
      }
      this.isLoading = false;
    },
    removeFile(name) {
      this.images = this.images.filter((item) => {
        this.image = null;
        return item.name != name;
      });
      this.videos = this.videos.filter((item) => {
        this.video = null;
        return item.name != name;
      });
      this.files = this.files.filter((item) => {
        this.file = null;
        return item.name != name;
      });
      this.fileSelected = this.fileSelected.filter((item) => item != name);
    },
  },
};
