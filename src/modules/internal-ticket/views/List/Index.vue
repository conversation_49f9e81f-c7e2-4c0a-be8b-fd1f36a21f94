<style src="./Style.scss" lang="scss"></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ $t('Tickets') }}</h1>
      </div>
      <div class="top-head-right">
        <span class="mr-3">{{ $t('Total Tickets: ') }}{{ totalTicket }}</span>
        <span class="mr-3">{{ $t('Resolved: ') }}{{ totalResolved }}</span>
        <span class="mr-3">{{ $t('In Progress: ') }}{{ totalInProgress }}</span>
        <span class="mr-3 text-red-500"
          >{{ $t('Unresolved: ') }}{{ totalUnresolved }}</span
        >
        <el-button type="primary" @click="create">
          <span class="icon-margin-right"><icon :data="iconAdd" /></span
          >{{ $t('Create') }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <div class="filter">
        <div class="label">{{ $t('Filter by:') }}</div>
        <div class="filter-item ml-2">
          <el-dropdown
            ref="ticket_number"
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('ticket_number') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('ticket_number')">
                <el-tooltip
                  effect="dark"
                  :content="$t('Ticket Number')"
                  placement="top-start"
                >
                  <span>{{ filter.ticket_number }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t('Ticket Number') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input
                  :placeholder="$t('Enter search Ticket Number')"
                  class="search"
                  v-model="filter.ticket_number"
                  @keydown.enter="onFilter('ticket_number')"
                  clearable
                  @clear="clearFilterItem('ticket_number')"
                />
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item ml-2">
          <el-dropdown
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('urgency') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('urgency')">
                <el-tooltip
                  effect="dark"
                  :content="$t('Urgency')"
                  placement="top-start"
                >
                  <span>{{ getUrgency(filter.urgency) }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(' Urgency ') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-select
                  filterable
                  v-model="filter.urgency"
                  :placeholder="$t('Select Urgency')"
                  @change="onFilter"
                >
                  <el-option
                    v-for="item in urgency"
                    :key="item.value"
                    :label="item.label"
                    :value="String(item.value)"
                  >
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item ml-2">
          <el-dropdown
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('status') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('status')">
                <el-tooltip
                  effect="dark"
                  :content="$t('Status')"
                  placement="top-start"
                >
                  <span>{{ getStatus(filter.status) }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(' Status ') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-select
                  filterable
                  v-model="filter.status"
                  :placeholder="$t('Select Status')"
                  @change="onFilter"
                >
                  <el-option
                    v-for="item in status"
                    :key="item.value"
                    :label="item.label"
                    :value="String(item.value)"
                  >
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item ml-2">
          <el-dropdown
            ref="ticket_number"
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('employee_name') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('employee_name')">
                <el-tooltip
                  effect="dark"
                  :content="$t('Submitter')"
                  placement="top-start"
                >
                  <span>{{ filter.employee_name }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t('Submitter') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input
                  :placeholder="$t('Enter Search Submitter')"
                  class="search"
                  v-model="filter.employee_name"
                  @keydown.enter="onFilter('employee_name')"
                  clearable
                  @clear="clearFilterItem('employee_name')"
                />
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item ml-2">
          <el-dropdown
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': date && date.length }"
          >
            <span class="el-dropdown-link">
              <template v-if="date && date.length">
                <el-tooltip
                  effect="dark"
                  :content="$t('Issued Date')"
                  placement="top-start"
                >
                  <span> {{ templateDateRange(date[0], date[1]) }}</span>
                </el-tooltip>
              </template>
              <template v-else> {{ $t('Issued Date') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-date-picker
                  format="YYYY-MM-DD"
                  v-model="date"
                  type="daterange"
                  range-separator="To"
                  :start-placeholder="$t('Start Date')"
                  :end-placeholder="$t('End Date')"
                  @change="onChangeDate"
                >
                </el-date-picker>
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item" v-if="hasFilter">
          <el-link type="danger" @click="onClearFilter" :underline="false">
            {{ $t('Clear') }}
          </el-link>
        </div>
      </div>

      <el-table
        stripe
        border
        size="small"
        :data="items"
        style="width: 100%"
        @sort-change="sortTable"
        :max-height="maxHeight"
        v-loading="isLoading"
        element-loading-text="Loading..."
        @row-click="selectedRow"
      >
        <el-table-column
          class-name="cursor-pointer"
          prop="ticket_number"
          :label="$t('Ticket Number')"
        >
          <template class="text-cyan-400 hover:" #default="scope">
            <el-link :underline="false" type="primary">
              {{ scope.row.ticket_number }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column prop="ticket_name" :label="$t('Ticket Name')">
          <template #default="scope">
            {{ scope.row.ticket_name }}
          </template>
        </el-table-column>

        <el-table-column
          prop="urgency"
          class-name="text-center"
          :label="$t('Urgency')"
        >
          <template #default="scope">
            <el-tag
              type="info"
              :class="getClassUrgency(scope.row.urgency)"
              class="rounded-xl"
              effect="dark"
              round
              size="small"
            >
              {{ getOrderStatusByValue(scope.row.urgency, urgency) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="status"
          class-name="text-center"
          :label="$t('Status')"
        >
          <template class="text-cyan-400 hover:" #default="scope">
            <el-tag
              :type="
                scope.row.status == 'unresolved'
                  ? 'error'
                  : scope.row.status == 'resolved'
                  ? 'success'
                  : ''
              "
            >
              {{ getOrderStatusByValue(scope.row.status, status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="employee_name" :label="$t('Submitter')">
          <template #default="scope">
            {{ scope.row?.employee?.name }}
          </template>
        </el-table-column>

        <el-table-column prop="employee_name" :label="$t('Resolved by')">
          <template #default="scope">
            {{ scope.row?.resolved_by?.name }}
          </template>
        </el-table-column>

        <el-table-column prop="assign_to" :label="$t('Assign')">
          <template #default="scope">
            <el-tooltip
              v-if="scope.row?.assign_to"
              effect="dark"
              :content="
                convertUtcToLocalTime(
                  scope.row.assign_at,
                  'YYYY-MM-DD HH:mm:ss'
                )
              "
              placement="top-start"
            >
              {{ getInfoTechEmployee(scope.row?.assign_to) }}
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column
          prop="created_at"
          sortable="custom"
          :label="$t('Issued Date')"
        >
          <template #default="scope">
            {{
              convertUtcToLocalTime(scope.row.created_at, 'YYYY-MM-DD HH:mm:ss')
            }}
          </template>
        </el-table-column>
        <el-table-column prop="age" :label="$t('Age')">
          <template #default="scope">
            {{ scope.row?.age ?? '' }}
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom">
        <div class="total">
          {{ $t('Total:') }} {{ items.length ? formatNumber(total) : 0 }}
        </div>
        <el-pagination
          :disabled="isLoading"
          background
          layout="prev, pager, next"
          :page-size="filter.limit"
          :total="total"
          @current-change="changePage"
          v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
            v-model="filter.limit"
            :placeholder="$t('Select')"
            size="mini"
            @change="onFilter"
          >
            <el-option
              v-for="item in limits"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
    <el-dialog
      v-model="assignDialog"
      @close="closeAssignDialog"
      :show-close="false"
      width="30%"
      custom-class="dialog"
    >
      <template #title>
        <span class="font-bold text-xl word-break-custom">Assign To Dev</span>
      </template>
      <div class="custom-dialog">
        <div>
          <div class="mb-2">
            <div v-if="employee" class="bg-gray-50 p-3 border rounded">
              <div class="flex justify-between">
                <b style="font-size: 18px" class="mr-2"
                  >{{ $t('Hi') }} {{ employee.name + ',' }}
                  {{ $t(' Have a nice day!') }}</b
                >
                <el-link
                  type="danger"
                  class="ml-3"
                  @click="resetEmployee"
                  :underline="false"
                  >{{ $t('Logout') }}
                </el-link>
              </div>
            </div>
          </div>
          <div v-if="!employee" class="mb-1 required-icon">
            Scan Employee ID
          </div>
          <div
            v-show="!employee"
            :error="employeeError"
            @keyup.enter="getScanCodeEmloyee"
          >
            <el-input
              :placeholder="$t('Employee ID')"
              class="el-form-item-scan-employee max-w-fit"
              ref="employeeCode"
              v-model="codeEmployee"
            />
          </div>
        </div>
        <el-form
          class="mt-4"
          status-icon
          ref="formData"
          :model="data"
          :rules="dataRules"
          @submit.prevent="onSubmit"
          label-width="130px"
          :label-position="'top'"
        >
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="assignDialog = false">Close</el-button>
          <el-button type="primary" :disabled="!employee" @click="assignTicket"
            >Assign</el-button
          >
        </span>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showDetail"
      @close="resetData"
      :show-close="false"
      custom-class=" el-dialog dialog-detail el-dialog-medium"
    >
      <template #title>
        <div class="flex justify-between">
          <span class="font-bold text-xl word-break-custom"
            >Ticket: {{ ticketSelected.ticket_name }} (#{{
              ticketSelected.ticket_number
            }})</span
          >
          <!-- <div
              v-if="ticketSelected.status == 'unresolved'"
              :class="['cursor-pointer']"
              style="width: 25px; height: 25px; justify-self: center"
              @click="openAssignDialog"
          >
            <icon class="!w-full !h-full" :data="iconUser2x" />
          </div> -->
        </div>
      </template>
      <div v-if="ticketSelected" class="mb-3">
        <div class="flex items-center content-between">
          <div class="font-semibold mr-3 flex-[0_0_80px]">
            {{ $t('Assign') }}:
          </div>
          <div class="flex-1">
            <el-select
              filterable
              v-model="assignToId"
              :placeholder="$t('Assign to')"
              :disabled="
                isLoadingAssignTo || ticketSelected.status === 'resolved'
              "
              @change="assignTo"
              class="w-52"
              clearable
            >
              <el-option
                v-for="item in techEmployees"
                :key="item.id"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </div>
        </div>
      </div>

      <div v-if="ticketSelected" class="border border-solid border-gray-300">
        <div class="flex justify-space-between ticket-item p-2">
          <div class="w-2/4 pr-2">
            <div class="flex justify-space-between mb-1">
              <span class="font-bold flex-[0_0_80px]"
                >{{ $t('Submitter') }}:</span
              >
              {{ ticketSelected?.employee?.name }}
            </div>
            <div class="flex justify-space-between">
              <span class="font-bold flex-[0_0_80px]">{{ $t('Date') }}:</span>
              {{
                convertUtcToLocalTime(
                  ticketSelected.created_at,
                  'YYYY-MM-DD HH:mm:ss'
                )
              }}
            </div>
          </div>
          <div class="w-2/4 pl-2">
            <div class="flex justify-space-between mb-1">
              <span class="font-bold flex-[0_0_80px]"
                >{{ $t('Urgency') }}:
              </span>
              <el-tag
                type="info"
                :class="getClassUrgency(ticketSelected.urgency)"
                class="rounded-xl"
                effect="dark"
                round
                size="small"
              >
                {{ getOrderStatusByValue(ticketSelected.urgency, urgency) }}
              </el-tag>
            </div>
            <div class="flex justify-space-between">
              <span class="font-bold flex-[0_0_80px]"
                >{{ $t('Status') }}:
              </span>
              <el-tag
                :class="['rounded-xl']"
                effect="dark"
                round
                size="small"
                :type="
                  ticketSelected.status == 'unresolved'
                    ? 'error'
                    : ticketSelected.status == 'resolved'
                    ? 'success'
                    : ''
                "
              >
                {{ getOrderStatusByValue(ticketSelected.status, status) }}
              </el-tag>
            </div>
          </div>
        </div>
        <div class="mt-2 ticket-item p-2">
          <p class="word-break-custom">
            <span class="font-bold">{{ $t('Email') }}: </span
            >{{ ticketSelected?.email ?? '' }}
          </p>
        </div>
        <div class="mt-2 ticket-item p-2">
          <p class="word-break-custom">
            <span class="font-bold">{{ $t('Phone Number') }}: </span
            >{{ ticketSelected?.phone_number ?? '' }}
          </p>
        </div>
        <div class="mt-2 ticket-item p-2">
          <p class="word-break-custom">
            <span class="font-bold">{{ $t('Description') }}: </span
            >{{ ticketSelected.long_description }}
          </p>
        </div>
        <div v-if="ticketSelected.order_numbers" class="mt-2 ticket-item p-2">
          <p class="word-break-custom">
            <span class="font-bold">{{ $t('Order Numbers') }}: </span
            >{{ ticketSelected?.order_numbers ?? '' }}
          </p>
        </div>
        <div class="mt-2 ticket-item p-2">
          <p class="font-bold">{{ $t('Attachments') }}:</p>
          <div
            @click="download(file.file)"
            class="hover:bg-gray-100 rounded cursor-pointer"
            v-for="file in ticketSelected?.internal_ticket_files"
          >
            <el-link :underline="false" type="primary">
              {{ getFileName(file.file) }}
            </el-link>
          </div>
        </div>
        <div
          v-if="ticketSelected.status == 'resolved'"
          class="mt-2 ticket-item p-2"
        >
          <p class="word-break-custom">
            <span class="font-bold">{{ $t('Note handle') }}: </span
            >{{ ticketSelected.note }}
          </p>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDetail = false">Close</el-button>
          <el-button
            v-if="ticketSelected.status != 'resolved'"
            type="primary"
            @click="
              showDetail = false;
              showDialogUpdateStatus = true;
            "
            >Resolve</el-button
          >
        </span>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showDialogUpdateStatus"
      custom-class="el-dialog-custom el-dialog-box"
      @close="resetData"
      width="35%"
      draggable
    >
      <template #title>
        <span class="font-bold text-xl">Change To Resolved</span>
      </template>
      <div>
        <div>
          <div class="mb-2">
            <div v-if="employee" class="bg-gray-50 p-3 border rounded">
              <div class="flex justify-between">
                <b style="font-size: 18px" class="mr-2"
                  >{{ $t('Hi') }} {{ employee.name + ',' }}
                  {{ $t(' Have a nice day!') }}</b
                >
                <el-link
                  type="danger"
                  class="ml-3"
                  @click="resetEmployee"
                  :underline="false"
                  >{{ $t('Logout') }}
                </el-link>
              </div>
            </div>
          </div>
          <div
            v-if="ticketSelected && ticketSelected.long_description"
            class="mb-2"
          >
            <div class="font-semibold">{{ $t('Description') }}:</div>
            <div
              style="white-space: pre-line"
              v-html="ticketSelected.long_description"
            ></div>
          </div>
          <div v-if="!employee" class="mb-1 required-icon">
            Scan Employee ID
          </div>
          <div
            v-show="!employee"
            :error="employeeError"
            @keyup.enter="getScanCodeEmloyee"
          >
            <el-input
              :placeholder="$t('Employee ID')"
              class="el-form-item-scan-employee max-w-fit"
              ref="employeeCode"
              v-model="codeEmployee"
            />
          </div>
        </div>
        <el-form
          class="mt-4"
          status-icon
          ref="formData"
          :model="data"
          :rules="dataRules"
          @submit.prevent="onSubmit"
          label-width="130px"
          :label-position="'top'"
        >
          <el-form-item :label="$t('Note:')" prop="note" class="block">
            <el-input
              type="textarea"
              rows="7"
              v-model="data.note"
              :class="{
                'border border-red-400 rounded':
                  errorValidator && errorValidator.note,
              }"
            ></el-input>
          </el-form-item>
          <div
            class="text-danger text-[12px]"
            v-if="errorValidator && errorValidator.note"
          >
            {{ errorValidator.note[0] }}
          </div>
        </el-form>
      </div>
      <template #footer>
        <el-button
          type="primary"
          @click="onSubmit"
          :disabled="!employee"
          :loading="isLoading"
          >Submit
        </el-button>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showDialogStatusDetail"
      custom-class="el-dialog-custom-body"
      @close="resetData"
      width="25%"
      :show-close="false"
    >
      <div v-if="ticketSelected">
        <div class="ticket-item p-2">
          <div class="flex justify-space-between mb-1">
            <span class="font-bold">{{ $t('Status') }}: </span>
            <el-tag
              class="rounded-xl"
              effect="dark"
              round
              size="small"
              :type="
                ticketSelected.status == 'unresolved' ? 'error' : 'success'
              "
            >
              {{ getOrderStatusByValue(ticketSelected.status, status) }}
            </el-tag>
          </div>
          <div class="flex justify-space-between mb-1">
            <span class="font-bold">{{ $t('Date Resolved') }}: </span>
            <el-tag
              class="rounded-xl"
              effect="dark"
              round
              size="small"
              type="info"
            >
              {{
                convertUtcToLocalTime(
                  ticketSelected.created_at,
                  'YYYY-MM-DD HH:mm:ss'
                )
              }}
            </el-tag>
          </div>
        </div>
        <div class="border-solid border border-gray-500 p-2 mt-1">
          <div class="flex justify-space-between mb-1">
            <span class="font-bold">{{ $t('Employee') }}: </span>
            <div>{{ ticketSelected?.resolved_by?.name }}</div>
          </div>
          <p style="word-break-custom">
            <span class="font-bold">{{ $t('Note') }}: </span
            >{{ ticketSelected.note }}
          </p>
        </div>
      </div>
    </el-dialog>
    <div v-if="renderCreatePopup">
      <CreateInternalTicket
        @createdTicket="fetchData"
        :status="status"
        :urgency="urgency"
        @forceRender="forceRender"
      />
    </div>
  </div>
</template>
<style scoped>
.word-break-custom {
  word-break: break-word;
}
.el-dialog__body {
  @apply py-2;
}
</style>
