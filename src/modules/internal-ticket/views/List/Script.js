import {
  list,
  updateStatus,
  getCount,
  assignTicket,
} from '@/api/internalTicket.js';
import { mapGetters } from 'vuex';
import { equals, isEmpty } from 'ramda';
import filterMixin from '@/mixins/filter';
import dateMixin from '@/mixins/date.js';
import focusMixin from '@/mixins/helpers.js';
import moment from 'moment';
import {
  employeeLogoutTimeChecking,
  employeeTimeChecking,
} from '@/api/employee.js';
import EventBus from '@/utilities/eventBus.js';
import CreateInternalTicket from '@/modules/internal-ticket/views/Create/Index.vue';
import { API_URL } from '@/utilities/constants';

export default {
  name: 'Ticket',
  components: {
    CreateInternalTicket,
  },
  mixins: [filterMixin, dateMixin, focusMixin],
  data() {
    return {
      renderCreatePopup: true,
      items: [],
      filter: this.setDefaultFilter(),
      isLoading: false,
      date: '',
      openDropdownTag: false,
      showDetail: false,
      showDialogUpdateStatus: false,
      showDialogStatusDetail: false,
      openDialogCreate: false,
      assignDialog: false,
      isEdit: false,
      ticketSelected: null,
      employee: null,
      time_checking_id: null,
      statusUpdate: null,
      codeEmployee: '',
      employeeError: '',
      note: '',
      id_time_checking: '',
      totalResolved: 0,
      totalInProgress: 0,
      totalUnresolved: 0,
      totalTicket: 0,
      total: 0,
      errorValidator: {},
      countTicket: {},
      data: {},
      dataRules: {
        note: [this.commonRule()],
      },
      job_type: 'resolved_ticket',
      urgency: [
        {
          label: 'Emergency',
          value: 'emergency',
        },
        {
          label: 'High',
          value: 'high',
        },
        {
          label: 'Medium',
          value: 'medium',
        },
        {
          label: 'Low',
          value: 'low',
        },
      ],
      status: [
        {
          label: 'Unresolved',
          value: 'unresolved',
        },
        {
          label: 'In Progress',
          value: 'in_progress',
        },
        {
          label: 'Resolved',
          value: 'resolved',
        },
      ],
      assignToId: '',
      isLoadingAssignTo: false,
    };
  },
  destroyed() {
    EventBus.$off('showCreateInternalTicket');
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 220);
    },
    ...mapGetters({
      getEmployees: 'getEmployees',
    }),
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },
    techEmployees() {
      return this.getEmployees.filter(
        (item) =>
          item.department && item.department.toLowerCase() === 'technical'
      );
    },
  },
  created() {
    this.fetchData();
    this.fetchEmployee();
    EventBus.$emit('showCreateInternalTicket');
  },
  methods: {
    forceRender() {
      this.renderCreatePopup = false;
      setTimeout(() => {
        this.renderCreatePopup = true;
      }, 100);
    },
    getInfoTechEmployee(code) {
      if (!code) return;
      const employee = this.techEmployees.find((item) => item.code === code);
      return employee?.name || '';
    },
    async fetchEmployee() {
      await this.$store.dispatch('getEmployees');
    },
    async assignTo() {
      this.isLoadingAssignTo = true;
      let params = {
        assign_to: this.assignToId,
        id: this.ticketSelected.id,
      };
      try {
        await assignTicket(params);
        await this.fetchTickets();
        this.notification(this.$t('Assign ticket successfully', 'success'));
        this.showDetail = false;
      } catch (e) {}
      this.isLoadingAssignTo = false;
    },
    async assignTicket() {
      this.isLoading = true;
      let params = {
        employee_id: this.employee.id,
        id: this.ticketSelected.id,
        type: 'update_in_progress',
      };
      try {
        await updateStatus(params);
        await this.fetchTickets();
        this.data = {};
        this.notification(this.$t('Assign ticket successfully', 'success'));
        this.ticketSelected.status = 'in_progress';
        this.assignDialog = false;
      } catch (e) {}
      this.isLoading = false;
    },
    checkDisableStatus(status) {
      if (this.statusUpdate == 'resolved') return true;
      if (this.statusUpdate == 'in_progress') {
        return !['unresolved', 'in_progress'].includes(status);
      }
      return ['unresolved'].includes(status);
    },
    openAssignDialog() {
      if (this.ticketSelected.status != 'unresolved') {
        return;
      }
      this.assignDialog = true;
    },
    closeAssignDialog() {
      this.resetEmployee();
    },
    sortTable(data) {
      let sortColumn = '';
      let sortBy = '';
      if (data.prop && data.order) {
        sortColumn = data.prop;
        if (data.order === 'ascending') {
          sortBy = 'ASC';
        } else if (data.order === 'descending') {
          sortBy = 'DESC';
        }
      }
      this.filter.sort_column = sortColumn;
      this.filter.sort_by = sortBy;
      this.$nextTick(() => {
        this.fetchTickets();
      });
    },
    download(filePath) {
      return window.open(`${filePath}`, '_blank');
    },
    getFileName(path) {
      let filePaths = path.split('/');
      return filePaths[filePaths.length - 1] ?? '';
    },
    commonRule() {
      return {
        required: true,
        message: this.$t('This field cannot be left blank.'),
        trigger: 'change',
      };
    },
    async fetchData() {
      this.filter = this.getRouteParam();
      this.fetchTickets();
      this.getCountTickets();
    },
    onFilter(item = '') {
      this.filter.page = 1;
      this.$nextTick(() => {
        this.fetchTickets();
        if (item && this.$refs[item]) {
          this.$refs[item].handleClose();
        }
      });
    },
    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.date = '';
      this.$nextTick(() => {
        this.fetchTickets();
      });
    },
    onChangeDate() {
      if (this.date && this.date.length) {
        this.filter.start_date = this.formatDate(this.date[0], true);
        this.filter.end_date = this.formatDate(this.date[1], true);
      } else {
        this.filter.start_date = '';
        this.filter.end_date = '';
      }

      this.onFilter();
    },
    setDefaultFilter() {
      let params = {
        limit: 25,
        page: 1,
        sort_column: '',
        ticket_number: '',
        urgency: '',
        status: '',
        employee_name: '',
        start_date: '',
        end_date: '',
      };
      return params;
    },
    setRouteParam() {
      const params = this.filter;
      this.$router.replace({ name: 'internal_ticket', query: params });
    },
    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);
      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }
      if (filter.start_date && filter.end_date) {
        this.date = [filter.start_date, filter.end_date];
      }
      filter.page = +filter.page || 1;
      return filter;
    },
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchTickets();
      });
    },
    async fetchTickets() {
      this.isLoading = true;
      this.setRouteParam();
      const res = await list(this.filter);
      this.isLoading = false;
      const data = res.data || [];
      this.total = data.total || 0;
      this.items = data.data || [];
    },
    async getCountTickets() {
      this.isLoading = true;
      const res = await getCount();
      this.countTicket = res.data;
      this.totalResolved =
        res.data.find((item) => item.status == 'resolved')?.quantity ?? 0;
      this.totalUnresolved =
        res.data.find((item) => item.status == 'unresolved')?.quantity ?? 0;
      this.totalInProgress =
        res.data.find((item) => item.status == 'in_progress')?.quantity ?? 0;
      this.totalTicket =
        this.totalResolved + this.totalUnresolved + this.totalInProgress;
    },
    hasChangeFilterByItem(name) {
      const query = this.$route.query;
      if (query[name]) {
        return true;
      }
      return false;
    },
    clearFilterItem(item) {
      this.filter[item] = '';
      if (this.$refs[item]) {
        this.$refs[item].handleClose();
      }
      this.onFilter();
    },

    getUrgency(value) {
      const selectItem = this.urgency.find(
        (item) => item.value.toLowerCase() === value.toLowerCase()
      );
      return (selectItem && selectItem.label) || '';
    },
    getStatus(value) {
      const selectItem = this.status.find(
        (item) => item.value.toLowerCase() === value.toLowerCase()
      );
      return (selectItem && selectItem.label) || '';
    },
    selectedRow(row, column) {
      this.ticketSelected = row;
      this.statusUpdate = row.status;
      this.assignToId = this.ticketSelected.assign_to || '';
      if (column.property == 'ticket_number') {
        this.showDetail = true;
      }
    },
    async resetData() {
      this.errorValidator = {};
      this.data = {};
      this.isEdit = false;
      await this.resetEmployee();
    },
    formatTime(time, format = 'MMM DD, YYYY') {
      return moment(time).format(format);
    },
    async getScanCodeEmloyee() {
      try {
        this.isLoading = true;
        const res = await employeeTimeChecking({
          code: Number(this.codeEmployee),
          job_type: this.job_type,
        });
        if (!res.data.data) {
          this.codeEmployee = '';
          this.employeeError = 'Scan employee code error, please scan again.';
          this.notification(
            'Scan employee code error, please scan again.',
            'error'
          );
          this.focusByElClassScanEmployee();
          return;
        }
        this.employee = res.data.data;
        this.time_checking_id = res.data.id_time_checking;
        this.focusByElClass();
        this.isLoading = false;
      } catch (e) {
        const data = e.response.data;
        let message = this.$t('Not found');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.isLoading = false;
        this.notification(message, 'error');
      }
    },
    async resetEmployee() {
      await employeeLogoutTimeChecking(this.time_checking_id);
      this.employee = null;
      this.employeeError = '';
      this.codeEmployee = '';
      this.time_checking_id = null;
      this.time = 0;
      this.focusByElClassScanEmployee();
    },
    async onSubmit() {
      const isValid = await this.$refs['formData'].validate();
      if (!isValid) return;
      this.isLoading = true;
      let params = {
        employee_id: this.employee.id,
        note: this.data.note,
        id: this.ticketSelected.id,
        type: 'resolve_ticket',
      };
      try {
        await updateStatus(params);
        await this.fetchTickets();
        await this.getCountTickets();
        this.data = {};
        this.$refs['formData'].resetFields();
        this.notification(this.$t('Update status successfully', 'success'));
        this.showDialogUpdateStatus = false;
      } catch (e) {
        this.errorValidator = e.response.data.errors;
      }
      this.isLoading = false;
    },
    create() {
      EventBus.$emit('showCreateInternalTicket');
    },
    getClassUrgency(urgency) {
      let classStatus = '';
      switch (urgency) {
        case 'emergency':
          classStatus = `bg-red-400`;
          break;
        case 'high':
          classStatus = `bg-orange-400`;
          break;
        case 'medium':
          classStatus = `bg-indigo-500`;
          break;
        case 'low':
          classStatus = 'bg-stone-400';
          break;
      }
      return classStatus;
    },
    getOrderStatusByValue(status, list) {
      const selectItem = list.find((item) => item.value === status);
      return (selectItem && selectItem.label) || '';
    },
  },
};
