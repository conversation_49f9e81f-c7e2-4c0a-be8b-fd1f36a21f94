<style src="./Style.scss" lang="scss" scoped>
</style>
<script src="./Script.js"></script>
<template>
  <div class="content">
    <HeaderMobile :name="$t('Addition')"></HeaderMobile>
    <div class="content-body">
      <div class="add-inventory-addition flex-col">
        <el-tabs v-model="currentTab" @tab-click="changeTab" class="el-tabs-addition">
          <el-tab-pane :label="$t('Distributor')" name="distributor">
            <el-form status-icon ref="addInventoryAdditionFormdistributor" :model="data" :rules="dataRules"
              @submit.prevent="
                onSubmit(`addInventoryAdditionForm${currentTab}`)
              " :label-position="'top'">
              <div class="bg-gray-50 p-3 border rounded mb-3">
                <el-form-item v-show="!Object.keys(employee).length" :error="employeeError" :label="$t('Employee ID')"
                  required>
                  <el-input v-model="employeeID" @change="scanEmployeeID"
                    class="el-form-item-employee"></el-input>
                </el-form-item>
                <div v-if="Object.keys(employee).length">
                  <div class="flex justify-between">
                    <b class="text-base">Hi {{ employee.name }}, Have a nice day!</b>
                    <el-link type="danger" @click="resetEmployee" :underline="false">{{ $t('Logout') }}</el-link>
                  </div>
                  <div class="text-lg text-fuchsia-500">
                    <IncrementTimer />
                  </div>
                </div>
              </div>
              <el-form-item prop="location_id" :label="$t('Location')" class="el-form-item-location">
                <el-input v-if="
                dataByTrackingNumber &&
                dataByTrackingNumber.items &&
                dataByTrackingNumber.items.length >= 2" class="flex-1" v-model="defaultPullingShelves" disabled>
                </el-input>
                <el-select v-else v-model="data.location_id" filterable remote reserve-keyword ref="locationRefDistributor"
                  @keyup.enter="selectLocationByEnter($event, 'locationRefDistributor')" @change="focusByElClass('el-form-item-tracking-number')"
                  class="flex-1 el-form-item-location-value" :automatic-dropdown="true">
                  <el-option v-for="item in locations" :key="item.id" :label="item.barcode" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="Tracking number (or input Box ID for stock transfer request)" prop="tracking_number"
                class="el-form-item-tracking-number d-flex">
                <el-input class="flex-1" :placeholder="$t('Enter search tracking number')"
                  v-model="data.tracking_number" @change="getPurchaseOrderBoxByTrackingNumber()" @blur="getPurchaseOrderBoxByTrackingNumber()">
                </el-input>
              </el-form-item>
              <el-form-item :label="$t('BoxID')" prop="barcode" class="el-form-item-box-id">
                <el-input v-if="warehouseId !== 18" v-model="data.barcode" :disabled="dataByTrackingNumber &&
                dataByTrackingNumber.items &&
                dataByTrackingNumber.items.length >= 2"
                  @change="onSubmit(`addInventoryAdditionForm${currentTab}`)"></el-input>
                <el-input v-else v-model="data.barcode" :disabled="dataByTrackingNumber &&
                dataByTrackingNumber.items &&
                dataByTrackingNumber.items.length >= 2"
                          @keyup.enter="focusByElClass('el-form-item-country-distributor-mobile')"></el-input>
              </el-form-item>
              <el-form-item
                  v-if="warehouseId === 18"
                  prop="country"
                  :label="$t('Country of origin')"
              >
                <el-select
                    :placeholder="$t('Select country')"
                    class="el-select-country w-full el-form-item-country-distributor-mobile"
                    v-model="data.country"
                    filterable
                    @change="changeCountry"
                >
                  <el-option
                      v-for="item in countries"
                      :key="item.iso2"
                      :label="item.name"
                      :value="item.iso2"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <div v-if="
              dataByTrackingNumber &&
              dataByTrackingNumber.items &&
              dataByTrackingNumber.items.length">
                <div>
                  <label>{{ $t('Vendor') }}: </label>
                  <span>{{ getVendorName(data.vendor_id) }}</span>
                </div>
                <div>
                  <label>{{ $t('PO#') }}: </label>
                  <span>{{ data.po_number }}</span>
                </div>
                <div>
                  <label>{{ $t('Invoice Number') }}: </label>
                  <span>{{ data.invoice_number }}</span>
                </div>
                <el-table
                        border
                        :data="dataByTrackingNumber.items"
                        style="width: 100%"
                        size="small"
                        class="mb-3 mt-1"
                >
                  <el-table-column :label="$t('GTIN')" min-width="140">
                    <template #default="scope">
                      {{ scope.row.gtin }}
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('SKU')" min-width="100">
                    <template #default="scope">
                      {{ scope.row.sku }}
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('Quantity')" width="68">
                    <template #default="scope">
                      {{ scope.row.quantity }}
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('Style')" width="68">
                    <template #default="scope">
                      {{ scope.row?.product?.style }}
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('Size')" width="68">
                    <template #default="scope">
                      {{ scope.row.product?.size }}
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('Color')" width="120">
                    <template #default="scope">
                      {{ scope.row.product?.color }}
                    </template>
                  </el-table-column>
                </el-table>
              </div>

            </el-form>
          </el-tab-pane>
          <el-tab-pane label="Mill / Manual" name="manual">
            <el-form status-icon ref="addInventoryAdditionFormmanual" :model="data" :rules="dataRules" @submit.prevent="
              onSubmit(`addInventoryAdditionForm${currentTab}`)
            " :label-position="'top'">
              <div class="bg-gray-50 p-3 border rounded mb-3">
                <el-form-item v-show="!Object.keys(employee).length" :error="employeeError" :label="$t('Employee ID')"
                  required>
                  <el-input class="el-form-item-manual-employee" v-model="employeeID" @change="scanEmployeeID">
                  </el-input>
                </el-form-item>
                <div v-if="Object.keys(employee).length">
                  <div class="flex justify-between">
                    <b class="text-base">Hi {{ employee.name }}, Have a nice day!</b>
                    <el-link type="danger" @click="resetEmployee" :underline="false">{{ $t('Logout') }}</el-link>
                  </div>
                  <div class="text-lg text-fuchsia-500">
                    <IncrementTimer />
                  </div>
                </div>
              </div>
              <el-form-item prop="location_id" :label="$t('Location')" class="el-form-item-location">
                <el-select v-model="data.location_id" filterable remote reserve-keyword ref="locationRefMil"
                  @keyup.enter="selectLocationByEnter($event, 'locationRefMil')" @change="focusByElClass('el-form-item-vendor')"
                  class="flex-1 el-form-manual-item-location-value" :automatic-dropdown="true">
                  <el-option v-for="item in locations" :key="item.id" :label="item.barcode" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('Vendor')"  class="el-form-item-vendor">
                <el-select v-model="data.vendor_id" :placeholder="$t('Select vendor')"
                  @change="fetchPurchaseOrderByVendor"
                  class="el-form-manual-item-vendor"
                  :automatic-dropdown="true"
                >
                  <el-option v-for="item in getVendorsForMil" :key="item.id" :label="item.name" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="PO#" prop="po_number">
                <!-- :remote-method="searchPurchaseOrder" -->
                <!-- remote -->
                <el-select v-model="data.po_number" filterable reserve-keyword
                  :placeholder="$t('Please enter a keyword')" :loading="isLoadingSearch"
                  :allow-create="purchaseOrders.length ? false : true" @change="selectPurchaseOrder"   
                  class="el-form-manual-item-po"
                  >
                  <el-option v-for="item in purchaseOrders" :key="item.id" :label="getLabelPurchaseOrder(item)"
                    :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('BoxID')" prop="barcode">
                <el-input v-model="data.barcode" class="el-form-manual-item-barcode"
                          @keyup.enter="warehouseId !== 18 ? focusByElClass('el-form-manual-item-gtin') : focusByElClass('el-form-item-country-manual-mobile')"></el-input>
              </el-form-item>
              <el-form-item
                  v-if="warehouseId === 18"
                  prop="country"
                  :label="$t('Country of origin')"
              >
                <el-select
                    :placeholder="$t('Select country')"
                    class="el-select-country w-full el-form-item-country-manual-mobile"
                    v-model="data.country"
                    filterable
                    @change="changeCountry"
                >
                  <el-option
                      v-for="item in countries"
                      :key="item.iso2"
                      :label="item.name"
                      :value="item.iso2"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <div class="el-item-group two">
                <el-form-item :label="$t('GTIN')" prop="gtin">
                  <el-input class="el-form-manual-item-gtin" v-model="data.gtin" @keyup.enter="searchProductByGTIN"></el-input>
                </el-form-item>
                <!-- (' + quantity + ') -->
                <el-form-item :label="
                  'Quantity'
                " prop="quantity">
                  <el-input-number class="el-form-manual-item-qty" @keyup.enter="focusByElClass('el-form-manual-item-product')" v-model="data.quantity" :precision="0" :step="1" :min="0" />
                </el-form-item>
              </div>
              <el-form-item :label="$t('Product')" prop="product_id">
                <el-select v-model="data.product_id" filterable :placeholder="$t('Select Product')"
                  @change="selectProduct" class="el-form-manual-item-product">
                  <el-option v-for="item in products" :key="item.id" :label="item.name" :value="item.id"
                    :disabled="item.disabled">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
        <div class="flex justify-end">
          <el-button @click="resetData">{{ $t('Reset') }}</el-button>
          <el-button type="primary" @click="onSubmit(`addInventoryAdditionForm${currentTab}`)" :disabled="isLoading" class="el-form-item-button-submit"
            :loading="isLoading">{{ $t('Add') }}</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

