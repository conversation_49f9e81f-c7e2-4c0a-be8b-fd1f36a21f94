import { add, addDistributor } from "@/api/inventoryAddition.js";
import {
  getPurchaseOrderBoxByTrackingNumber,
  getPurchaseOrderByVendor,
} from "@/api/purchaseOrder.js";
import { mapGetters } from "vuex";
import { list } from "@/api/location.js";
import { searchProductInPurchaseOrder } from "@/api/product.js";
import { isEmpty } from "ramda";
import ListTrackingNumber from "@/modules/inventory-addition/components/ListTrackingNumber.vue";
import { employeeLogoutTimeChecking, employeeTimeChecking } from "@/api/employee.js";
import IncrementTimer from "@/components/IncrementTimer.vue";
import HeaderMobile from "@/components/HeaderMobile.vue";
import { countries } from '@/api/default.js';

export default {
  name: "InventoryAdditionAdd",
  components: { ListTrackingNumber, IncrementTimer, HeaderMobile },
  props: ['employees'],
  data() {
    return {
      data: {},
      isLoading: false,
      isLoadingSearch: false,
      purchaseOrders: [],
      invoicePurchaseOrders: [],
      products: [],
      currentTab: "distributor",
      locations: [],
      defaultPullingShelves: "Pulling Shelves",
      dataByTrackingNumber: "",
      quantity: "",
      employeeID: '',
      employee: {},
      employeeError: '',
      job_type: "addition",
      id_time_checking: null,
      warehouseId: '',
      countries: [],
    };
  },
  computed: {
    ...mapGetters(["getVendors"]),
    getVendorsForMil() {
      return this.getVendors.filter((item) => +item.type === 1);
    },
    dataRules() {
      let dataRules = this.defaultDataRules();
      if (this.currentTab === "distributor") {
        dataRules["tracking_number"] = [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: "blur",
          },
        ];
      } else {
        dataRules["po_number"] = [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: "change",
          },
        ];
      }
      if (this.warehouseId === 18) {
        dataRules['country'] = [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: ['change', 'blur'],
          },
        ];
      }
      return dataRules;
    },
  },
  mounted() {
    this.focusByElClass('el-form-item-employee');
    this.fetchVendor();
    this.fetchLocation();
    this.fetchCountries();
  },
  created() {
    this.warehouseId = this.$author.warehouseId();
  },
  methods: {
    async resetEmployee() {
      const res = await employeeLogoutTimeChecking(this.id_time_checking);
      this.employee = {};
      this.employeeError = '';
      this.employeeID = '';
      this.id_time_checking = null
    },
    async scanEmployeeID() {
      if (this.id_time_checking) {
        return true;
      }
      if (!this.employeeID) {
        this.employeeError = "Employee ID field cannot be left blank.";
        return false;
      }

      const res = await employeeTimeChecking({
        code: Number(this.employeeID),
        job_type: this.job_type
      })

      if (!res.data.data) {
        this.employeeError = "Can't find your employee ID, please scan again";
        return false;
      }
      this.employeeError = "";
      this.employee = res.data.data;
      this.id_time_checking = res.data.id_time_checking;

      if (this.currentTab === "distributor") {
        this.focusByElClass('el-form-item-location-value');
        return;
      }
      this.focusByElClass('el-form-manual-item-location-value');
      return;
    },
    async fetchVendor() {
      await this.$store.dispatch("getVendors");
    },
    async fetchLocation() {
      const params = {
        without_pagination: true,
      };
      const res = await list(params);
      const data = res.data || [];
      this.locations = data;
    },
    filterLocation(type = 0) {
      return this.locations.filter((item) => item.type == type);
    },
    getVendorName(id) {
      const vendor = this.getVendors.find(
        (item) => +item.id === +id
      );
      return vendor.name || "";
    },
    async fetchPurchaseOrderByVendor() {
      if (!this.data.vendor_id) {
        return;
      }
      const res = await getPurchaseOrderByVendor({
        vendor_id: this.data.vendor_id,
      });
      this.purchaseOrders = res.data || [];
      this.focusByElClass('el-form-manual-item-po');
    },
    async getPurchaseOrderBoxByTrackingNumber() {
      if (!this.data.tracking_number) {
        return;
      }
      const res = await getPurchaseOrderBoxByTrackingNumber(
        this.data.tracking_number
      );
      const data = res.data || undefined;
      this.locationCurrentType = 0;
      if (!data) {
        this.data.location_id = "";
        this.data.vendor_id = "";
        this.dataByTrackingNumber = "";
        this.data.po_number = "";
        this.data.invoice_number = "";
        this.notification(this.$t('Tracking number not found.'), "error");
        return;
      }

      if (data && data.items.length && data.items.length >= 2) {
        this.locationCurrentType = 1;
        this.data.location_id =
          this.getLocationsByType &&
          this.getLocationsByType.length &&
          this.getLocationsByType[0].id;
      }
      if (data.purchase_order && data.purchase_order.vendor_id) {
        this.data.vendor_id = data.purchase_order.vendor_id;
      }
      this.dataByTrackingNumber = data;
      this.data.po_number =
        (data.purchase_order && data.purchase_order.po_number) || "";
      this.data.invoice_number =
        (data.purchase_order && data.purchase_order.invoice_number) || "";
      this.focusByElClass("el-form-item-box-id");
    },
    defaultDataRules() {
      return {
        quantity: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: "blur",
          },
          { type: "number", message: this.$t('Quantity must be a number') },
        ],
        product_id: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: "change",
          },
        ],
      };
    },
    changeTab() {
      this.resetData();

      if(!this?.employee?.name) {
        if (this.currentTab === "distributor") {
          this.focusByElClass('el-form-item-employee');
          return;
        }

        this.focusByElClass('el-form-item-manual-employee');
        return;
      }

      if (this.currentTab === "distributor") {
        this.focusByElClass('el-form-item-location-value');
        return;
      }
      this.focusByElClass('el-form-manual-item-location-value');
    },
    async searchProductByGTIN() {
      const purchaseOrder = this.purchaseOrders.find(
        (item) => +item.id === this.data.po_number
      );
      if(!purchaseOrder?.id) {
        this.notification('PO is required.', "error");
        return;
      }
      try {
        const res = await searchProductInPurchaseOrder({ gtin: this.data.gtin, po_id: purchaseOrder.id });
        const data = res.data;
        if (data && data.id) {
          this.data.quantity = data.gtin_case || 0;
          this.data.product_id = data.id;
          this.focusByElClass('el-form-manual-item-qty');
        }
      } catch (e) {
        const data = e.response.data || {};
        let message = data?.message || "Product not found.";
        if (!isEmpty(data) && !data?.message) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.data.quantity = '';
        this.data.product_id = '';
        this.notification(message, "error");
      }
    },
    selectProduct() {
      const product = this.products.find(
        (item) => +item.id === +this.data.product_id
      );
      const quantity_onhand = product.quantity_onhand || 0;
      const infoQuantity = quantity_onhand + "/" + product.quantity;
      this.quantity = infoQuantity;
      if (product && product.gtin_case > 0) {
        this.data.quantity = product.gtin_case;
        this.data.gtin = product.gtin;
      }
    },
    getLabelPurchaseOrder(item) {
      if (item.invoice_number) {
        return item.po_number + " - Invoice Number: " + item.invoice_number;
      }
      return item.po_number;
    },
    selectPurchaseOrder(id) {
      const purchaseOrderCurrent = this.purchaseOrders.find(
        (item) => +item.id === +id
      );
      this.quantity = "";
      if (!purchaseOrderCurrent) {
        return;
      }
      if (purchaseOrderCurrent.vendor_id) {
        this.data.vendor_id = purchaseOrderCurrent.vendor_id;
      }

      const invoiceNumber = purchaseOrderCurrent.invoice_number || "";
      this.data.invoice_number = "";
      this.invoicePurchaseOrders = [];
      this.products = [];
      const products = purchaseOrderCurrent.items || [];
      this.setProductItems(products);
      if (invoiceNumber) {
        this.data.invoice_number = id;
        this.invoicePurchaseOrders = [
          {
            id: id,
            invoice_number: invoiceNumber,
          },
        ];
      }
      this.focusByElClass('el-form-manual-item-barcode');
    },
    setProductItems(items) {
      items = items.map((item) => {
        return {
          id: item.product && item.product.id,
          name: item.product && item.product.name,
          sku: item.product && item.product.sku,
          gtin: item.product && item.product.gtin,
          gtin_case: item.product && item.product.gtin_case,
          quantity: item.quantity,
          quantity_onhand: item.quantity_onhand,
        };
      });
      this.products = items;
    },
    resetInsert(onAction = 'default') {
      this.data = {
        ...this.data,
        gtin: null,
        quantity: null,
        product_id: null,
        barcode: null,
      };
      if (onAction == 'default') {
        this.data = {
          ...this.data,
          location_id: null,
          location_name: null,
        }
      }
      this.$nextTick(() => {
        this.$refs["addInventoryAdditionFormdistributor"].clearValidate();
        this.$refs["addInventoryAdditionFormmanual"].clearValidate();
      });
    },
    resetData(onAction = 'default') {
      this.$refs["addInventoryAdditionFormdistributor"].resetFields();
      this.$refs["addInventoryAdditionFormmanual"].resetFields();
      this.dataByTrackingNumber = "";
      this.data = {
        vendor_id: "",
        po_number: "",
        po_id: "",
        gtin: "",
        quantity: "",
        product_id: "",
        barcode: "",
        tracking_number: "",
        invoice_number: "",
        country: '',
      };
      if (onAction == 'default') {
        this.data = {
          ...this.data,
          location_id: "",
          location_name: "",
        }
      }
      if (this.currentTab === "distributor") {
        this.focusByElClass("el-form-item-tracking-number");
      }
    },
    async onSubmit(formName) {
      if (!this.scanEmployeeID()) return;
      const isValid = await this.$refs[formName].validate();
      if (!isValid) {
        return;
      }
      this.isLoading = true;
      try {
        let data = Object.assign({}, this.data);
        const location = this.locations.find(
          (item) => +item.id === this.data.location_id
        );
        let locationType = this.dataByTrackingNumber && this.dataByTrackingNumber.items && this.dataByTrackingNumber.items.length >= 2 ? 1 : 0;
        if (this.currentTab === "distributor") {
          if (locationType === 0 && !this.data.location_id) {
            this.notification(this.$t('Location is required.'), "error");
            this.isLoading = false;
            return;
          }
          let newData = {
            tracking_number: data.tracking_number,
            vendor_id: data.vendor_id,
            location_id: locationType == 0 ? location.id : '',
            barcode: data.barcode || "",
            po_number: data.po_number,
            invoice_number: data.invoice_number,
            location_type: locationType,
            location_name: locationType == 0 ? location.barcode : this.defaultPullingShelves,
            employee_id: this.employeeID,
            id_time_checking: this.id_time_checking,
            country: data.country,
          };
          newData.items =
            (this.dataByTrackingNumber && this.dataByTrackingNumber.items) ||
            [];
          const res = await addDistributor(newData);
          this.isLoading = false;
          this.resetData('success');
        } else {
          const PONumber = this.purchaseOrders.find(
            (item) => +item.id === this.data.po_number
          );

          data.location_type == location.type
          data.po_number =
            (PONumber && PONumber.po_number) || this.data.po_number;
          data.po_id = (PONumber && PONumber.id) || "";
          data.employee_id = this.employeeID;
          data.id_time_checking = this.id_time_checking
          const res = await add(data);
          this.isLoading = false;
          this.resetInsert('success');
          this.focusByElClass('el-form-manual-item-barcode');
        }
        this.notification(this.$t('Inventory addition add successfully.'));
      } catch (e) {
        const data = e.response.data;
        this.isLoading = false;
        let message = this.$t('Inventory addition add error.');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.notification(message, "error");
      }
    },
    focusByElClass(elClass = "el-form-item-tracking-number") {
      setTimeout(() => {
        const el = document.getElementsByClassName(elClass);
        if (!el || !el.length) {
          return;
        }
        const firtsElError = (el && el[0]) || undefined;
        const input = firtsElError.querySelector("input");
        document.body.scrollTop = (input.offsetTop - 120) | 0;
        input.focus();
        this.$refs["addInventoryAdditionFormdistributor"].clearValidate();
      });
    },
    async selectLocationByEnter(evt, ref) {
      const value = evt.target.value || "";
      if (!value) return;
      for (const item of this.locations) {
        if (item.barcode.toLowerCase() === value.toLowerCase()) {
          this.data.location_id = item.id;
          this.$refs[ref].blur();
          if (ref == 'locationRefDistributor') this.focusByElClass("el-form-item-tracking-number");
          else this.focusByElClass("el-form-item-vendor");
          return;
        }
      }
    },
    async fetchCountries() {
      const res = await countries();
      this.countries = res.data || [];
    },
    async changeCountry() {
      this.country = this.countries.find(item => item.iso2 == this.data.country).iso2;
      if (this.currentTab == 'manual') {
        this.focusByElClass('el-form-manual-item-gtin')
      }
    },
  },
};
