<template>
  <div>
    <el-dialog
      v-model="modalProductExport"
      :title="$t('Notification')"
      custom-class="el-dialog-custom "
      :close-on-click-modal="false"
    >
      <template #default>
        <div class="modal-notification-body">
          <div class="text-system">
            The system is processing please wait a moment.
          </div>
          <el-progress
            :text-inside="true"
            :stroke-width="20"
            :percentage="progress"
          />
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { downloadCsv } from "@/utilities/helper.js";
import EventBus from "@/utilities/eventBus.js";
import { splitEvery } from "ramda";
import { list } from "@/api/product.js";

export default {
  name: "ProductExport",
  props: {
    filter: {
      type: Object,
    },
    total: {
      type: Number,
    },
  },
  data() {
    return {
      maxRowExport: 500,
      isLoadingExport: false,
      progress: 0,
      modalProductExport: false,
    };
  },
  created() {
    EventBus.$on("productExport", () => {
      this.exportData();
    });
  },
  methods: {
    async exportData() {
      this.modalProductExport = true;
      this.progress = 0;

      const totalPage = Math.ceil(this.total / this.maxRowExport);
      const totalPageArr = Array.from({ length: totalPage }, (_, i) => i);

      const chunk = splitEvery(5, totalPageArr);
      let dataItems = [];

      for (let chunkItem of chunk) {
        let progress = parseInt(100 / chunk.length);
        this.progress += progress;
        if (this.progress >= 100) {
          this.progress = 95;
        }
        this.$forceUpdate();

        const promiseAll = [];
        let params = Object.assign({}, this.filter);
        params.limit = this.maxRowExport;

        for await (let i of chunkItem) {
          params.page = i + 1;
          promiseAll.push(list(params));
        }

        await Promise.all(promiseAll).then((res) => {
          for (let data of res) {
            let items = (data.data && data.data.data) || [];
            dataItems = [...dataItems, ...items];
          }
        });
      }

      const csvData = this.formatCsvData(dataItems);
      downloadCsv("products.csv", csvData);
      this.modalProductExport = false;
    },
    formatCsvData(data) {
      let csvData = "Name, Stock\n";
      for (let item of data) {
        for (let property in item) {
          if (typeof item[property] === "string") {
            item[property] = item[property].replace(new RegExp('"', "g"), '""');
          }
        }
        csvData += `"${item.name || ""}","${
          item.in_stock ? "In stock" : "Out of stock"
        }"\n`;
      }
      return csvData;
    },
  },
};
</script>

<style lang="scss">
.modal-notification-body {
  text-align: center;
  .text-system {
    margin-bottom: 15px;
  }
}
</style>
