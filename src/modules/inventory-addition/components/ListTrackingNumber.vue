<template>
  <div>
    <el-dialog
        v-model="openDialogListTrackingNumber"
        :title="$t('List Tracking Number')"
        custom-class="el-dialog-custom" style="max-width: 500px!important; width: 500px;"
        :close-on-click-modal="false"
    >
      <template #default>
        <el-radio-group v-model="type">
          <el-radio :label="0">{{ $t('Invoice Number') }}</el-radio>
          <el-radio :label="1">{{ $t('PO Number') }}</el-radio>
        </el-radio-group>
        <el-form-item class="d-flex">
          <el-input
              class="flex-1 mr-2"
              v-model="value"
              @keyup.enter="fetchListTrackingNumber"
          ></el-input>
          <el-button type="primary" @click="fetchListTrackingNumber"
          >{{ $t('Search') }}
          </el-button
          >
        </el-form-item>
        <el-table
            stripe
            :max-height="maxHeight"
            :data="items"
            style="width: 100%;"
            size="small"
            v-if="items && items.length"
        >
          <el-table-column :label="$t('Tracking Number')">
            <template #default="scope">
              {{ scope.row.tracking_number }}
            </template>
          </el-table-column>

          <el-table-column :label="$t('Action')" width="80">
            <template #default="scope">
              <el-button type="success" size="small" @click="selectTrackingNumber(scope.row.tracking_number)"
              >{{ $t('Select') }}
              </el-button>
            </template>
          </el-table-column>

        </el-table>

<!--        <div v-if="items && items.length == 0 && value !=''">
          <el-empty description="not found" />
        </div>-->

      </template>
    </el-dialog>
  </div>
</template>

<script>
import EventBus from "@/utilities/eventBus.js";
import {fetchTrackingNumberByParams} from "@/api/purchaseOrder.js";
import {equals} from "ramda";

export default {
  name: "ProductExport",
  props: {
    filter: {
      type: Object,
    },
    total: {
      type: Number,
    },
  },
  data() {
    return {
      isLoading: false,
      type: 0,
      value: "",
      openDialogListTrackingNumber: false,
      items: null,
      select: "",
    };
  },
  watch: {
    type: {
      handler() {
        this.setDefaultData();
      },
      deep: true,
    },
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 400);
    },

  },
  created() {
    EventBus.$on("showListTrackingNumber", () => {
      this.type = 0;
      this.openDialogListTrackingNumber = true;
      this.setDefaultData();
    });
  },
  methods: {
    setDefaultData() {
      this.value = "";
      this.items = [];
    },
    async fetchListTrackingNumber() {
      if (!this.value) {
        this.items = [];
        return;
      }
      const params = {};
      if (this.type === 0) {
        params.invoice_number = this.value;
      } else if (this.type === 1) {
        params.po_number = this.value;
      }
      const res = await fetchTrackingNumberByParams(params);
      if (res.data.length == 0) {
        this.notification(this.$t('Tracking number not found.'), "error");
      }
      this.items = res.data || [];
    },
    selectTrackingNumber(tracking) {
      if (!tracking) {
        this.notification(this.$t('Tracking number not found.'), "error");
        return;
      }
      this.$emit("selectTrackingNumber", tracking);
      this.openDialogListTrackingNumber = false;
    },
  },
};
</script>

<style lang="scss"></style>
