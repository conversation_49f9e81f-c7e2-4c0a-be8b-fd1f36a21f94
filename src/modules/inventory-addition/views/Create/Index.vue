<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
      v-model="openDialogAddInventoryAddition"
      :title="$t('Create Inventory Addition')"
      custom-class="el-dialog-custom el-dialog-inventory-addition"
      @close="closeModal"
      destroy-on-close
      :close-on-click-modal="false"
      width="88%"
    >
      <template #default>
        <div class="add-inventory-addition flex-col">
          <el-tabs
            v-model="currentTab"
            @tab-click="changeTab"
            class="el-tabs-addition"
          >
            <el-tab-pane :label="$t('Distributor')" name="distributor">
              <el-form
                status-icon
                ref="addInventoryAdditionFormdistributor"
                :model="data"
                :rules="dataRules"
                @submit.prevent="
                  onSubmit(`addInventoryAdditionForm${currentTab}`)
                "
                :label-position="'top'"
              >
                <div class="bg-gray-50 p-3 border rounded mb-3">
                  <el-form-item v-show="!Object.keys(employee).length" :error="employeeError" :label="$t('Employee ID')" required>
                    <el-input class="el-form-distributor-item-employee" v-model="employeeID" @keyup.enter="scanEmployeeID"></el-input>
                  </el-form-item>
                  <div v-if="Object.keys(employee).length">
                    <div class="flex justify-between">
                      <b class="text-base">Hi {{ employee.name }}, Have a nice day!</b>
                      <el-link type="danger" @click="resetEmployee" :underline="false">{{ $t('Logout') }}</el-link>
                    </div>
                    <div class="text-lg text-fuchsia-500" >
                      <IncrementTimer/>
                    </div>
                  </div>
                </div>
                <el-form-item
                  label="Tracking number (or input Box ID for stock transfer request)"
                  prop="tracking_number"
                  class="el-form-item-tracking-number d-flex"
                >
                  <el-input
                    class="flex-1"
                    :placeholder="$t('Enter search tracking number')"
                    ref="trackingNumber"
                    v-model="data.tracking_number"
                    @keyup.enter="getPurchaseOrderBoxByTrackingNumber()"
                    @blur="getPurchaseOrderBoxByTrackingNumber()"
                  ></el-input>
                  <el-button
                    class="ml-2"
                    type="primary"
                    @click.prevent="showListTrackingNumber()"
                    >{{ $t('Select') }}</el-button
                  >
                </el-form-item>
                <el-form-item :label="$t('BoxID')" prop="barcode">
                  <el-input
                    v-model="data.barcode"
                    :disabled="+locationCurrentType === 1"
                    class="el-form-item-box-id"
                    @keyup.enter="warehouseId !== 18 ? focusByElClass('el-form-item-location-value') : focusByElClass('el-form-item-country-distributor')"
                  ></el-input>
                </el-form-item>
                <el-form-item
                    v-if="warehouseId === 18"
                    prop="country"
                    :label="$t('Country of origin')"
                >
                  <el-select
                      :placeholder="$t('Select country')"
                      class="el-select-country w-full el-form-item-country-distributor"
                      v-model="data.country"
                      :disabled="is_disabled_country === true"
                      filterable
                      @change="changeCountry"
                  >
                    <el-option
                        v-for="item in countries"
                        :key="item.iso2"
                        :label="item.name"
                        :value="item.iso2"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item
                  prop="location_id"
                  :label="$t('Location')"
                  class="el-form-item-location"
                >
                  <el-select
                    class="el-form-item-location-type"
                    v-model="locationCurrentType"
                    @change="changeLocationType"
                    :disabled="
                      dataByTrackingNumber.items &&
                      dataByTrackingNumber.items.length >= 2
                    "
                  >
                    <el-option
                      v-for="item in locationTypes"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                  <template v-if="+locationCurrentType === 1">
                    <el-select
                      v-model="data.location_id"
                      filterable
                      remote
                      reserve-keyword
                      :disabled="locationCurrentType === 1"
                      class="flex-1 el-form-item-location-value"
                      @keyup.enter="selectLocation($event)"
                      :remote-method="searchLocation"
                    >
                      <el-option
                        v-for="item in optionLocations"
                        :key="item.id"
                        :label="item.barcode"
                        :value="item.id"
                      >
                      </el-option>
                    </el-select>
                  </template>
                  <template v-else>
                    <el-input
                      v-model="data.location_name"
                      class="flex-1 el-form-item-location-value"
                      @keyup.enter="
                        onSubmit(`addInventoryAdditionForm${currentTab}`)
                      "
                    ></el-input>
                  </template>
                </el-form-item>
                <el-form-item :label="$t('Vendor')">
                  <el-select
                    v-model="data.vendor_id"
                    disabled
                    placeholder=""
                    :suffix-icon="''"
                  >
                    <el-option
                      v-for="item in getVendors"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <div class="el-item-group two">
                  <el-form-item label="PO#" prop="po_number">
                    <el-input v-model="data.po_number" disabled></el-input>
                  </el-form-item>
                  <el-form-item :label="$t('Invoice Number')" prop="invoice_number">
                    <el-input v-model="data.invoice_number" disabled></el-input>
                  </el-form-item>
                </div>
                <div
                  class="product-list mb-3"
                  v-if="
                    dataByTrackingNumber &&
                    dataByTrackingNumber.items &&
                    dataByTrackingNumber.items.length
                  "
                >
                  <el-table
                          border
                          :data="dataByTrackingNumber.items"
                          style="width: 100%"
                          size="small"
                  >
                    <el-table-column :label="$t('GTIN')" min-width="120">
                      <template #default="scope">
                        {{ scope.row.gtin }}
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('SKU')">
                      <template #default="scope">
                        {{ scope.row.sku }}
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('Quantity')" width="68">
                      <template #default="scope">
                        {{ scope.row.quantity }}
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('Style')" width="68">
                      <template #default="scope">
                        {{ scope.row?.product?.style }}
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('Size')" width="68">
                      <template #default="scope">
                        {{ scope.row.product?.size }}
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('Color')" width="120">
                      <template #default="scope">
                        {{ scope.row.product?.color }}
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="Mill / Manual" name="manual">
              <el-form
                status-icon
                ref="addInventoryAdditionFormmanual"
                :model="data"
                :rules="dataRules"
                @submit.prevent="
                  onSubmit(`addInventoryAdditionForm${currentTab}`)
                "
                :label-position="'top'"
              >
                <div class="bg-gray-50 p-3 border rounded mb-3">
                  <el-form-item v-show="!Object.keys(employee).length" :error="employeeError" :label="$t('Employee ID')" required>
                    <el-input class="el-form-manual-item-employee" v-model="employeeID" @keyup.enter="scanEmployeeID"></el-input>
                  </el-form-item>
                  <div v-if="Object.keys(employee).length">
                    <div class="flex justify-between">
                      <b class="text-base">Hi {{ employee.name }}, Have a nice day!</b>
                      <el-link type="danger" @click="resetEmployee" :underline="false">{{ $t('Logout') }}</el-link>
                    </div>
                    <div class="text-lg text-fuchsia-500" >
                      <IncrementTimer/>
                    </div>
                  </div>
                </div>
                <el-form-item :label="$t('Vendor')">
                  <el-select
                    ref="vendor"
                    v-model="data.vendor_id"
                    :placeholder="$t('Select vendor')"
                    @change="fetchPurchaseOrderByVendor"
                    class="el-form-manual-item-vendor"
                    :automatic-dropdown="true"
                  >
                    <el-option
                      v-for="item in getVendorsForMil"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="PO#" prop="po_number">
                  <!-- :remote-method="searchPurchaseOrder" -->
                  <!-- remote -->
                  <el-select
                    v-model="data.po_number"
                    filterable
                    reserve-keyword
                    :placeholder="$t('Please enter a keyword')"
                    :loading="isLoadingSearch"
                    :allow-create="purchaseOrders.length ? false : true"
                    @change="selectPurchaseOrder"
                    class="el-form-manual-item-po"
                  >
                    <el-option
                        v-for="item in purchaseOrders"
                        :key="item.id"
                        :value="item.id"
                        :label="getLabelPurchaseOrder(item)"
                    >
                      <div class="truncate max-w-xs">{{getLabelPurchaseOrder(item)}}</div>
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item
                  prop="location_id"
                  :label="$t('Location')"
                  class="el-form-item-location"
                >
                  <el-select
                    class="el-form-item-location-type"
                    v-model="locationCurrentType"
                    @change="changeLocationType"
                  >
                    <el-option
                      v-for="item in locationTypes"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                  <el-select
                    v-model="data.location_id"
                    filterable
                    remote
                    reserve-keyword
                    :disabled="locationCurrentType === 1"
                    class="flex-1 el-form-manual-item-location"
                    @change="focusByElClass('el-form-manual-item-barcode')"
                    :remote-method="searchLocation"
                  >
                    <el-option
                      v-for="item in optionLocations"
                      :key="item.id"
                      :label="item.barcode"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item :label="$t('BoxID')" prop="barcode">
                  <el-input
                    v-model="data.barcode"
                    :disabled="+locationCurrentType === 1"
                    class="el-form-manual-item-barcode"
                    @keyup.enter="warehouseId !== 18 ? focusByElClass('el-form-manual-item-gtin') : focusByElClass('el-form-item-country-manual')"
                  ></el-input>
                </el-form-item>
                <div class="el-item-group two">
                  <el-form-item :label="$t('GTIN')" prop="gtin">
                    <el-input
                      v-model="data.gtin"
                      @keyup.enter="searchProductByGTIN"
                      class="el-form-manual-item-gtin"
                    ></el-input>
                  </el-form-item>
                  <!-- (' + quantity + ') -->
                  <el-form-item
                    :label="
                      quantity ? 'Quantity' : 'Quantity'
                    "
                    prop="quantity"
                  >
                    <el-input-number
                      v-model="data.quantity"
                      :precision="0"
                      :step="1"
                      :min="0"
                      class="el-form-manual-item-qty"
                      @keyup.enter="focusByElClass('el-form-manual-item-product')"
                    />
                  </el-form-item>
                </div>
                <el-form-item :label="$t('Product')" prop="product_id">
                  <el-select
                    v-model="data.product_id"
                    filterable
                    :placeholder="$t('Select Product')"
                    @change="selectProduct"
                    class="el-form-manual-item-product"
                  >
                    <el-option
                      v-for="item in products"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                      :disabled="item.disabled"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item
                    v-if="warehouseId === 18"
                    prop="country"
                    :label="$t('Country of origin')"
                >
                  <el-select
                      :placeholder="$t('Select country')"
                      class="el-select-country w-full el-form-item-country-manual"
                      v-model="data.country"
                      :disabled="is_disabled_country === true"
                      filterable
                      @change="changeCountry"
                  >
                    <el-option
                        v-for="item in countries"
                        :key="item.iso2"
                        :label="item.name"
                        :value="item.iso2"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-form
            ></el-tab-pane>
          </el-tabs>
          <div class="flex justify-end">
            <el-button @click="resetData">{{ $t('Reset') }}</el-button>
            <el-button
              type="primary"
              @click="onSubmit(`addInventoryAdditionForm${currentTab}`)"
              :disabled="isLoading"
              :loading="isLoading"
              >{{ $t('Add') }}</el-button
            >
          </div>
        </div>
        <div class="add-inventory-addition-history">
          <h3 class="title mb-3">{{ $t('Import history') }}</h3>
          <div class="list-item">
            <el-table
              border
              :data="history"
              style="width: 100%"
              size="small"
              :row-class-name="tableRowClassName"
            >
              <el-table-column :label="$t('Tracking Number')" width="150">
                <template #default="scope">
                  {{ scope.row.tracking_number }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('Location')" width="150">
                <template #default="scope">
                  {{ scope.row.location }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('BoxID')" width="120">
                <template #default="scope">
                  {{ scope.row.box }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('Qty')" width="80">
                <template #default="scope">
                  {{ scope.row.quantity }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('COO')" width="100" v-if="warehouse_id === 18">
                <template #default="scope">
                  <span class="break-normal">
                    {{ scope.row.country_name }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('PO')" width="120">
                <template #default="scope">
                  {{ scope.row.po_number }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('GTIN')" width="150">
                <template #default="scope">
                  {{ scope.row.gtin }}
                </template>
              </el-table-column>

              <el-table-column
                prop="action"
                :label="$t('Action')"
                width="80"
                fixed="right"
              >
                <template #default="scope">
                  <el-popconfirm
                    v-if="!scope.row.is_deleted"
                    :title="'Are you sure to delete ?'"
                    @confirm="inventoryAdditionDestroy(scope.row)"
                  >
                    <template #reference>
                      <el-link :underline="false" type="danger"
                        ><icon :data="iconDelete"
                      /></el-link>
                    </template>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </template>
    </el-dialog>
    <ListTrackingNumber @selectTrackingNumber="selectTrackingNumber" />
  </div>
</template>
