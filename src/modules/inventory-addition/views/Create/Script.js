import EventBus from "@/utilities/eventBus.js";
import { add, revert, addDistributor } from "@/api/inventoryAddition.js";
import {
  getPurchaseOrderByPoNumber,
  getPurchaseOrderBoxByTrackingNumber,
  getPurchaseOrderByVendor,
} from "@/api/purchaseOrder.js";
import { mapGetters } from "vuex";
import { list } from "@/api/location.js";
import { searchProductInPurchaseOrder } from "@/api/product.js";
import { isEmpty } from "ramda";
import ListTrackingNumber from "@/modules/inventory-addition/components/ListTrackingNumber.vue";
import { employeeLogoutTimeChecking, employeeTimeChecking } from "@/api/employee.js";
import IncrementTimer from "@/components/IncrementTimer.vue";
import {fetchCountryPartNumber, countries} from '@/api/default.js';
import {WAREHOUSE_MEXICO} from "@/utilities/constants";
import warehouseMixin from '@/mixins/warehouse';

export default {
  name: "InventoryAdditionAdd",
  components: { ListTrackingNumber, IncrementTimer },
  props: ['employees'],
  mixins: [warehouseMixin],
  data() {
    return {
      data: {},
      isLoading: false,
      openDialogAddInventoryAddition: false,
      isLoadingSearch: false,
      purchaseOrders: [],
      invoicePurchaseOrders: [],
      products: [],
      boxs: [],
      locations: [],
      history: [],
      locationTypes: [
        { label: this.$t('Rack'), value: 0 },
        { label: this.$t('Pulling Shelves'), value: 1 },
      ],
      locationCurrentType: 0,
      currentTab: "distributor",
      dataByTrackingNumber: "",
      quantity: "",
      employeeID: '',
      employee: {},
      employeeError: '',
      job_type: "addition",
      id_time_checking: null,
      warehouseId: '',
      countries: [],
      warehouseMexico: WAREHOUSE_MEXICO,
      poSelected : {},
      is_disabled_country : false,
      warehouse_id: null,
      keyword: ''
    };
  },
  computed: {
    ...mapGetters(["getVendors"]),
    getVendorsForMil() {
      return this.getVendors.filter((item) => +item.type === 1);
    },
    getLocationsByType() {
      return this.locations.filter(
        (item) => item.type === this.locationCurrentType
      );
    },
    optionLocations() {
      if(!this.keyword) {
        return this.getLocationsByType.slice(0,50);
      }
      return this.getLocationsByType.filter((item) => item.barcode.toLowerCase().indexOf(this.keyword.toLowerCase()) > -1).slice(0,50);
    },
    dataRules() {
      let dataRules = this.defaultDataRules();
      if (this.currentTab === "distributor") {
        dataRules["tracking_number"] = [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: "blur",
          },
        ];
      } else {
        dataRules["po_number"] = [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: "change",
          },
        ];
        dataRules["location_id"] = [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: "change",
          },
        ];
      }
      if (this.locationCurrentType === 0) {
        dataRules['barcode'] = [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: ['change', 'blur'],
          },
        ];
      } else {
        this.$refs['addInventoryAdditionFormdistributor'].clearValidate();
        this.$refs['addInventoryAdditionFormmanual'].clearValidate();
      }
      if (this.warehouseId === 18) {
        dataRules['country'] = [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: ['change', 'blur'],
          },
        ];
      }
      return dataRules;
    },
  },
  mounted() {
    this.warehouse_id = this.getWarehouseId();
    this.fetchLocation();
  },
  created() {
    this.warehouseId = this.$author.warehouseId();
    EventBus.$on("showCreateInventoryAddition", () => {
      this.openDialogAddInventoryAddition = true;
      this.keyword = '';
      this.focusByElClass('el-form-distributor-item-employee');
    });
  },
  watch: {
    currentTab(value) {
      this.countries = [];
    }
  },
  methods: {
    searchLocation(query) {
      this.keyword = query;
    },
   async resetEmployee() {
      const res = await employeeLogoutTimeChecking(this.id_time_checking);
      this.employee = {};
      this.employeeError = '';
      this.employeeID = '';
      this.id_time_checking = null
    },
    async scanEmployeeID() {
      
      if (this.id_time_checking) {
        return true;
      }
      if (!this.employeeID) {
        this.employeeError = "Employee ID field cannot be left blank.";
        return false;
      }

      const res = await employeeTimeChecking({
        code: Number(this.employeeID),
        job_type: this.job_type
      })

      if (!res.data.data) {
        this.employeeError = "Can't find your employee ID, please scan again";
        return false;
      }
      this.employeeError = "";
      this.employee = res.data.data;
      this.id_time_checking = res.data.id_time_checking;
      if(this.currentTab == 'manual') {
        this.focusByElClass('el-form-manual-item-vendor');
        return;
      }
      this.focusByElClass();
      return true;
    },
    showListTrackingNumber() {
      EventBus.$emit("showListTrackingNumber");
    },
    selectTrackingNumber(tracking_number) {
      this.data.tracking_number = tracking_number;
      this.getPurchaseOrderBoxByTrackingNumber();
    },
    async fetchPurchaseOrderByVendor() {
      if (!this.data.vendor_id) {
        return;
      }
      const res = await getPurchaseOrderByVendor({
        vendor_id: this.data.vendor_id,
      });
      this.purchaseOrders = res.data || [];
      this.focusByElClass('el-form-manual-item-po');
    },
    async getPurchaseOrderBoxByTrackingNumber() {
      this.is_disabled_country = false;
      this.data.country = "";
      if (!this.data.tracking_number) {
        return;
      }
      const res = await getPurchaseOrderBoxByTrackingNumber(
        this.data.tracking_number
      );
      const data = res.data || undefined;
      this.locationCurrentType = 0;
      if (!data) {
        this.data.location_id = "";
        this.data.vendor_id = "";
        this.dataByTrackingNumber = "";
        this.data.po_number = "";
        this.data.invoice_number = "";
        this.notification(this.$t('Tracking number not found.'), "error");
        return;
      }
      if (this.warehouseMexico.includes(this.$author.warehouseId())) {
        let product_ids = [];
        console.log(data, "data")
        data.items.forEach((item) => {
          product_ids.push(item.product_id)
        })
        if(data.coo_id){
          this.data.country = data.country.iso2;
          const countries = await this.fetchCountries();
          this.countries = countries.filter(item => item.iso2 === this.data.country);
          if(this.countries.length === 1){
            this.is_disabled_country = true;
          }

        }else{
          let params = {
            product_id: product_ids
          }
          fetchCountryPartNumber(params).then((response) => {
            this.countries = response.data || [];
          });
        }
      }

      this.focusByElClass("el-form-item-box-id");
      if (data && data.items.length && data.items.length >= 2) {
        this.locationCurrentType = 1;
        this.data.location_id =
          this.getLocationsByType &&
          this.getLocationsByType.length &&
          this.getLocationsByType[0].id;
      }
      if (data.purchase_order && data.purchase_order.vendor_id) {
        this.data.vendor_id = data.purchase_order.vendor_id;
      }
      this.dataByTrackingNumber = data;
      this.data.po_number =
        (data.purchase_order && data.purchase_order.po_number) || "";
      this.data.invoice_number =
        (data.purchase_order && data.purchase_order.invoice_number) || "";
    },
    defaultDataRules() {
      return {
        quantity: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: "blur",
          },
          { type: "number", message: this.$t('Quantity must be a number') },
        ],
        product_id: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: "change",
          },
        ],
      };
    },
    changeTab() {
      this.resetData();
      if(!this?.employee?.name) {
        if (this.currentTab === "distributor") {
          this.focusByElClass('el-form-distributor-item-employee');
          return;
        }
        this.focusByElClass('el-form-manual-item-employee');
        return;
      }

      if (this.currentTab === "distributor") {
        this.focusByElClass();
        return;
      }
      this.focusByElClass('el-form-manual-item-vendor');
    },
    async fetchLocation() {
      const params = {
        without_pagination: true,
      };
      const res = await list(params);
      const data = res.data || [];
      this.locations = data;
    },
    changeLocationType() {
      this.data.location_id = "";
      this.$nextTick(() => {
        if (+this.locationCurrentType === 1) {
          this.data.location_id =
            this.getLocationsByType.length && this.getLocationsByType[0].id;
          this.data.barcode = "";
          this.focusByElClass("el-form-manual-item-gtin");
          return;
        }
        this.focusByElClass("el-form-manual-item-location");
        return;
      });
    },
    selectLocation(evt) {
      const value = evt.target.value || "";
      if (!value) {
        return;
      }
      const location = this.getLocationsByType.find(
        (item) => item.barcode === value
      );
      if (!location) {
        return;
      }
      if (location && location.id) {
        this.data.location_id = location.id;
        this.onSubmit(`addInventoryAdditionForm${this.currentTab}`);
      }
    },
    async fetchCountries() {
      const res = await countries();
      return res.data || [];
    },
    async searchProductByGTIN() {
      const purchaseOrder = this.purchaseOrders.find(
        (item) => +item.id === this.data.po_number
      );
      if(!purchaseOrder?.id) {
        this.notification('PO is required.', "error");
        return;
      }
      try {
        const res = await searchProductInPurchaseOrder({ gtin: this.data.gtin, po_id: purchaseOrder.id });
        const data = res.data;
        if (data && data.id) {
          this.data.quantity = data.gtin_case || 0;
          this.data.product_id = data.id;
          if (this.warehouseMexico.includes(this.$author.warehouseId())) {
            let params = {
              product_id: [this.data.product_id]
            }
            fetchCountryPartNumber(params).then((response) => {
              this.countries = response.data || [];
            });
          }
          this.focusByElClass('el-form-manual-item-qty');
        }
      } catch (e) {
        const data = e.response.data || {};
        let message = data?.message || "Product not found.";
        if (!isEmpty(data) && !data?.message) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.data.quantity = '';
        this.data.product_id = '';
        this.notification(message, "error");
      }
    },
    async selectProduct() {
      this.is_disabled_country = false;
      this.data.country = "";
      const product = this.products.find(
        (item) => +item.id === +this.data.product_id
      );
      if (this.warehouseMexico.includes(this.$author.warehouseId())) {
        if(this.poSelected.is_fulfill){
          const cooIds = this.poSelected.box.flatMap(box => {
            const productItems = box.items.filter(item => item.product_id === product.id);
            const cooIds = productItems.map(item => item.coo_id);
            return cooIds;
          });
          if(cooIds.length){
            const countries = await this.fetchCountries()
            this.countries = countries.filter(item => cooIds.includes(item.id));
            if(cooIds.length === 1){
             this.data.country = this.countries.find(item => cooIds.includes(item.id))?.iso2;
             this.is_disabled_country = true;
            }
          }
        }else{
          let params = {
            product_id: [this.data.product_id]
          }
          fetchCountryPartNumber(params).then((response) => {
            this.countries = response.data || [];
          });
        }
      }
      const quantity_onhand = product.quantity_onhand || 0;
      const infoQuantity = quantity_onhand + "/" + product.quantity;
      this.quantity = infoQuantity;
      if (product && product.gtin_case > 0) {
        this.data.quantity = product.gtin_case;
        this.data.gtin = product.gtin;
      }
    },
    getLabelPurchaseOrder(item) {
      if (item.invoice_number) {
        return item.po_number + " - Invoice Number: " + item.invoice_number;
      }
      return item.po_number;
    },
    selectPurchaseOrder(id) {
      const purchaseOrderCurrent = this.purchaseOrders.find(
        (item) => +item.id === +id
      );
      this.quantity = "";
      if (!purchaseOrderCurrent) {
        return;
      }
      if (purchaseOrderCurrent.vendor_id) {
        this.data.vendor_id = purchaseOrderCurrent.vendor_id;
      }
      this.poSelected = purchaseOrderCurrent;
      const invoiceNumber = purchaseOrderCurrent.invoice_number || "";
      this.data.invoice_number = "";
      this.invoicePurchaseOrders = [];
      this.products = [];
      const products = purchaseOrderCurrent.items || [];
      this.setProductItems(products);
      if (invoiceNumber) {
        this.data.invoice_number = id;
        this.invoicePurchaseOrders = [
          {
            id: id,
            invoice_number: invoiceNumber,
          },
        ];
      }
      this.focusByElClass('el-form-manual-item-location');
    },
    async searchPurchaseOrder(query) {
      if (query) {
        this.isLoadingSearch = true;
        const res = await getPurchaseOrderByPoNumber({
          po_number: query,
          vendor_id: this.data.vendor_id,
        });
        this.isLoadingSearch = false;
        const data = res.data || [];
        this.purchaseOrders = data;
      } else {
        this.purchaseOrders = [];
      }
    },
    setProductItems(items) {
      items = items.map((item) => {
        return {
          id: item.product && item.product.id,
          name: item.product && item.product.name,
          sku: item.product && item.product.sku,
          gtin: item.product && item.product.gtin,
          gtin_case: item.product && item.product.gtin_case,
          quantity: item.quantity,
          quantity_onhand: item.quantity_onhand,
        };
      });
      this.products = items;
    },
    resetInsert(onAction = 'default') {
      this.data = {
        ...this.data,
        gtin: null,
        quantity: null,
        product_id: null,
        barcode: null,
      };
      if (onAction == 'default') {
        this.data = {
          ...this.data,
          location_id: null,
          location_name: null,
        }
      }
      this.$nextTick(() => {
        this.$refs["addInventoryAdditionFormdistributor"].clearValidate();
        this.$refs["addInventoryAdditionFormmanual"].clearValidate();
      });
    },
    resetData(onAction = 'default') {
      this.$refs["addInventoryAdditionFormdistributor"].resetFields();
      this.$refs["addInventoryAdditionFormmanual"].resetFields();
      this.dataByTrackingNumber = "";
      this.locationCurrentType = 0;
      this.is_disabled_country = false;
      this.quantity = "";
      this.keyword = '';
      this.data = {
        vendor_id: "",
        po_number: "",
        po_id: "",
        gtin: "",
        quantity: "",
        product_id: "",
        barcode: "",
        tracking_number: "",
        invoice_number: "",
        country: '',
      };
      if (onAction == 'default') {
        this.data = {
          ...this.data,
          location_id: "",
          location_name: "",
        }
      }
      if (this.currentTab === "distributor") {
        this.focusByElClass();
      }
    },
    closeModal() {
      if (this.history.length) {
        this.$emit("refresh");
      }
      this.purchaseOrders = [];
      this.invoicePurchaseOrders = [];
      this.boxs = [];
      this.products = [];
      this.history = [];
      this.countries = []
      this.is_disabled_country = false;
      this.resetData();
      this.resetEmployee();
    },
    async inventoryAdditionDestroy(item) {
      try {
        await revert({ id: item.id });
        this.notification("Inventory addition delete successfully!");
        this.history.forEach((history) => {
          if (+history.id === +item.id) {
            history.is_deleted = true;
          }
        });
      } catch (e) {
        let message =
          e.response.data.message ||
          "An error occurred, please try again later!";
        this.notification(message, "error");
      }
    },
    tableRowClassName(data) {
      return data.row.is_deleted ? "is-delete" : "";
    },
    async onSubmit(formName) {
      if (!this.scanEmployeeID()) return;
      const isValid = await this.$refs[formName].validate();
      if (!isValid) {
        return;
      }
      this.isLoading = true;
      try {
        let data = Object.assign({}, this.data);
        const getVendor = this.getVendors.find(
          (item) => +item.id === +data.vendor_id
        );
        const getLocation = this.locations.find(
          (item) => +item.id === +data.location_id
        );
        if (this.currentTab === "distributor") {
          if (+this.locationCurrentType === 0 && !this.data.location_name) {
            this.notification(this.$t('Location is required.'), "error");
            this.isLoading = false;
            return;
          }
          let newData = {
            tracking_number: data.tracking_number,
            vendor_id: data.vendor_id,
            location_id: data.location_name ? data.location_id : "",
            barcode: data.barcode || "",
            po_number: data.po_number,
            invoice_number: data.invoice_number,
            location_type: this.locationCurrentType,
            location_name: data.location_name,
            employee_id: this.employeeID,
            id_time_checking: this.id_time_checking,
            country: data.country,
          };
          newData.items =
            (this.dataByTrackingNumber && this.dataByTrackingNumber.items) ||
            [];
          const res = await addDistributor(newData);
          this.isLoading = false;
          const resData = res.data || [];
          if (resData && resData.length) {
            resData.forEach((item) => {
              this.history.push({
                id: item.id,
                vendor: getVendor && getVendor.name,
                po_number: newData.po_number,
                invoice_number: newData.invoice_number,
                location:
                  (getLocation && getLocation.barcode) || newData.location_name,
                box: newData.barcode,
                tracking_number: newData.tracking_number,
                gtin: item.gtin,
                quantity: item.quantity,
                product: item.name,
                country_name: item.country?.name
              });
            });
          }
          this.resetData('success');
        } else {
          const PONumber = this.purchaseOrders.find(
            (item) => +item.id === this.data.po_number
          );
          data.po_number =
            (PONumber && PONumber.po_number) || this.data.po_number;
          data.po_id = (PONumber && PONumber.id) || "";
          data.employee_id = this.employeeID;
          data.id_time_checking = this.id_time_checking
          const res = await add(data);
          const resData = res.data;
          this.isLoading = false;

          const getProduct = this.products.find(
            (item) => +item.id === +data.product_id
          );
          let itemHistory = {
            id: resData.id,
            vendor: getVendor && getVendor.name,
            po_number: data.po_number,
            invoice_number: data.invoice_number,
            location: getLocation && getLocation.barcode,
            gtin: data.gtin,
            product: getProduct && getProduct.name,
            box: data.barcode,
            quantity: data.quantity,
            tracking_number: "",
            country_name: resData.country?.name
          };
          this.history.push(itemHistory);
          this.resetInsert('success');
          this.focusByElClass('el-form-manual-item-barcode');
        }
        this.notification(this.$t('Inventory addition add successfully.'));
        // this.openDialogAddInventoryAddition = false;
        // this.$emit("refresh");
      } catch (e) {
        const data = e.response.data;
        this.isLoading = false;
        let message = this.$t('Inventory addition add error.');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.notification(message, "error");
      }
    },
    focusByElClass(elClass = "el-form-item-tracking-number") {
      setTimeout(() => {
        const el = document.getElementsByClassName(elClass);
        if (!el || !el.length) {
          return;
        }
       
        const firtsElError = (el && el[0]) || undefined;
        const input = firtsElError.querySelector("input");
        document.body.scrollTop = (input.offsetTop - 120) | 0;
        input.focus();
        this.$refs["addInventoryAdditionFormdistributor"].clearValidate();
      });
    },
    async changeCountry() {
      this.data.country = this.countries.find(item => item.iso2 == this.data.country)?.iso2;
      if (this.currentTab == 'manual') {
        this.focusByElClass('el-form-manual-item-gtin');
      } else {
        this.focusByElClass('el-form-item-location-value');
      }
    },
  },
};
