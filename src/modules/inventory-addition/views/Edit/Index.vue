<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
      v-model="openDialogEditBox"
      :title="$t('Edit box')"
      custom-class="el-dialog-custom el-dialog-box"
      @close="resetData"
      :close-on-click-modal="false"
    >
      <template #default>
        <div class="add-box">
          <el-form
            status-icon
            ref="editBoxForm"
            :model="box"
            :rules="boxRules"
            @submit.prevent="onSubmit('editBoxForm')"
            label-width="130px"
            :label-position="'left'"
          >
            <el-form-item
              :label="$t('Barcode')"
              prop="barcode"
              @keyup.enter="onSubmit('editBoxForm')"
            >
              <el-input
                v-model="box.barcode"
                type="text"
                autocomplete="off"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            @click="onSubmit('editBoxForm')"
            :disabled="isLoading"
            :loading="isLoading"
            >{{ $t('Update') }}</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>
