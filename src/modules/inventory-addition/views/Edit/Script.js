import EventBus from "@/utilities/eventBus.js";
import { update } from "@/api/box.js";
import { isEmpty } from "ramda";

export default {
  name: "BoxEdit",
  components: {},
  data() {
    return {
      box: {},
      isLoading: false,
      openDialogEditBox: false,
      boxRules: {
        barcode: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: "blur",
          },
        ],
      },
    };
  },
  mounted() {},
  created() {
    EventBus.$on("showEditBox", (data) => {
      this.openDialogEditBox = true;
      this.box = {
        ...data,
      };
    });
  },
  methods: {
    resetData() {
      this.$refs["editBoxForm"].resetFields();
      this.box = {
        barcode: "",
      };
    },
    async onSubmit(formName) {
      const isValid = await this.$refs[formName].validate();
      if (!isValid) {
        return;
      }
      this.isLoading = true;
      try {
        await update(this.box);
        this.isLoading = false;
        this.notification(this.$t('Box update successfully.'));
        this.openDialogEditBox = false;
        this.$emit("refresh");
      } catch (e) {
        const data = e.response.data;
        this.isLoading = false;
        let message = this.$t('Box update error.');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.notification(message, "error");
      }
    },
  },
};
