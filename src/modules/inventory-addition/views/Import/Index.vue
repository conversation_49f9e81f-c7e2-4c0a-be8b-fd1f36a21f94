<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
      v-model="openDialogImportBox"
      :title="$t('Import box')"
      custom-class="el-dialog-custom el-dialog-box"
      @close="resetData"
      :close-on-click-modal="false"
    >
      <template #default>
        <div class="add-box">
          <el-form
            status-icon
            ref="importBoxForm"
            :model="box"
            :rules="boxRules"
            @submit.prevent="onSubmit('importBoxForm')"
            label-width="130px"
            :label-position="'left'"
          >
            <el-form-item :label="$t('BoxID')" prop="barcode">
              <el-input
                v-model="box.barcode"
                :rows="6"
                type="textarea"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            @click="onSubmit('importBoxForm')"
            :disabled="isLoading"
            :loading="isLoading"
            >{{ $t('Import') }}</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>
