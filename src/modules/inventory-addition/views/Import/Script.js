import EventBus from "@/utilities/eventBus.js";
import { importBox } from "@/api/box.js";

export default {
  name: "BoxImport",
  components: {},
  data() {
    return {
      box: {},
      isLoading: false,
      openDialogImportBox: false,
      boxRules: {
        barcode: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: "blur",
          },
        ],
      },
    };
  },
  mounted() {},
  created() {
    EventBus.$on("showImportBox", () => {
      this.openDialogImportBox = true;
    });
  },
  methods: {
    resetData() {
      this.$refs["importBoxForm"].resetFields();
      this.box = {
        barcode: "",
      };
    },
    async onSubmit(formName) {
      const isValid = await this.$refs[formName].validate();
      if (!isValid) {
        return;
      }
      this.isLoading = true;
      try {
        await importBox(this.box);
        this.isLoading = false;
        this.notification(this.$t('Box import successfully.'));
        this.openDialogImportBox = false;
        this.$emit("refresh");
      } catch (e) {
        this.isLoading = false;
        let message = this.$t('Box import error.');
        this.notification(message, "error");
      }
    },
  },
};
