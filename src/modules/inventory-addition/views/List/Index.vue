<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ $t("Inventory Addition") }}</h1>
      </div>
      <div class="top-head-right">
        <el-button @click="openModalExport" plain type="primary">
          <span class="icon-margin-right">
            <icon :data="iconExport" />
          </span>{{ $t('Export') }}
        </el-button>
        <el-button type="primary" @click="createInventoryAddition">
          <span class="icon-margin-right">
            <icon :data="iconAdd" />
          </span>{{ $t("Add") }}
        </el-button>
      </div>
    </div>

    <div class="table-content">
      <div class="filter">
        <div class="label">{{ $t("Filter by:") }}</div>
        <div class="filter-item">
          <el-dropdown ref="po_number" trigger="click" class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('po_number') }">
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('po_number')">
                <el-tooltip effect="dark" content="PO#" placement="top-start">
                  <span>{{ filter.po_number }}</span>
                </el-tooltip>
              </template>
              <template v-else> PO# </template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input :placeholder="$t('Enter search keyword')" class="search" v-model="filter.po_number"
                  @keydown.enter="onFilter('po_number')" clearable @clear="clearFilterItem('po_number')" />
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item">
          <el-dropdown ref="sku" trigger="click" class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('sku') }">
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('sku')">
                <el-tooltip effect="dark" :content="$t('SKU')" placement="top-start">
                  <span>{{ filter.sku }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(" SKU ") }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input :placeholder="$t('Enter search keyword')" class="search" v-model="filter.sku"
                  @keydown.enter="onFilter('sku')" clearable @clear="clearFilterItem('sku')" />
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item">
          <el-dropdown ref="invoice_number" trigger="click" class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('invoice_number') }">
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('invoice_number')">
                <el-tooltip effect="dark" :content="$t('Invoice Number')" placement="top-start">
                  <span>{{ filter.invoice_number }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(" Invoice Number ") }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input :placeholder="$t('Enter search keyword')" class="search" v-model="filter.invoice_number"
                  @keydown.enter="onFilter('invoice_number')" clearable @clear="clearFilterItem('invoice_number')" />
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item">
          <el-dropdown ref="tracking_number" trigger="click" class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('tracking_number') }">
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('tracking_number')">
                <el-tooltip effect="dark" :content="$t('Tracking Number')" placement="top-start">
                  <span>{{ filter.tracking_number }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(" Tracking Number ") }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input :placeholder="$t('Enter search keyword')" class="search" v-model="filter.tracking_number"
                  @keydown.enter="onFilter('tracking_number')" clearable @clear="clearFilterItem('tracking_number')" />
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item">
          <el-dropdown ref="box" trigger="click" class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('box') }">
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('box')">
                <el-tooltip effect="dark" :content="$t('Box')" placement="top-start">
                  <span>{{ filter.box }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(" Box ") }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input :placeholder="$t('Enter search keyword')" class="search" v-model="filter.box"
                  @keydown.enter="onFilter('box')" clearable @clear="clearFilterItem('box')" />
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item">
          <el-dropdown ref="location" trigger="click" class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('location') }">
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('location')">
                <el-tooltip effect="dark" :content="$t('Location')" placement="top-start">
                  <span>{{ filter.location }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(" Location ") }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input :placeholder="$t('Enter search keyword')" class="search" v-model="filter.location"
                  @keydown.enter="onFilter('location')" clearable @clear="clearFilterItem('location')" />
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item">
          <el-dropdown ref="product" trigger="click" class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('product') }">
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('product')">
                <el-tooltip effect="dark" :content="$t('Product')" placement="top-start">
                  <span>{{ filter.product }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(" Product ") }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input :placeholder="$t('Enter search keyword')" class="search" v-model="filter.product"
                  @keydown.enter="onFilter('product')" clearable @clear="clearFilterItem('product')" />
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item">
          <el-dropdown trigger="click" class="el-dropdown-filter-item"
            :class="{ 'is-active': filter.date && filter.date.length }">
            <span class="el-dropdown-link">
              <template v-if="filter.date && filter.date.length">
                <el-tooltip effect="dark" :content="$t('Date')" placement="top-start">
                  <span>
                    {{ templateDateRange(filter.date[0], filter.date[1]) }}
                  </span>
                </el-tooltip>
              </template>
              <template v-else> {{ $t("Date") }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-date-picker format="YYYY-MM-DD" v-model="filter.date" type="daterange" range-separator="To"
                  :start-placeholder="$t('Start date')" :end-placeholder="$t('End date')" @change="onChangeDate">
                </el-date-picker>
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item" v-if="hasFilter">
          <el-link type="danger" @click="onClearFilter" :underline="false">
            {{ $t("Clear") }}
          </el-link>
        </div>
      </div>

      <el-table stripe size="small" border :data="items" style="width: 100%" :max-height="maxHeight"
        v-loading="isLoading" element-loading-text="Loading..." :row-class-name="tableRowClassName">
        <el-table-column fixed prop="po_number" label="Purchase Order#" width="150">
          <template #default="scope">
            {{ scope.row.po_number || "N/A" }}
          </template>
        </el-table-column>

        <el-table-column prop="box_id" :label="$t('Box ID')" min-width="150">
          <template #default="scope">
            {{ scope.row.box_id }}
          </template>
        </el-table-column>

        <el-table-column prop="product_sku" :label="$t('SKU')" min-width="150">
          <template #default="scope">
            {{ scope.row.product_sku }}
          </template>
        </el-table-column>

        <el-table-column prop="product_id" :label="$t('Product')" min-width="300">
          <template #default="scope">
            {{ scope.row.product_name }}
          </template>
        </el-table-column>

        <el-table-column prop="quantity" :label="$t('Qty')" width="100">
          <template #default="scope">
            {{ scope.row.quantity }}
          </template>
        </el-table-column>

        <el-table-column prop="cost_value" :label="$t('Cost Value')" width="100">
          <template #default="scope">
            <span v-if="scope.row.cost_value !== null"> ${{ formatNumber(scope.row.cost_value) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="invoice_number" :label="$t('Invoice Number')" width="150">
          <template #default="scope">
            {{ scope.row.invoice_number || "N/A" }}
          </template>
        </el-table-column>

        <el-table-column prop="tracking_number" :label="$t('Tracking Number')" width="150">
          <template #default="scope">
            {{ scope.row.tracking_number || "N/A" }}
          </template>
        </el-table-column>

        <el-table-column prop="location_id" :label="$t('Location')" min-width="150">
          <template #default="scope">
            {{ scope.row.location }}
          </template>
        </el-table-column>

        <el-table-column prop="created_at" :label="$t('Date')" min-width="150">
          <template #default="scope">
            {{ formatDate(scope.row.created_at, false) }}
          </template>
        </el-table-column>

        <el-table-column prop="quantity" :label="$t('COO')" width="100" v-if="warehouse_id === 18">
          <template #default="scope">
            <span class="break-normal">
              {{ scope.row.country_name }}
            </span>
          </template>
        </el-table-column>

        <el-table-column :label="$t('User')" width="120">
          <template #default="scope">
            {{ scope.row.username }}
          </template>
        </el-table-column>

        <el-table-column :label="$t('Employee')" width="120">
          <template #default="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>

        <el-table-column prop="action" :label="$t('Action')" width="80">
          <template #default="scope">
            <el-popconfirm :title="'Are you sure to delete ?'" @confirm="deleteInventoryAddition(scope.row)"
              v-if="!scope.row.is_deleted">
              <template #reference>
                <el-tooltip placement="top" v-if="scope.row.is_referenced">
                  <template #content>
                    <span>Cannot revert – this addition has been used in FIFO cost calculation.</span>
                  </template>

                  <el-link type="danger" disabled>
                    <icon :data="iconDelete" />
                  </el-link>
                </el-tooltip>
                <el-link v-else :underline="false" type="danger" :disabled="scope.row.is_referenced">
                  <icon :data="iconDelete" />
                </el-link>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>

      <div class="bottom">
        <div class="total">
          {{ $t("Total:") }} {{ items.length ? formatNumber(total) : 0 }}
        </div>
        <el-pagination :disabled="isLoading" background layout="prev, pager, next" :page-size="filter.limit"
          :total="total" @current-change="changePage" v-model:currentPage="filter.page">
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select v-model="filter.limit" :placeholder="$t('Select')" size="mini" @change="onFilter">
            <el-option v-for="item in limits" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </div>
      </div>
    </div>

    <el-dialog v-model="dialogExport" :title="$t('Export Addition')"
      custom-class="el-dialog-custom custom-dialog rounded-xl" @close="resetData" :close-on-click-modal="false">
      <template #default>
        <div class="my-4 mx-auto items-center flex flex-col justify-center">
          <el-date-picker format="YYYY-MM-DD" v-model="created_at" :clearable="false" type="daterange"
            range-separator="To" :start-placeholder="$t('Start Date')" :end-placeholder="$t('End Date')">
          </el-date-picker>
        </div>
      </template>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetData">Cancel</el-button>
          <el-button @click="exportExcel" type="primary">Export</el-button>
        </div>
      </template>
    </el-dialog>

    <CreateInventoryAddition @refresh="fetchData" :employees="employees" />
  </div>
</template>
