import { list, revert } from "@/api/inventoryAddition.js";
import EventBus from "@/utilities/eventBus.js";
import CreateInventoryAddition from "@/modules/inventory-addition/views/Create/Index.vue";
import { equals } from "ramda";
import { mapGetters } from "vuex";
import warehouseMixin from "@/mixins/warehouse";
import moment from "moment/moment";
import { API_URL } from "@/utilities/constants";

export default {
  name: "InventoryAddition",
  mixins: [warehouseMixin],
  components: {
    CreateInventoryAddition,
  },
  data() {
    return {
      items: [],
      isLoading: false,
      filter: this.setDefaultFilter(),
      warehouse_id: null,
      dialogExport: false,
      created_at: null,
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 230);
    },
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },
    ...mapGetters({
      employees: "getEmployees"
    }),
    buildLinkDownload() {
      const link = `${API_URL}/addition-export`;
      let params = { ...this.filter };
      delete params.page;
      delete params.limit;
      params.warehouse_id = this.userWarehouseId;

      if (this.created_at) {
        params.start_date = this.formatDate(this.created_at[0], false);
        params.end_date = this.formatDate(this.created_at[1], false);
      }
      params = new URLSearchParams(params);
      return `${link}?${params.toString()}`;
    },
  },
  beforeUnmount() {
    EventBus.$off("showCreateInventoryAddition");
  },
  mounted() {
    this.warehouse_id = this.getWarehouseId();
    this.fetchData();
    this.fetchVendor();
    this.fetchEmployee();
    this.setDefaultDate();
  },
  methods: {
    exportExcel() {
      return (window.location.href = this.buildLinkDownload);
    },
    openModalExport() {
      this.dialogExport = true;
    },
    resetData() {
      this.dialogExport = false;
      this.setDefaultDate();
    },
    setDefaultDate() {
      let time = new Date();
      this.created_at = [
        this.formatDate(moment(time).subtract(7, 'days'), false),
        this.formatDate(new Date(), false),
      ];
    },
    async fetchEmployee() {
      await this.$store.dispatch("getEmployees");
    },
    async fetchVendor() {
      await this.$store.dispatch("getVendors");
    },
    onFilter(item = "") {
      this.filter.page = 1;
      this.$nextTick(() => {
        this.fetchInventoryAddition();
        if (item) {
          this.$refs[item].handleClose();
        }
      });
    },
    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.$nextTick(() => {
        this.fetchInventoryAddition();
      });
    },
    onChangeDate() {
      if (this.filter.date && this.filter.date.length) {
        this.filter.date[0] = this.formatDate(this.filter.date[0], false);
        this.filter.date[1] = this.formatDate(this.filter.date[1], false);
        this.onFilter();
      }
    },
    clearFilterItem(item) {
      this.filter[item] = "";
      this.$refs[item].handleClose();
      this.onFilter();
    },
    setDefaultFilter() {
      let params = {
        limit: 25,
        page: 1,
        po_number: "",
        sku: "",
        invoice_number: "",
        tracking_number: "",
        box: "",
        location: "",
        product: "",
        date: "",
      };
      return params;
    },
    setRouteParam() {
      const params = this.filter;
      this.$router.replace({ name: "inventory_addition", query: params });
    },
    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);
      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }
      filter.page = +filter.page || 1;
      return filter;
    },
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchInventoryAddition();
      });
    },
    createInventoryAddition() {
      EventBus.$emit("showCreateInventoryAddition");
    },
    async fetchData() {
      this.filter = this.getRouteParam();
      this.fetchInventoryAddition();
    },
    async fetchInventoryAddition() {
      this.isLoading = true;
      this.setRouteParam();
      const res = await list(this.filter);
      this.isLoading = false;
      const data = res.data || [];
      this.total = data.total;
      this.items = data.data;
    },
    async deleteInventoryAddition(item) {
      try {
        await revert({ id: item.id });
        this.notification("Inventory addition delete successfully!");
        this.fetchData();
      } catch (e) {
        let message = e.response.data.message || "An error occurred, please try again later!";
        this.notification(message, "error");
      }
    },
    tableRowClassName(data) {
      return data.row.is_deleted ? "is-delete" : "";
    },
    hasChangeFilterByItem(name) {
      const query = this.$route.query;
      if (query[name]) {
        return true;
      }
      return false;
    },
  },
};
