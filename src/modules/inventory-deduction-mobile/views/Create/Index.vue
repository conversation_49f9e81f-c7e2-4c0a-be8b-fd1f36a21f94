<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div class="content">
    <HeaderMobile :name="$t('Deduction')"></HeaderMobile>
    <div class="content-body">
      <el-form
        status-icon
        ref="addInventoryDeductionMobile"
        :label-position="'top'"
        @submit.prevent="onSubmit()"
      >
        <el-form-item
          v-if="warehouseId !== 18"
          label="Scan SKU"
          prop="sku"
          class="el-form-item-sku d-flex"
        >
          <el-input
            class="flex-1"
            :placeholder="$t('Enter sku')"
            v-model="sale_order_sku"
          >
          </el-input>
        </el-form-item>
        <el-form-item
            v-else
            label="Scan SKU"
            prop="sku"
            @keyup.enter="changeFocus()"
            class="el-form-item-sku d-flex"
        >
          <el-input
              class="flex-1"
              :placeholder="$t('Enter sku')"
              v-model="sale_order_sku"
          >
          </el-input>
        </el-form-item>
        <el-form-item
            v-if="warehouseId === 18"
            label="Scan box"
            prop="sku"
            class="el-form-item-barcode-box d-flex"
        >
          <el-input
              class="flex-1"
              :placeholder="$t('Enter barcode box')"
              v-model="barcodeBox"
              @keyup.enter="onSubmit()"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <div>
        <h3 class="title mb-3">{{ $t('Scan history') }}</h3>
        <el-table
          border
          :data="histories"
          style="width: 100%"
          size="small"
          :max-height="500"
          :row-class-name="tableRowClassName"
        >
          <el-table-column :label="$t('SKU')">
            <template #default="scope">
              <span :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
                {{ scope.row.product.sku }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('Product')">
            <template #default="scope">
              <span :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
                {{ scope.row.product.style }}
                / {{ scope.row.product.color }} / {{ scope.row.product.size }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="action"
            :label="$t('Action')"
            width="80"
            fixed="right"
          >
            <template #default="scope">
              <el-popconfirm
                v-if="!scope.row.is_deleted"
                :title="'Are you sure to delete ?'"
                @confirm="inventoryDeductionDestroy(scope.row)"
              >
                <template #reference>
                  <el-link :underline="false" type="danger">
                    <icon :data="iconDelete" />
                  </el-link>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
