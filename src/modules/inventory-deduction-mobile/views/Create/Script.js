import { add, revert } from "@/api/inventoryDeduction.js";
import { isEmpty } from "ramda";
import HeaderMobile from "@/components/HeaderMobile.vue";

export default {
  name: "InventoryDeductionAddMobile",
  components: { HeaderMobile },
  data() {
    return {
      sale_order_sku: "",
      isLoading: false,
      histories: [],
      warehouseId: '',
      barcodeBox: '',
    };
  },
  mounted() {
    this.focusByElClass();
  },
  created() {
    this.warehouseId = this.$author.warehouseId();
  },
  methods: {
    isWarehouse18() {
      return this.warehouseId === 18;
    },
    async inventoryDeductionDestroy(item) {
      await revert({ id: item.id });
      this.notification(this.$t('Inventory deduction delete successfully.'));
      this.histories.forEach((history) => {
        if (+history.id === +item.id) {
          history.is_deleted = true;
        }
      });
    },
    tableRowClassName(data) {
      return data.row.is_deleted ? "is-delete" : "";
    },
    async onSubmit() {
      if (this.sale_order_sku == '') {
        this.notification('Please scan sku', "error");
        return;
      }
      if (this.barcodeBox == '' && this.isWarehouse18()) {
        this.notification('Please scan barcode box', "error");
        return;
      }
      this.isLoading = true;
      try {
        let data = {
          sale_order_sku: this.sale_order_sku,
          box_barcode: this.barcodeBox
        }
        const res = await add(data);
        const resData = res.data;
        this.histories.unshift(resData);
        console.log(this.histories);
        this.notification(this.$t('Inventory deduction add successfully.'), "success");
      } catch (e) {
        const data = e.response.data;
        let message = this.$t('Inventory deduction add error.');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.notification(message, "error");
      } finally {
        this.sale_order_sku = "";
        this.barcodeBox = "";
        this.isLoading = false;
      }
      this.focusByElClass();
    },
    focusByElClass(elClass = "el-form-item-sku") {
      this.$nextTick(() => {
        const el = document.getElementsByClassName(elClass);
        if (!el || !el.length) {
          return;
        }
        const firtsElError = (el && el[0]) || undefined;
        const input = firtsElError.querySelector("input");
        input.focus();
      });
    },
    changeFocus() {
      this.focusByElClass('el-form-item-barcode-box')
    }
  },
};
