<template>
  <div>
    <el-dialog
      v-model="modalDeductSKU"
      :title="$t('Deduct SKUs')"
      custom-class="el-dialog-custom el-dialog-inventory-deduction"
      :close-on-click-modal="false"
      width="88%"
    >
      <template #default>
        <div class="flex mb-3 items-end flex-row items-stretch">
          <div class="flex flex-col justify-between">
            <h3 class="text-xs">{{ $t('Product SKU') }}</h3>
            <el-input
                v-model="sku"
                @keyup.enter="scanProductBySku"
                class="mr-2 el-input-scan el-input-sku"
                :placeholder="$t('Enter Product SKU')"
                :disabled="isLoading"
            ></el-input>
          </div>
          <div class="flex items-end">
            <div class="mr-2 mb-2 text-xs">{{ $t('OR') }}</div>
            <div class="flex flex-col">
              <div class="text-xs text-danger">
                <h3>{{ $t('Manual SKU Select') }}</h3>
                  {{
                    $t(
                      ' Incase you can’t scan product SKU, please select manual.'
                    )
                  }}
              </div>
              <div class="flex items-center">
                <el-select
                    v-model="product.style"
                    filterable
                    class="mr-2"
                    :placeholder="$t('Select style')"
                    @change="selectStyle"
                >
                  <el-option :label="'Select style'" :value="''" />
                  <el-option
                      v-for="item in styles"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                  />
                </el-select>

                <el-select
                    v-model="product.color"
                    filterable
                    class="mr-2"
                    :placeholder="$t('Select color')"
                >
                  <el-option :label="'Select color'" :value="''" />
                  <el-option
                      v-for="item in colors"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                  />
                </el-select>
                <el-select
                    v-model="product.size"
                    filterable
                    class="mr-2"
                    :placeholder="$t('Select size')"
                >
                  <el-option :label="'Select size'" :value="''" />
                  <el-option
                      v-for="item in sizes"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                  />
                </el-select>
                <div v-if="hasChangeData">
                  <el-link type="danger" @click="setDefaultData" :underline="false">
                    {{ $t("Reset") }}
                  </el-link>
                </div>
              </div>
            </div>

          </div>
        </div>
        <div class="mb-3" v-if="warehouse_id !== 18">
          <el-input
            :disabled="isLoading"
            v-model="barcode"
            @keyup.enter="scanData"
            class="el-input-scan el-input-scan-barcode"
            :placeholder="$t('Scan Barcode')"
          ></el-input>
        </div>
        <div class="mb-3" v-else>
          <el-input
              :disabled="isLoading"
              v-model="barcode"
              @keyup.enter="changeFocus"
              class="el-input-scan el-input-scan-barcode"
              :placeholder="$t('Scan Barcode')"
          ></el-input>
        </div>
        <div class="mb-3" v-if="warehouse_id === 18">
          <el-input
              :disabled="isLoading"
              v-model="barcodeBox"
              @keyup.enter="scanData"
              class="el-input-scan el-input-scan-barcode-box-sku"
              :placeholder="$t('Scan Barcode box')"
          ></el-input>
        </div>
        <h3 class="title mb-3">{{ $t('Scan history') }}</h3>
        <el-table
          border
          :data="history"
          style="width: 100%"
          size="small"
          :max-height="500"
          :row-class-name="tableRowClassName"
        >
          <el-table-column :label="$t('Date')" width="145">
            <template #default="scope">
              <span :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
                {{ utcToLocalTime(scope.row.created_at).format('lll') }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('SKU#')" width="200">
            <template #default="scope">
              <span :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
                {{ scope.row.sale_order_sku }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('Label#')" width="200">
            <template #default="scope">
              <span :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
                {{ scope.row.label_id }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('Part Number')" width="140" v-if="warehouse_id === 18">
            <template #default="scope">
              <span :class="{'text-danger': scope.row.is_duplicate == 1 }">
                {{ scope.row?.part_number }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('Box ID')" width="110" v-if="warehouse_id === 18">
            <template #default="scope">
              <span :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
                {{ scope.row.box?.barcode }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('COO')" width="110" v-if="warehouse_id === 18">
            <template #default="scope">
              <span :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
                {{ scope.row.country?.name }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('Employee')" width="120">
            <template #default="scope">
              <span :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
                {{ getEmployeeName(scope.row.employee_id) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('WIP SKU')" width="125">
            <template #default="scope">
              <span :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
                {{ scope.row && scope.row.product && scope.row.wip_product.sku }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('Deducted SKU')" width="125">
            <template #default="scope">
              <span :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
                {{ scope.row && scope.row.product && scope.row.product.sku }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('Style')">
            <template #default="scope">
              <span :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
                {{ scope.row && scope.row.product && scope.row.product.style }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('Color')">
            <template #default="scope">
              <span :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
                {{ scope.row && scope.row.product && scope.row.product.color }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('Size')">
            <template #default="scope">
              <span :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
                {{ scope.row && scope.row.product && scope.row.product.size }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="action"
            :label="$t('Action')"
            width="55"
            fixed="right"
          >
            <template #default="scope">
              <el-popconfirm
                v-if="!scope.row.is_deleted"
                :title="'Are you sure to delete ?'"
                @confirm="revert(scope.row)"
              >
                <template #reference>
                  <el-link :underline="false" type="danger"
                    ><icon :data="iconDelete"
                  /></el-link>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import EventBus from '@/utilities/eventBus.js';
import { getProductAttributes, getProductByParams, getProductBySku } from '@/api/product';
import { deductSKU, revert } from '@/api/inventoryDeduction.js';
import { isEmpty } from 'ramda';
import { mapGetters } from 'vuex';
import warehouseMixin from '@/mixins/warehouse';
export default {
  name: 'DeductSKUs',
  props: {},
  mixins: [warehouseMixin],
  data() {
    return {
      sku: '',
      barcode: '',
      history: [],
      modalDeductSKU: false,
      styles: [],
      colors: [],
      sizes: [],
      attributes: {},
      product: {
        style: '',
        size: '',
        color: '',
      },
      isLoading: false,
      warehouse_id: null,
      barcodeBox: '',
    };
  },
  computed: {
    ...mapGetters({
      employees: 'getEmployees',
    }),
    hasChangeData() {
      if(this.sku || this.barcode || this.product.style || this.product.color || this.product.size) {
        return true;
      }
      return false;
    }
  },
  created() {
    EventBus.$on('deductSKU', () => {
      this.setDefaultData();
      this.history = [];
      this.modalDeductSKU = true;
      this.fetchProductAttribute();
    });
  },
  async mounted() {
    this.warehouse_id = await this.getWarehouseId();
  },
  watch: {
    product: {
      handler() {
        if (!this.product.style || !this.product.color || !this.product.size)
          return;
        this.getProductByParams();
      },
      deep: true,
    },
  },
  methods: {
    isWarehouse18() {
      return this.warehouse_id === 18;
    },
    async scanProductBySku() {
      if (!this.sku) {
        this.notification('Sku is required.', 'error');
        return;
      }
      try {
        const res = await getProductBySku({
          sku: this.sku,
        });
        const data = res?.data || {};
        if(data.id) {
          this.product.style = data.style;
          this.selectStyle();
          this.product.color = data.color;
          this.product.size = data.size;
        }
        this.focusByElClass('el-input-scan-barcode');
      } catch (e) {
        const data = e.response.data;
        let message = data?.message || this.$t('Product not found');
        this.notification(message, 'error');
      }
    },
    getEmployeeName(id) {
      const employee = this.employees.find((item) => +item.id === +id);
      return employee && employee.name;
    },
    async getProductByParams() {
      this.isLoading = true;
      const res = await getProductByParams(this.product);
      const product = res.data ? res.data : {};
      if (!product || !product.sku) {
        this.notification('SKU not found.', 'error');
        return;
      }
      this.sku = product.sku;
      this.isLoading = false;
      this.focusByElClass('el-input-scan-barcode');
    },
    async revert(item) {
      await revert({ id: item.id });
      this.notification(this.$t('Inventory deduction delete successfully.'));
      this.history.forEach((history) => {
        if (+history.id === +item.id) {
          history.is_deleted = true;
        }
      });
    },
    tableRowClassName(data) {
      return data.row.is_deleted ? 'is-delete' : '';
    },
    async fetchProductAttribute() {
      const res = await getProductAttributes();
      const data = res.data || '';
      let styles = data && Object.keys(data);
      styles =
        styles.length &&
        styles.map((item) => {
          return {
            label: item,
            value: item,
          };
        });
      this.styles = styles;
      this.attributes = data;
    },
    selectStyle() {
      this.product.size = '';
      this.product.color = '';
      if (!this.product.style) {
        this.colors = [];
        this.sizes = [];
        return;
      }
      const currentStyle = this.attributes[this.product.style];
      let colors = currentStyle.colors;
      colors =
        colors.length &&
        colors.map((item) => {
          return {
            label: item,
            value: item,
          };
        });
      let sizes = currentStyle.sizes;
      sizes =
        sizes.length &&
        sizes.map((item) => {
          return {
            label: item,
            value: item,
          };
        });
      this.colors = colors;
      this.sizes = sizes;
    },
    setDefaultData() {
      this.sku = '';
      this.barcode = '';
      this.barcodeBox = '';
      this.colors = [];
      this.sizes = [];
      this.product = {
        style: '',
        size: '',
        color: '',
      };
    },
    scanData() {
      if (!this.sku) {
        this.focusByElClass();
        return;
      }
      if (!this.barcode) {
        this.focusByElClass('el-input-scan-barcode');
        return;
      }
      this.onSubmit();
    },
    async onSubmit() {
      if (!this.sku || !this.barcode) {
        return;
      }
      const params = {
        sku: this.sku.trim(),
        sale_order_sku: this.barcode.trim(),
        box_barcode : this.barcodeBox
      };
      this.isLoading = true;
      try {
        let data = Object.assign({}, params);
        const res = await deductSKU(data);
        const resData = res.data;
        this.history.unshift(resData);
        this.$emit('refresh');
        this.notification(this.$t('Inventory deduction add successfully.'));
        this.barcode = '';
        this.barcodeBox = '';
      } catch (e) {
        const data = e.response.data;
        let message = this.$t('Inventory deduction add error.');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.notification(message, 'error');
        this.barcode = '';
        this.barcodeBox = '';
      } finally {
        this.isLoading = false;
      }
      this.focusByElClass('el-input-scan-barcode');
    },
    focusByElClass(elClass = 'el-input-sku') {
      this.$nextTick(() => {
        const el = document.getElementsByClassName(elClass);
        if (!el || !el.length) {
          return;
        }
        const firtsElError = (el && el[0]) || undefined;
        const input = firtsElError.querySelector('input');
        document.body.scrollTop = (input.offsetTop - 120) | 0;
        input.focus();
      });
    },
    focusByElClassBox(elClass = "el-input-scan-barcode-box-sku") {
      this.$nextTick(() => {
        const el = document.getElementsByClassName(elClass);
        if (!el || !el.length) {
          return;
        }
        const firtsElError = (el && el[0]) || undefined;
        const input = firtsElError.querySelector("input");
        input.focus();
      });
    },
    changeFocus() {
      this.focusByElClassBox();
    },
  },
};
</script>

<style lang="scss" scoped>
.el-input-scan {
  width: 200px !important;
}
</style>
