<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
      v-model="openDialogAddInventoryDeduction"
      :title="$t('Create Inventory Deduction')"
      custom-class="el-dialog-custom el-dialog-inventory-deduction"
      @close="closeModal"
      destroy-on-close
      :close-on-click-modal="false"
      width="88%"
    >
      <template #default>
        <div class="add-inventory-addition">
          <el-form
            status-icon
            ref="addInventoryDeduction"
            :model="data"
            :rules="dataRules"
            @submit.prevent="onSubmit()"
            :label-position="'top'"
            class="w-full"
          >
            <el-form-item
                v-if="warehouse_id !== 18"
              :label="$t('Scan Barcode')"
              prop="sale_order_sku"
              class="el-form-item-barcode font-semi-bold"
            >
              <el-input
                  v-model="data.sale_order_sku"
                  @keyup.enter="onSubmit"
                  :disabled="isLoading"
                  :loading="isLoading"
                  class="el-input-barcode"
              ></el-input>
            </el-form-item>
            <el-form-item
                v-else
                :label="$t('Scan Barcode')"
                prop="sale_order_sku"
                class="el-form-item-barcode font-semi-bold"
            >
              <el-input
                  v-model="data.sale_order_sku"
                  @keyup.enter="changeFocus()"
                  :disabled="isLoading"
                  :loading="isLoading"
                  class="el-input-barcode"
              ></el-input>
            </el-form-item>
            <el-form-item
                v-if="warehouse_id === 18"
                :label="$t('Scan Barcode box')"
                prop="box_barcode"
                ref="boxBarcode"
                class="el-form-item-barcode font-semi-bold"
            >
              <el-input
                  v-model="data.box_barcode"
                  @keyup.enter="onSubmit"
                  :disabled="isLoading"
                  :loading="isLoading"
                  class="el-input-barcode-box"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>

        <h3 class="title mb-3">{{ $t('Scan history') }}</h3>
        <el-table
          border
          :data="history"
          style="width: 100%"
          size="small"
          :max-height="500"
          :row-class-name="tableRowClassName"
        >
          <el-table-column :label="$t('Date')" width="150">
            <template #default="scope">
              <span :class="{'text-danger': scope.row.is_duplicate == 1 }">
                {{ utcToLocalTime(scope.row.created_at).format("lll") }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('SKU#')" width="220">
            <template #default="scope">
              <span :class="{'text-danger': scope.row.is_duplicate == 1 }"> {{ scope.row.sale_order_sku }} </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('Label#')" width="220">
            <template #default="scope">
              <span :class="{'text-danger': scope.row.is_duplicate == 1 }"> {{ scope.row.label_id }} </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('Part Number')" width="140" v-if="warehouse_id === 18">
            <template #default="scope">
              <span :class="{'text-danger': scope.row.is_duplicate == 1 }">
                {{ scope.row?.part_number }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('Box ID')" width="110" v-if="warehouse_id === 18">
            <template #default="scope">
              <span :class="{'text-danger': scope.row.is_duplicate == 1 }">
                {{ scope.row.box?.barcode }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('COO')" width="110" v-if="warehouse_id === 18">
            <template #default="scope">
              <span :class="{'text-danger': scope.row.is_duplicate == 1 }">
                {{ scope.row.country?.name }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('Employee')" width="120">
            <template #default="scope">
              <span :class="{'text-danger': scope.row.is_duplicate == 1 }">
                {{ getEmployeeName(scope.row.employee_id) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('SKU')" width="120">
            <template #default="scope">
              <span :class="{'text-danger': scope.row.is_duplicate == 1 }">
                {{ scope.row && scope.row.product && scope.row.product.sku }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('Style')">
            <template #default="scope">
              <span :class="{'text-danger': scope.row.is_duplicate == 1 }">
                {{ scope.row && scope.row.product && scope.row.product.style }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('Color')">
            <template #default="scope">
              <span :class="{'text-danger': scope.row.is_duplicate == 1 }">
                {{ scope.row && scope.row.product && scope.row.product.color }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('Size')">
            <template #default="scope">
              <span :class="{'text-danger': scope.row.is_duplicate == 1 }">
                {{ scope.row && scope.row.product && scope.row.product.size }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="action"
            :label="$t('Action')"
            width="80"
            fixed="right"
          >
            <template #default="scope">
              <el-popconfirm
                v-if="!scope.row.is_deleted"
                :title="'Are you sure to delete ?'"
                @confirm="inventoryDeductionDestroy(scope.row)"
              >
                <template #reference>
                  <el-link :underline="false" type="danger"
                    ><icon :data="iconDelete"
                  /></el-link>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!-- <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetData">{{ $t('Reset') }}</el-button>
          <el-button
            type="primary"
            @click="onSubmit()"
            :disabled="isLoading"
            :loading="isLoading"
            >{{ $t('Create') }}</el-button
          >
        </div>
      </template> -->
    </el-dialog>
  </div>
</template>
