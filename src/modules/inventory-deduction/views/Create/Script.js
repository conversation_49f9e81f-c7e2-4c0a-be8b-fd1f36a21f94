import EventBus from "@/utilities/eventBus.js";
import { add, revert } from "@/api/inventoryDeduction.js";
import { isEmpty } from "ramda";
import { mapGetters } from "vuex";
import warehouseMixin from '@/mixins/warehouse';

export default {
  name: "InventoryDeductionAdd",
  components: {},
  mixins: [warehouseMixin],
  data() {
    return {
      data: this.setDefaultData(),
      dataRules: {
        sale_order_sku: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: "change",
          },
        ],
        box_barcode: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: "change",
            validator: (rule, value, callback) => {
              if (this.isWarehouse18()) {
                if (!value) {
                  callback(new Error('Barcode box is required.'));
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
          },
        ],
      },
      isLoading: false,
      openDialogAddInventoryDeduction: false,
      history: [],
      warehouse_id: null,
    };
  },
  computed: {
    ...mapGetters({
      employees: "getEmployees"
    }),
  },
  async mounted() {
    this.warehouse_id = await this.getWarehouseId();
  },
  created() {
    EventBus.$on("showCreateInventoryDeduction", () => {
      this.openDialogAddInventoryDeduction = true;
    });
  },
  methods: {
    isWarehouse18() {
      return this.warehouse_id === 18;
    },
    getEmployeeName(id) {
      const employee = this.employees.find((item) => +item.id === +id);
      return employee && employee.name;
    },
    setDefaultData() {
      return {
        sale_order_sku: "",
        employee_id: "",
        box_barcode: "",
      };
    },
    resetData() {
      this.data = this.setDefaultData();
    },
    closeModal() {
      this.history = [];
      this.resetData();
    },
    async inventoryDeductionDestroy(item) {
      await revert({ id: item.id });
      this.notification(this.$t('Inventory deduction delete successfully.'));
      this.history.forEach((history) => {
        if (+history.id === +item.id) {
          history.is_deleted = true;
        }
      });
    },
    tableRowClassName(data) {
      return data.row.is_deleted ? "is-delete" : "";
    },
    async onSubmit() {
      const isValid = await this.$refs.addInventoryDeduction.validate();
      if (!isValid) {
        return;
      }
      this.isLoading = true;
      try {
        let data = Object.assign({}, this.data);
        const res = await add(data);
        const resData = res.data;

        if (resData.warning) {
          this.notification(resData.message, "warning");
        } else {
          this.history.unshift(resData);
          this.$emit('refresh');
          this.notification(this.$t('Inventory deduction add successfully.'));
        }
      } catch (e) {
        const data = e.response.data;
        let message = this.$t('Inventory deduction add error.');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        if (this.isWarehouse18()) {
          this.resetData();
        }
        this.notification(message, "error");
      } finally {
        this.$refs.addInventoryDeduction.resetFields();
        this.isLoading = false;
      }
      this.focusByElClass();
    },
    focusByElClass(elClass = "el-input-barcode") {
      this.$nextTick(() => {
        const el = document.getElementsByClassName(elClass);
        if (!el || !el.length) {
          return;
        }
        const firtsElError = (el && el[0]) || undefined;
        const input = firtsElError.querySelector("input");
        input.focus();
      });
    },
    focusByElClassBox(elClass = "el-input-barcode-box") {
      this.$nextTick(() => {
        const el = document.getElementsByClassName(elClass);
        if (!el || !el.length) {
          return;
        }
        const firtsElError = (el && el[0]) || undefined;
        const input = firtsElError.querySelector("input");
        input.focus();
      });
    },
    changeFocus() {
      this.focusByElClassBox();
    },
  },
};
