import EventBus from "@/utilities/eventBus.js";
import { getToken } from "@/utilities/jwt";
import notificationMixin from "@/mixins/notification";
import { importCsvFile, getCsvHistoryList } from "@/api/inventoryDeduction";
import { API_URL, STORAGE_URL, S3_URL } from "@/utilities/constants";
import dateMixin from "@/mixins/date";
import filterMixin from "@/mixins/filter";

export default {
    name: "ImportInventoryDeduction",
    mixins: [notificationMixin, dateMixin, filterMixin],
    data() {
        return {
            dialogVisible: false,
            headerInfo: {
                'Authorization': getToken()
            },
            currentTab: 'import',
            uploadResponse: {
                csv_id: 0,
                status: false,
                validation_result: [],
                total_row: 0,
                total_success: 0,
                total_error: 0
            },
            importing: false,
            uploadApi: API_URL + '/inventory-deduction/verify-csv-file',
            csvHistoryList: [],
            filter: this.setDefaultFilter(),
            isLoading: false
        }
    },
    created() {
        EventBus.$on("showImportInventoryDeduction", () => {
            this.dialogVisible = true;
        });
    },
    async mounted() {
        await this.getCsvHistoryList();
    },
    methods: {
        onSuccess(res) {
            this.$refs.uploadRef.clearFiles();
            Object.assign(this.uploadResponse, res);
        },
        onError(err) {
            const res = JSON.parse(err.message);
            Object.assign(this.uploadResponse, res);
            this.notification(res.message,'error');
        },
        downloadCsvTemplate() {
            window.open(`${STORAGE_URL}/csv-template/inventory-deduction.xlsx`, '_blank');
        },
        downloadCsvFile(filePosition) {
            window.open(`${S3_URL}/${filePosition}`, '_blank');
        },
        async importCsvFile() {
            try {
                this.importing = true;
                const res = await importCsvFile(this.uploadResponse.csv_id);
                this.$emit('refresh');
                await this.getCsvHistoryList();
                this.notification(res.data.message,'success');
                this.uploadResponse.validation_result = [];
                this.currentTab = 'history';
            } catch (e) {
                this.notification(res.data.message,'error');
            } finally {
                this.importing = false;
            }
        },
        async getCsvHistoryList() {
            this.isLoading = true;
            const { data: res } = await getCsvHistoryList(this.filter);
            this.csvHistoryList = res.data;
            this.total = res.total;
            this.isLoading = false;
        },
        async sortTable(data) {
            let sortColumn = "";
            let sortBy = "";
            if (data.prop && data.order) {
                sortColumn = data.prop;
                sortBy = data.order === "ascending" ? "ASC" : "DESC";
            }
            this.filter.sort_column = sortColumn;
            this.filter.sort_by = sortBy;
            await this.getCsvHistoryList();
        },
        async changePage(page) {
            this.filter.page = page;
            await this.getCsvHistoryList();
        },
        async onFilter() {
            this.filter.page = 1;
            await this.getCsvHistoryList();
        },
        setDefaultFilter() {
            return {
                limit: 10,
                page: 1,
                sort_column: "",
                sort_by: ""
            };
        }
    }
}