<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ $t('Inventory Deduction') }}</h1>
      </div>
      <div class="top-head-right">
        <el-button plain type="primary" @click="deductSKU">
          <span class="icon-margin-right">
            <icon :data="iconMinus" />
          </span>{{ $t('Deduct #SKU') }}
        </el-button>
        <el-button type="info" @click="importInventoryDeduction" v-if="warehouse_id !== 18">
          <span class="icon-margin-right">
            <icon :data="iconAdd" />
          </span>{{ $t('Import') }}
        </el-button>
        <el-button @click="openModalExport" plain type="primary">
          <span class="icon-margin-right">
            <icon :data="iconExport" />
          </span>{{ $t('Export') }}
        </el-button>
        <el-button type="primary" @click="createInventoryDeduction">
          <span class="icon-margin-right">
            <icon :data="iconAdd" />
          </span>{{ $t('Create') }}
        </el-button>
      </div>
    </div>

    <div class="table-content">
      <div class="filter">
        <div class="label">{{ $t('Filter by:') }}</div>
        <div class="filter-item">
          <el-dropdown ref="keyword" trigger="click" class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('keyword') }">
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('keyword')">
                <el-tooltip effect="dark" :content="$t('Order / Label')" placement="top-start">
                  <span>{{ filter.keyword || $t('Order / Label') }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t('Order / Label') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input :placeholder="$t('Enter search keyword')" class="search" v-model="filter.keyword"
                  @keydown.enter="onFilter('keyword')" clearable @clear="clearFilterItem('keyword')" />
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown ref="sku" trigger="click" class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('sku') }">
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('sku')">
                <el-tooltip effect="dark" :content="$t('SKU')" placement="top-start">
                  <span>{{ filter.sku }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(' SKU ') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input :placeholder="$t('Enter search keyword')" class="search" v-model="filter.sku"
                  @keydown.enter="onFilter('sku')" clearable @clear="clearFilterItem('sku')" />
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown trigger="click" class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('employee_id') }">
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('employee_id')">
                <el-tooltip effect="dark" :content="$t('Employee')" placement="top-start">
                  <span>{{ getEmployeeNameById(filter.employee_id) }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(' Employee ') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-select filterable v-model="filter.employee_id" :placeholder="$t('Select employee')"
                  @change="onFilter">
                  <el-option v-for="item in getEmployees" :key="item.id" :label="item.name" :value="String(item.id)">
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown trigger="click" class="el-dropdown-filter-item"
            :class="{ 'is-active': filter.date && filter.date.length }">
            <span class="el-dropdown-link">
              <template v-if="filter.date && filter.date.length">
                <el-tooltip effect="dark" :content="$t('Date')" placement="top-start">
                  <span>
                    {{ templateDateRange(filter.date[0], filter.date[1]) }}
                  </span>
                </el-tooltip>
              </template>
              <template v-else> {{ $t('Date') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-date-picker format="YYYY-MM-DD" v-model="filter.date" type="daterange" range-separator="To"
                  :start-placeholder="$t('Start date')" :end-placeholder="$t('End date')" @change="onChangeDate">
                </el-date-picker>
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown ref="product_style" trigger="click" class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('product_style') }">
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('product_style')">
                <el-tooltip effect="dark" :content="$t('Product')" placement="top-start">
                  <span>{{ filter.product_style }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t('Product') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input :placeholder="$t('Enter search keyword')" class="search" v-model="filter.product_style"
                  @keydown.enter="onFilter('product_style')" clearable @clear="clearFilterItem('product_style')" />
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item" v-if="hasFilter">
          <el-link type="danger" @click="onClearFilter" :underline="false">
            {{ $t('Clear') }}
          </el-link>
        </div>
      </div>

      <el-table border stripe size="small" :data="items" style="width: 100%" :max-height="maxHeight"
        v-loading="isLoading" element-loading-text="Loading..." :row-class-name="tableRowClassName">
        <el-table-column :label="$t('ID')" width="100">
          <template #default="scope">
            <span :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
              {{ scope.row.id }}
            </span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('Date')" width="180">
          <template #default="scope">
            <span :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
              {{ utcToLocalTime(scope.row.created_at).format('lll') }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="SKU#" min-width="220">
          <template #default="scope">
            <span :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
              {{ scope.row.sale_order_sku }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="Label#" min-width="220">
          <template #default="scope">
            <span :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
              {{ scope.row.label_id }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Part Number" min-width="220" v-if="warehouse_id === 18">
          <template #default="scope">
            <span :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
              {{ scope.row?.part_number }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Box ID" min-width="220" v-if="warehouse_id === 18">
          <template #default="scope">
            <span :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
              {{ scope.row?.box_barcode }}</span>
          </template>
        </el-table-column>
        <el-table-column label="COO" min-width="220" v-if="warehouse_id === 18">
          <template #default="scope">
            <span :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
              {{ scope.row?.coo_name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Order#" min-width="200">
          <template #default="scope">
            <span :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
              {{ scope.row.external_number }}
            </span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('Product')" min-width="300">
          <template #default="scope">
            <span :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
              {{ scope.row.product_name }}
            </span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('Qty')" width="50">
          <template #default="scope">
            <span :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
              {{ scope.row.quantity }}
            </span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('Employee')" min-width="170">
          <template #default="scope">
            <span class="break-normal" :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
              {{ getEmployeeNameById(scope.row.employee_id) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('User')" width="150">
          <template #default="scope">
            <span class="break-normal" :class="{ 'text-danger': scope.row.is_duplicate == 1 }">
              {{ scope.row.username }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="action" :label="$t('Action')" width="80">
          <template #default="scope">
            <el-popconfirm :title="'Are you sure to delete ?'" @confirm="deleteInventoryDeduction(scope.row)"
              v-if="!scope.row.is_deleted">
              <template #reference>
                <el-link :underline="false" type="danger">
                  <icon :data="iconDelete" />
                </el-link>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>

      <div class="bottom">
        <PaginationComponent :is-loading="isLoading" :current-page="currentPage" :is-load-more="isLoadMore"
          @changePage="changePage" />
        <div class="limit" :disabled="isLoading">
          <el-select v-model="filter.limit" :placeholder="$t('Select')" size="mini" @change="onFilter">
            <el-option v-for="item in limits" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
    <create-inventory-deduction @refresh="fetchData" />
  </div>

  <el-dialog v-model="dialogExport" :title="$t('Export Deduction')"
    custom-class="el-dialog-custom custom-dialog rounded-xl" @close="resetData" :close-on-click-modal="false">
    <template #default>
      <div class="my-4 mx-auto items-center flex flex-col justify-center">
        <el-date-picker format="YYYY-MM-DD" v-model="date" :clearable="false" type="daterange" range-separator="To"
          :start-placeholder="$t('Start Date')" :end-placeholder="$t('End Date')">
        </el-date-picker>
      </div>
    </template>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="resetData">Cancel</el-button>
        <el-button @click="exportExcel" type="primary">Export</el-button>
      </div>
    </template>
  </el-dialog>

  <import-inventory-deduction @refresh="fetchData" />
  <DeductSKU @refresh="fetchData" />
</template>
