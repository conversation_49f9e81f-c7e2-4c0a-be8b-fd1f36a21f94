import { list, revert } from "@/api/inventoryDeduction.js";
import EventBus from "@/utilities/eventBus.js";
import CreateInventoryDeduction from "@/modules/inventory-deduction/views/Create/Index.vue";
import ImportInventoryDeduction from "@/modules/inventory-deduction/views/Import/Index.vue";
import DeductSKU from "@/modules/inventory-deduction/components/DeductSKU.vue";
import PaginationComponent from '@/components/PaginationComponent.vue'
import filterMixin from "@/mixins/filter";
import {equals, isEmpty} from "ramda";
import dateMixin from "@/mixins/date.js";
import { mapGetters } from "vuex";
import warehouseMixin from '@/mixins/warehouse';
import { API_URL } from "@/utilities/constants";
import moment from "moment/moment";

export default {
  name: "InventoryDeduction",

  mixins: [filterMixin, dateMixin, warehouseMixin],

  components: {
    CreateInventoryDeduction,
    ImportInventoryDeduction,
    DeductSKU,
    PaginationComponent,
  },

  data() {
    return {
      items: [],
      isLoading: false,
      filter: this.setDefaultFilter(),
      warehouse_id: null,
      dialogExport: false,
      date: null,
      currentPage: 0,
      isLoadMore: false,
    };
  },

  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 230);
    },

    hasFilter() {
      const defaultFilter = this.setDefaultFilter();

      return !equals(defaultFilter, this.filter);
    },

    buildLinkDownload() {
      let link = `${API_URL}/inventory-deduction/export`;
      let params = {
        start_date: "",
        end_date: "",
        warehouse_id: this.userWarehouseId
      };

      if (this.date && this.date.length) {
        params.start_date = this.formatDate(this.date[0], false);
        params.end_date = this.formatDate(this.date[1], false);
      }

      params = new URLSearchParams(params);

      return `${link}?${params.toString()}`;
    },

    ...mapGetters(["getEmployees"]),
  },

  beforeUnmount() {
    EventBus.$off("showCreateInventoryDeduction");
    EventBus.$off("deductSKU");
  },

  mounted() {
    this.warehouse_id = this.getWarehouseId();
    this.setDefaultDate();
    this.fetchData();
    this.fetchEmployee();
  },

  methods: {
    deductSKU() {
      EventBus.$emit("deductSKU");
    },

    async fetchEmployee() {
      await this.$store.dispatch("getEmployees");
    },

    getEmployeeNameById(id) {
      const employee = this.getEmployees.find(
        (item) => +item.id === +id
      );

      return employee && employee.name;
    },

    onFilter(item = "") {
      this.filter.page = 1;
      this.$nextTick(() => {
        this.fetchInventoryDeduction();
        if (item) {
          this.$refs[item].handleClose();
        }
      });
    },

    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.$nextTick(() => {
        this.fetchInventoryDeduction();
      });
    },

    onChangeDate() {
      if (this.filter.date && this.filter.date.length) {
        this.filter.date[0] = this.formatDate(this.filter.date[0], false);
        this.filter.date[1] = this.formatDate(this.filter.date[1], false);
        this.onFilter();
      }
    },

    clearFilterItem(item) {
      this.filter[item] = "";
      this.$refs[item].handleClose();
      this.onFilter();
    },

    setDefaultDate() {
      let time = new Date();
      this.date = [
        this.formatDate(moment(time).subtract(7, 'days'), false),
        this.formatDate(new Date(), false),
      ];
    },

    setDefaultFilter() {
      let params = {
        limit: 25,
        page: 1,
        keyword: "",
        sku: "",
        employee_id: "",
        date: "",
        product_style: "",
      };

      return params;
    },

    setRouteParam() {
      const params = this.filter;
      this.$router.replace({ name: "inventory_deduction", query: params });
    },

    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = { ...filter, ...routeQuery };
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);

      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }

      filter.page = +filter.page || 1;

      return filter;
    },

    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchInventoryDeduction();
      });
    },

    createInventoryDeduction() {
      EventBus.$emit("showCreateInventoryDeduction");
    },

    importInventoryDeduction() {
      EventBus.$emit("showImportInventoryDeduction");
    },

    openModalExport() {
      this.dialogExport = true;
    },

    resetData() {
      this.dialogExport = false;
      this.setDefaultDate();
    },

    exportExcel() {
      return (window.location.href = this.buildLinkDownload);
    },

    async fetchData() {
      this.filter = this.getRouteParam();
      this.fetchInventoryDeduction();
    },

    async fetchInventoryDeduction() {
      this.isLoading = true;
      this.setRouteParam();
      const res = await list(this.filter);
      this.isLoading = false;
      const data = res.data || [];
      this.items = data.data;
      this.currentPage = data?.current_page || 0;
      this.isLoadMore = !!data?.next_page_url;
    },

    async deleteInventoryDeduction(item) {
      try {
        await revert({ id: item.id });
      } catch (e) {
        let data = e.response?.data;
        let message = data?.message || this.$t('Delete deduction error.');
        this.notification(message, 'error');
      } finally {
        this.fetchData();
      }
    },

    tableRowClassName(data) {
      return data.row.is_deleted ? "is-delete" : "";
    },

    hasChangeFilterByItem(name) {
      const query = this.$route.query;

      if (query[name] && query[name].trim()) {
        return true;
      }

      return false;
    },
  },
};
