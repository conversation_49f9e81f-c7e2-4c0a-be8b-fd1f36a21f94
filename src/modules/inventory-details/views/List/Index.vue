<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left">
        <h1>
          Inventory Details
          <span v-if="filter.date && filter.date.length" class="text-sm">
            (
            <template v-if="filter.date[0]">
              {{ formatView(filter.date[0]) }}
            </template>
            <template v-if="filter.date[1]">
              - {{ formatView(filter.date[1]) }}
            </template>
            )
          </span>
        </h1>
      </div>
      <div class="top-head-right">
        <el-button @click="exportExcel" type="primary">
          <span class="icon-margin-right"><icon :data="iconExport" /></span
          >{{ $t("Export") }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <div class="statistical">
        <el-row :gutter="20">
          <el-col :span="6" v-for="(item, index) in statistical" :key="index">
            <div class="border rounded p-3 shadow">
              <span class="label">{{ item.label }}: </span>
              <span
                class="value font-semibold"
                v-loading="isLoadingStatistical"
                >{{
                  (dataStatistical[item.key] &&
                    formatNumber(dataStatistical[item.key])) ||
                  0
                }}</span
              >
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="filter">
        <div class="label">{{ $t("Filter by:") }}</div>
        <div class="filter-item">
          <el-dropdown
            ref="style"
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('style') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('style')">
                <el-tooltip
                  effect="dark"
                  :content="$t('Style')"
                  placement="top-start"
                >
                  <span>{{ filter.style }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(" Style ") }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input
                  :placeholder="$t('Enter search keyword')"
                  class="search"
                  v-model="filter.style"
                  @keydown.enter="onFilter('style')"
                  clearable
                  @clear="clearFilterItem('style')"
                />
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown
            ref="sku"
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('sku') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('sku')">
                <el-tooltip
                  effect="dark"
                  :content="$t('SKU')"
                  placement="top-start"
                >
                  <span>{{ filter.sku }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(" SKU ") }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input
                  :placeholder="$t('Enter search keyword')"
                  class="search"
                  v-model="filter.sku"
                  @keydown.enter="onFilter('sku')"
                  clearable
                  @clear="clearFilterItem('sku')"
                />
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown
            ref="gtin"
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('gtin') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('gtin')">
                <el-tooltip
                  effect="dark"
                  :content="$t('GTIN')"
                  placement="top-start"
                >
                  <span>{{ filter.gtin }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(" GTIN ") }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input
                  :placeholder="$t('Enter search keyword')"
                  class="search"
                  v-model="filter.gtin"
                  @keydown.enter="onFilter('gtin')"
                  clearable
                  @clear="clearFilterItem('gtin')"
                />
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('brand_id') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('brand_id')">
                <el-tooltip
                  effect="dark"
                  :content="$t('Brand')"
                  placement="top-start"
                >
                  <span>{{ getBrandNameById(filter.brand_id) }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(" Brand ") }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-select
                  filterable
                  v-model="filter.brand_id"
                  :placeholder="$t('Select brand')"
                  @change="onFilter"
                >
                  <el-option value="">{{ $t(" All ") }}</el-option>
                  <el-option
                    v-for="item in getBrands"
                    :key="item.id"
                    :label="item.name"
                    :value="String(item.id)"
                  >
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown
            ref="ending"
            trigger="click"
            class="el-dropdown-filter-item"
          >
            <span class="el-dropdown-link">
              Ending
              {{ ending }}
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div
                class="el-dropdown-menu-filter-item el-dropdown-menu-filter-item-range"
              >
                <el-input-number
                  v-model="filter.ending"
                  size="small"
                  @blur="onFilter('ending')"
                />
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': filter.date && filter.date.length }"
          >
            <span class="el-dropdown-link">
              <template v-if="filter.date && filter.date.length">
                <el-tooltip
                  effect="dark"
                  :content="$t('Date')"
                  placement="top-start"
                >
                  <span>
                    {{
                      templateDateRange(filter.date[0], filter.date[1])
                    }}</span
                  >
                </el-tooltip>
              </template>
              <template v-else> {{ $t("Date") }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-date-picker
                  format="YYYY-MM-DD"
                  v-model="filter.date"
                  type="daterange"
                  range-separator="To"
                  :start-placeholder="$t('Start date')"
                  :end-placeholder="$t('End date')"
                  @change="onChangeDate"
                >
                </el-date-picker>
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item" v-if="hasFilter">
          <el-link type="danger" @click="onClearFilter" :underline="false">
            {{ $t("Clear") }}
          </el-link>
        </div>
      </div>
      <el-table
        border
        stripe
        size="small"
        :data="items"
        style="width: 100%"
        :max-height="maxHeight"
        v-loading="isLoading"
        element-loading-text="Loading..."
        :row-class-name="tableRowClassName"
        @sort-change="sortTable"
      >
        <el-table-column fixed prop="sku" :label="$t('SKU')" min-width="150">
          <template #default="scope">
            {{ scope.row.sku }}
          </template>
        </el-table-column>
        <el-table-column prop="style" :label="$t('Style')" min-width="150">
          <template #default="scope">
            {{ scope.row.style }}
          </template>
        </el-table-column>
        <el-table-column prop="color" :label="$t('Color')" min-width="200">
          <template #default="scope">
            {{ scope.row.color }}
          </template>
        </el-table-column>
        <el-table-column prop="size" :label="$t('Size')" min-width="120">
          <template #default="scope">
            {{ scope.row.size }}
          </template>
        </el-table-column>
        <el-table-column prop="gtin" :label="$t('GTIN')" min-width="200">
          <template #default="scope">
            {{ scope.row.gtin }}
          </template>
        </el-table-column>
        <el-table-column prop="brand_name" :label="$t('Brand')" min-width="150">
          <template #default="scope">
            {{ scope.row.brand_name }}
          </template>
        </el-table-column>
        <el-table-column prop="begining" :label="$t('Beginning')" width="100">
          <template #default="scope">
            {{ scope.row.begining }}
          </template>
        </el-table-column>
        <el-table-column prop="input" :label="$t('In')" width="100">
          <template #default="scope">
            {{ scope.row.input }}
          </template>
        </el-table-column>
        <el-table-column prop="output" :label="$t('Out')" width="100">
          <template #default="scope">
            {{ scope.row.output }}
          </template>
        </el-table-column>
        <el-table-column
          prop="ending"
          :label="$t('Ending')"
          width="100"
          sortable="custom"
          fixed="right"
        >
          <template #default="scope">
            {{ scope.row.ending }}
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom">
        <div class="total">
          {{ $t("Total:") }} {{ items.length ? formatNumber(total) : 0 }}
        </div>
        <el-pagination
          :disabled="isLoading"
          background
          layout="prev, pager, next"
          :page-size="filter.limit"
          :total="total"
          @current-change="changePage"
          v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
            v-model="filter.limit"
            :placeholder="$t('Select')"
            size="mini"
            @change="onFilter"
          >
            <el-option
              v-for="item in limits"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
  </div>
</template>
