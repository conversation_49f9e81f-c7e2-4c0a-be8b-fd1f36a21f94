import { inventoryDetails, inventoryTotalDetails } from "@/api/inventory.js";
import filterMixin from "@/mixins/filter";
import { equals } from "ramda";
import dateMixin from "@/mixins/date.js";
import { mapGetters } from "vuex";
import formatNumberMixin from "@/mixins/formatNumber.js";
import { API_URL } from "@/utilities/constants";
import warehouseMixin from "@/mixins/warehouse";
export default {
  name: "InventoryDetails",
  mixins: [filterMixin, dateMixin, formatNumberMixin, warehouseMixin],
  components: {},
  data() {
    return {
      items: [],
      isLoading: false,
      filter: this.setDefaultFilter(),
      isLoadingStatistical: false,
      statistical: [
        {
          label: this.$t("Total Beginning"),
          key: "begining",
        },
        {
          label: this.$t("Total In"),
          key: "input",
        },
        {
          label: this.$t("Total Out"),
          key: "output",
        },
        {
          label: this.$t("Total Ending"),
          key: "ending",
        },
      ],
      dataStatistical: {},
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 305);
    },
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },
    ...mapGetters(["getBrands"]),
    buildLinkDownload() {
      let link = `${API_URL}/inventory-detail/export`;

      let params = {
        start_date: "",
        end_date: "",
        warehouse_id: this.userWarehouseId,
      };

      if (this?.filter?.date?.length) {
        params.start_date = this.formatDate(this.filter.date[0], false);
        params.end_date = this.formatDate(this.filter.date[1], false);
      }

      params = new URLSearchParams(params);

      return `${link}?${params.toString()}`;
    },
  },
  beforeUnmount() {},
  mounted() {
    this.fetchData();
    this.fetchBrands();
  },
  methods: {
    exportExcel() {
      return (window.location.href = this.buildLinkDownload);
    },
    setDefaultDate() {
      const today = new Date();
      const priorDate = new Date(new Date().setDate(today.getDate() - 30));
      return (
        (today &&
          priorDate && [
            this.formatDate(priorDate, false),
            this.formatDate(today, false),
          ]) ||
        ""
      );
    },
    getBrandNameById(id) {
      const item = this.getBrands.find((item) => +item.id === +id);
      return item && item.name;
    },
    fetchBrands() {
      this.$store.dispatch("getBrands");
    },
    onFilter(item = "") {
      this.filter.page = 1;
      this.$nextTick(() => {
        this.fetchInventoryDetails();
        this.fetchInventoryTotalDetails();
        if (item) {
          this.$refs[item].handleClose();
        }
      });
    },
    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.$nextTick(() => {
        this.fetchInventoryDetails();
        this.fetchInventoryTotalDetails();
      });
    },
    onChangeDate() {
      if (this.filter.date && this.filter.date.length) {
        this.filter.date[0] = this.formatDate(this.filter.date[0], false);
        this.filter.date[1] = this.formatDate(this.filter.date[1], false);
        this.onFilter();
      }
    },
    clearFilterItem(item) {
      this.filter[item] = "";
      this.$refs[item].handleClose();
      this.onFilter();
    },
    setDefaultFilter() {
      let params = {
        limit: 25,
        page: 1,
        style: "",
        sku: "",
        gtin: "",
        brand_id: "",
        sort_column: "",
        sort_by: "",
        ending: "",
        date: this.setDefaultDate(),
      };
      return params;
    },
    setRouteParam() {
      const params = this.filter;
      this.$router.replace({ name: "inventory_details", query: params });
    },
    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);
      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }
      filter.page = +filter.page || 1;
      return filter;
    },
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchInventoryDetails();
        this.fetchInventoryTotalDetails();
      });
    },
    async fetchData() {
      this.filter = this.getRouteParam();
      this.fetchInventoryDetails();
      this.fetchInventoryTotalDetails();
    },
    async fetchInventoryDetails() {
      this.isLoading = true;
      this.setRouteParam();
      const res = await inventoryDetails(this.filter);
      this.isLoading = false;
      const data = res.data || [];
      this.total = data.total;
      this.items = data.data;
    },
    async fetchInventoryTotalDetails() {
      this.isLoadingStatistical = true;
      const res = await inventoryTotalDetails(this.filter);
      const data = res.data || {};
      this.dataStatistical = data;
      this.isLoadingStatistical = false;
    },
    tableRowClassName(data) {
      return data.row.is_deleted ? "is-delete" : "";
    },
    hasChangeFilterByItem(name) {
      const query = this.$route.query;
      if (query[name]) {
        return true;
      }
      return false;
    },
    sortTable(data) {
      let sortColumn = "";
      let sortBy = "";
      if (data.prop && data.order) {
        sortColumn = data.prop;
        if (data.order === "ascending") {
          sortBy = "ASC";
        } else if (data.order === "descending") {
          sortBy = "DESC";
        }
      }
      this.filter.sort_column = sortColumn;
      this.filter.sort_by = sortBy;
      this.$nextTick(() => {
        this.fetchInventoryDetails();
        this.fetchInventoryTotalDetails();
      });
    },
  },
};
