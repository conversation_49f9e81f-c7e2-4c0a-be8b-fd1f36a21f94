<template>
  <div>
    <el-dialog
      v-model="openModalIncoming"
      :title="
        this?.data?.sku ? this?.data?.sku + ' Incoming' : $t('SKU Incoming')
      "
      custom-class="el-dialog-custom "
      :close-on-click-modal="false"
    >
      <template #default>
        <div class="table-content">
          <el-table
            border
            stripe
            size="small"
            :data="items"
            style="width: 100%"
            v-loading="isLoading"
            element-loading-text="Loading..."
            :max-height="500"
          >
            <el-table-column prop="po_number" :label="$t('PO Number')">
              <template #default="scope">
                {{ scope.row.po_number }}
              </template>
            </el-table-column>
            <el-table-column prop="order_date" :label="$t('Order Date')">
              <template #default="scope">
                {{ formatDate(scope.row.order_date, false) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" :label="$t('Status')">
              <template #default="scope">
                <div v-html="getOrderStatus(scope.row)"></div>
              </template>
            </el-table-column>
          </el-table>
          <div class="bottom">
            <div class="total">
              {{ $t('Total:') }} {{ items.length ? formatNumber(total) : 0 }}
            </div>
            <el-pagination
              :disabled="isLoading"
              background
              layout="prev, pager, next"
              :page-size="filter.limit"
              :total="total"
              @current-change="changePage"
              v-model:currentPage="filter.page"
            >
            </el-pagination>
            <div class="limit" :disabled="isLoading">
              <el-select
                v-model="filter.limit"
                :placeholder="$t('Select')"
                size="mini"
                @change="onFilter"
              >
                <el-option
                  v-for="item in limits"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import EventBus from '@/utilities/eventBus.js';
import filterMixin from '@/mixins/filter';
import dateMixin from '@/mixins/date.js';
import purchaseOrderMixin from '@/mixins/purchaseOrder.js';
import { getPurchaseOrderByProductId } from '@/api/purchaseOrder.js';
export default {
  name: 'ForecastIncoming',
  mixins: [filterMixin, dateMixin, purchaseOrderMixin],
  props: {},
  computed: {},
  data() {
    return {
      items: [],
      isLoading: false,
      openModalIncoming: false,
      data: {},
      total: 0,
      filter: {
        page: 1,
        limit: 25,
      },
    };
  },
  created() {
    EventBus.$on('showModalIncoming', (data) => {
      this.data = data;
      this.openModalIncoming = true;
      this.fetchData();
    });
  },
  methods: {
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchData();
      });
    },
    onFilter() {
      this.filter.page = 1;
      this.$nextTick(() => {
        this.fetchData();
      });
    },
    async fetchData() {
      this.isLoading = true;
      if (!this.data || !this.data.product_id) return;
      const res = await getPurchaseOrderByProductId(
        this.data.product_id,
        this.filter
      );
      const data = res.data || {};
      this.total = data.total || 0;
      this.items = data.data || [];
      this.isLoading = false;
    },
  },
};
</script>

<style lang="scss"></style>
