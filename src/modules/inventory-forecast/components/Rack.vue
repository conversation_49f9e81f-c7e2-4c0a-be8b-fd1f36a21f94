<template>
  <div>
    <el-dialog
      v-model="openModalRack"
      :title="this?.data?.sku ? this?.data?.sku + ' Rack' : $t('SKU Rack')"
      custom-class="el-dialog-custom "
      :close-on-click-modal="false"
    >
      <template #default>
        <div class="table-content">
          <el-table
            border
            stripe
            size="small"
            :data="items"
            style="width: 100%"
            v-loading="isLoading"
            element-loading-text="Loading..."
            :max-height="500"
          >
            <el-table-column prop="box_id" :label="$t('Box ID')" width="120">
              <template #default="scope">
                {{ scope.row.barcode }}
              </template>
            </el-table-column>
            <el-table-column
              prop="location"
              :label="$t('Location')"
              width="140"
            >
              <template #default="scope">
                {{ scope.row?.location?.barcode }}
              </template>
            </el-table-column>
            <el-table-column prop="product_name" :label="$t('Product Name')">
              <template #default="scope">
                {{ scope.row?.product?.name }}
              </template>
            </el-table-column>
            <el-table-column prop="sku" :label="$t('Product SKU')">
              <template #default="scope">
                {{ scope.row?.product?.sku }}
              </template>
            </el-table-column>
            <el-table-column prop="quantity" :label="$t('Quantity')">
              <template #default="scope">
                {{ formatNumber(scope.row.quantity) }}
              </template>
            </el-table-column>
            <el-table-column prop="created_at" :label="$t('Created At')">
              <template #default="scope">
                {{ formatDate(scope.row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column prop="updated_at" :label="$t('Updated At')">
              <template #default="scope">
                {{ formatDate(scope.row.updated_at) }}
              </template>
            </el-table-column>
          </el-table>
          <div class="bottom">
            <div class="total">
              {{ $t('Total:') }} {{ items.length ? formatNumber(total) : 0 }}
            </div>
            <el-pagination
              :disabled="isLoading"
              background
              layout="prev, pager, next"
              :page-size="filter.limit"
              :total="total"
              @current-change="changePage"
              v-model:currentPage="filter.page"
            >
            </el-pagination>
            <div class="limit" :disabled="isLoading">
              <el-select
                v-model="filter.limit"
                :placeholder="$t('Select')"
                size="mini"
                @change="onFilter"
              >
                <el-option
                  v-for="item in limits"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import EventBus from '@/utilities/eventBus.js';
import filterMixin from '@/mixins/filter';
import dateMixin from '@/mixins/date.js';
import { getBoxByRackByProductId } from '@/api/box.js';
export default {
  name: 'ForecastRack',
  mixins: [filterMixin, dateMixin],
  props: {},
  computed: {},
  data() {
    return {
      items: [],
      isLoading: false,
      openModalRack: false,
      data: {},
      total: 0,
      filter: {
        page: 1,
        limit: 25,
      },
    };
  },
  created() {
    EventBus.$on('showModalRack', (data) => {
      this.data = data;
      this.openModalRack = true;
      this.fetchData();
    });
  },
  methods: {
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchData();
      });
    },
    onFilter() {
      this.filter.page = 1;
      this.$nextTick(() => {
        this.fetchData();
      });
    },
    async fetchData() {
      this.isLoading = true;
      if (!this.data || !this.data.product_id) return;
      const res = await getBoxByRackByProductId(
        this.data.product_id,
        this.filter
      );
      const data = res.data || {};
      this.total = data.total || 0;
      this.items = data.data || [];
      this.isLoading = false;
    },
  },
};
</script>

<style lang="scss"></style>
