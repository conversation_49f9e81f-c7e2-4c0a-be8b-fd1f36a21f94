<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <el-dialog
      v-model="dialogVisible"
      :title="$t('Import Backorder')"
      custom-class="el-dialog-custom"
      width="50%"
  >
    <el-tabs v-model="currentTab">
      <el-tab-pane :label="$t('Import')" name="import">
        <p class="mb-2">
          Upload file CSV/XLS (<el-link :underline="false" type="primary" @click="downloadCsvTemplate">{{ $t('Download example') }}</el-link>)
        </p>
        <el-upload
            class="el-upload-drag-full mb-1"
            ref="uploadRefBackorder"
            name="inventory_backorder"
            limit="1"
            accept=".csv,.xlsx"
            drag
            :headers="headerInfo"
            :on-success="onSuccess"
            :on-error="onError"
            :action="uploadApi"
        >
          <el-icon class="el-icon--upload">
            <icon :data="iconPlus" />
          </el-icon>
          <div class="el-upload__text">
            Drop file here or <em>{{ $t('click to upload') }}</em>
          </div>
        </el-upload>
        <template v-if="uploadResponse.validation_result.length">
          <el-table :data="uploadResponse.validation_result" border size="small" class="w-full mt-2 mb-3" max-height="260">
            <el-table-column prop="product_sku" :label="$t('SKU')" />
            <el-table-column prop="backorder" :label="$t('Backorder')" />
            <el-table-column prop="allocated" :label="$t('Allocated')" />
            <el-table-column :label="$t('Status')">
              <template #default="scope">
                <el-tag :type="scope.row.status ? 'success' : 'error'">{{ scope.row.status ? 'Valid' : 'Invalid' }}</el-tag>
              </template>
            </el-table-column>
          </el-table>
          <div class="flex justify-space-between">
            <div>
              <span class="mr-2">Total: {{ uploadResponse.total_row }}</span>
              <span class="mr-2">Valid: {{ uploadResponse.total_success }}</span>
              <span class="mr-2">Invalid: {{ uploadResponse.total_error }}</span>
              <span>Total quantity: {{ uploadResponse.total_quantity }}</span>
            </div>
            <el-button :disabled="!uploadResponse.status" type="primary" :loading="importing" @click="importCsvFile()">{{ $t('Import') }}</el-button>
          </div>
        </template>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>
