import EventBus from "@/utilities/eventBus.js";
import { getToken } from "@/utilities/jwt";
import notificationMixin from "@/mixins/notification";
import { importCsvFile } from "@/api/inventoryForecast.js";
import { API_URL, STORAGE_URL } from "@/utilities/constants";
import dateMixin from "@/mixins/date";
import filterMixin from "@/mixins/filter";

export default {
    name: "ImportInventoryBackorder",
    mixins: [notificationMixin, dateMixin, filterMixin],
    data() {
        return {
            dialogVisible: false,
            headerInfo: {
                'Authorization': getToken()
            },
            currentTab: 'import',
            uploadResponse: {
                csv_id: 0,
                status: false,
                validation_result: [],
                total_row: 0,
                total_success: 0,
                total_error: 0
            },
            importing: false,
            uploadApi: API_URL + '/backorder/verify-file-import/',
            csvHistoryList: [],
            isLoading: false,
            employeeId : ''
        }
    },
    beforeUnmount() {
        EventBus.$off("showImportInventoryBackorder");
    },
    created() {
        EventBus.$on("showImportInventoryBackorder", (employeeId) => {
            this.defaultData()
            this.employeeId = employeeId
            this.uploadApi = this.uploadApi + employeeId
            this.dialogVisible = true;
        });
    },
    async mounted() {

    },
    methods: {
        onSuccess(res) {
            this.$refs.uploadRefBackorder.clearFiles();
            Object.assign(this.uploadResponse, res);
        },
        onError(err) {
            const res = JSON.parse(err.message);
            Object.assign(this.uploadResponse, res);
            this.notification(res.message,'error');
        },
        async importCsvFile() {
            try {
                this.importing = true;
                const res = await importCsvFile({
                    id : this.uploadResponse.csv_id,
                    employee_id : this.employeeId
                });
                this.$emit('refresh');
                this.notification(res.data.message,'success');
                this.uploadResponse.validation_result = [];
                this.dialogVisible = false;
            } catch (e) {
                this.notification(res.data.message,'error');
            } finally {
                this.importing = false;
            }
        },
        defaultData(){
            this.uploadResponse.csv_id = 0
            this.uploadResponse.status = false
            this.uploadResponse.validation_result = []
            this.uploadResponse.total_row = 0
            this.uploadResponse.total_success = 0
            this.uploadResponse.total_error = 0
        },
        downloadCsvTemplate() {
            window.open(`${STORAGE_URL}/csv-template/backorder.csv`, '_blank');
        },
    }
}