<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ $t('Inventory Forecast') }}</h1>
      </div>
      <div class="top-head-right flex">
        <el-dropdown trigger="click" class="el-dropdown-filter-item mr-2" ref="productionStatus" :max-height="400">
          <el-button type="primary" plain>
            {{ $t('Columns') }}
          </el-button>
          <template #dropdown>
            <div class="el-dropdown-menu-filter-item">
              <el-form label-width="160px">
                <template v-for="(item, index) in columns" :key="index">
                  <el-form-item :key="index" :label="item.label" v-if="!item.isHide" class="mb-2">
                    <el-checkbox v-model="item.isShow" @change="changeColumn()" />
                  </el-form-item>
                </template>
              </el-form>
            </div>
          </template>
        </el-dropdown>

        <el-button type="info" @click="importInventoryBackorder">
          <span class="icon-margin-right">
            <icon :data="iconAdd" />
          </span>{{ $t('Import') }}
        </el-button>
        <el-button type="primary" @click="exportToExcel">
          <span class="icon-margin-right">
            <icon :data="iconExport" />
          </span>{{ $t('Export') }}
        </el-button>
      </div>
    </div>

    <div class="table-content">
      <div class="mb-3">
        <el-form>
          <div v-if="employee" class="bg-gray-50 p-3 border rounded max-w-md">
            <div class="flex justify-between">
              <b class="mr-2">{{ $t('Hi') }} {{ employee.name + ',' }}
                {{ $t(' Have a nice day!') }}</b>
              <el-link type="danger" class="ml-3" @click="resetEmployee" :underline="false">{{ $t('Logout') }}</el-link>
            </div>
          </div>
        </el-form>
        <el-form v-show="!employee" :error="employeeError" @submit.prevent="getScanCodeEmloyee">
          <el-input :placeholder="$t('Scan Employee ID')" class="el-form-item-scan-employee max-w-fit"
            ref="employeeCode" v-model="codeEmployee" />
        </el-form>
      </div>

      <div class="mb-3">
        <el-date-picker v-model="date" type="daterange" unlink-panels range-separator="To"
          start-placeholder="Start date" end-placeholder="End date" @change="onChangeDate" :shortcuts="shortcuts"
          :disabled-date="disabledDate" :clearable="false">
        </el-date-picker>
      </div>

      <div class="filter">
        <div class="label">{{ $t('Filter by:') }}</div>
        <el-input :placeholder="$t('SKU')" class="el-form-item-scan-employee max-w-fit mr-2" ref="sku"
          v-model="filter.sku" @keyup.enter="onFilter" />
        <div class="filter-item">
          <el-select :placeholder="$t('Brand')" class="brand" v-model="filter.brand_id" filterable @change="onFilter"
            clearable @clear="onClearBrand">
            <el-option v-for="brand in getBrands" :key="brand.id" :label="brand.name" :value="String(brand.id)">
            </el-option>
          </el-select>
        </div>
        <div class="filter-item">
          <el-select v-model="filter.style" @change="changeStyle" filterable :placeholder="$t('Product Style')"
            clearable @clear="onClearStyle">
            <el-option v-for="item in styles" :key="item.style" :label="item.style" :value="item.style">
            </el-option>
          </el-select>
        </div>
        <div class="filter-item">
          <el-select v-model="filter.color" filterable :placeholder="$t('Color')" clearable @change="onFilter">
            <el-option v-for="item in colors" :key="item" :label="item" :value="item">
            </el-option>
          </el-select>
        </div>
        <div class="filter-item">
          <el-select v-model="filter.size" filterable :placeholder="$t('Size')" clearable @change="onFilter">
            <el-option v-for="item in sizes" :key="item" :label="item" :value="item">
            </el-option>
          </el-select>
        </div>
        <div class="filter-item">
          <el-select v-model="filter.rank" filterable :placeholder="$t('SKU Rank')" clearable @change="onFilter">
            <el-option v-for="item in ranks" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class="filter-item" v-if="hasFilter">
          <el-link type="danger" @click="onClearFilter" :underline="false">
            <span style="white-space: nowrap;">{{ $t('Clear') }}</span>
          </el-link>
        </div>
      </div>

      <el-table border stripe size="small" :data="items" style="width: 100%" :max-height="maxHeight"
        v-loading="isLoading" element-loading-text="Loading..." @sort-change="sortTable"
        :cell-class-name="tableCellClassName" :header-cell-class-name="headerCellClassName">
        <template v-for="(col, index) in columns" :key="index">
          <el-table-column :prop="col.slug" :label="$t(col.label)" :width="col.width || 'auto'"
            :sortable="col.sortable ? 'custom' : false" v-if="col.isShow">
            <template #header v-if="['product_rank'].includes(col.slug)">
              <el-tooltip placement="top-start">
                <template #content>
                  Rank SKUs by number of units sold in the previous quarter<br />
                  A - top 20%, B - next 20%, C - next 20%, D - next 20%, E - last 20%
                </template>
                <span> {{ $t('SKU Rank') }}</span>
              </el-tooltip>
            </template>
            <template #default="scope">
              <template v-if="col.formatNumber">
                {{ formatNumber(scope.row[col.slug]) || col.default }}
                {{ col.slug == 'growth_rate' ? '%' : '' }}
              </template>
              <template v-else-if="['backorder', 'allocated'].includes(col.slug)">
                <template v-if="!employee">
                  <el-link type="primary" @click="hasEmployee" :underline="false">
                    {{ formatNumber(scope.row[col.slug]) || col.default }}
                  </el-link>
                </template>
                <template v-else>
                  <el-dropdown trigger="click" :ref="col.slug"
                    @visible-change="handleOpenEditColum(scope.row[col.slug])">
                    <el-link type="primary" :underline="false">
                      {{ formatNumber(scope.row[col.slug]) || col.default }}
                    </el-link>
                    <template #dropdown>
                      <div class="flex p-3">
                        <div class="mr-2">
                          <el-input-number style="max-width: 180px" :placeholder="$t(col.label)" v-model="columnVal"
                            @keyup.enter="updateByColumn(col, scope.row)" />
                        </div>
                        <div class="flex justify-end">
                          <el-button type="primary" @click="updateByColumn(col, scope.row)">{{ $t('Save') }}</el-button>
                          <el-button @click="cancelUpdateByColumn(col)">
                            {{ $t('Cancel') }}
                          </el-button>
                        </div>
                      </div>
                    </template>
                  </el-dropdown>
                </template>
              </template>
              <template v-else-if="['rack'].includes(col.slug)">
                <el-link type="primary" @click="showModalRack(scope.row)" :underline="false">
                  {{ formatNumber(scope.row[col.slug]) || col.default }}
                </el-link>
              </template>
              <template v-else-if="['incoming'].includes(col.slug)">
                <el-link type="primary" @click="showModalIncoming(scope.row)" :underline="false">
                  {{ formatNumber(scope.row[col.slug]) || col.default }}
                </el-link>
              </template>
              <template v-else-if="['product_rank'].includes(col.slug)">
                <el-tag v-if="scope.row.product_rank.toLowerCase() == 'a'"
                  @click="showProductRankHistory(scope.row.product_id, scope.row.sku)"
                  style="color: white; cursor: pointer; background-color: #dc2626;">
                  A
                </el-tag>
                <el-tag v-else-if="scope.row.product_rank.toLowerCase() == 'b'"
                  @click="showProductRankHistory(scope.row.product_id, scope.row.sku)"
                  style="color: white; cursor: pointer; background-color: #059669;">
                  B
                </el-tag>
                <el-tag v-else-if="scope.row.product_rank.toLowerCase() == 'c'"
                  @click="showProductRankHistory(scope.row.product_id, scope.row.sku)"
                  style="color: white; cursor: pointer; background-color: #5583e4;">
                  C
                </el-tag>
                <el-tag v-else-if="scope.row.product_rank.toLowerCase() == 'd'"
                  @click="showProductRankHistory(scope.row.product_id, scope.row.sku)"
                  style="color: white; cursor: pointer; background-color: #e4b255;">
                  D
                </el-tag>
                <el-tag v-else-if="scope.row.product_rank.toLowerCase() == 'e'"
                  @click="showProductRankHistory(scope.row.product_id, scope.row.sku)"
                  style="color: white; cursor: pointer; background-color: #bb55e4;">
                  E
                </el-tag>
                <el-tag v-else @click="showProductRankHistory(scope.row.product_id, scope.row.sku)"
                  style="font-weight: bold; cursor: pointer; background-color: transparent; border: none; color: black; font-size: 20px;">
                  -
                </el-tag>
              </template>
              <template v-else>
                {{ scope.row[col.slug] || col.default }}
              </template>
            </template>
          </el-table-column>
        </template>
        <el-table-column prop="action" :label="$t('Action')" width="68" fixed="right">
          <template #default="scope">
            <el-popconfirm :title="'Are you sure to hide ' + scope.row.sku + '?'" @confirm="hideItem(scope.row)">
              <template #reference>
                <el-button type="primary" size="small">
                  {{ $t('Hide') }}
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>

      <div class="bottom">
        <div class="total">
          {{ $t('Total:') }} {{ items.length ? formatNumber(total) : 0 }}
        </div>
        <el-pagination :disabled="isLoading" background layout="prev, pager, next" :page-size="filter.limit"
          :total="total" @current-change="changePage" v-model:currentPage="filter.page">
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select v-model="filter.limit" :placeholder="$t('Select')" size="mini" @change="onFilter">
            <el-option v-for="item in limits" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </div>
      </div>
    </div>

    <Rack />
    <Incoming />
    <import-inventory-backorder @refresh="fetchInventoryForecast" />
    <ProductRankHistory />
  </div>
</template>
