import { list, hideSKU, updateBackOrder } from '@/api/inventoryForecast.js';
import filterMixin from '@/mixins/filter';
import { equals, isEmpty } from 'ramda';
import dateMixin from '@/mixins/date.js';
import formatNumberMixin from '@/mixins/formatNumber.js';
import { getProductAttributes } from '@/api/product.js';
import { API_URL } from '@/utilities/constants';
import warehouseMixin from '@/mixins/warehouse';
import { mapGetters } from 'vuex';
import moment from 'moment-timezone';
import EventBus from '@/utilities/eventBus.js';
import ImportInventoryBackorder from '@/modules/inventory-forecast/views/Import/Index.vue';
import ProductRankHistory from "@/modules/inventory-pulling-shelf/views/ProductRankHistory/Index.vue";
import { employeeLogoutTimeChecking, employeeTimeChecking } from '@/api/employee.js';
import Rack from '@/modules/inventory-forecast/components/Rack.vue';
import Incoming from '@/modules/inventory-forecast/components/Incoming.vue';

export default {
  name: 'InventoryForecast',

  mixins: [
    filterMixin,
    dateMixin,
    formatNumberMixin,
    warehouseMixin,
  ],

  components: {
    Rack,
    Incoming,
    ImportInventoryBackorder,
    ProductRankHistory,
  },

  data() {
    return {
      items: [],
      isLoading: false,
      filter: this.setDefaultFilter(),
      styles: [],
      colors: [],
      sizes: [],
      date: '',
      shortcuts: [
        {
          text: 'Next 30 days',
          value: () => {
            const date = new Date();
            const endDate = moment(date).add(30, 'days');

            return [this.formatDate(date), this.formatDate(endDate)];
          },
        },
        {
          text: 'Next 60 days',
          value: () => {
            const date = new Date();
            const endDate = moment(date).add(60, 'days');

            return [this.formatDate(date), this.formatDate(endDate)];
          },
        },
        {
          text: 'Next 90 days',
          value: () => {
            const date = new Date();
            const endDate = moment(date).add(90, 'days');

            return [this.formatDate(date), this.formatDate(endDate)];
          },
        },
      ],
      columns: [
        {
          label: 'Brand',
          slug: 'brand_name',
          isShow: true,
          width: 100,
        },
        {
          label: 'SKUs',
          slug: 'sku',
          isShow: true,
          isHide: true,
          width: 150,
        },
        {
          label: 'Style',
          slug: 'style',
          isShow: true,
        },
        {
          label: 'Color',
          slug: 'color',
          isShow: true,
          width: 150,
        },
        {
          label: 'Size',
          slug: 'size',
          isShow: true,
        },
        {
          label: 'SKU Rank ',
          slug: 'product_rank',
          isShow: true,
        },
        {
          label: 'Rack',
          slug: 'rack',
          isShow: true,
          default: 0,
        },
        {
          label: 'Pulling',
          slug: 'pulling',
          isShow: true,
          formatNumber: true,
          default: 0,
        },
        {
          label: 'Incoming',
          slug: 'incoming',
          isShow: true,
          default: 0,
        },
        {
          label: 'Total',
          slug: 'total',
          isShow: true,
          formatNumber: true,
          default: 0,
        },
        {
          label: 'BackOrder',
          slug: 'backorder',
          isShow: true,
          default: 0,
        },
        {
          label: 'Allocated',
          slug: 'allocated',
          isShow: true,
          default: 0,
        },
        {
          label: 'Employee',
          slug: 'employee',
          isShow: true,
          width: 150,
        },
        {
          label: 'Last PO',
          slug: 'last_po',
          isShow: true,
          width: 150,
        },
        {
          label: 'Same Period Last Year',
          slug: 'last_year',
          isShow: true,
          sortable: true,
          width: 180,
          formatNumber: true,
        },
        {
          label: 'Growth Rate',
          slug: 'growth_rate',
          isShow: true,
          sortable: true,
          width: 140,
          formatNumber: true,
        },
        {
          label: 'Need Buy',
          slug: 'need_buy',
          isShow: true,
          sortable: true,
          width: 140,
          formatNumber: true,
        },
        {
          label: 'Last 30 Days Volume',
          slug: 'volume',
          isShow: true,
          sortable: true,
          width: 166,
          formatNumber: true,
        },
        {
          label: 'Months Remaining',
          slug: 'months_remaining',
          isShow: true,
          sortable: true,
          width: 150,
          formatNumber: true,
        },
      ],
      columnName: 'inventoryForecastNewColumns',
      job_type: 'inventory_forecast',
      employee: '',
      time_checking_id: '',
      codeEmployee: '',
      employeeError: '',
      columnVal: '',
      ranks: [
        {
          label: this.$t('All'),
          value: 0,
        },
        {
          label: this.$t('A'),
          value: "A",
        },
        {
          label: this.$t('B'),
          value: "B",
        },
        {
          label: this.$t('C'),
          value: "C",
        },
        {
          label: this.$t('D'),
          value: "D",
        },
        {
          label: this.$t('E'),
          value: "E",
        },
        {
          label: this.$t('-'),
          value: "-",
        },
      ],
    };
  },

  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 350);
    },

    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },

    ...mapGetters(['getBrands']),

    buildLinkDownload() {
      let link = `${API_URL}/forecast-inventory/export`;
      let columns = [];

      this.columns.forEach((item) => {
        if (item.isShow) {
          columns.push(item.slug);
        }
      });

      let params = {
        ...this.filter,
        columns: columns,
        warehouse_id: this.userWarehouseId,
      };
      params = new URLSearchParams(params);

      return `${link}?${params.toString()}`;
    },
  },

  beforeUnmount() {
    EventBus.$off('showModalRack');
    EventBus.$off('showModalIncoming');
    EventBus.$off("showProductRankHistory");
  },

  mounted() {
    this.filter = this.getRouteParam();
    this.date = this.setDefaultDate();

    if (this.date && this.date.length) {
      this.filter.start_date = this.formatDate(this.date[0], false);
      this.filter.end_date = this.formatDate(this.date[1], false);
    }

    this.filter.sort_by = 'DESC';
    this.filter.sort_column = 'need_buy';
    this.getColumnsLocalStorage();
    this.fetchInventoryForecast();
    this.fetchProductAttributes();
    this.fetchBrands();
  },

  methods: {
    handleOpenEditColum(val) {
      this.columnVal = val || 0;
    },

    async hideItem(item) {
      try {
        const params = {
          product_sku: item.sku,
        };
        await hideSKU(params);
        this.notification(`Hide SKU ${item.sku} successfully.`);
        this.fetchInventoryForecast();
      } catch (e) {
        this.notification('Error', 'error');
      }
    },

    async updateByColumn(col, item) {
      let length = this.items.length;
      let prevData = 0;

      for (let i = 0; i < length; i++) {
        const data = this.items[i];

        if (data.product_id === item.product_id) {
          prevData = data[col.slug] || 0;

          continue;
        }
      }

      if (prevData == this.columnVal) {
        this.cancelUpdateByColumn(col);

        return;
      }

      try {
        let params = {
          employee_id: this.employee.id,
          product_sku: item.sku,
        };
        params[col.slug] = this.columnVal;
        const res = await updateBackOrder(params);

        for (let i = 0; i < length; i++) {
          const data = this.items[i];

          if (data.product_id === item.product_id) {
            data[col.slug] = this.columnVal;
            data.employee = this.employee.name;

            continue;
          }
        }

        this.cancelUpdateByColumn(col);
        this.notification(`Updated successfully.`);
      } catch (e) {
        this.notification('Error', 'error');
      }
    },

    cancelUpdateByColumn(col) {
      this.columnVal = 0;
      const $refs = this.$refs[col.slug];

      if ($refs.length) {
        for (let i = 0; i <= $refs.length; i++) {
          if (!$refs[i]) continue;
          $refs[i].handleClose();
        }
      } else if ($refs) {
        $refs.handleClose();
      }
    },

    hasEmployee() {
      this.notification('Please Scan Employee ID', 'warning');
      this.focusByElClass();
    },

    async getScanCodeEmloyee() {
      try {
        const res = await employeeTimeChecking({
          code: Number(this.codeEmployee),
          job_type: this.job_type,
        });

        if (!res.data.data) {
          this.codeEmployee = '';
          this.employeeError = 'Scan employee code error, please scan again.';
          this.notification(this.employeeError, 'error');
          this.focusByElClass();

          return;
        }

        this.employee = res.data.data;
        this.time_checking_id = res.data.id_time_checking;
      } catch (e) {
        const data = e.response.data;
        let message = this.$t('Not found');

        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }

        this.notification(message, 'error');
      }
    },

    async resetEmployee() {
      await employeeLogoutTimeChecking(this.time_checking_id);
      this.employee = null;
      this.employeeError = '';
      this.codeEmployee = '';
      this.time_checking_id = null;
      this.focusByElClass();
    },

    focusByElClass(elClass = 'el-form-item-scan-employee') {
      this.$nextTick(() => {
        const el = document.getElementsByClassName(elClass);

        if (!el || !el.length) {
          return;
        }

        const firtsElError = (el && el[0]) || undefined;
        const input = firtsElError.querySelector('input');
        document.body.scrollTop = (input.offsetTop - 120) | 0;
        input.focus();
      });
    },

    setColumnsLocalStorage() {
      localStorage.setItem(this.columnName, JSON.stringify(this.columns));
    },

    getColumnsLocalStorage() {
      let inventoryForecastColumns = localStorage.getItem(this.columnName);

      if (!inventoryForecastColumns) {
        this.setColumnsLocalStorage();

        return;
      }

      this.columns = JSON.parse(inventoryForecastColumns);
    },

    changeColumn() {
      this.setColumnsLocalStorage();
    },

    showModalRack(item) {
      EventBus.$emit('showModalRack', item);
    },

    showModalIncoming(item) {
      EventBus.$emit('showModalIncoming', item);
    },

    disabledDate(date) {
      const today = moment().subtract(1, 'days');

      return moment(today) > moment(date);
    },

    fetchBrands() {
      this.$store.dispatch('getBrands');
    },

    exportToExcel() {
      return (window.location.href = this.buildLinkDownload);
    },

    changeStyle() {
      const item = this.styles.find((item) => item.style === this.filter.style);
      this.filter.size = '';
      this.filter.color = '';
      this.colors = item?.colors || [];
      this.sizes = item?.sizes || [];
      this.onFilter();
    },

    async fetchProductAttributes() {
      const res = await getProductAttributes();
      const data = res.data || {};
      this.styles = Object.values(data);
    },

    setDefaultDate() {
      const today = new Date();
      const endDate = moment().add(90, 'days');

      return ((today && endDate && [this.formatDate(today, false), this.formatDate(endDate, false)]) || '');
    },

    onFilter(item = '') {
      this.filter.page = 1;

      this.$nextTick(() => {
        this.fetchInventoryForecast();

        if (item && this.$refs[item]) {
          this.$refs[item].handleClose();
        }
      });
    },

    onClearBrand() {
      this.filter.brand_id = '';
      this.$nextTick(() => {
        this.fetchInventoryForecast();
      });
    },

    onClearStyle() {
      this.filter.style = '';
      this.filter.size = '';
      this.filter.color = '';

      this.$nextTick(() => {
        this.fetchInventoryForecast();
      });
    },

    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.date = this.setDefaultDate();

      if (this.date && this.date.length) {
        this.filter.start_date = this.formatDate(this.date[0], false);
        this.filter.end_date = this.formatDate(this.date[1], false);
      }

      this.$nextTick(() => {
        this.fetchInventoryForecast();
      });
    },

    onChangeDate() {
      if (this.date && this.date.length) {
        this.filter.start_date = this.formatDate(this.date[0], false);
        this.filter.end_date = this.formatDate(this.date[1], false);
      } else {
        this.filter.start_date = '';
        this.filter.end_date = '';
      }

      this.onFilter();
    },

    clearFilterItem(item) {
      this.filter[item] = '';
      this.$refs[item].handleClose();
      this.onFilter();
    },

    setDefaultFilter() {
      let params = {
        limit: 25,
        page: 1,
        sort_column: '',
        sort_by: '',
        style: '',
        color: '',
        size: '',
        start_date: '',
        end_date: '',
        brand_id: '',
        rank: '',
      };

      return params;
    },

    setRouteParam() {
      const params = this.filter;
      this.$router.replace({
        name: 'inventory_forecast',
        query: params,
      });
    },

    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);

      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }

      filter.page = +filter.page || 1;

      return filter;
    },

    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchInventoryForecast();
      });
    },

    async fetchData() {
      this.filter = this.getRouteParam();
      this.fetchInventoryForecast();
    },

    async fetchInventoryForecast() {
      this.isLoading = true;

      try {
        this.setRouteParam();
        list(this.filter).then((res) => {
          this.isLoading = false;
          const data = res.data || [];
          this.items = data.data || [];
        });
        list({ ...this.filter, get_total: true }).then((resTotal) => {
          this.total = resTotal.data.total || 1;
        });
      } catch (e) {
        this.isLoading = false;
        this.total = 0;
        this.items = [];

        return e;
      }
    },

    hasChangeFilterByItem(name) {
      const query = this.$route.query;

      if (query[name]) {
        return true;
      }

      return false;
    },

    sortTable(data) {
      let sortColumn = '';
      let sortBy = '';

      if (data.prop && data.order) {
        sortColumn = data.prop;

        if (data.order === 'ascending') {
          sortBy = 'ASC';
        } else if (data.order === 'descending') {
          sortBy = 'DESC';
        }
      }

      this.filter.sort_column = sortColumn;
      this.filter.sort_by = sortBy;

      this.$nextTick(() => {
        this.fetchInventoryForecast();
      });
    },

    importInventoryBackorder() {
      if (!this.employee) {
        this.notification('Please input employee', 'error');
        this.$refs.employeeCode.focus();

        return;
      }

      EventBus.$emit('showImportInventoryBackorder', this.employee.id);
    },

    tableCellClassName(data) {
      if (['action'].includes(data?.column?.property)) {
        return 'text-center';
      }
    },

    headerCellClassName(data) {
      if (['action'].includes(data?.column?.property)) {
        return 'text-center';
      }
    },

    showProductRankHistory(id, sku) {
      EventBus.$emit("showProductRankHistory", { id, sku });
    },
  },
};
