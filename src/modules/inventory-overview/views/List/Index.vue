<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left"><h1>{{$t('Inventory Overview')}}</h1></div>
    </div>
    <div class="table-content">
      <div class="filter">
        <div class="label">{{ $t('Filter by:') }}</div>
        <div class="filter-item">
          <el-dropdown
            ref="style"
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('style') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('style')">
                <el-tooltip effect="dark" :content="$t('Style')" placement="top-start">
                  <span>{{ filter.style }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(' Style ') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input
                  :placeholder="$t('Enter search keyword')"
                  class="search"
                  v-model="filter.style"
                  @keydown.enter="onFilter('style')"
                  size="small"
                  clearable
                  @clear="clearFilterItem('style')"
                />
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown
            ref="sku"
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('sku') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('sku')">
                <el-tooltip effect="dark" :content="$t('SKU')" placement="top-start">
                  <span>{{ filter.sku }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(' SKU ') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input
                  :placeholder="$t('Enter search keyword')"
                  class="search"
                  v-model="filter.sku"
                  @keydown.enter="onFilter('sku')"
                  size="small"
                  clearable
                  @clear="clearFilterItem('sku')"
                />
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown
            ref="gtin"
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('gtin') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('gtin')">
                <el-tooltip effect="dark" :content="$t('GTIN')" placement="top-start">
                  <span>{{ filter.gtin }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(' GTIN ') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input
                  :placeholder="$t('Enter search keyword')"
                  class="search"
                  v-model="filter.gtin"
                  @keydown.enter="onFilter('gtin')"
                  size="small"
                  clearable
                  @clear="clearFilterItem('gtin')"
                />
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown
            ref="brand_id"
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('brand_id') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('brand_id')">
                <el-tooltip effect="dark" :content="$t('Brand')" placement="top-start">
                  <span>{{ getBrandNameById(filter.brand_id) }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(' Brand ') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-select
                  filterable
                  v-model="filter.brand_id"
                  :placeholder="$t('Select brand')"
                  @change="onFilter('brand_id')"
                >
                  <el-option value="">{{ $t(' All ') }}</el-option>
                  <el-option
                    v-for="item in getBrands"
                    :key="item.id"
                    :label="item.name"
                    :value="String(item.id)"
                  >
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item" v-if="hasFilter">
          <el-link type="danger" @click="onClearFilter" :underline="false">
            {{ $t('Clear') }}
          </el-link>
        </div>
      </div>
      <table :key="index" v-for="(item, index) in items">
        <thead>
          <tr>
            <th></th>
            <th v-for="(size, indexS) in item.sizes" :key="indexS">
              {{ size }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(color, indexC) in item.colors" :key="indexC">
            <td>{{ color }}</td>
            <td v-for="(size, indexS1) in item.sizes" :key="indexS1">
              {{ color }}{{ size }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
