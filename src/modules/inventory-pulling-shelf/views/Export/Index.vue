<style src="./Style.scss" lang="scss"></style>
<script src="./Script.js"></script>
<template>
  <div class="export-overview">
    <el-dialog
      v-model="openDialogExport"
      :title="$t('Export Inventory Overview')"
      custom-class="el-dialog-custom el-dialog-box"
      width="35%"
      @close="onClose"
    >
      <template #default>
        <div class="w-full flex flex-col gap-2">
         <el-select
            filterable
            multiple
            :multiple-limit="10"
            v-model="styleSelecteds"
            :placeholder="$t('Select Product Styles')"
            :popper-append-to-body="openDropdownTag"
          >
            <el-option
              v-for="item in styles"
              :key="item.name"
              :label="item.name"
              :value="String(item.name)"
            >
              <span>{{ item.name }}</span>
            </el-option>
          </el-select>
          <span class="text-red-500 text-sm">Note: {{ $t('Maximum of 10 styles per export only') }}</span>
        </div>
      </template>
      <template #footer class="mt-3">
        <span class="dialog-footer">
          <el-button
            type="primary"
            :disabled="!styleSelecteds.length"
            @click="exportData()"
            >Export Inventory</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>