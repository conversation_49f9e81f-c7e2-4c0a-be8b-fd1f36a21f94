import EventBus from '@/utilities/eventBus.js';
import moment from 'moment';
import warehouseMixin from '@/mixins/warehouse';
import {
  report,
  reportPurchaseByVendor,
  reportSaleByCustomer,
  exportDataInventoryOverview,
} from '@/api/inventory';

import { fetchAll } from '@/api/productStyle';

export default {
  name: 'ExportInventory',
  mixins: [warehouseMixin],
  props: {
    filter: {
      style: '',
      sku: '',
      gtin: '',
      out_stock_pulling_quantity: '',
      out_stock_rack_quantity: '',
      sort_column: '',
      sort_by: '',
      location: '',
    }
  },
  components: {},
  data() {
    return {
      isLoading: false,
      openDialogExport: false,
      isExportActive: false,
      passwordExport: null,
      date: null,
      warehouseId: null,
      loadingExport: false,
      percentage: 0,
      fileExports: this.setDefaultExports(),
      typeExports: [
        {
          label: 'Inventory Report',
          value: 'inventory',
        },
        {
          label: 'Monthly purchase by style and vendor',
          value: 'purchase_by_vendor',
        },
        {
          label: 'Monthly sale by style and customer',
          value: 'purchase_by_customer',
        },
      ],
      typeExport: 'inventory',
      styles: [],
      styleSelecteds: [],
    };
  },
  computed: {},
  created() {
    EventBus.$on('showExportInventoryExport', () => {
      this.openDialogExport = true;
    });
  },
  mounted() {
    this.fetchAllStyles();
  },
  watch: {
    userWarehouseId: {
      handler() {
        this.warehouseId = this.userWarehouseId;
      },
      deep: true,
    },
  },
  beforeUnmount() {
    EventBus.$off('showExportInventoryExport');
  },
  methods: {
    setDefaultExports() {
      let result = [];
      const months = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
      ];
      let d = new Date();
      for (let i = 1; i <= 1; i++) {
        d.setDate(0);
        let dateString =
          d.getFullYear() +
          '-' +
          (d.getMonth() < 9 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) +
          '-' +
          d.getDate();
        console.log(dateString);

        result.push({
          label: months[d.getMonth()] + ', ' + d.getFullYear(),
          value: '2022-01-01-' + dateString + '-fifo-',
        });
      }
      return result;
    },
    async fetchAllStyles() {
      const response = await fetchAll();
      this.styles = response.data ?? [];
    },
    checkPasswordExport() {
      let d = new Date();
      let date = d.getDate();
      let month = d.getMonth() + 1;
      date = (month < 10 ? '0' + month : month.toString()) + (date < 10 ? '0' + date : date.toString());
      if (this.passwordExport.toUpperCase() == 'S' + date) {
        this.isExportActive = true;
      } else {
        console.log( 'S' + date);
        this.notification(this.$t('Password is incorrect!'), 'error');
      }
    },
    disabledDate(time) {
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      return (
        time.getTime() > yesterday.getTime() ||
        time.getTime() < new Date('2022-01-01 00:00:00').getTime()
      );
    },
    disabledMonth(time) {
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setMonth(yesterday.getMonth() - 1);
      return (
        time.getTime() > yesterday.getTime() ||
        time.getTime() < new Date('2022-01-01 00:00:00').getTime()
      );
    },
    onClose() {
      this.openDialogExport = false;
      this.isExportActive = false;
      this.loadingExport = false;
      this.passwordExport = null;
      this.styleSelecteds = [];
    },
    async exportData() {
      this.loadingExport = true;
      this.percentage = 0;
      let arrayStyles = [...this.styleSelecteds];
      let params = {
        warehouse_id: this.warehouseId,
        warehouse_code: this.userWarehouseCode,
        style: this.filter.style,
        sku: this.filter.sku,
        gtin: this.filter.gtin,
        out_stock_pulling_quantity: this.filter.out_stock_pulling_quantity,
        out_stock_rack_quantity: this.filter.out_stock_rack_quantity,
        sort_column: this.filter.sort_column,
        sort_by: this.filter.sort_by,
        location: this.filter.location,
        styles: arrayStyles,
      };
      const config = {
        responseType: 'arraybuffer',
        onDownloadProgress: (progressEvent) => {
          this.percentage = parseInt(
            Math.round((progressEvent.loaded / progressEvent.total) * 100)
          );
        },
      };
      try {
        const {data} = await exportDataInventoryOverview(params, config);


        console.log(data, 'data');

        const url = URL.createObjectURL(new Blob([data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }))
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `inventory-overview-${this.userWarehouseCode}.xlsx`);
        document.body.appendChild(link);
        link.click();
        window.URL.revokeObjectURL(url);
        link.remove();
      } catch (error) {
        console.log(error);
        this.notification('Export fail.', 'error');
      }
      this.loadingExport = false;
      return true;
    },
    changeType() {
      this.date = null;
    },
    async exportDataInventory() {
      this.loadingExport = true;
      this.percentage = 0;
      let endDate = moment(this.date).endOf('month').format('YYYY-MM-DD');
      let startDate = moment(this.date).startOf('month').format('YYYY-MM-DD');
      let params = {
        end_date: endDate,
        start_date: startDate,
        warehouse_id: this.warehouseId,
        warehouse_code: this.userWarehouseCode,
      };
      const config = {
        responseType: 'arraybuffer',
        onDownloadProgress: (progressEvent) => {
          this.percentage = parseInt(
            Math.round((progressEvent.loaded / progressEvent.total) * 100)
          );
        },
      };
      try {
        const response = await report(params, config);
        var blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
        var downloadElement = document.createElement('a');
        var href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        downloadElement.download = `${params.start_date}-${params.end_date}-fifo-${this.userWarehouseCode}.xlsx`;
        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);
      } catch (error) {
        this.notification('Export fail.', 'error');
      }

      this.loadingExport = false;
    },
    async exportDataPurchaseByVendor() {
      this.loadingExport = true;
      this.percentage = 0;
      let params = {
        start_date: moment(this.date[0]).format('YYYY-MM-DD'),
        end_date: moment(this.date[1]).format('YYYY-MM-DD'),
        warehouse_id: this.warehouseId,
        warehouse_code: this.userWarehouseCode,
      };
      const config = {
        responseType: 'arraybuffer',
        onDownloadProgress: (progressEvent) => {
          this.percentage = parseInt(
            Math.round((progressEvent.loaded / progressEvent.total) * 100)
          );
        },
      };
      try {
        const response = await reportPurchaseByVendor(params, config);
        var blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
        var downloadElement = document.createElement('a');
        var href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        downloadElement.download = `${params.start_date}-${params.end_date}-fifo-${this.userWarehouseCode}.xlsx`;
        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);
      } catch (error) {
        this.notification('Export fail.', 'error');
      }

      this.loadingExport = false;
    },
    async exportDataPurchaseByCustomer() {
      this.loadingExport = true;
      this.percentage = 0;
      let params = {
        start_date: moment(this.date[0]).format('YYYY-MM-DD'),
        end_date: moment(this.date[1]).format('YYYY-MM-DD'),
        warehouse_id: this.warehouseId,
        warehouse_code: this.userWarehouseCode,
      };
      const config = {
        responseType: 'arraybuffer',
        onDownloadProgress: (progressEvent) => {
          this.percentage = parseInt(
            Math.round((progressEvent.loaded / progressEvent.total) * 100)
          );
        },
      };
      try {
        const response = await reportSaleByCustomer(params, config);
        var blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
        var downloadElement = document.createElement('a');
        var href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        downloadElement.download = `${params.start_date}-${params.end_date}-fifo-${this.userWarehouseCode}.xlsx`;
        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);
      } catch (error) {
        this.notification('Export fail.', 'error');
      }

      this.loadingExport = false;
    },
  },
};
