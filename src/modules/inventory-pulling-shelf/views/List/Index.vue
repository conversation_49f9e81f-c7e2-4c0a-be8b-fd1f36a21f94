<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ $t('Inventory Overview') }}</h1>
      </div>
      <div class="top-head-right">
        <el-button type="primary" @click="exportFile">
          <span class="icon-margin-right">
            <icon :data="iconExport" />
          </span>{{ $t('Export') }}
        </el-button>
      </div>
    </div>

    <div class="table-content">
      <div class="statistical">
        <el-row :gutter="20" class="justify-center">
          <el-col :span="6" v-for="(item, index) in filteredStatistical" :key="index">
            <div class="shadow-sm border rounded p-3">
              <span class="label">{{ item.label }}: </span>
              <span class="value font-semibold" v-loading="isLoadingStatistical">
                {{ (dataStatistical[item.key] && formatNumber(dataStatistical[item.key])) || 0 }}
              </span>
              <br />
              <span class="label">Cost value: </span>
              <span class="value font-semibold" v-loading="isLoadingStatistical">
                ${{ (dataStatistical[item.cost_value_key] && formatNumber(dataStatistical[item.cost_value_key])) || 0 }}
              </span>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="filter">
        <div class="label">{{ $t('Filter by:') }}</div>
        <div class="filter-item">
          <el-dropdown ref="style" trigger="click" class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('style') }">
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('style')">
                <el-tooltip effect="dark" :content="$t('Style')" placement="top-start">
                  <span>{{ filter.style }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t('Style') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input :placeholder="$t('Enter search keyword')" class="search" v-model="filter.style"
                  @keydown.enter="onFilter('style')" clearable @clear="clearFilterItem('style')" />
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown ref="sku" trigger="click" class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('sku') }">
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('sku')">
                <el-tooltip effect="dark" :content="$t('SKU')" placement="top-start">
                  <span>{{ filter.sku }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t('SKU') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input :placeholder="$t('Enter search keyword')" class="search" v-model="filter.sku"
                  @keydown.enter="onFilter('sku')" clearable @clear="clearFilterItem('sku')" />
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown ref="gtin" trigger="click" class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('gtin') }">
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('gtin')">
                <el-tooltip effect="dark" :content="$t('GTIN')" placement="top-start">
                  <span>{{ filter.gtin }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t('GTIN') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input :placeholder="$t('Enter search keyword')" class="search" v-model="filter.gtin"
                  @keydown.enter="onFilter('gtin')" clearable @clear="clearFilterItem('gtin')" />
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown trigger="click" class="el-dropdown-filter-item">
            <span class="el-dropdown-link">
              {{ $t('SKU Rank') }}
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-select filterable v-model="filter.rank" :placeholder="$t('Select SKU Rank')" @change="onFilter">
                  <el-option v-for="item in ranks" :key="item.value" :label="item.label" :value="String(item.value)">
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown ref="location" trigger="click" class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('location') }">
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('location')">
                <el-tooltip effect="dark" :content="$t('Location')" placement="top-start">
                  <span>{{ filter.location }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t('Location') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input :placeholder="$t('Enter search keyword')" class="search" v-model="filter.location"
                  @keydown.enter="onFilter('location')" clearable @clear="clearFilterItem('location')" />
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown trigger="click" class="el-dropdown-filter-item">
            <span class="el-dropdown-link">
              {{ $t('Low Stock Pulling Shelves') }}
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-select filterable v-model="filter.out_stock_pulling_quantity"
                  :placeholder="$t('Select out of stock pulling shelves')" @change="onFilter">
                  <el-option v-for="item in outOfStock" :key="item.value" :label="item.label"
                    :value="String(item.value)">
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown trigger="click" class="el-dropdown-filter-item">
            <span class="el-dropdown-link">
              {{ $t('Low Stock Rack') }}
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-select filterable v-model="filter.out_stock_rack_quantity"
                  :placeholder="$t('Select out of stock rack')" @change="onFilter">
                  <el-option v-for="item in outOfStockRack" :key="item.value" :label="item.label"
                    :value="String(item.value)">
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown trigger="click" class="el-dropdown-filter-item">
            <span class="el-dropdown-link">
              {{ $t('Low Stock Dark Pods') }}
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-select filterable v-model="filter.out_stock_moving_quantity"
                  :placeholder="$t('Select out of stock Dark Pods')" @change="onFilter">
                  <el-option v-for="item in outOfStockMoving" :key="item.value" :label="item.label"
                    :value="String(item.value)">
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item" v-if="hasFilter">
          <el-link type="danger" @click="onClearFilter" :underline="false">
            {{ $t('Clear') }}
          </el-link>
        </div>
      </div>
      <el-table border stripe size="small" :data="items" style="width: 100%" :max-height="maxHeight"
        v-loading="isLoading" element-loading-text="Loading..." :cell-class-name="tableCellClassName"
        @sort-change="sortTable">
        <el-table-column fixed prop="sku" :label="$t('SKU')" min-width="130">
          <template #default="scope">
            {{ scope.row.sku }}
          </template>
        </el-table-column>
        <el-table-column prop="style" :label="$t('Style')" min-width="130">
          <template #default="scope">
            {{ scope.row.style }}
          </template>
        </el-table-column>
        <el-table-column prop="color" :label="$t('Color')" min-width="180">
          <template #default="scope">
            {{ scope.row.color }}
          </template>
        </el-table-column>
        <el-table-column prop="size" :label="$t('Size')" min-width="100">
          <template #default="scope">
            {{ scope.row.size }}
          </template>
        </el-table-column>
        <el-table-column prop="product_rank" min-width="100">
          <template #header>
            <el-tooltip placement="top-start">
              <template #content>
                Rank SKUs by number of units sold in the previous quarter<br />
                A - top 20%, B - next 20%, C - next 20%, D - next 20%, E - last 20%
              </template>
              <span> {{ $t('SKU Rank') }}</span>
            </el-tooltip>
          </template>
          <template #default="scope">
            <el-tag v-if="scope.row.product_rank.toLowerCase() == 'a'"
              @click="showProductRankHistory(scope.row.product_id, scope.row.sku)"
              style="color: white; cursor: pointer; background-color: #dc2626;">
              A
            </el-tag>
            <el-tag v-else-if="scope.row.product_rank.toLowerCase() == 'b'"
              @click="showProductRankHistory(scope.row.product_id, scope.row.sku)"
              style="color: white; cursor: pointer; background-color: #059669;">
              B
            </el-tag>
            <el-tag v-else-if="scope.row.product_rank.toLowerCase() == 'c'"
              @click="showProductRankHistory(scope.row.product_id, scope.row.sku)"
              style="color: white; cursor: pointer; background-color: #5583e4;">
              C
            </el-tag>
            <el-tag v-else-if="scope.row.product_rank.toLowerCase() == 'd'"
              @click="showProductRankHistory(scope.row.product_id, scope.row.sku)"
              style="color: white; cursor: pointer; background-color: #e4b255;">
              D
            </el-tag>
            <el-tag v-else-if="scope.row.product_rank.toLowerCase() == 'e'"
              @click="showProductRankHistory(scope.row.product_id, scope.row.sku)"
              style="color: white; cursor: pointer; background-color: #bb55e4;">
              E
            </el-tag>
            <el-tag v-else @click="showProductRankHistory(scope.row.product_id, scope.row.sku)"
              style="font-weight: bold; cursor: pointer; background-color: transparent; border: none; color: black; font-size: 20px;">
              -
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="gtin" :label="$t('GTIN')" min-width="160">
          <template #default="scope">
            {{ scope.row.gtin }}
          </template>
        </el-table-column>
        <el-table-column prop="incoming_stock" :label="$t('Incoming Stock')" min-width="120">
          <template #default="scope">
            {{ formatNumber(scope.row.incoming_stock) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="Blank Cost"
          :label="$t('Blank Cost')"
          min-width="80"
        >
          <template #default="scope">
            <span
              class="cursor-pointer blank-cost"
               @click="showHistoryBlankCostModal(scope.row)"
            >
              ${{ calculateBlankCostYear(scope.row) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="total_box" :label="$t('Box')" width="80">
          <template #default="scope">
            <router-link :to="{ name: 'box', query: { type: 'product_sku', keyword: scope.row.sku, limit: 25, } }">
              <el-link :underline="false" type="primary">{{ formatNumber(scope.row.total_box) || 0 }}
              </el-link>
            </router-link>
          </template>
        </el-table-column>
        <el-table-column :label="$t('Pulling shelves')" width="200">
          <el-table-column prop="pulling_quantity" sortable="custom" :label="$t('Qty')" width="120">
            <template #default="scope">
              {{ formatNumber(scope.row.pulling_quantity) || 0 }}
            </template>
          </el-table-column>
          <el-table-column prop="pulling_shelves_cost_value" sortable="custom" :label="$t('Cost Value')" width="140">
            <template #default="scope">
              ${{ formatNumberFloat(scope.row.pulling_shelves_cost_value) || 0 }}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column v-if="this.warehouse_id === 1" :label="$t('Dark Pods')" width="200">
          <el-table-column prop="moving_quantity" sortable="custom" :label="$t('Qty')" width="120">
            <template #default="scope">
              {{ formatNumber(scope.row.moving_quantity) || 0 }}
            </template>
          </el-table-column>
          <el-table-column prop="moving_cost_value" sortable="custom" :label="$t('Cost Value')" width="140">
            <template #default="scope">
              ${{ formatNumberFloat(scope.row.moving_cost_value) || 0 }}
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column :label="$t('Rack')" width="150">
          <el-table-column prop="rack_quantity" :label="$t('Qty')" sortable="custom" width="120">
            <template #default="scope">
              {{ formatNumber(scope.row.rack_quantity) || 0 }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('Cost Value')" prop="box_total_cost_value" sortable="custom" width="140">
            <template #default="scope">
              ${{ formatNumberFloat(scope.row.box_total_cost_value) || 0 }}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column prop="total" :label="$t('Total value')" min-width="100" v-if="this.warehouse_id === 1">
          <template #default="scope">
            ${{ formatNumberFloat(Number(scope.row.pulling_shelves_cost_value || 0) +
              Number(scope.row.box_total_cost_value || 0) + Number(scope.row.moving_cost_value || 0)) }}
          </template>
        </el-table-column>
        <el-table-column prop="total" :label="$t('Total value')" min-width="100" v-else>
          <template #default="scope">
            ${{ formatNumberFloat(Number(scope.row.pulling_shelves_cost_value || 0) +
              Number(scope.row.box_total_cost_value || 0)) }}
          </template>
        </el-table-column>

      </el-table>
      <div class="bottom">
        <div class="total">
          {{ $t('Total:') }} {{ items.length ? formatNumber(total) : 0 }}
        </div>
        <el-pagination :disabled="isLoading" background layout="prev, pager, next" :page-size="filter.limit"
          :total="total" @current-change="changePage" v-model:currentPage="filter.page">
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select v-model="filter.limit" :placeholder="$t('Select')" size="mini" @change="onFilter">
            <el-option v-for="item in limits" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </div>
      </div>
    </div>

    <ExportInventory :filter="filter" />
    <ProductRankHistory />
  </div>
  <HistoryBlankCostModal />
</template>
