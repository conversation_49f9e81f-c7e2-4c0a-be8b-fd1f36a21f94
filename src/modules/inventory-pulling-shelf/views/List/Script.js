import EventBus from "@/utilities/eventBus.js";
import { inventoryOverview, inventoryTotalOverview } from '@/api/inventory.js';
import filterMixin from '@/mixins/filter';
import { equals } from 'ramda';
import dateMixin from '@/mixins/date.js';
import formatNumberMixin from '@/mixins/formatNumber.js';
import ExportInventory from "@/modules/inventory-pulling-shelf/views/Export/Index.vue";
import ProductRankHistory from "@/modules/inventory-pulling-shelf/views/ProductRankHistory/Index.vue";
import warehouseMixin from '@/mixins/warehouse';
import HistoryBlankCostModal from "@/modules/product/components/HistoryBlankCostModal.vue";

export default {
  name: 'InventoryPullingShelf',

  mixins: [
    filterMixin,
    dateMixin,
    formatNumberMixin,
    warehouseMixin,
  ],

  components: {
    ExportInventory,
    HistoryBlankCostModal,
    ProductRankHistory,
  },

  data() {
    return {
      items: [],
      isLoading: false,
      isLoadingStatistical: false,
      filter: this.setDefaultFilter(),
      filterProductRankHistory: {
        id: "",
        sku: "",
      },
      warehouse_id: null,
      outOfStock: [
        {
          label: this.$t('All'),
          value: 0,
        },
        {
          label: this.$t('Low Stock Pulling Shelves'),
          value: 1,
        },
      ],
      outOfStockRack: [
        {
          label: this.$t('All'),
          value: 0,
        },
        {
          label: this.$t('Low Stock Rack'),
          value: 1,
        },
      ],
      outOfStockMoving: [
        {
          label: this.$t('All'),
          value: 0,
        },
        {
          label: this.$t('Low Stock Dark Pods'),
          value: 1,
        },
      ],
      ranks: [
        {
          label: this.$t('All'),
          value: 0,
        },
        {
          label: this.$t('A'),
          value: "A",
        },
        {
          label: this.$t('B'),
          value: "B",
        },
        {
          label: this.$t('C'),
          value: "C",
        },
        {
          label: this.$t('D'),
          value: "D",
        },
        {
          label: this.$t('E'),
          value: "E",
        },
        {
          label: this.$t('-'),
          value: "-",
        },
      ],
      statistical: [
        {
          label: this.$t('Total Pulling Shelves'),
          key: 'total_pulling',
          cost_value_key: 'total_pulling_shelves_cost_value',
        },
        {
          label: this.$t('Total Dark Pods'),
          key: 'total_moving',
          cost_value_key: 'total_moving_shelves_cost_value',
        },
        {
          label: this.$t('Total Rack'),
          key: 'total_rack',
          cost_value_key: 'total_box_cost_value',
        },
        {
          label: this.$t('Total Quantity'),
          key: 'total_qty',
          cost_value_key: 'total_warehouse_cost_value',
        },
      ],
      dataStatistical: {},
    };
  },

  computed: {
    filteredStatistical() {
      // Exclude "Total Shelves Face" when warehouse_id is not 1
      return this.warehouse_id === 1 ? this.statistical : this.statistical.filter(item => item.key !== 'total_moving');
    },

    maxHeight() {
      return parseInt(window.innerHeight - 305);
    },

    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },
  },

  beforeUnmount() {
    EventBus.$off("showExportInventoryExport");
    EventBus.$off("showProductRankHistory");
  },

  mounted() {
    this.warehouse_id = this.userWarehouseId
    this.fetchData();
  },

  methods: {
    setDefaultDate() {
      const today = new Date();
      const priorDate = new Date(new Date().setDate(today.getDate() - 30));

      return ((today && priorDate && [this.formatDate(priorDate, false), this.formatDate(today, false),]) || '');
    },

    onFilter(item = '') {
      this.filter.page = 1;
      this.$nextTick(() => {
        this.fetchInventoryOverview();
        this.fetchInventoryTotalOverview();

        if (item) {
          this.$refs[item].handleClose();
        }
      });
    },

    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.$nextTick(() => {
        this.fetchInventoryOverview();
        this.fetchInventoryTotalOverview();
      });
    },

    onChangeDate() {
      if (this.filter.date && this.filter.date.length) {
        this.filter.date[0] = this.formatDate(this.filter.date[0], false);
        this.filter.date[1] = this.formatDate(this.filter.date[1], false);
        this.onFilter();
      }
    },

    clearFilterItem(item) {
      this.filter[item] = '';
      this.$refs[item].handleClose();
      this.onFilter();
    },

    setDefaultFilter() {
      let params = {
        limit: 25,
        page: 1,
        style: '',
        sku: '',
        gtin: '',
        rank: '',
        out_stock_pulling_quantity: '0',
        out_stock_rack_quantity: '0',
        out_stock_moving_quantity: '0',
        sort_column: '',
        sort_by: '',
        location: '',
      };

      return params;
    },

    setRouteParam() {
      const params = this.filter;
      this.$router.replace({
        name: 'inventory_pulling_shelves',
        query: params,
      });
    },

    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);

      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }

      filter.page = +filter.page || 1;

      return filter;
    },

    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchInventoryOverview();
        this.fetchInventoryTotalOverview();
      });
    },

    async fetchData() {
      this.filter = this.getRouteParam();
      this.fetchInventoryOverview();
      this.fetchInventoryTotalOverview();
    },

    async fetchInventoryTotalOverview() {
      this.isLoadingStatistical = true;
      const res = await inventoryTotalOverview(this.filter);
      let data = res.data || {};

      if (Object.keys(data).length) {
        const totalPulling = (data['total_pulling'] && +data['total_pulling']) || 0;
        const totalRack = (data['total_rack'] && +data['total_rack']) || 0; data['total_qty'] = totalPulling + totalRack;

        if (this.warehouse_id === 1) {
          const totalMoving = (data['total_moving'] && +data['total_moving']) || 0; data['total_qty'] += totalMoving;
          const totalCostMoving = (data['total_moving_shelves_cost_value'] && +data['total_moving_shelves_cost_value']) || 0;
          const totalCostBox = (data['total_box_cost_value'] && +data['total_box_cost_value']) || 0;
          const totalCostPulling = (data['total_pulling_shelves_cost_value'] && +data['total_pulling_shelves_cost_value']) || 0;
          data['total_warehouse_cost_value'] = parseFloat((totalCostMoving + totalCostBox + totalCostPulling).toFixed(2));
        }
      }

      this.dataStatistical = data;
      this.isLoadingStatistical = false;
    },

    async fetchInventoryOverview() {
      this.isLoading = true;
      this.setRouteParam();
      const res = await inventoryOverview(this.filter);
      this.isLoading = false;
      const data = res.data || [];
      this.total = data.total;
      this.items = data.data;
    },

    hasChangeFilterByItem(name) {
      const query = this.$route.query;

      if (query[name]) {
        return true;
      }

      return false;
    },

    sortTable(data) {
      let sortColumn = '';
      let sortBy = '';

      if (data.prop && data.order) {
        sortColumn = data.prop;
        if (data.order === 'ascending') {
          sortBy = 'ASC';
        } else if (data.order === 'descending') {
          sortBy = 'DESC';
        }
      }

      this.filter.sort_column = sortColumn;
      this.filter.sort_by = sortBy;
      this.$nextTick(() => {
        this.fetchInventoryOverview();
        this.fetchInventoryTotalOverview();
      });
    },

    tableRowClassName(data) {
      return data.row.percent_quantity && parseInt(data.row.percent_quantity) <= 25 ? 'is-warning' : '';
    },

    tableCellClassName(data) {
      if (data.column && data.column.property && data.column.property == 'pulling_quantity') {
        if (data.row && ((data.row.percent_quantity && data.row.percent_quantity <= 25) || !data.row.percent_quantity)) {
          return 'is-warning';
        }
      }

      if (data.column && data.column.property && data.column.property == 'moving_quantity') {
        if (data.row && ((data.row.moving_percent_quantity && data.row.moving_percent_quantity <= 25) || !data.row.moving_percent_quantity)) {
          return 'is-warning';
        }
      }

      if (data.column && data.column.property && data.column.property == 'rack_quantity') {
        if (data.row && ((data.row.total_box && data.row.total_box < 5) || !data.row.total_box)) {
          return 'is-warning';
        }
      }
    },

    exportFile() {
      EventBus.$emit("showExportInventoryExport");
    },
    showHistoryBlankCostModal(item) {
      const data = {
        product_id: item.product_id,
        current_blank_cost: this.calculateBlankCostYear(item)
      };

      EventBus.$emit("showProductHistoryBlankCostModal", data);
    },
    calculateBlankCostYear(product) {
      const endUnitLastYear = Number(product?.end_unit || 0);
      const blankCostLastYear = endUnitLastYear > 0 ? Number(product?.blank_cost_last_year || 0) : 0;

      const totalPrice = Number(product?.total_price || 0) + blankCostLastYear;
      const totalPo = endUnitLastYear > 0 ? (Number(product?.total_po || 0) + 1) : Number(product?.total_po || 0);

      if (totalPo !== 0) {
        return this.formatNumberFloat(totalPrice / totalPo);
      }

      return this.formatNumberFloat(Number(product?.blank_cost_last_year || 0));
    },
    showProductRankHistory(id, sku) {
      EventBus.$emit("showProductRankHistory", {id, sku});
    },
  },
};
