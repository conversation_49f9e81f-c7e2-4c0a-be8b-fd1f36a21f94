<style src="./Style.scss" lang="scss"></style>
<script src="./Script.js"></script>
<template>
  <div class="export-overview">

    <el-dialog v-model="openDialogProductRankHistory" :title="`SKU RANK HISTORY: ${sku}`"
      custom-class="el-dialog-custom el-dialog-box" @close="onClose">
      <template #default>
        <el-scrollbar :style="data.length > 1 ? { height: '390px' } : ''">
          <el-table v-if="isLoading" v-loading="true" class="h-full"></el-table>
          <div v-else class="w-full px-10 py-2 h-full">
            <el-collapse v-model="activeNames" @change="handleChange">
              <el-collapse-item v-for="(item, index) in data" :title="item?.year" :name="index" :key="index">
                <el-table border size="small" :data="item?.data || []" style="width: 100%">
                  <el-table-column :label="$t('Timestamp')" min-width="130">
                    <template #default="scope">
                      {{ `Q${scope.row.quarter}/${scope.row.year}` }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="style" :label="$t('Total Revenue')" min-width="130">
                    <template #default="scope">
                      <span v-if="scope.row.total_revenue">
                        {{ `$${Number(scope.row.total_revenue).toLocaleString()}` }}
                      </span>
                      <span v-else>-</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="color" :label="$t('Total Units Sold')" min-width="180">
                    <template #default="scope">
                      <span v-if="scope.row.total_unit_sold">
                        {{ Number(scope.row.total_unit_sold).toLocaleString() }}
                      </span>
                      <span v-else>-</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="size" :label="$t('Rank')" min-width="100">
                    <template #default="scope">
                      <span v-if="scope.row.rank">
                        {{ scope.row.rank }}
                      </span>
                      <span v-else>-</span>
                    </template>
                  </el-table-column>
                </el-table>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-scrollbar>
      </template>
    </el-dialog>
  </div>
</template>