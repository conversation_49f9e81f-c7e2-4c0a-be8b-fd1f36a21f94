import EventBus from '@/utilities/eventBus.js';
import { getDetail } from '@/api/productRankHistory';

export default {
  name: 'ProductRankHistory',

  data() {
    return {
      isLoading: false,
      sku: "",
      openDialogProductRankHistory: false,
      activeNames: [0, 1],
      data: [],
    };
  },

  created() {
    EventBus.$on('showProductRankHistory', ({ id, sku }) => {
      this.openDialogProductRankHistory = true;
      this.sku = sku;
      this.getDetail(id)
    });
  },

  watch: {
    userWarehouseId: {
      handler() {
        this.warehouseId = this.userWarehouseId;
      },
      deep: true,
    },
  },

  beforeUnmount() {
    EventBus.$off('showProductRankHistory');
  },

  methods: {
    async getDetail(id) {
      this.isLoading = true;

      try {
        const response = await getDetail(id);
        this.data = response.data ?? [];
      } catch (error) {
        console.log(error);
      }

      this.isLoading = false;
    },

    onClose() {
      this.openDialogProductRankHistory = false;
      this.isLoading = true;
      this.data = [];
      this.activeNames = [0, 1];
    },
  },
};
