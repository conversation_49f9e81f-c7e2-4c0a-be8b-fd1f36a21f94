<template>


  <div class="flex justify-between">
    <div class="filter mb-3 flex justify-between">
      <div class="flex">
        <el-input class="!w-[300px]" v-model="filter.keyword" @keyup.enter="fetchBypass" placeholder="Enter search username or email"></el-input>
        <el-button class="!ml-3 !bg-blue" type="primary" @click="clearFilters">Clear</el-button>
      </div>


    </div>
    <el-button class="float-right" type="primary" @click="openUserModal" style="margin-bottom: 15px;">
    <span class="icon-margin-right">
      <icon :data="iconAdd" />
    </span>{{ $t('Add Bypass User') }}
    </el-button>
  </div>

  <el-dialog :title="$t('Bypass User')" v-model="isUserModalOpen">
    <el-input v-model="searchQuery" :placeholder="$t('Search name, email')" clearable class="mb-3" />

    <el-table :data="filteredUsers" stripe size="small" border style="width: 100%" max-height="400">
      <el-table-column width="50">
        <template #default="scope">
          <el-checkbox v-model="scope.row.selected"></el-checkbox>
        </template>
      </el-table-column>

      <el-table-column :label="$t('Username')" prop="username" width="150"></el-table-column>
      <el-table-column :label="$t('Email')" min-width="150" prop="email"></el-table-column>

      <el-table-column :label="$t('Role')" min-width="150">
        <template #default="scope">
          {{ scope.row.is_admin === 1 ? 'Admin' : scope.row.roles?.[0]?.name || '-' }}
        </template>
      </el-table-column>

      <el-table-column prop="department" :label="$t('Department')" width="150">
        <template #default="scope">
          {{ scope.row.department?.name || '-' }}
        </template>
      </el-table-column>
    </el-table>

    <div slot="footer" class="dialog-footer float-right mt-4">
      <el-button @click="isUserModalOpen = false">{{ $t('Cancel') }}</el-button>
      <el-button type="primary" @click="addSelectedUsers">Save</el-button>
    </div>
  </el-dialog>

  <div>
    <el-table stripe size="small" border :data="listBypass" style="width: 100%" :max-height="maxHeight" class="mb-3"
      v-loading="isLoading" element-loading-text="Loading...">
      <el-table-column :label="$t('User')" min-width="150">
        <template #default="scope">
          {{ scope.row.username }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('Email')" min-width="200">
        <template #default="scope">
          {{ scope.row.email }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('Role')" min-width="150">
        <template #default="scope">
          {{ scope.row.is_admin === 1 ? 'Admin' : scope.row.roles?.[0]?.name || '-' }}
        </template>
      </el-table-column>

      <el-table-column prop="department" :label="$t('Department')" width="150">
        <template #default="scope">
          {{ scope.row.department?.name || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="action" :label="$t('Action')" width="150" align="center">
        <template #default="scope">
          <el-link class="el-link-delete" :underline="false" type="danger" @click="deleteRecord(scope.row)">
            <icon :data="iconDelete" />
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <div class="bottom flex justify-between">
      <div class="total">
        {{ $t('Total:') }} {{ total ? formatNumber(total) : 0 }}
      </div>
      <el-pagination :disabled="isLoading" background layout="prev, pager, next" :page-size="filter.limit"
                     :total="total" @current-change="changePage" v-model:currentPage="filter.page">
      </el-pagination>
      <div class="limit" :disabled="isLoading">
        <el-select v-model="filter.limit" :placeholder="$t('Select')" size="mini" @change="fetchBypass">
          <el-option v-for="item in limits" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </div>
    </div>

  </div>
  <createUser />
  <updateUser />

</template>

<script>

import { listBypass, disableBypass, addBypassUsers } from "@/api/ipManagement.js";
import { listAll } from "@/api/user.js";
import numberMixin from "@/mixins/formatNumber";

export default {
  name: "BypassUserTab",
  mixins: [numberMixin],
  components: {
  },
  data() {
    return {
      total: 0,
      listBypass: [], // Current bypass users
      listSettingUser: [], // All available users for selection
      isLoading: false,
      isUserModalOpen: false,
      selectedUsers: [],
      searchQuery: "",
      filter: {
        page: 1,
        limit: 10,
        keyword: "",
      },
      limits: [
        {
          label: "Show 10 items",
          value: 10,
        },
        {
          label: "Show 25 items",
          value: 25,
        },
        {
          label: "Show 50 items",
          value: 50,
        },
        {
          label: "Show 100 items",
          value: 100,
        },
      ],

    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 230);
    },
    filteredUsers() {
      return this.listSettingUser
        .filter(user =>
          user.username.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
          user.email.toLowerCase().includes(this.searchQuery.toLowerCase())
        )
        .filter(user => !this.listBypass.some(existingUser => existingUser.email === user.email));
    }

  },
  methods: {
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchBypass();
      });
    },
    async addSelectedUsers() {
      try {
        const userIds = this.listSettingUser
          .filter(user => user.selected)
          .map(user => user.id);

        if (userIds.length === 0) {
          this.$message.warning(this.$t("No users selected."));
          return;
        }

        console.log("Adding Users:", userIds);

        const res = await addBypassUsers({ id: userIds });
        this.fetchBypass();
        this.$message.success(this.$t("Users added successfully!"));
      } catch (error) {
        console.error("Error adding users:", error);
        this.$message.error(this.$t("An error occurred while adding users."));
      } finally {
        this.isUserModalOpen = false;
      }
    },
    async fetchBypass() {
      this.isLoading = true;
      const res = await listBypass(this.filter);
      const dataUser = res.data.data || [];
      this.listBypass = dataUser;
      this.total = res.data.total
      this.isLoading = false;
    },
    async getAllUsers() {
      const res = await listAll();
      this.listSettingUser = res.data.map(user => ({
        ...user,
        selected: false // Ensure all users start unselected
      }));
    },
    async deleteRecord(row) {
      try {
        this.$confirm(this.$t('Are you sure you want to delete this ?'), this.$t('Warning'), {
          confirmButtonText: this.$t('Yes'),
          cancelButtonText: this.$t('No'),
          type: 'warning'
        }).then(async () => {
          const res = await disableBypass(row.id);
          this.$emit('recordDeleted', res.data);
          this.notification(res.data.message, 'success');
          this.fetchBypass();
        }).catch(() => {
          // Handle cancel action if user presses 'No'
        });
      } catch (error) {
        this.notification('Error deleting record, please try again.', 'error');
      }
    }, openUserModal() {
      this.isUserModalOpen = true;
    },
    clearFilters() {
      this.filter = this.setDefaultFilter();
      this.fetchBypass();
    },
    setDefaultFilter() {
      return {
        page: 1,
        limit: 10,
        keyword: "",
      };
    }
  },
  mounted() {
    this.fetchBypass();
    this.getAllUsers();
  }
};
</script>