<script src="./script.js"></script>
<template>
    <el-dialog v-model="dialogVisible" destroy-on-close :title="$t('Update User')" custom-class="el-dialog-custom" :destroy-on-close="true">
        <el-form status-icon ref="updateUserForm" :model="data" @submit.prevent="onSubmit" :label-position="right" label-width="150px" class="mt-4">
            <el-form-item :label="$t('Username')" :class="{'is-error': isError('username')}" required>
                <el-input v-model="data.username" @keyup.enter="onSubmit"></el-input>
                <div v-if="isError('username')" class="el-form-item__error">{{ getErrorMessage('username') }}</div>
            </el-form-item>
            <el-form-item :label="$t('Email')" :class="{'is-error': isError('email')}" required>
                <el-input v-model="data.email" @keyup.enter="onSubmit"></el-input>
                <div v-if="isError('email')" class="el-form-item__error">{{ getErrorMessage('email') }}</div>
            </el-form-item>
            <el-form-item :label="$t('Password')" :class="{'is-error': isError('password')}">
                <el-input v-model="data.password" type="password" @keyup.enter="onSubmit"></el-input>
                <div v-if="isError('password')" class="el-form-item__error">{{ getErrorMessage('password') }}</div>
            </el-form-item>
        </el-form>

        <template #footer>
            <el-button type="primary" @click="onSubmit" :disabled="isLoading" :loading="isLoading">{{ $t('Submit') }}</el-button>
        </template>
    </el-dialog>
</template>
