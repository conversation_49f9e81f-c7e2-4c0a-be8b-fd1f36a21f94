import EventBus from '@/utilities/eventBus';
import { createUser } from '@/api/ipManagement';

export default {
  name: 'createUser',
  data() {
    return {
      dialogVisible: false,
      data: this.setDefaultData(),
      serverErrors: [],
      isLoading: false,
      disable: false,
    };
  },
  created() {
    EventBus.$on('createUser', () => {
      this.data = this.setDefaultData(),
      this.dialogVisible = true;
    });
  },
  methods: {
    isError(field) {
      return !!this.serverErrors[field];
    },
    getErrorMessage(field) {
      return this.serverErrors[field] ? this.serverErrors[field][0] : '';
    },
    setDefaultData() {
      return {
        username: '',
        email: '',
      };
    },
    async onSubmit() {
      if (this.isLoading) return;
      this.isLoading = true;
      try {
        const payload = {
          id: this.data.id,
          username: this.data.username,
          email: this.data.email,
          password: this.data.password ?? null,
        };
        if (this.data.password) {
          payload.password = this.data.password;
        }
        const res = await createUser(payload);
        EventBus.$emit('createUser', res.data);
        this.dialogVisible = false;
        this.notification(res.data.message);
        window.location.reload();
      } catch (e) {
        this.serverErrors = e.response.data;
        this.notification(this.$t("Invalid data, please check again."), "error");
        this.isLoading = false;
  } finally {
        this.isLoading = false;
      }
    },
  },
};
