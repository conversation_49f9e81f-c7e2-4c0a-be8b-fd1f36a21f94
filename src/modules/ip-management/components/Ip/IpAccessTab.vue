<template>
  <div>
    <div class="filter mb-3 flex justify-between">
      <div class="flex">
        <el-select v-model="filter.country" :placeholder="$t('Select country')" size="mini" @change="onFilter('country')">
          <el-option v-for="item in country" :key="item.country" :label="item.country" :value="item.country"></el-option>
        </el-select>

        <el-select class="ml-4" v-model="filter.user" :placeholder="$t('Select user')" size="mini" @change="onFilter('user')">
          <el-option v-for="item in user" :key="item.id" :label="item.username" :value="item.id"></el-option>
        </el-select>

        <el-select class="ml-4" v-model="filter.is_allowed" :placeholder="$t('Status')" size="mini" @change="onFilter('approve')">
          <el-option v-for="item in allowed" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>

        <el-button class="!ml-3 !bg-blue" type="primary" @click="clearFilters">Clear</el-button>
      </div>

      <div class="flex ml-auto">
        <div class="flex items-center space-x-2 mr-3">
          <span>Restrict IP Access</span>
          <el-switch v-model="status" @change="openConfirmDialog"></el-switch>
        </div>

        <el-dialog title="Confirmation" :width="350" :model-value="isConfirmDialogVisible" @close="cancelUpdateStatus">
          <span>{{ $t('Are you sure you want to update this?') }}</span>
          <template #footer>
            <el-button @click="cancelUpdateStatus">{{ $t('Cancel') }}</el-button>
            <el-button type="primary" @click="confirmUpdateStatus">{{ $t('Confirm') }}</el-button>
          </template>
        </el-dialog>

        <el-button class="ml-4" type="success" @click="openCreateDialog">Create IP Address</el-button>
      </div>
    </div>

    <el-table stripe size="small" border :data="items" style="width: 100%" :max-height="maxHeight" v-loading="isLoading" element-loading-text="Loading..."
    :cell-class-name="tableCellClassName">

    >
      <el-table-column class="text-center" :label="$t('Status')" width="60">
        <template #default="scope">
          <el-switch v-model="scope.row.is_allowed" :active-value="1" :inactive-value="0"
            style="--el-switch-on-color: #13ce66" @change="updateStatus(scope.row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Ip Address')" min-width="200">
        <template #default="scope">
          {{ scope.row.ip_address }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('Country')" >
        <template #default="scope">
          {{ scope.row.country }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('User')">
        <template #default="scope">
          {{ scope.row.username }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('Last active at')" min-width="200">
        <template #default="scope">
          <span v-if="scope.row.last_active_at">{{ formatTime(scope.row.last_active_at, 'llll')  }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Expires at')" min-width="150">
        <template #default="{ row }">
          <span v-if="row.expired_at">{{ formattedCreatedAt(row.expired_at) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Created at')" min-width="150">
        <template #default="scope">
          {{ formatTime(scope.row.created_at, 'llll') }}

        </template>
      </el-table-column>
      <el-table-column :label="$t('Note')" min-width="100">
        <template #default="scope">
          {{ scope.row.note }}
        </template>
      </el-table-column>
      <el-table-column prop="action" :label="$t('Action')" width="150" align="center">
        <template #default="scope">
          <div class="flex justify-between px-8">
            <el-link class="el-link-edit" :underline="false" type="primary" @click="update(scope.row)">
              <icon :data="iconEdit" />
            </el-link>
            <el-link class="el-link-delete" :underline="false" type="danger" @click="deleteRecord(scope.row)">
              <icon :data="iconDelete" />
            </el-link>
          </div>
        </template>
      </el-table-column>

    </el-table>

    <div class="bottom">
      <div class="total">
        {{ $t('Total:') }} {{ items.length ? formatNumber(total) : 0 }}
      </div>
      <el-pagination :disabled="isLoading" background layout="prev, pager, next" :page-size="filter.limit"
        :total="total" @current-change="changePage" v-model:currentPage="filter.page"></el-pagination>
    </div>
  </div>

  <!-- Create IP Address Modal -->
  <el-dialog title="Create IP Address" v-model="createIpDialog" width="30%">
    <el-form :model="newIp" label-width="120px" ref="ipForm" :rules="rules">
      <el-form-item label="IP Address" prop="ip_address">
        <el-input v-model="newIp.ip_address" placeholder="Enter IP Address"></el-input>
      </el-form-item>
      <el-form-item label="Expired At" prop="expired_at">
        <el-date-picker v-model="newIp.expired_at" type="datetime" format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss" placeholder="Select Expiration Date"></el-date-picker>
      </el-form-item>
      <el-form-item label="Note" prop="note">
        <el-input v-model="newIp.note" placeholder="Enter Note"></el-input>
      </el-form-item>
      <el-form-item :label="$t('Status')">
        <el-switch v-model="newIp.is_active" :active-value="1" :inactive-value="0"
          style="--el-switch-on-color: #13ce66"></el-switch>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="createIpDialog = false">Cancel</el-button>
      <el-button type="primary" @click="createIp">Create</el-button>
    </template>
  </el-dialog>
  <update />
</template>

<script>
import { list, filter, createIp, updateIp, destroyIp, approve, getIpRestriction, updateIpRestriction} from "@/api/ipManagement.js";
import update from "@/modules/ip-management/components/Ip/Update/Index.vue";
import EventBus from '@/utilities/eventBus';
import moment from 'moment';

export default {
  name: "IpAccessTab",
  components: {
    update
  },
  data() {
    return {
      createIpDialog: false,
      allowed: [
        { label: 'Approved', value: 'allowed' },
        { label: 'Not Approved', value: 'disallowed' }
      ],
      approved: [
        { label: 'Reviewed', value: 'reviewed' },
        { label: 'In Review', value: 'unreview' }
      ],
      items: [],
      country: [],
      user: [],
      isLoading: false,
      date: '',
      filter: this.setDefaultFilter(),
      total: 0,
      newIp: {
        ip_address: "",
        is_active: 1,
        expired_at: "",
        note: "",
      },
      rules: {
        ip_address: [{ required: true, message: 'IP Address is required', trigger: 'blur' }],
        status: [{ required: true, message: 'Status is required', trigger: 'change' }],
        id: [{ required: true, message: 'ID is required', trigger: 'blur' }],
      },
      isConfirmDialogVisible: false,
      status: false,
      originalStatus: false,
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 230);
    }
  },
  methods: {
    formattedCreatedAt(date) {
      return moment(date).format('lll');
    },

    openCreateDialog() {
      this.createIpDialog = true;
    },
    setDefaultFilter() {
      return {
        limit: 25,
        page: 1,
        user: '',
        country: '',
        is_allowed: '',
        is_reviewed: '',
        start_date: '',
        end_date: ''
      };
    },
    async fetchData() {
      const res = await list(this.filter);
      const resFilter = await filter({ start_date: this.filter.start_date, end_date: this.filter.end_date });
      this.isLoading = false;
      const data = res.data || [];
      this.items = data.data;
      this.total = data.total;
      this.country = resFilter.data.country || [];
      this.user = resFilter.data.user || [];
      console.log(this.user);
    },
    changePage(page) {
      this.filter.page = page;
      this.fetchData();
    },
    onFilter() {
      this.filter.page = 1;
      this.fetchData();
    },
    clearFilters() {
      this.filter = this.setDefaultFilter();
      this.fetchData();
    },
    onChangeDate() {
      if (this.date && this.date.length) {
        const startDate = new Date(this.date[0]);
        this.filter.start_date = startDate.getFullYear() + '-' + ('0' + (startDate.getMonth() + 1)).slice(-2) + '-' + ('0' + startDate.getDate()).slice(-2);

        // Format end_date
        const endDate = new Date(this.date[1]);
        this.filter.end_date = endDate.getFullYear() + '-' + ('0' + (endDate.getMonth() + 1)).slice(-2) + '-' + ('0' + endDate.getDate()).slice(-2);
      } else {
        this.filter.start_date = '';
        this.filter.end_date = '';
      }

      this.onFilter();
    },
    setDefaultDates() {
      const currentDate = new Date();
      const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
      const lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);

      const startDate = `${firstDayOfMonth.getFullYear()}-${(firstDayOfMonth.getMonth() + 1).toString().padStart(2, '0')}-${firstDayOfMonth.getDate().toString().padStart(2, '0')}`;
      const endDate = `${lastDayOfMonth.getFullYear()}-${(lastDayOfMonth.getMonth() + 1).toString().padStart(2, '0')}-${lastDayOfMonth.getDate().toString().padStart(2, '0')}`;

      this.date = [startDate, endDate];
      this.filter.start_date = '';
      this.filter.end_date = '';
    },
    async createIp() {
      if (!this.newIp.ip_address) {
        this.$message.error("IP Address is required");
        return;
      }
      try {
        this.isLoading = true;
        await createIp(this.newIp);
        this.$message.success("IP Address created successfully");
        this.createIpDialog = false;
        this.fetchData();
      } catch (error) {
        this.isLoading = false;

        if (error.response && error.response.data && error.response.data.errors) {
          const errors = error.response.data.errors;

          if (errors.expired_at) {
            this.$message.error(errors.expired_at.join(' '));
          } else {
            this.$message.error("Failed to create IP Address");
          }
        } else {
          this.$message.error("Failed to create IP Address");
        }

        this.isLoading = false;
      }
    },
    update(item) {
      EventBus.$emit("updateIp", item);
    },
    async updateStatus(row) {
      try {
        const res = await approve({ id: row.id, is_allowed: row.is_allowed });
        console.log("Record status updated successfully", res);
      } catch (error) {
        console.error("Failed to update record status", error);
      }
    },
    async getStatusIpRestriction() {
      this.isLoading = true;
      const { data } = await getIpRestriction();
      if (data.status === '1') {
        this.status = true;
      } else {
        this.status = false;
      }
      this.isLoading = false;
    },

    async deleteRecord(row) {
      try {
        this.$confirm(this.$t('Are you sure you want to delete this IP address?'), this.$t('Warning'), {
          confirmButtonText: this.$t('Yes'),
          cancelButtonText: this.$t('No'),
          type: 'warning'
        }).then(async () => {
          const res = await destroyIp(row.id); 
          this.$emit('recordDeleted', res.data); 
          this.notification(res.data.message, 'success'); 
          this.fetchData();
        }).catch(() => {
          // Handle cancel action if user presses 'No'
        });
      } catch (error) {
        this.notification('Error deleting record, please try again.', 'error');
      }
    },
    openConfirmDialog() {
      if (this.status === true) {
        this.originalStatus = false;
      } else {
        this.originalStatus = true;
      }
      this.isConfirmDialogVisible = true; // Hiển thị dialog
    },
    cancelUpdateStatus() {
        this.isConfirmDialogVisible = false; // Đóng dialog
        this.status = this.originalStatus; // Khôi phục trạng thái
    },
    confirmUpdateStatus() {
        this.isConfirmDialogVisible = false;
        this.updateStatusIpRestriction(); // Thực hiện cập nhật trạng thái
    },
    async updateStatusIpRestriction() {
      try {
        const params = {
          status: this.status,
        };
        const { data } = await updateIpRestriction(params);
        this.status = params.status;
        this.isConfirmDialogVisible = false;
        this.notification(this.$t('Update successfully.'));
      } catch (error) {
        this.notification('Invalid data', "error");
      }
    },
    tableCellClassName({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 5) {
        let timeQc = this.calculateTimeDifferent(row, true);
        return timeQc >= 24 ? '!bg-red-200' : '';
      }
    },
    calculateTimeDifferent(row, getTableClass = false) {
      const timezone = 'America/Los_Angeles'; // PST/PDT depending on daylight saving

      const start = moment.tz(row.expired_at, timezone);
      const end = moment.tz(timezone); // current time in PST

      const duration = moment.duration(end.diff(start));
      if (getTableClass) {
        return Math.floor(duration.asMinutes());
      }
    }

  },
  mounted() {
    this.fetchData();
    this.setDefaultDates();
    this.getStatusIpRestriction();
  }
};
</script>