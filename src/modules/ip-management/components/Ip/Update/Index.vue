<script src="./script.js"></script>
<template>
  <el-dialog v-model="dialogVisible" destroy-on-close :title="$t('Update User')" custom-class="el-dialog-custom">
    <el-form status-icon ref="updateUserForm" :model="data" :rules="rules" @submit.prevent="onSubmit"
      label-position="right" label-width="150px" class="mt-4">
      <el-form-item :label="$t('Ip Address')" :prop="'ip_address'" :error="isError('ip_address')">
        <el-input v-model="data.ip_address" @keyup.enter="onSubmit"></el-input>
        <span v-if="isError('ip_address')" class="el-form-item__error">{{ getErrorMessage('ip_address') }}</span>
      </el-form-item>
      <el-form-item label="Expired At" prop="expired_at">
        <el-date-picker v-model="data.expired_at" type="datetime" format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss" placeholder="Select Expiration Date"></el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('Note')" :prop="'note'" :error="isError('note')">
        <el-input v-model="data.note" @keyup.enter="onSubmit"></el-input>
      </el-form-item>
      <el-form-item :label="$t('Status')">
        <el-switch v-model="data.is_allowed" :active-value="1" :inactive-value="0"
          style="--el-switch-on-color: #13ce66"></el-switch>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button type="primary" @click="onSubmit" :disabled="isLoading" :loading="isLoading">{{ $t('Submit')
        }}</el-button>
    </template>
  </el-dialog>
</template>