import EventBus from '@/utilities/eventBus';
import { updateIp } from '@/api/ipManagement';

export default {
  name: 'updateIp',
  data() {
    return {
      dialogVisible: false,
      data: this.setDefaultData(),
      serverErrors: [],
      isLoading: false,
      disable: false,
      rules: {
        ip_address: [{ required: true, message: 'IP Address is required', trigger: 'blur' }],
        status: [{ required: true, message: 'Status is required', trigger: 'change' }],
        id: [{ required: true, message: 'ID is required', trigger: 'blur' }],
      }
    };
  },
  created() {
    EventBus.$on('updateIp', (item) => {
      this.data = Object.assign(this.data, item);
      this.data.is_default = this.data.is_default === 1 ? true : false;
      this.dialogVisible = true;
    });
  },
  methods: {
    isError(field) {
      return this.serverErrors && this.serverErrors[field];
    },
    getErrorMessage(field) {
      return this.serverErrors[field] ? this.serverErrors[field][0] : '';
    },
    setDefaultData() {
      return {
        username: '',
        email: '',
        note: ''
      };
    },
    async onSubmit() {
      if (this.isLoading) return;
      this.isLoading = true;

      this.$refs.updateUserForm.validate(async valid => {
        if (!valid) {
          this.isLoading = false;
          return false;
        }

        try {
          const payload = {
            id: this.data.id,
            ip_address: this.data.ip_address,
            expired_at: this.data.expired_at,
            is_active: this.data.is_allowed,
            note: this.data.note,
          };

          const res = await updateIp(payload);
          this.dialogVisible = false;

          this.notification(res.data.message, 'success');
          window.location.reload();
        } catch (e) {
          this.serverErrors = e.response.data;
          this.notification(message, 'Invalid data, please check again');
          this.isLoading = false;
        } finally {
          this.isLoading = false;
        }
      });
    },
  },
};
