<script src="./script.js"></script>
<template>
    <el-dialog v-model="dialogVisible" destroy-on-close :title="$t('Update User')" custom-class="el-dialog-custom">
      <el-form status-icon ref="updateUserForm" :model="data" :rules="rules" @submit.prevent="onSubmit" label-position="right" label-width="150px" class="mt-4">
        <el-form-item :label="$t('Username')" :prop="'username'" :error="isError('username')">
          <el-input v-model="data.username" @keyup.enter="onSubmit"></el-input>
          <span v-if="isError('username')" class="el-form-item__error">{{ getErrorMessage('username') }}</span>
        </el-form-item>
        <el-form-item :label="$t('Email')" :prop="'email'" :class="{'is-error': isError('email')}" required>
          <el-input v-model="data.email" @keyup.enter="onSubmit"></el-input>
          <div v-if="isError('email')" class="el-form-item__error">{{ getErrorMessage('email') }}</div>
        </el-form-item>
        <el-form-item :label="$t('Password')" :prop="'password'" :class="{'is-error': isError('password')}">
          <el-input v-model="data.password" type="password" @keyup.enter="onSubmit"></el-input>
          <div v-if="isError('password')" class="el-form-item__error">{{ getErrorMessage('password') }}</div>
        </el-form-item>
        <el-form-item :label="$t('Status')">
          <el-switch
            v-model="data.is_active"
            :active-value="1"
            :inactive-value="0"
            style="--el-switch-on-color: #13ce66"
          ></el-switch>
        </el-form-item>
      </el-form>
  
      <template #footer>
        <el-button type="primary" @click="onSubmit" :disabled="isLoading" :loading="isLoading">{{ $t('Submit') }}</el-button>
      </template>
    </el-dialog>
  </template>
  