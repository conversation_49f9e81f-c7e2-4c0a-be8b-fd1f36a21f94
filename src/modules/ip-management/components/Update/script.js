import EventBus from '@/utilities/eventBus';
import { updateUser } from '@/api/ipManagement';

export default {
  name: 'updateUser',
  data() {
    return {
      dialogVisible: false,
      data: this.setDefaultData(),
      serverErrors: [],
      isLoading: false,
      disable: false,
      rules: {
        id: [{ required: true, message: 'ID is required', trigger: 'blur' }],
        username: [{ required: true, message: 'Username is required', trigger: 'blur' }],
        email: [
          { required: true, message: 'Email is required', trigger: 'blur' },
          { type: 'email', message: 'Email is not valid', trigger: 'blur' }
        ],
        password: [
          { min: 6, message: 'Password must be at least 6 characters', trigger: 'blur' }
        ]
      }
    };
  },
  created() {
    EventBus.$on('updateUser', (item) => {
      this.data = Object.assign(this.data, item);
      this.data.is_default = this.data.is_default === 1 ? true : false;
      this.dialogVisible = true;
    });
  },
  methods: {
    isError(field) {
      return this.serverErrors && this.serverErrors[field];
    },
    getErrorMessage(field) {
      return this.serverErrors[field] ? this.serverErrors[field][0] : '';
    },
    setDefaultData() {
      return {
        username: '',
        email: '',
      };
    },
    async onSubmit() {
      if (this.isLoading) return;
      this.isLoading = true;

      this.$refs.updateUserForm.validate(async valid => {
        if (!valid) {
          this.isLoading = false;
          return false;
        }

        try {
          const payload = {
            id: this.data.id,
            username: this.data.username,
            email: this.data.email,
            password: this.data.password || null,
            is_active: this.data.is_active
          };

          const res = await updateUser(payload);
          this.$emit('userUpdated', res.data);
          this.dialogVisible = false;

          this.notification(res.data.message, 'success');
          window.location.reload();
        } catch (e) {
          this.serverErrors = e.response.data;
          this.notification(message, 'Invalid data, please check again');
          this.isLoading = false;
        } finally {
          this.isLoading = false;
        }
      });
    },
  },
};
