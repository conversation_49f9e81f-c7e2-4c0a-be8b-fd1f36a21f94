<template>
      <el-button class="float-right" type="primary" @click="createUser" style="margin-bottom: 15px;">
      <span class="icon-margin-right">
        <icon :data="iconAdd" />
      </span>{{ $t('Create') }}
    </el-button>

  <div>
    <el-table stripe size="small" border :data="listUser" style="width: 100%" :max-height="maxHeight"
      v-loading="isLoading" element-loading-text="Loading..." class="mt-3">
      <el-table-column :label="$t('User')" min-width="150">
        <template #default="scope">
          {{ scope.row.username }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('Last active at')" min-width="200">
        <template #default="scope">
          {{ utcToLocalTime(scope.row.last_active_at).format('lll') }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('Created at')" min-width="150">
        <template #default="scope">
          {{ utcToLocalTime(scope.row.created_at).format('lll') }}
        </template>
      </el-table-column>
      <el-table-column class="text-center" :label="$t('Status')" min-width="150">
        <template #default="scope">
          <div class="flex justify-center items-center">
            <el-switch v-model="scope.row.is_active" :active-value="1" :inactive-value="0"
              style="--el-switch-on-color: #13ce66" disabled></el-switch>
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Action')"  width="150" align="center">
      <template #default="scope">
          <div class="flex justify-between px-8">
            <el-link class="el-link-edit" :underline="false" type="primary" @click="updateUser(scope.row)">
            <icon :data="iconEdit" />
          </el-link>
         <el-link class="el-link-delete" :underline="false" type="danger" @click="deleteRecord(scope.row)">
            <icon :data="iconDelete" />
          </el-link>
          </div>
        </template>
    </el-table-column>
    </el-table>
  </div>
  <createUser />
  <updateUser />

</template>

<script>
import { list, filter, approve, listUser, destroyUser } from "@/api/ipManagement.js";
import updateUser from "@/modules/ip-management/components/Update/Index.vue";
import createUser from "@/modules/ip-management/components/Create/Index.vue";
import EventBus from "@/utilities/eventBus.js";

export default {
  name: "UserManagementTab",
  components: {
    updateUser,
    createUser
  },
  data() {
    return {
      listUser: [],
      isLoading: false
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 230);
    }
  },
  methods: {
    async fetchUsers() {
      this.isLoading = true;
      const res = await listUser();
      const dataUser = res.data || [];
      this.listUser = dataUser;
      this.isLoading = false;
    },
    updateUser(item) {
      EventBus.$emit("updateUser", item);
    },

    createUser() {
      EventBus.$emit("createUser",);
    },
    async deleteRecord(row) {
      try {
        this.$confirm(this.$t('Are you sure you want to delete this ?'), this.$t('Warning'), {
          confirmButtonText: this.$t('Yes'),
          cancelButtonText: this.$t('No'),
          type: 'warning'
        }).then(async () => {
          const res = await destroyUser(row.id);
          this.$emit('recordDeleted', res.data);
          this.notification(res.data.message, 'success');
          this.fetchUsers();
        }).catch(() => {
          // Handle cancel action if user presses 'No'
        });
      } catch (error) {
        this.notification('Error deleting record, please try again.', 'error');
      }
    }

  },
  mounted() {
    this.fetchUsers();
  }
};
</script>