import UserManagementTab from '@/modules/ip-management/components/User/UserManagementTab.vue';
import IpAccessTab from '@/modules/ip-management/components/Ip/IpAccessTab.vue';
import BypassUserTab from '@/modules/ip-management/components/Bypass/BypassUserTab.vue';

export default {
  name: "App",
  components: {
    IpAccessTab,
    UserManagementTab,
    BypassUserTab,
  },
  data() {
    return {
      activeTab: "ip-access"
    };
  }
};
