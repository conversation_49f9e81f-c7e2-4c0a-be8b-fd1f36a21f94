<template>
  <div>
    <el-input
        :placeholder="$t('Scan Label ID to verify')"
        class="mb-3 border-stone-500 w-full"
        size="large"
        ref="scanLabelId"
        v-model.trim="labelId"
        @keyup.enter="!(shipmentId) ? scanLabelVerifyFe() : getScanLabel()"
    />
    <div class="border rounded">
    <div v-loading="isLoadingComponent">
          <div class="flex items-center px-3 py-2 border-b">
            <div class="grow font-semibold">{{ $t("Items") }}</div>
            <div class="flex items-center">
              <div class="mr-3">TOTAL:</div>
              <div class="text-4xl font-semibold mr-2"
                    :class="statusClassForTotal(saleOrder?.total_verify, saleOrder?.total)">{{ saleOrder?.total_verify }}/{{ saleOrder?.total }}</div>
            </div>
          </div>
        <template v-for="(item, index) in saleOrder?.items" :key="index">
          <div pan class="flex border-b last:border-0 pr-5 py-2 odd:bg-white even:bg-slate-50" >
            <div class="max-w-[520px]">
              <div class="pl-5">{{ item?.product?.name }}</div>
              <div class="flex flex-wrap max-w-[520px]">
                <div v-for="option in item.map_options" class="w-[140px] mt-2">
                  <div class=" flex cursor-pointer m-auto justify-center">
                    <el-image
                      lazy="true"
                      class="border rounded"
                      style="width: 90px; height: 90px;
                      background-image: linear-gradient(45deg,#b0b0b0 25%,transparent 25%),linear-gradient(-45deg,#b0b0b0 25%,transparent 25%),linear-gradient(45deg,transparent 75%,#b0b0b0 75%),linear-gradient(-45deg,transparent 75%,#b0b0b0 75%);
                      background-size: 20px 20px;
                      background-position: 0 0,0 10px,10px -10px,-10px 0px;"
                      :src="option.PreviewFiles ? option.PreviewFiles :  option.artwork"
                      :fit="fit"
                      @click="previewFile(option.PreviewFiles ? option.PreviewFiles :  option.artwork)"
                    ></el-image>
                  </div>
                  <el-link
                    target="_blank"
                    :href="`${option.artwork}`"
                    :underline="false"
                    type="primary"
                    class="font-bold text-center !text-[16px] m-auto w-fit mt-[2px]"
                    style="display: block">
                    {{ generatePrintSide(option.name) }}
                  </el-link>
                </div>
              </div>
            </div>
            <div class="m-auto text-right mr-0">
              <span class="text-4xl font-semibold" :class="statusClassForItem(item?.shipment_item_count, item?.quantity)">
                {{ item?.shipment_item_count }}/{{ item?.quantity }}
              </span>
            </div>
          </div>
        </template>
        <el-dialog v-model="dialogPreviewFile" title="Preview" width="40%" top="5vh" @close="dialogPreviewFile = false">
          <template #default>
            <div class="dialog-footer">
              <img class="w-full" :src="imagePreviewFile">
            </div>
          </template>
        </el-dialog>
      </div>
      </div>

    <el-dialog
        v-model="dialogScanLabelFail"
        @close="dialogScanLabelFail = false"
        :close-on-click-modal="false"
        width="40%"
        center
    >
      <div class="text-center">
        <el-result icon="warning" :title="$t('FAILED TO VERIFY')" class="mt-0 text-xl"></el-result>
        <div class="text-base whitespace-normal mb-2 text-xl" style="word-break: break-word">
          Invalid Label ID or Shipment. Please ensure that you have inputted the correct Label ID and try again!
        </div>
        <div class="text-base whitespace-normal text-xl" style="word-break: break-word">
          Label ID hoặc lô hàng vận chuyển không hợp lệ. Xin vui lòng thử lại!
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer flex justify-center">
          <el-button type="primary" @click="dialogScanLabelFail = false" >Got it</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>

import {scanLabelToVerify} from "@/api/label.js";
import { S3_URL } from "@/utilities/constants";
import EventBus from "@/utilities/eventBus";

export default {
  name: "ScanLabelToVerify",
  props: {
    saleOrder: {
      type: Object,
    },
    employeeID: {
      type: Number,
    },
    shipmentId: {
      type: Number,
    },
    timeTrackingId: {
      type: Number,
    },
    isVerifyLabel: {
      type: Boolean,
      default: false,
    },
  },
  components: {},
  data() {
    return {
      labelId: "",
      isLoadingComponent: false,
      dialogPreviewFile: false,
      imagePreviewFile: '',
      ImagePreviewFile: '',
      endPoint: '',
      dialogScanLabelFail: false
    }
  },
  computed: {},
  beforeUnmount() {
    EventBus.$off("focusToScanLabelVerify");
  },
  mounted() {
    this.loading()
    this.$refs.scanLabelId.focus();
  },
  created() {
    this.endPoint = S3_URL;
    EventBus.$on("focusToScanLabelVerify", () => {
      this.focusToScanLabelVerify()
    });
  },
  methods: {
    isKittedOrder(str) {
      return str ? str.includes("215") : false;;
    },
    previewFile(image_front) {
      this.dialogPreviewFile = true;
      this.imagePreviewFile = image_front;
    },
    previewBack(image_back) {
      this.dialogPreviewFile = true;
      this.ImagePreviewFile = image_back;
    },
    async getScanLabel() {
      try {
        const dataParam = {
          order_id: this.saleOrder.id,
          label_id: this.labelId,
          shipment_id: this.shipmentId,
          employee_id: this.employeeID,
          id_time_checking: this.timeTrackingId
        }
        const res = await scanLabelToVerify(dataParam);
        this.$refs.scanLabelId.focus();
        if (res.data.code == "label_scanned") {
          this.notification(res.data.message, "error");
        }
        if (res.data.code == "label_success") {
         await this.calculatorTotal(res.data.data)
          this.notification(res.data.message, "success");
          if ( this.saleOrder.total_verify == this.saleOrder.total ) {
            EventBus.$emit("printLabelForScanVerifyAllSuccess");
          }
        }
        if (res.data.code == "label_insert") {
          await this.calculatorTotal(res.data.data)
          this.notification(res.data.message, "success");
          if ( this.saleOrder.total_verify == this.saleOrder.total ) {
            EventBus.$emit("printLabelForScanVerifyAllSuccess");
          }
        }
        this.labelId = ""
      } catch (e) {
        this.labelId = ""
        console.log(11111);
        this.$refs.scanLabelId.focus();
        this.dialogScanLabelFail = true
      }
    },
    calculatorTotal(orderItemId) {
      orderItemId.forEach((element) => {
        this.saleOrder.total_verify = this.saleOrder.total_verify + element.total_verify
        const index = this.saleOrder.items.findIndex(item => item.id == element.order_item_id)
        this.saleOrder.items[index].shipment_item_count = this.saleOrder.items[index].shipment_item_count + element.total_verify;
      });
      const indexShipment = this.saleOrder.shipment.findIndex(itemShipment => itemShipment.id == this.shipmentId)
      this.saleOrder.shipment[indexShipment].shipment_item_labels.push({label_id: this.labelId})
    },
    loading() {
      this.isLoadingComponent = true;
      // now 'this' is referencing the Vue object and not the 'setTimeout' scope
      setTimeout(
          () => this.isLoadingComponent = false, 1000
      );
    },
    statusClassForItem(shipmentItemCount, quantity) {
      if (shipmentItemCount > quantity) {
        return "text-red-700"
      }
      if (shipmentItemCount == quantity) {
        return "text-green-800"
      }
      if (shipmentItemCount < quantity) {
        return "text-black"
      }
    },
    statusClassForTotal(totalVerify, total) {
      if (totalVerify > total) {
        return "text-red-700"
      }
      if (totalVerify == total) {
        return "text-green-800"
      }
      if (totalVerify < total) {
        return "text-red-700"
      }
    },
    focusToScanLabelVerify(){
      this.labelId = ""
      this.$refs.scanLabelId.focus();
    },
    generatePrintSide(side) {
      return side.replaceAll('_', ' ').replaceAll(/([A-Z])/g, ' $1').toUpperCase();
    },
   async scanLabelVerifyFe() {

      // check xem label đó đã được scan chưa
      if (this.shipmentId) {

      } else {
        if (!this.labelId) return;
        let verifyLabelList = await JSON.parse(window.localStorage.getItem('verify_label')) || [];
        if (await verifyLabelList.includes(this.labelId)) {
          this.notification("Label has been scanned", "error");
          this.labelId = ''
        } else {
          // nếu chưa thì add vào local storage
          const orderItem = await this.saleOrder.items.flatMap(item => item.barcodes)
              .find(child => child.label_id === this.labelId);
          if (!orderItem) {
            this.notification("Label " + this.labelId + " not in this order", "error");
            this.labelId = ''
            return;
          } else {
           await verifyLabelList.push(this.labelId);
           await window.localStorage.setItem('verify_label', JSON.stringify(verifyLabelList));
          }
          EventBus.$emit('isVerifyLabelOrderNotShipment');
          await this.calculatorTotalV1(orderItem.order_item_id);
          this.notification("Scan label success", "success");
          this.labelId = ''
        }
      }
    },
    calculatorTotalV1(orderItemId) {
      this.saleOrder.total_verify = this.saleOrder.total_verify + 1
      const index = this.saleOrder.items.findIndex(item => item.id == orderItemId)
      this.saleOrder.items[index].shipment_item_count = this.saleOrder.items[index].shipment_item_count + 1;
    },

  }
}
</script>

<style scoped>

</style>