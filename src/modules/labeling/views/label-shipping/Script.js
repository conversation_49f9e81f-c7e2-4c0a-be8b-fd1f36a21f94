import {employeeLogoutTimeChecking, employeeTimeChecking} from "@/api/employee.js";
import {getOrderByLabelId} from "@/api/saleOrder.js";
import IncrementTime from "@/components/IncrementTime.vue";
import AddressOrder from "../../components/AddressOrder.vue";
import PrintLabel from "../../components/PrintLabel.vue";
import ScanLabelToVerify from "../../components/ScanLabelToVerify.vue";
import EventBus from "@/utilities/eventBus.js";
import CreateShipmentLabel from "../../components/CreateShipmentLabel.vue";
import {API_URL, STORAGE_URL} from "@/utilities/constants";
import {
    getHistoryPrintLabel,
    getListPrinters,
    getUrlZLP,
    logPrinterShipment,
    postLabelGelato,
    print,
    refund
} from "@/api/label.js";
import {logPrinted} from "@/api/performance";
import moment from "moment-timezone";
import warehouseMixin from '@/mixins/warehouse';
import numberMixin from '@/mixins/formatNumber';
import {livePerformance} from '@/api/performance.js';
import {ArrowDownBold, ArrowUpBold, Medal, Trophy} from '@element-plus/icons-vue';
import {logPrintedSuccess} from "@/api/insertPrinting.js"
import {getBacklogWithJobType} from "@/api/backlog";

export default {
    name: "LabelShipping",
    mixins: [warehouseMixin, numberMixin],
    components: {
        IncrementTime,
        AddressOrder,
        PrintLabel,
        ScanLabelToVerify,
        CreateShipmentLabel,
        ArrowDownBold,
        ArrowUpBold,
        Medal,
        Trophy

    },
    data() {
        return {
            time_checking_id: null,
            isLoading: false,
            codeEmployee: "",
            label_id: '',
            labelIdError: '',
            employeeID: '',
            employee: {},
            employeeError: '',
            job_type: "create_shipment_label",
            id_time_checking: null,
            dataRules: {
                label_id: [
                    {
                        required: true,
                        message: this.$t('This field cannot be left blank.'),
                        trigger: "change",
                    },
                ],
            },
            saleOrder: null,
            urlLabel: '',
            shipmentId: '',
            printers: [],
            printer: null,
            reprint: '',
            dialogConfirmReprint: false,
            confirmReprint: "Are you sure you want to reprint label shipment?",
            dialogVisible: false,
            shipmentRefundId: null,
            labelFlagId: '',
            isVoidLabel: false,
            openDialogInputPass: false,
            isCreateLabelActive: false,
            passwordCreateLabel: null,
            typeFilePrinters: [
                {
                    name: "pdf",
                    displayName: "PDF",
                },
                {
                    name: "zpl",
                    displayName: "ZPL",
                },
            ],
            typeFilePrinter: null,
            dialogPrePrintLabel: false,
            openDialogHistory: false,
            urlStorage: STORAGE_URL,
            storeZazzle: 'Zazzle',
            tab: null,
            types: [],
            files: {
                Label: {}
            },
            THANKYOU_CARD: 'thankyou_card',
            PACKING_SLIP: 'packing_slip',
            LABEL: 'Label',
            GIFT_MESSAGE: 'gift_message',
            ORDER_TYPE: 5,
            scanHistory: [],
            total_item: 0,
            avg_item_by_hour: 0,
            avg_item_last_year: 0,
            ave_item_current_year: 0,
            performanceHistory: {},
            warehouse_id: '',
            department: 21,
            age: '',
            isCollapsed: true, // Collapsible state
            is15Minutes: true,
            countPerformanceReport: 0,
            dialogItemShipment: false,
            dataItemShipmentLabel: [],
            reprintInsert: '',
            storeGelato: 484182,
            totalBacklog: 0,
            intervalId: null,
            isVerifyLabel: false,
            kittedOrder: false,
            serviceCodeFirstClass: ['First', 'FirstClassMailInternational'],
        };
    },

    computed: {
        performanceColor() {
            if(this.countPerformanceReport < 2) {
                return '';
            }
            let percentageDifference = (this.avg_item_by_hour / this.ave_item_current_year) * 100 - 100;

            if (percentageDifference < -30) {
                return 'bg-red-200 border border-red-500'; // Change background to red if below 30%
            } else if (percentageDifference < -20) {
                return 'bg-yellow-200 border border-t-yellow-500'; // Change background to yellow if below 20%
            } else {
                return ''; // Default background color
            }
        },
        performanceColorBorderTop() {
            if(this.countPerformanceReport < 2) {
                return '';
            }
            let percentageDifference = (this.avg_item_by_hour / this.ave_item_current_year) * 100 - 100;

            if (percentageDifference < -30) {
                return 'border-t border-t-red-500'; // Change background to red if below 30%
            } else if (percentageDifference < -20) {
                return 'border-t border-t-yellow-500'; // Change background to yellow if below 20%
            } else {
                return ''; // Default background color
            }
        },

        collapseIcon() {
            return this.isCollapsed ? ArrowUpBold : ArrowDownBold;
        },

    },
    unmounted() {
        this.stopInterval();
        this.clearPerformanceInterval();
    },

    beforeUnmount() {
        window.removeEventListener("keydown", this.escapeListener);
        EventBus.$off("createShipmentLabel");
        EventBus.$off("focusToScanLabel");
        EventBus.$off("focusToScanLabel");
    },
    mounted() {
        this.listPrinters()
        // this.loadLabel()
        this.fetchBacklog();
        this.intervalId = setInterval( () => {
            this.fetchBacklog();
        }, 60000 )
        this.loadTypeFileForPrinter(),
        this.fetchStateByUsId();
        this.warehouse_id = this.getWarehouseId();
        this.clearPerformanceInterval();
    },
    created() {
        window.addEventListener("keydown", this.escapeListener);
        EventBus.$off("refreshScanLabelShipment");
        EventBus.$on("refreshScanLabelShipment", async () => {
            await this.loadTypeFileForPrinter(),
            await this.getScanLabelWithFlag()
            if (this.saleOrder?.shipment?.length == 1) {
                await this.autoPrint()
            }
        })
        EventBus.$on("focusToScanLabel", () => {
            this.focusToScanLabel()
        });
        EventBus.$on("printLabelForScanVerifyAllSuccess", () => {
            // this.prePrintLabel()
            this.autoPrint()
        });
        EventBus.$on("isVerifyLabelOrderNotShipment", () => {
            this.isVerifyLabel = true;
        });
    },
    watch: {
        tab(value) {
            this.urlLabel = this.files[value]?.url ?? '';
        }
    },
    beforeDestroy() {
        this.clearPerformanceInterval();
    },
    methods: {
        isKittedOrder(str) {
            return str ? str.includes("215") : false;;
        },
        stopInterval() {
            if (this.intervalId) {
                clearInterval(this.intervalId);
                this.intervalId = null;
            }
        },
        async fetchBacklog() {
            let params = {
                'job_type': this.job_type,
            }
            const res = await getBacklogWithJobType(params);
            this.totalBacklog = res.data?.total;
        },
        showAllIframe(item) {
            if (item === undefined) {
                return false;
            }
            return [this.LABEL, this.THANKYOU_CARD].includes(item?.type);
        },
        checkValidFile(files) {
            let keys = Object.keys(files);
            return files && keys.includes(this.LABEL) && keys.includes('TU');
        },
        totalFiles(files) {
            return Object.keys(files).length ?? 0;
        },
        getImageUrl(item) {
            return this.urlStorage + '/IconProduct/' + item
        },
        async fetchStateByUsId() {
            await this.$store.dispatch("fetchStateByUs")
        },
        gotoLinkTracking(item) {
            if (!item || !item?.url_tracking_easypost) {
                this.notification('Tracking Url is empty.', "info");
                return;
            }
            window.open(item.url_tracking_easypost);
        },
        dialogConfirmRefund(shipment_refund_id) {
            this.dialogVisible = true;
            this.shipmentRefundId = shipment_refund_id;
        },
        async refund() {
            try {
                this.dialogVisible = false;
                this.isLoading = true;
                const res = await refund({
                    shipmentId: this.shipmentRefundId,
                    orderId: this.saleOrder.id,
                    employeeId: this.employee.id
                })
                this.isLoading = false;
                let shipment = this.saleOrder.shipment.find((item) => item.id == res.data.data.id);
                let key = this.saleOrder.shipment.indexOf(shipment);
                this.saleOrder.shipment.splice(key, 1);
                this.saleOrder.shipment.splice(key, 0, res.data.data);
                this.notification(res.data.message, "success");
            } catch (err) {
                this.isLoading = false;
                this.notification(err.response.data.message, "error");
            } finally {
                this.loadTypeFileForPrinter(),
                this.getScanLabelWithFlag()
            }
            this.shipmentRefundId = null;
        },
        async printLabel(auto = false) {
            this.$refs.scanLabelId.focus();
            this.isLoading = true;
            const shipment = this.saleOrder.shipment.filter((i) => i.id === this.shipmentId);
            const item = shipment[0]
            if (!this.printer) {
                this.isLoading = false;
                return this.notification(this.$t('Please select printer for label'), 'error');
            }
            if (item.refund_status) {
                this.notification("The Shipment has been voided", "error");
            } else {
                try {
                    let URL = ''
                    let nameFile = ''
                    if (this.typeFilePrinter === 'pdf') {
                        URL = API_URL + '/label/download/' + item?.id
                        nameFile = item?.id + '.pdf'
                    } else {
                        if (item.label_zpl_url === null) {
                            const resData = await getUrlZLP(item?.id)
                            URL = resData.data
                            nameFile = URL.substring(URL.lastIndexOf('/') + 1)
                        } else {
                            URL = item.label_zpl_url
                            nameFile = URL.substring(URL.lastIndexOf('/') + 1)
                        }
                    }
                    const res = await print({ url: URL, file: nameFile, printer: this.printer, width: 4, height: 6 })
                    const data =  await this.logPrintedLabelShipment(item?.id)
                    window.localStorage.setItem(`printer_${this.LABEL}`, this.printer);
                    this.saveLabelIdLocalStorage(this.labelFlagId);
                    // check store has note
                    if (!this.saleOrder?.store?.has_note && this.saleOrder?.store?.name != this.storeZazzle && this.saleOrder?.order_type !== this.ORDER_TYPE) {
                        window.localStorage.setItem('filePrinter', this.typeFilePrinter);
                    }
                    if (auto) {
                        await this.getScanLabelWithFlag()
                    }
                    this.dialogConfirmReprint = false
                    this.dialogPrePrintLabel = false
                    this.isLoading = false;
                    this.notification(this.$t('Print label shipping success'), "success");
                } catch (e) {
                    this.dialogConfirmReprint = false
                    this.dialogPrePrintLabel = false
                    this.isLoading = false;
                    this.notification(this.$t(e.response.data.status), "error");
                }
            }
        },
        async loadLabel() {
            if (this.tab == 'PS') {
                this.printer = window.localStorage.getItem(`printer_${this.PACKING_SLIP}`);
            }
            if (this.tab == 'TU') {
                this.printer = window.localStorage.getItem(`printer_${this.THANKYOU_CARD}`);
            }
            if (this.tab == 'Label') {
                this.printer = window.localStorage.getItem(`printer_${this.LABEL}`);
            }
            if (this.tab == 'GM') {
                this.printer = window.localStorage.getItem(`printer_${this.GIFT_MESSAGE}`);
            }
        },
        async listPrinters() {
            getListPrinters().then((res) => {
                if (res.data?.status == 'Success') {
                    this.printers = res.data.data
                }
            }).catch((error) => {
                this.notification(this.$t('Please turn on the SWIFTPOD CONNECT #2.0.8 app'), 'error');
            });
        },
        async scanEmployeeID() {
            if (this.id_time_checking) {
                return true;
            }
            if (!this.employeeID) {
                this.employeeError = "Employee ID field cannot be left blank.";
                return false;
            }
            const res = await employeeTimeChecking({
                code: Number(this.employeeID),
                job_type: this.job_type
            })
            if (!res.data.data) {
                this.employeeError = "Can't find your employee ID, please scan again";
                return false;
            }
            this.employeeError = "";
            this.employee = res.data.data;
            this.id_time_checking = res.data.id_time_checking;
            await this.fetchLivePerformance();
            this.startPerformanceInterval();

            this.$refs.scanLabelId.focus();

            return true;
        },
        async resetEmployee() {
            const res = await employeeLogoutTimeChecking(this.id_time_checking)
            this.employee = {}
            this.employeeError = ""
            this.codeEmployee = ""
            this.id_time_checking = null
            this.employeeID = ''
            this.saleOrder = null
            this.label_id = ''
            this.urlLabel = ''
            this.tab = null;
            this.files = {}
            this.shipmentId = '',
            this.types = [];
            window.location.reload();
        },
        async generateLink() {
            if (this.saleOrder?.shipment?.length > 0) {
                this.types = ['Label']
                this.tab = this.LABEL;
                this.urlLabel = API_URL + '/label/download/' + this.saleOrder?.shipment[0]?.id + '#toolbar=0'
                this.files.Label = {
                    type: this.LABEL,
                    url: API_URL + '/label/download/' + this.saleOrder?.shipment[0]?.id + '#toolbar=0'
                }
            } else {
                this.types = []
                this.tab = '';
                this.urlLabel = null;
            }
            if (this?.saleOrder?.order_inserts?.length > 0) {
                this.saleOrder.order_inserts.forEach((item) => {
                    switch (item.type) {
                        case this.PACKING_SLIP:
                            this.types.push('PS');
                            this.files.PS = {
                                type: this.PACKING_SLIP,
                                size: item.size,
                                printed_in: item.printed_in ?? '',
                                url: item.url,
                                file_id: item.file_id,
                                printed_at : item.printed_at
                            }
                            this.urlLabel = this.urlLabel ?? item.url;
                            if (!this.tab) {
                                this.tab = 'PS';
                            }
                            break;
                        case this.THANKYOU_CARD:
                            this.types.push('TU');
                            this.files.TU = {
                                type: this.THANKYOU_CARD,
                                size: item.size,
                                printed_in: item.printed_in ?? '',
                                url: item.url,
                                file_id: item.file_id,
                                printed_at : item.printed_at
                            }
                            this.urlLabel = this.urlLabel ?? item.url;
                            if (!this.tab) {
                                this.tab = 'TU';
                            }
                            break;
                        case this.GIFT_MESSAGE:
                            this.types.push('GM');
                            this.files.GM = {
                                type: this.GIFT_MESSAGE,
                                size: item.size,
                                printed_in: item.printed_in ?? '',
                                url: item.url,
                                file_id: item.file_id,
                                printed_at : item.printed_at
                            }
                            this.urlLabel = this.urlLabel ?? item.url;
                            if (!this.tab) {
                                this.tab = 'GM';
                            }
                            break;
                        default:
                            break;
                    }

                })
            }
            await this.loadLabel()
        },
        async printInsert(params, auto = false) {
            if (!params.printer) {
                return this.notification(this.$t('Please select printer for ' + params.type.replaceAll("_", "")), 'error');
            }
            this.isLoading = true;
            try {
                const nameFile = params.file_id + '.pdf';
                const res = await print({
                    url: params.url,
                    file: nameFile,
                    printer: params.printer,
                    paper: params.paper,
                    is_auto: params.is_auto ?? false
                });
                if (res && res?.data?.status === 'Success') {
                    this.notification(this.$t(`Insert printing printer success - ${params.type.replaceAll("_", " ")}`), 'success');
                    await this.logPrintInsert(params.file_id)
                    if (auto) {
                        await this.getScanLabelWithFlag()
                    }
                } else {
                    throw new Error('Print Failed');
                }
                window.localStorage.setItem(`printer_${params.type}`, params.printer);
            } catch (e) {
                this.notification(this.$t(`Print Failed - ${params.type.replaceAll("_", " ")}`), 'error');
            } finally {
                this.isLoading = false;
            }
        },
        // chi auto khi verify tat ca cac item thanh cong
        async autoPrint() {
            for (const property in this.files) {
                if (property != this.LABEL && (!this.files[property].printed_in || this.files[property].printed_in == this.LABEL.toLowerCase())) {
                    let printer = await window.localStorage.getItem(`printer_${this.files[property].type}`)
                    if (printer) {
                        const url = this.files[property].url;
                        let params = {
                            url: url,
                            printer: printer,
                            paper: this.files[property].size,
                            is_auto: true,
                            type: this.files[property].type,
                            file_id: this.files[property].file_id
                        }
                        await this.printInsert(params)
                    } else {
                        this.notification(this.$t('Please select printer for ' + this.files[property].type.replaceAll("_", " ")), 'error');
                    }
                }
                if (property === this.LABEL) {
                    await this.printLabel();
                }
            }
            await this.getScanLabelWithFlag()
        },
        async logPrinted(orderID) {
            try {
                const res = await logPrinted({
                    order_id: orderID,
                    type: this.title,
                });
                return res;
            } catch (e) { }
        },
        async getScanLabel() {
           await window.localStorage.removeItem('verify_label');
            this.isVerifyLabel = false
            if (!this.scanEmployeeID()) return;
            if (!this.label_id) {
                this.labelIdError = "Employee ID field cannot be left blank.";
                return;
            }
            this.isLoading = true;
            this.labelIdError = "";
            this.saleOrder = {}
            this.urlLabel = ''
            this.files = {}
            this.shipmentId = ''
            this.labelFlagId = ''
            try {
                const res = await getOrderByLabelId({
                    label_id: this.label_id,
                    time_tracking_id: this.id_time_checking
                })
                this.saleOrder = res.data;
                this.saveLabelIdLocalStorage(this.label_id.trim())
                this.generateLink()
                if (this.saleOrder?.shipment?.length > 0) {
                    // check store has note
                    if (res.data.store?.has_note || res.data.store.name == this.storeZazzle
                        || res.data.order_type === this.ORDER_TYPE
                        || (this.saleOrder?.shipment[0]?.carrier_code == 'USPS' && this.serviceCodeFirstClass.includes(this.saleOrder?.shipment[0]?.service_code))){
                        this.typeFilePrinter = 'pdf';
                    } else {
                        this.loadTypeFileForPrinter();
                    }
                    this.shipmentId = this.saleOrder?.shipment[0]?.id
                    this.reprint = this.saleOrder?.shipment[0]?.shipment_label_printed ? this.saleOrder?.shipment[0]?.shipment_label_printed[0]?.printed_date : false
                    //this.urlLabel = this.saleOrder?.shipment[0]?.label_url
                    this.isVoidLabel = this.saleOrder?.shipment[0]?.refund_status ? true : false
                    if (this.saleOrder?.total_verify !== this.saleOrder?.total) {
                        EventBus.$emit("focusToScanLabelVerify");
                    }
                    this.selectTabPrinter('Label')
                }

                if (this.saleOrder?.shipment?.length == 0) {
                    EventBus.$emit("focusToScanLabelVerify");
                }

                if (this?.saleOrder?.order_inserts?.length && this.saleOrder?.shipment?.length == 0) {
                    this.selectTabPrinter(this.types[0])
                }
                this.isLoading = false;
                this.labelFlagId = this.label_id;
                this.label_id = ''
            } catch (e) {
                this.saleOrder = null
                const data = e.response.data;
                this.isLoading = false;
                this.label_id = ''
                let message = data.message || this.$t('Something went wrong. Try again.');
                this.notification(message, "error");
                this.$refs.scanLabelId.focus();
            }
        },
        saveLabelIdLocalStorage(labelId) {
            if (!labelId) return;
            let labels = JSON.parse(localStorage.getItem('labels')) || [];
            // Check if the labelId already exists in the array, remove it
            labels = labels.filter(id => id !== labelId);
            // Add the new labelId to the beginning of the array
            labels.unshift(labelId);
            // Limit the array to 20 elements
            labels = labels.slice(0, 20);
            // Save the updated array back to localStorage
            localStorage.setItem('labels', JSON.stringify(labels));
        },
        async creatLabelShipment() {
            this.openDialogInputPass = false;
            EventBus.$emit("createShipmentLabel", {
                saleOrder: this.saleOrder,
                time_tracking_id: this.id_time_checking,
                warehouse: this.saleOrder.warehouse,
                employeeId: this.employee.id
            });
        },
        async creatLabelShipmentGelato() {
            this.isLoading = true
            const param = {
               order_id:  this.saleOrder.id,
               employee_id: this.employee.id
            }
            try {
                const res = await postLabelGelato(param)
                await this.getScanLabelWithFlag()
            } catch (e) {
                const data = e.response.data;
                let message = data.message || this.$t('Create label shipping error. Try again.');
                this.notification(message, "error");
            } finally {
                this.isLoading = false
            }
        },
        loadedIframe() {
            if (this.saleOrder?.store?.name == this.storeZazzle) {
                this.isLoading = false;
            }
        },
        async showLabelFromShipment(item) {
            this.shipmentId = item.id
            const currentShipment = this.saleOrder?.shipment?.find(
                (s) => s.id === this.shipmentId
            );
            if (this.saleOrder.store?.has_note || this.saleOrder.store.name == this.storeZazzle
                || this.saleOrder.order_type === this.ORDER_TYPE
                || (currentShipment?.carrier_code === 'USPS' &&
                    this.serviceCodeFirstClass.includes(currentShipment?.service_code))){
                this.typeFilePrinter = 'pdf';
            } else {
                this.loadTypeFileForPrinter();
            }
            this.tab = this.LABEL;
            this.urlLabel = API_URL + '/label/download/' + item.id + '#toolbar=0'
            this.reprint = item.shipment_label_printed ? item.shipment_label_printed[0]?.printed_date : false
            this.isVoidLabel = item.refund_status ? true : false
        },
        showWeight(value) {
            let weightValue = "";
            switch (value) {
                case "pounds":
                    weightValue = "lb";
                    break;
                case "ounces":
                    weightValue = "oz";
                    break;
                case "grams":
                    weightValue = "g";
                    break;
            }
            return weightValue;
        },
        PrintDiv() {
            var contents = document.getElementById('myIFrame').innerHTML;
            var frame1 = document.createElement('iframe');
            frame1.name = "frame1";
            frame1.style.position = "absolute";
            frame1.style.top = "-1000000px";
            document.body.appendChild(frame1);
            var frameDoc = frame1.contentWindow ? frame1.contentWindow : frame1.contentDocument.document ? frame1.contentDocument.document : frame1.contentDocument;
            frameDoc.document.open();
            frameDoc.document.write(`<html><head><title>'Label shipment'</title><style>@media print {
    @page {
        size: 6in 4in landscape;
        margin: 0in 0in 0in 0in;
    }
    img {
    width: 4in;
    height: 6in;
    }
    #printable {
        display: block;
        rotation:90deg;
    }
}</style>`);
            frameDoc.document.write('</head><body>');
            frameDoc.document.write(contents);
            frameDoc.document.write('</body></html>');
            frameDoc.document.close();
            setTimeout(function () {
                window.frames["frame1"].focus();
                window.frames["frame1"].print();
                document.body.removeChild(frame1);
            }, 500);
        },
        async logPrintedLabelShipment(shipmentId) {
            try {
                const res = await logPrinterShipment({
                    shipmentId: shipmentId,
                    employeeId: this.employee.id,
                    id_time_checking: this.id_time_checking
                });
                if (!res.data.labeled) {
                    this.total_item++;
                }
                return res;
            } catch (e) {
                let message = this.$t('Log print label error.');
                this.notification(message, "error");
            }

        },
        reprintLabel() {
            this.dialogConfirmReprint = true;
        },
        closeSubmitReprint() {
            this.$refs.scanLabelId.focus();
            this.dialogConfirmReprint = false;
        },
        dimensionForShipmentItem(item) {
            const length = item.dimension_length ?? '0'
            const width = item.dimension_width ?? '0'
            const height = item.dimension_height ?? '0'
            return '(' + length + 'x' + width + 'x' + height + ') in /'
        },
        statusShipment(item) {
            let status = "";
            switch (item) {
                case "unknown":
                    status = `<span class="text-warning">Unknown</span>`;
                    break;
                case "pre_transit":
                    status = `<span class="text-yellow-400">Pre transit</span>`;
                    break;
                case "in_transit":
                    status = `<span class="text-yellow-400">In transit</span>`;
                    break;
                case "out_for_delivery":
                    status = `<span class="text-orange-400">Out for delivery</span>`;
                    break;
                case "delivered":
                    status = `<span class="text-success">Delivered</span>`;
                    break;
                case "available_for_pickup":
                    status = `<span class="text-sky-400">Available for pickup</span>`;
                    break;
                case "return_to_sender":
                    status = `<span class="text-danger">Return to sender</span>`;
                    break;
                case "failure":
                    status = `<span class="text-danger">Failure</span>`;
                    break;
                case "cancelled":
                    status = `<span class="text-warning">Cancelled</span>`;
                    break;
                case "error":
                    status = `<span class="text-warning">Error</span>`;
                    break;
            }
            return status;
        },
        resetDataScanLabel() {
            this.saleOrder = null,
                this.urlLabel = '',
                this.shipmentId = '',
                this.reprint = '',
                this.shipmentRefundId = null
            this.label_id = ''
            this.reprintInsert = ''
            this.$refs.scanLabelId.focus();
        },
        escapeListener(event) {
            if ((event.key === "f8" || event.key === "F8") && this.shipmentId) {
                this.printLabel()
            }
            if (event.key === "f2" || event.key === "F2") {

                if (this.saleOrder?.order_type == this.ORDER_TYPE || this.saleOrder?.tag?.includes('203')) {
                    this.notification("Cannot create shipment label manually for TikTok label orders.", "error");
                    return ;
                }
                if (this.saleOrder?.shipment?.length > 0) {
                    this.openDialogInputPassForCreateLabel()
                } else {
                    if(!this.isVerifyLabel) {
                        this.notification("Label has not been verified. Please verify before creating shipment.", "error");
                    } else {
                        this.creatLabelShipment()
                    }
                }
            }
        },
        async getScanLabelWithFlag() {
            this.$refs.scanLabelId.focus();
            await window.localStorage.removeItem('verify_label');
            this.isVerifyLabel = false
            if (!this.scanEmployeeID()) return;
            this.isLoading = true;
            this.labelIdError = "";
            this.saleOrder = {}
            this.urlLabel = ''
            this.files = {};
            this.shipmentId = ''
            try {
                const res = await getOrderByLabelId({
                    label_id: this.labelFlagId,
                    time_tracking_id: this.id_time_checking
                })
                this.saleOrder = res.data
                this.generateLink()
                if (this.saleOrder?.shipment?.length > 0) {
                    // check store has note
                    if (res.data.store?.has_note || res.data.store.name == this.storeZazzle
                        || res.data.order_type === this.ORDER_TYPE
                        || (this.saleOrder?.shipment[0]?.carrier_code == 'USPS' && ['First', 'FirstClassMailInternational'].includes(this.saleOrder?.shipment[0]?.service_code))) {
                        this.typeFilePrinter = 'pdf';
                    } else {
                        this.loadTypeFileForPrinter();
                    }
                    this.shipmentId = this.saleOrder?.shipment[0]?.id
                    this.reprint = this.saleOrder?.shipment[0]?.shipment_label_printed ? this.saleOrder?.shipment[0]?.shipment_label_printed[0]?.printed_date : false
                    this.isVoidLabel = this.saleOrder?.shipment[0]?.refund_status ? true : false
                    // focus to scan label verify
                    if (this.saleOrder?.total_verify !== this.saleOrder?.total) {
                        EventBus.$emit("focusToScanLabelVerify");
                    }
                    this.selectTabPrinter('Label')
                }
                if (this.saleOrder?.shipment?.length == 0) {
                    EventBus.$emit("focusToScanLabelVerify");
                }
                if (this?.saleOrder?.order_inserts?.length > 0 && this.saleOrder?.shipment?.length == 0) {
                    this.selectTabPrinter(this.types[0])
                }
                this.isLoading = false;
                // this.label_id = ''
            } catch (e) {
                this.saleOrder = null
                const data = e.response.data;
                this.isLoading = false;
                this.label_id = ''
                let message = data.message || this.$t('Something went wrong. Try again.');
                this.notification(message, "error");
            }
        },
        async openDialogInputPassForCreateLabel() {
            this.passwordCreateLabel = null;
            this.isCreateLabelActive = false;
            this.openDialogInputPass = true;
            this.focusByElClassInputPass()
        },
        checkPasswordCreateLabel() {
            let d = new Date();
            let date = d.getDate();
            date = date < 10 ? '0' + date : date;
            if (this.passwordCreateLabel == date) {
                this.isCreateLabelActive = true;
            } else {
                this.notification(this.$t('Password is incorrect!'), "error");
            }
        },
        async loadTypeFileForPrinter() {
            this.typeFilePrinter = await window.localStorage.getItem('filePrinter') ?? 'zpl';
        },
        focusByElClassInputPass(elClass = "el-form-item-input-password") {
            this.$nextTick(() => {
                const el = document.getElementsByClassName(elClass);
                if (!el || !el.length) {
                    return;
                }
                const firtsElError = (el && el[0]) || undefined;
                const input = firtsElError.querySelector("input");
                document.body.scrollTop = (input.offsetTop - 120) | 0;
                input.focus();
            });
        },
        cancelInputPassCreateLabel() {
            this.openDialogInputPass = false
            this.$refs.scanLabelId.focus();
        },
        focusToScanLabel() {
            this.$refs.scanLabelId.focus();
        },
        async prePrintLabel() {
            if (this.tab == this.LABEL) {
                if (!this.printer) {
                    this.notification(this.$t('Please select printer for label'), "error");
                }
                if (this.saleOrder?.total_verify !== this.saleOrder?.total) {
                    this.dialogPrePrintLabel = true
                }
                if (this.printer && this.saleOrder?.total_verify == this.saleOrder?.total) {
                   await this.printLabel(true)
                }
            } else {
                let params = {
                    url: this.files[this.tab].url,
                    printer: this.printer,
                    paper: this.files[this.tab].size,
                    is_auto: false,
                    type: this.files[this.tab].type,
                    file_id: this.files[this.tab].file_id,
                }
                await this.printInsert(params, true)
            }

        },
        exceedsDate(date) {
            const a = moment(new Date(), "YYYY-MM-DD");
            const b = moment(date, "YYYY-MM-DD");
            return a.diff(b, "days"); // =1
        },
        selectTabPrinter(type) {
            this.tab = type
            if (type == 'PS') {
                this.printer = window.localStorage.getItem(`printer_${this.PACKING_SLIP}`);
                this.reprintInsert = this.files.PS ? this.files.PS.printed_at : null;
            }
            if (type == 'TU') {
                this.printer = window.localStorage.getItem(`printer_${this.THANKYOU_CARD}`);
                this.reprintInsert = this.files.TU ? this.files.TU.printed_at : null;
            }
            if (type == 'Label') {
                this.printer = window.localStorage.getItem(`printer_${this.LABEL}`);
                // this.reprint = null
            }
            if (type == 'GM') {
                this.printer = window.localStorage.getItem(`printer_${this.GIFT_MESSAGE}`);
                this.reprintInsert = this.files.GM ? this.files.GM.printed_at : null;
            }
        },

        async showHistory() {
            this.openDialogHistory = true;
            const storedValue = localStorage.getItem('labels');
            if (storedValue !== null) {
                try {
                    const res = await getHistoryPrintLabel({
                        labels: JSON.parse(storedValue)
                    });
                    this.scanHistory = res.data || {};
                } catch (e) {
                    let message = this.$t('Log print label error.');
                    this.notification(message, "error");
                }
            }
        },
        async reprintLabelInHistory(labelID) {

            this.notification(this.$t('Reprint label' + ' ' + labelID), "success");

            const res = await getOrderByLabelId({
                label_id: labelID,
                time_tracking_id: this.id_time_checking
            })
            this.saleOrder = res.data;
            const shipment = res.data.shipment.find(item => item.is_deleted === 0);
            this.printLabelHistory(shipment);
            this.saveLabelIdLocalStorage(labelID);
            this.saleOrder = null;
        },
        async printLabelHistory(item) {
            this.isLoading = true;
            if (!this.printer) {
                this.isLoading = false;
                return this.notification(this.$t('Please select printer for label'), 'error');
            }
            if (item.refund_status) {
                this.notification("The Shipment has been voided", "error");
            } else {
                try {
                    let URL = ''
                    let nameFile = ''
                    if (this.typeFilePrinter === 'pdf') {
                        URL = API_URL + '/label/download/' + item?.id
                        nameFile = item?.id + '.pdf'
                    } else {
                        if (item.label_zpl_url === null) {
                            const resData = await getUrlZLP(item?.id)
                            URL = resData.data
                            nameFile = URL.substring(URL.lastIndexOf('/') + 1)
                        } else {
                            URL = item.label_zpl_url
                            nameFile = URL.substring(URL.lastIndexOf('/') + 1)
                        }
                    }
                    const res = await print({ url: URL, file: nameFile, printer: this.printer, width: 4, height: 6 })
                    const data = this.logPrintedLabelShipment(item?.id)
                    window.localStorage.setItem(`printer_${this.LABEL}`, this.printer);
                    if (!this.saleOrder?.store?.has_note && this.saleOrder?.store?.name != this.storeZazzle && this.saleOrder?.order_type !== this.ORDER_TYPE) {
                        window.localStorage.setItem('filePrinter', this.typeFilePrinter);
                    }
                    this.isLoading = false;
                    this.notification(this.$t('Print label shipping success'), "success");
                } catch (e) {
                    this.isLoading = false;
                    this.saleOrder = null
                    this.notification(this.$t(e.response.data.status), "error");
                }
            }
        },
        async fetchLivePerformance() {
            try {
                const resPerformance = await livePerformance({
                    employee_id: this.employee.id,
                    job_type: this.job_type,
                    department_id: this.department,
                    time_tracking_id: this.id_time_checking,
                    warehouse_id: this.warehouse_id,
                });
                if (resPerformance.data) {
                    const performance = resPerformance.data;
                    this.avg_item_by_hour = performance.average_user;
                    this.ave_item_current_year = performance.average_current_year;
                    this.avg_item_last_year = performance.average_last_year;
                    this.performanceHistory = performance;
                    if (performance.age) {
                        this.age = performance.age
                    }
                    this.is15Minutes = true;
                    this.countPerformanceReport++;
                }
            } catch (e) {
                console.error('Failed to fetch live performance:', e);
            }
        },
        startPerformanceInterval() {
            this.clearPerformanceInterval();
            this.performanceInterval = setInterval(async () => {
                if (this.employee) {
                    await this.fetchLivePerformance();
                }
            }, 15 * 60 * 1000); // 5 minutes
        },
        clearPerformanceInterval() {
            if (this.performanceInterval) {
                clearInterval(this.performanceInterval);
                this.performanceInterval = null;
            }
        },
        toggleCollapse() {
            this.isCollapsed = !this.isCollapsed;
        },
        convertItem(data) {
            if (data > 1) {
                return data + ' items/hour'
            } else {
                return data + ' item/hour'
            }
        },
        convertOutPut(data) {
            if (data > 1) {
                return data + ' items'
            } else {
                return data + ' item'
            }
        },
        openDialogItemShipment(data){
            this.dataItemShipmentLabel = data;
            this.dialogItemShipment = true;
        },
        async logPrintInsert(orderInsertId) {
            try {
                await logPrintedSuccess({
                    order_insert_id: orderInsertId,
                    employee_id: this.employee.id,
                });
            } catch (e) {
                let message = this.$t('Log print insert error.');
                this.notification(message, "error");
            }
        }
    },
};
