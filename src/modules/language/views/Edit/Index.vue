<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
      v-model="openDialogEditLanguage"
      :title="$t('Edit language')"
      custom-class="el-dialog-custom el-dialog-language"
      @close="resetData"
      :close-on-click-modal="false"
    >
      <template #default>
        <div class="add-language">
          <el-form
            status-icon
            ref="editLanguageForm"
            :model="language"
            :rules="languageRules"
            @submit.prevent="onSubmit('editLanguageForm')"
            label-width="130px"
            :label-position="'left'"
          >
            <el-form-item :label="$t('Key')" prop="key">
              <el-input v-model="language.key"></el-input>
            </el-form-item>
            <el-form-item :label="$t('EN')" prop="en">
              <el-input v-model="language.en"></el-input>
            </el-form-item>
            <el-form-item :label="$t('VI')" prop="vi">
              <el-input v-model="language.vi"></el-input>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            @click="onSubmit('editLanguageForm')"
            :disabled="isLoading"
            :loading="isLoading"
            >{{ $t('Update') }}</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>
