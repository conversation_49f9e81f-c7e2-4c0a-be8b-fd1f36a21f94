export default [
    {
        path: "/insights/late-orders",
        name: "looker_studio_late_orders",
        component: () => import("./views/LateOrders/Index.vue"),
    },
    {
        path: "/insights/fulfillment-time/tiktok-orders",
        name: "looker_studio_tiktok_orders_fulfillment_time",
        component: () => import("./views/TiktokOrdersFulfillmentTime/Index.vue"),
    },
    {
        path: "/insights/fulfillment-time/label-orders",
        name: "looker_studio_label_orders_fulfillment_time",
        component: () => import("./views/LabelOrdersFulfillmentTime/Index.vue"),
    },
    {
        path: "/insights/offender-department",
        name: "looker_studio_offender_department",
        component: () => import("./views/OffenderDepartment/Index.vue"),
    },
    {
        path: "/looker-studio/easypost-adjustment",
        name: "looker_studio_easypost_adjustment",
        component: () => import("./views/EasypostAdjustment/Index.vue"),
    },
    {
        path: "/insights/missing-scanned-wip",
        name: "missing_scanned_wip",
        component: () => import("./views/MissingScannedWIP/Index.vue"),
    },
];
