<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
    <div>
        <div class="top-head mb-4">
            <div class="top-head-left">
                <h1>{{ $t('Easypost Adjustment') }}</h1>
            </div>
        </div>
        <el-form>
            <div class="flex gap-3 px-5 filter-custom" style="align-items: start;">
                <el-form-item>
                    <el-select class="w-full" v-model="filters.warehouse_id" @change="handleFilterChange()"
                        placeholder="All Warehouse" clearable multiple>
                        <el-option v-for="item in getWarehouses" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <div>
                    <el-form-item>
                        <el-date-picker v-model="filters.daterange" @change="handleFilterChange()" type="daterange"
                            range-separator="To" start-placeholder="Start date" clearable end-placeholder="End date">
                        </el-date-picker>
                    </el-form-item>
                    <div class="compare-label">{{ compareLabel }}</div>
                </div>
                <el-form-item>
                    <el-button type="danger" plain @click="onReset()">Reset</el-button>
                </el-form-item>
            </div>
        </el-form>
        <el-row :gutter="80" class="px-5 mt-5">
            <el-col :span="24" class="pb-2">
                <h3>Overview</h3>
            </el-col>
            <el-col :span="24">
                <EasypostAdjustmentOverview />
            </el-col>
        </el-row>

        <el-row :gutter="80" class="px-5 mt-10">
            <el-col :span="14">
                <h3>Adjustments by reason and warehouse</h3>
                <EasypostAdjustmentOverviewChart />
            </el-col>
            <el-col :span="10">
                <h3>Adjustments by reason</h3>
                <EasypostAdjustmentOverviewTable />
            </el-col>
        </el-row>

        <el-row class="px-5 mt-2">
            <el-col :span="24">
                <h3>Adjustments by Order type - Overview</h3>
                <EasypostAdjustmentOverviewDetail />
            </el-col>
        </el-row>

        <el-row class="px-5 mt-10">
            <el-col :span="24">
                <EasypostAdjustmentOverviewOrderTypeChart />
            </el-col>
        </el-row>

        <el-row :gutter="80" class="px-5 mt-10">
            <el-col :span="14">
                <h3>Adjustments by Reason and Size</h3>
                <EasypostAdjustmentSizeChart />
            </el-col>
            <el-col :span="10">
                <h3>Adjustment by Size</h3>
                <EasypostAdjustmentSizeTable />
            </el-col>
        </el-row>

        <el-row :gutter="80" class="px-5 mt-10">
            <el-col :span="14">
                <h3>Adjustments by Reason and Style</h3>
                <EasypostAdjustmentStyleChart />
            </el-col>
            <el-col :span="10">
                <h3>Adjustment by Style</h3>
                <EasypostAdjustmentStyleTable />
            </el-col>
        </el-row>
        <el-row :gutter="80" class="px-5 mt-10">
            <el-col :span="24">
                <EasypostAdjustmentExport />
            </el-col>
        </el-row>
    </div>
</template>

<style scoped>
::v-deep .filter-custom .el-form-item {
    margin-bottom: 0;
}

::v-deep .filter-custom .compare-label {
    opacity: 0.8;
    font-style: italic;
    color: gray;
}
</style>