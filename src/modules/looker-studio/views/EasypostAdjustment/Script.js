import EasypostAdjustmentOverview from "./_partials/EasypostAdjustmentOverview.vue";
import EasypostAdjustmentOverviewChart from "./_partials/EasypostAdjustmentOverviewChart.vue";
import EasypostAdjustmentOverviewTable from "./_partials/EasypostAdjustmentOverviewTable.vue";
import EasypostAdjustmentOverviewDetail from "./_partials/EasypostAdjustmentOverviewDetail.vue";
import EasypostAdjustmentOverviewOrderTypeChart from "./_partials/EasypostAdjustmentOverviewOrderTypeChart.vue";
import EasypostAdjustmentSizeChart from "./_partials/EasypostAdjustmentSizeChart.vue";
import EasypostAdjustmentSizeTable from "./_partials/EasypostAdjustmentSizeTable.vue";
import EasypostAdjustmentStyleChart from "./_partials/EasypostAdjustmentStyleChart.vue";
import EasypostAdjustmentStyleTable from "./_partials/EasypostAdjustmentStyleTable.vue";
import EasypostAdjustmentExport from "./_partials/EasypostAdjustmentExport.vue";
import EventBus from "@/utilities/eventBus.js";
import { mapGetters } from 'vuex';
import moment from "moment";

export default {
    name: "LookerStudioEsypostAdjustment",

    components: {
        EasypostAdjustmentOverview,
        EasypostAdjustmentOverviewChart,
        EasypostAdjustmentOverviewTable,
        EasypostAdjustmentOverviewDetail,
        EasypostAdjustmentOverviewOrderTypeChart,
        EasypostAdjustmentSizeChart,
        EasypostAdjustmentSizeTable,
        EasypostAdjustmentStyleChart,
        EasypostAdjustmentStyleTable,
        EasypostAdjustmentExport,
    },

    data() {
        return {
            filters: {},
        }
    },

    computed: {
        ...mapGetters([
            'getWarehouses',
        ]),
        
        compareLabel() {
            let startDate = this.filters?.daterange?.[0];
            let endDate = this.filters?.daterange?.[1];

            if (startDate && endDate) {
                let start = moment(startDate);
                let end = moment(endDate);

                if (start.clone().startOf('month').format('YYYYMMDD') === start.format('YYYYMMDD') &&
                    end.clone().endOf('month').format('YYYYMMDD') === end.format('YYYYMMDD')) {
                    start = end.clone().subtract(1, 'months').startOf('month').format('YYYY-MM-DD');
                    end = end.clone().subtract(1, 'months').endOf('month').format('YYYY-MM-DD');
                } else {
                    let days = moment(endDate).diff(moment(startDate), 'days');
                    start = moment(startDate).subtract(1, 'days').subtract(days, 'days').format('YYYY-MM-DD');
                    end = moment(startDate).subtract(1, 'days').format('YYYY-MM-DD');
                }

                return `Compared to: ${start} to ${end}`;
            }

            return '';
        }
    },

    created() {
        this.initFilter();
        this.$store.dispatch('getWarehouses');
    },

    methods: {
        handleFilterChange() {
            let daterange = this.filters?.daterange || [];

            if (!daterange.length) {
                return;
            }

            this.debounceFilter({
                warehouse_id: this.filters.warehouse_id,
                start: moment(daterange[0]).format('YYYY-MM-DD'),
                end: moment(daterange[1]).format('YYYY-MM-DD'),
            });
        },

        initFilter() {
            this.filters = {
                warehouse_id: "",
                daterange: [
                    moment().subtract(1, 'months').startOf('month'), 
                    moment().subtract(1, 'months').endOf('month')
                ],
            }
        },

        onReset() {
            this.initFilter();
            this.handleFilterChange();
        },

        debounceFilter(params) {
            clearTimeout(this.emitFnFilter);
            this.emitFnFilter = setTimeout(() => EventBus.$emit("handleFilterChange", params), 1000);
        }
    },

    beforeUnmount() {
        EventBus.$off("handleFilterChange");
    },
}
