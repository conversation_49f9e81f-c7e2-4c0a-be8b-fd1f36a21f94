<template>
    <div class="mb-10">
        <el-row :gutter="20">
            <el-col :span="24">
                <h3>Export Data</h3>
            </el-col>
            <el-col :span="6">
                <el-input v-model="trackingNumber" size="large" @clear="loadData" @keydown.enter="loadData"
                    placeholder="Tracking Number" clearable class="input-filter">
                </el-input>
            </el-col>
            <el-col :span="6">
                <el-input v-model="orderNumber" size="large" @clear="loadData" @keydown.enter="loadData"
                    placeholder="Order Number" clearable class="input-filter">
                </el-input>
            </el-col>            
            <el-col :span="12" style="display: flex; align-items: end; justify-content: right;">
                <el-button :loading="exporting" type="primary" plain @click="handleBtnExportClick()">Export</el-button>
            </el-col>
        </el-row>

        <el-card shadow="none" class="mt-2">
            <el-table ref="filterTable" :data="tableData" max-height="630" stripe style="width: 100%" v-loading="isLoading"
                header-cell-class-name="td-header">
                <el-table-column prop="warehouse_code" label="Warehouse" min-width="100">
                </el-table-column>
                <el-table-column prop="invoice_date" label="Invoice Date" min-width="120">
                </el-table-column>
                <el-table-column prop="tracking_number" label="Tracking Number" min-width="250">
                </el-table-column>
                <el-table-column prop="zip" label="Zip Code" min-width="100">
                </el-table-column>
                <el-table-column prop="country" label="Country">
                </el-table-column>
                <el-table-column prop="claimed_service" label="Claimed Service" min-width="150">
                </el-table-column>
                <el-table-column prop="claimed_package" label="Claimed Package" min-width="90">
                </el-table-column>
                <el-table-column prop="claimed_width" label="Claimed Width" min-width="90">
                </el-table-column>
                <el-table-column prop="claimed_height" label="Claimed Height" min-width="90">
                </el-table-column>
                <el-table-column prop="claimed_length" label="Claimed Length" min-width="90">
                </el-table-column>
                <el-table-column prop="claimed_weight" label="Claimed Weight" min-width="90">
                </el-table-column>
                <el-table-column prop="captured_service" label="Captured Service" min-width="160">
                </el-table-column>
                <el-table-column prop="captured_package" label="Captured Package" min-width="90">
                </el-table-column>
                <el-table-column prop="captured_width" label="Captured Width" min-width="90">
                </el-table-column>
                <el-table-column prop="captured_height" label="Captured Height" min-width="90">
                </el-table-column>
                <el-table-column prop="captured_length" label="Captured Length" min-width="90">
                </el-table-column>
                <el-table-column prop="captured_weight" label="Captured Weight" min-width="90">
                </el-table-column>
                <el-table-column prop="initially_paid_amount" label="Initially Paid Amount" min-width="90">
                </el-table-column>
                <el-table-column prop="adjustment_amount" label="Adjustment Amount" min-width="110">
                </el-table-column>
                <el-table-column prop="final_invoice_amount" label="Final Invoice Amount" min-width="90">
                </el-table-column>
                <el-table-column prop="adjustment_reason" label="Adjustment Reason" min-width="110">
                </el-table-column>
                <el-table-column prop="order_number" label="Order Number" min-width="160">
                </el-table-column>
                <el-table-column prop="type" label="Type" min-width="115">
                </el-table-column>
                <el-table-column prop="product_sku" label="Product SKU" min-width="120">
                </el-table-column>
                <el-table-column prop="product_style" label="Style" min-width="120">
                </el-table-column>
                <el-table-column prop="product_color" label="Color" min-width="120">
                </el-table-column>
                <el-table-column prop="product_size" label="Size" min-width="120">
                </el-table-column>
                <el-table-column prop="quantity" label="Quantity" min-width="90">
                </el-table-column>
            </el-table>
            <div style="display: flex; justify-content: space-between;" class="mt-3">
                <el-pagination :disabled="isLoading" background layout="prev, pager, next" :total="total"
                    v-model:currentPage="dataFilters.page" :page-size="dataFilters.limit" @current-change="loadData">
                </el-pagination>
                <el-select :disabled="isLoading" v-model="dataFilters.limit" :placeholder="$t('Select')" size="mini"
                    style="width: 200px;">
                    <el-option v-for="item in limits" :key="item.value" :label="item.label" :value="item.value"
                        @change="loadData">
                    </el-option>
                </el-select>
            </div>
        </el-card>
    </div>
</template>

<script>
import { exportExport, getDataTable } from "@/api/lookerStudioEasypostAdjustmentExport";
import EventBus from "@/utilities/eventBus.js";
import moment from 'moment';

export default {
    name: "EasypostAdjustmentExport",

    data() {
        return {
            lastFilter: 0,
            tableData: [],
            isLoading: false,
            exporting: false,
            total: 0,
            trackingNumber: "",
            orderNumber: "",
            dataFilters: {
                page: 1,
                limit: 25,
                warehouse_id: "",
                start: moment().subtract(1, 'months').startOf('month').format('YYYY-MM-DD'),
                end: moment().subtract(1, 'months').endOf('month').format('YYYY-MM-DD'),
            },
        }
    },

    async created() {
        this.loadData();
        EventBus.$on("handleFilterChange", (dataFilters) => {
            this.dataFilters = {
                page: 1,
                limit: 25,
                ...dataFilters
            };
            this.loadData();
        });
    },
    methods: {
        async loadData() {
            const currentTime = moment().valueOf();
            this.lastFilter = currentTime;
            this.isLoading = true;
            this.series = [];
            let filters = {
                ...this.dataFilters,
                'tracking_number': this.trackingNumber,
                'order_number': this.orderNumber,
            }
            const response = await getDataTable(filters);

            if (this.lastFilter != currentTime) { return; }

            let pagination = response.data?.data;
            this.tableData = pagination?.data || [];
            this.total = pagination?.total || 0;
            this.isLoading = false;
        },

        async handleBtnExportClick() {
            this.exporting = true;
            let filters = {
                ...this.dataFilters,
                'tracking_number': this.trackingNumber,
                'order_number': this.orderNumber,
            }
            const response = await exportExport(filters);
            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', `swiftpod_report_adjustment_${moment().format('MMDDYYYY')}.xlsx`);
            document.body.appendChild(link);
            link.click();
            this.exporting = false;
        }
    }
}
</script>

<style scoped>
::v-deep .cell {
    word-break: break-word;
}

::v-deep .el-button svg {
    margin: 0;
    top: 0;
}
</style>
