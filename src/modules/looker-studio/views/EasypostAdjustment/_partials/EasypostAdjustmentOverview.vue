<template>
    <el-row :gutter="50">
        <el-col :span="7">
            <el-card shadow="always" v-loading="isLoading">
                <div>Adjustment Amount</div>
                <div class="point">$ {{ formatNumberWithCommas(adjustmentTotalAmount) }}</div>
                <div class="rate" v-if="adjustmentTotalAmountRate"
                    :class="{ 'text-success': adjustmentTotalAmountRate > 0, 'text-danger': adjustmentTotalAmountRate < 0 }">
                    <icon v-if="adjustmentTotalAmountRate != 0" class="pb-1"
                        :data="adjustmentTotalAmountRate > 0 ? iconArrowUp : iconArrowDown" />
                    <span class="ml-1">{{ `${Math.abs(adjustmentTotalAmountRate)}%` }}</span>
                </div>
            </el-card>
        </el-col>
        <el-col :span="5">
            <el-card shadow="always" v-loading="isLoading">
                <div>Adjusted Shipments</div>
                <div class="point">{{ formatNumberWithCommas(adjustmentCount) }}</div>
                <div class="rate" v-if="adjustmentCountRate"
                    :class="{ 'text-success': adjustmentCountRate > 0, 'text-danger': adjustmentCountRate < 0 }">
                    <icon v-if="adjustmentCountRate != 0" class="pb-1"
                        :data="adjustmentCountRate > 0 ? iconArrowUp : iconArrowDown" />
                    <span class="ml-1">{{ `${Math.abs(adjustmentCountRate)}%` }}</span>
                </div>
            </el-card>
        </el-col>
        <el-col :span="5">
            <el-card shadow="always" v-loading="isLoading">
                <div>Total Shipment</div>
                <div class="point">{{ formatNumberWithCommas(shipmentCount) }}</div>
                <div class="rate" v-if="shipmentCountRate"
                    :class="{ 'text-success': shipmentCountRate > 0, 'text-danger': shipmentCountRate < 0 }">
                    <icon v-if="shipmentCountRate != 0" class="pb-1"
                        :data="shipmentCountRate > 0 ? iconArrowUp : iconArrowDown" />
                    <span class="ml-1">{{ `${Math.abs(shipmentCountRate)}%` }}</span>
                </div>
            </el-card>
        </el-col>
        <el-col :span="7">
            <el-card shadow="always" v-loading="isLoading">
                <div>Adjusted Shipments / Total Shipment</div>
                <div class="point">{{ adjustedShipmentRate }}%</div>
                <div class="rate" v-if="adjustedShipmentWithPreviousMonthRate"
                    :class="{ 'text-success': adjustedShipmentWithPreviousMonthRate > 0, 'text-danger': adjustedShipmentWithPreviousMonthRate < 0 }">
                    <icon v-if="adjustedShipmentWithPreviousMonthRate != 0" class="pb-1"
                        :data="adjustedShipmentWithPreviousMonthRate > 0 ? iconArrowUp : iconArrowDown" />
                    <span class="ml-1">{{ `${Math.abs(adjustedShipmentWithPreviousMonthRate)}%` }}</span>
                </div>
            </el-card>
        </el-col>
    </el-row>
</template>

<script>
import { getOverview, getTotalShipment } from "@/api/lookerStudioEasypostAdjustment";
import EventBus from "@/utilities/eventBus.js";
import moment from 'moment';

export default {
    name: "EasypostAdjustmentOverview",
    data() {
        return {
            lastFilter: 0,
            isLoading: false,
            adjustmentCount: 0,
            adjustmentTotalAmount: 0,
            shipmentCount: 0,
            adjustmentCountPreviousPeriod: 0,
            adjustmentTotalAmountPreviousPeriod: 0,
            shipmentCountPreviousPeriod: 0,
            dataFilters: {
                warehouse_id: "",
                start: moment().subtract(1, 'months').startOf('month').format('YYYY-MM-DD'),
                end: moment().subtract(1, 'months').endOf('month').format('YYYY-MM-DD'),
            },
        }
    },

    computed: {
        adjustedShipmentRate() {
            if (!this.shipmentCount) {
                return 0;
            }

            let rate = this.adjustmentCount / this.shipmentCount * 100;

            return rate ? rate.toFixed(1) : 0;
        },

        adjustedShipmentPreviousMonthRate() {
            if (!this.shipmentCountPreviousPeriod) {
                return 0;
            }

            let rate = this.adjustmentCountPreviousPeriod / this.shipmentCountPreviousPeriod * 100;

            return rate ? rate.toFixed(1) : 0;
        },

        adjustmentCountRate() {
            if (!this.adjustmentCountPreviousPeriod) {
                return 0;
            }

            let rate = (this.adjustmentCount - this.adjustmentCountPreviousPeriod) / this.adjustmentCountPreviousPeriod * 100;

            return rate ? Number(rate.toFixed(1)) : 0;
        },

        adjustmentTotalAmountRate() {
            if (!this.adjustmentTotalAmountPreviousPeriod) {
                return 0;
            }

            let rate = (this.adjustmentTotalAmount - this.adjustmentTotalAmountPreviousPeriod) / this.adjustmentTotalAmountPreviousPeriod * 100;

            return rate ? rate.toFixed(1) : 0;
        },

        adjustedShipmentWithPreviousMonthRate() {
            let rate = this.adjustedShipmentRate - this.adjustedShipmentPreviousMonthRate;

            return rate ? rate.toFixed(1) : 0;
        },

        shipmentCountRate() {
            if (!this.shipmentCountPreviousPeriod) {
                return 0;
            }

            let rate = (this.shipmentCount - this.shipmentCountPreviousPeriod) / this.shipmentCountPreviousPeriod * 100;

            return rate ? rate.toFixed(1) : 0;
        },
    },

    async created() {
        this.loadData();
        EventBus.$on("handleFilterChange", (params) => {
            this.dataFilters = { ...params };
            this.loadData();
        });
    },

    methods: {
        async loadData() {
            const currentTime = moment().valueOf();
            this.lastFilter = currentTime;
            this.isLoading = true;
            let filterPrevious = { ...this.dataFilters, previous: true };
            let getOverviewPending = getOverview(this.dataFilters);
            let getTotalShipmentPending = getTotalShipment(this.dataFilters);
            let getTotalShipmentPendingPrevious = getTotalShipment(filterPrevious);
            let resOverview = await getOverviewPending;
            let resTotalShipment = await getTotalShipmentPending;
            let resTotalShipmentPrevious = await getTotalShipmentPendingPrevious;

            if (this.lastFilter != currentTime) { return; }

            let overviewData = resOverview?.data || {};
            let shipmentData = resTotalShipment?.data || {};
            let shipmentDataPrevious = resTotalShipmentPrevious?.data || {};
            this.adjustmentCount = overviewData?.data?.adjustment_count || 0
            this.adjustmentTotalAmount = overviewData?.data?.adjustment_total_amount || 0
            this.shipmentCount = shipmentData?.data?.shipment_count || 0
            this.adjustmentCountPreviousPeriod = overviewData?.previous?.data?.adjustment_count || 0
            this.adjustmentTotalAmountPreviousPeriod = overviewData?.previous?.data?.adjustment_total_amount || 0
            this.shipmentCountPreviousPeriod = shipmentDataPrevious?.data?.shipment_count || 0
            this.isLoading = false;
        },

        formatNumberWithCommas(number) {
            return Number(number).toLocaleString();
        },
    }
}
</script>

<style scoped lang="scss">
.point {
    font-size: 32px;
}

.rate {
    font-weight: bold;
}

::v-deep .el-card__body {
    min-height: 110px;
}
</style>