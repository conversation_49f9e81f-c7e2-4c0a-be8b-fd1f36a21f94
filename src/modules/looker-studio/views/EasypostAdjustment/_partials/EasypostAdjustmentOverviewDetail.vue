<template>
    <el-row :gutter="30">
        <el-col :span="12">
            <h4>Shipments of Single order</h4>
            <el-card shadow="always" class="wrap-box left" v-loading="isLoading">
                <el-row>
                    <el-col :span="8">
                        <div class="text-center">Adjusted</div>
                    </el-col>
                    <el-col :span="8">
                        <div class="text-center">Total</div>
                    </el-col>
                    <el-col :span="8">
                        <div class="text-center">
                            Adjusted / Total
                        </div>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <div class="point text-center">
                            {{ formatNumberWithCommas(adjustment?.single_order || 0) }}
                        </div>
                        <div class="rate text-center"
                            :class="{ 'text-success': singleOrderAdjustmentCompared > 0, 'text-danger': singleOrderAdjustmentCompared < 0 }">
                            <icon v-if="singleOrderAdjustmentCompared" class="pb-1"
                                :data="singleOrderAdjustmentCompared > 0 ? iconArrowUp : iconArrowDown" />
                            <span class="ml-1">{{ `${Math.abs(singleOrderAdjustmentCompared)}%` }}</span>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="point text-center">
                            {{ formatNumberWithCommas(shipment?.single_order || 0) }}
                        </div>
                        <div class="rate text-center"
                            :class="{ 'text-success': singleOrderShipmentCompared > 0, 'text-danger': singleOrderShipmentCompared < 0 }">
                            <icon v-if="singleOrderShipmentCompared" class="pb-1"
                                :data="singleOrderShipmentCompared > 0 ? iconArrowUp : iconArrowDown" />
                            <span class="ml-1">{{ `${Math.abs(singleOrderShipmentCompared)}%` }}</span>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="point text-center">{{ `${singleOrderRate}%` }}</div>
                        <div class="rate text-center"
                            :class="{ 'text-success': singleOrderPreviousRateCompared > 0, 'text-danger': singleOrderPreviousRateCompared < 0 }">
                            <icon v-if="singleOrderPreviousRateCompared" class="pb-1"
                                :data="singleOrderPreviousRateCompared > 0 ? iconArrowUp : iconArrowDown" />
                            <span class="ml-1">{{ `${Math.abs(singleOrderPreviousRateCompared)}%` }}</span>
                        </div>
                    </el-col>
                </el-row>
            </el-card>
        </el-col>

        <el-col :span="12">
            <h4>Shipments of Multiple order</h4>
            <el-card shadow="always" class="wrap-box right" v-loading="isLoading">
                <el-row>
                    <el-col :span="8">
                        <div class="text-center">Adjusted</div>
                    </el-col>
                    <el-col :span="8">
                        <div class="text-center">Total</div>
                    </el-col>
                    <el-col :span="8">
                        <div class="text-center">
                            Adjusted / Total
                        </div>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <div class="point text-center">
                            {{ formatNumberWithCommas(adjustment?.multiple_order || 0) }}
                        </div>
                        <div class="rate text-center"
                            :class="{ 'text-success': multipleOrderAdjustmentCompared > 0, 'text-danger': multipleOrderAdjustmentCompared < 0 }">
                            <icon v-if="multipleOrderAdjustmentCompared" class="pb-1"
                                :data="multipleOrderAdjustmentCompared > 0 ? iconArrowUp : iconArrowDown" />
                            <span class="ml-1">{{ `${Math.abs(multipleOrderAdjustmentCompared)}%` }}</span>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="point text-center">
                            {{ formatNumberWithCommas(shipment?.multiple_order || 0) }}
                        </div>
                        <div class="rate text-center"
                            :class="{ 'text-success': multipleOrderShipmentCompared > 0, 'text-danger': multipleOrderShipmentCompared < 0 }">
                            <icon v-if="multipleOrderShipmentCompared" class="pb-1"
                                :data="multipleOrderShipmentCompared > 0 ? iconArrowUp : iconArrowDown" />
                            <span class="ml-1">{{ `${Math.abs(multipleOrderShipmentCompared)}%` }}</span>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="point text-center">{{ `${multipleOrderRate}%` }}</div>
                        <div class="rate text-center"
                            :class="{ 'text-success': multipleOrderPreviousRateCompared > 0, 'text-danger': multipleOrderPreviousRateCompared < 0 }">
                            <icon v-if="multipleOrderPreviousRateCompared" class="pb-1"
                                :data="multipleOrderPreviousRateCompared > 0 ? iconArrowUp : iconArrowDown" />
                            <span class="ml-1">{{ `${Math.abs(multipleOrderPreviousRateCompared)}%` }}</span>
                        </div>
                    </el-col>
                </el-row>
            </el-card>
        </el-col>
    </el-row>
</template>

<script>
import { getDataByType, getDataShipmentByType } from "@/api/lookerStudioEasypostAdjustment";
import EventBus from "@/utilities/eventBus.js";
import moment from 'moment';

export default {
    name: "EasypostAdjustmentOverviewDetail",

    data() {
        return {
            lastFilter: 0,
            tableData: [],
            isLoading: false,
            dataFilters: {
                warehouse_id: "",
                start: moment().subtract(1, 'months').startOf('month').format('YYYY-MM-DD'),
                end: moment().subtract(1, 'months').endOf('month').format('YYYY-MM-DD'),
            },
            adjustment: {},
            shipment: {},
            adjustmentPrevious: {},
            shipmentPrevious: {},
        }
    },

    computed: {
        singleOrderRate() {
            if (this.adjustment.single_order && this.shipment.single_order) {
                let rate = this.adjustment.single_order / this.shipment.single_order * 100;

                return Number(rate.toFixed(1));
            }

            return 0;
        },

        multipleOrderRate() {
            if (this.adjustment.multiple_order && this.shipment.multiple_order) {
                let rate = this.adjustment.multiple_order / this.shipment.multiple_order * 100;

                return Number(rate.toFixed(1));
            }

            return 0;
        },

        singleOrderAdjustmentCompared() {
            let adjustmentPrevious = this.adjustmentPrevious?.single_order || 0;

            return Number((this.adjustment.single_order - adjustmentPrevious) / (adjustmentPrevious || 1) * 100).toFixed(1);
        },

        multipleOrderAdjustmentCompared() {
            let adjustmentPrevious = this.adjustmentPrevious.multiple_order || 0;

            return Number((this.adjustment.multiple_order - adjustmentPrevious) / (adjustmentPrevious || 1) * 100).toFixed(1);
        },

        singleOrderShipmentCompared() {
            let shipmentPrevious = this.shipmentPrevious?.single_order || 0;

            return Number((this.shipment.single_order - shipmentPrevious) / (shipmentPrevious || 1) * 100).toFixed(1);
        },

        multipleOrderShipmentCompared() {
            let shipmentPrevious = this.shipmentPrevious?.multiple_order || 0;

            return Number((this.shipment.multiple_order - shipmentPrevious) / (shipmentPrevious || 1) * 100).toFixed(1);
        },

        singleOrderPreviousRate() {
            if (this.adjustmentPrevious?.single_order && this.shipmentPrevious?.single_order) {
                let rate = this.adjustmentPrevious.single_order / this.shipmentPrevious.single_order * 100;

                return Number(rate.toFixed(1));
            }

            return 0;
        },

        multipleOrderPreviousRate() {
            if (this.adjustmentPrevious.multiple_order && this.shipmentPrevious.multiple_order) {
                let rate = this.adjustmentPrevious.multiple_order / this.shipmentPrevious.multiple_order * 100;

                return Number(rate.toFixed(1));
            }

            return 0;
        },

        singleOrderPreviousRateCompared() {
            let rate = this.singleOrderRate - this.singleOrderPreviousRate;

            return rate ? Number(rate.toFixed(1)) : 0;
        },

        multipleOrderPreviousRateCompared() {
            let rate = this.multipleOrderRate - this.multipleOrderPreviousRate;

            return rate ? Number(rate.toFixed(1)) : 0;
        },
    },

    async created() {
        this.loadData();
        EventBus.$on("handleFilterChange", (dataFilters) => {
            this.dataFilters = dataFilters;
            this.loadData();
        });
    },

    methods: {
        async loadData() {
            const currentTime = moment().valueOf();
            this.lastFilter = currentTime;
            this.isLoading = true;
            let filterPrevious = {...this.dataFilters, previous: true};
            let adjustmentPending = getDataByType(this.dataFilters);
            let shipmentPending = getDataShipmentByType(this.dataFilters);
            let shipmentPrevious = getDataShipmentByType(filterPrevious);
            let adjustmentResponse = (await adjustmentPending)?.data;
            let shipmentResponse = (await shipmentPending)?.data;
            let shipmentPreviousResponse = (await shipmentPrevious)?.data;

            if (this.lastFilter != currentTime) { return; }

            this.adjustment = adjustmentResponse?.data || {};
            this.shipment = shipmentResponse?.data || {};
            this.adjustmentPrevious = adjustmentResponse?.previous?.data || {};
            this.shipmentPrevious = shipmentPreviousResponse?.data || {};
            this.isLoading = false;
        },

        formatNumberWithCommas(number) {
            return Number(number).toLocaleString();
        },
    }
}
</script>

<style scoped lang="scss">
.point {
    font-size: 32px;
}

.wrap-box {
    min-height: 120px;
    border-radius: 10px;

    &.left {
        background-color: #FFECB3;
    }

    &.right {
        background-color: #F0F4C3;
    }
}
</style>
