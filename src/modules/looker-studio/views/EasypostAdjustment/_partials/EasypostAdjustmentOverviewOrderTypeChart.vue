<template>
    <el-row :gutter="80" class="px-5 mt-5">
        <el-col :span="12" class="pb-2">
            <h3>Adjustments by Order type and Reason</h3>
            <div v-loading="isLoading" style="min-height: 350px;">
                <apexchart v-if="series.length" type="bar" height="350" :options="chartOptions" :series="series">
                </apexchart>
                <div v-else class="text-center pt-10">No Data</div>
            </div>
        </el-col>
        <el-col :span="12">
            <h3>Adjustments by Order type</h3>
            <div v-loading="isLoading"
                style="min-height: 350px; display: flex; justify-content: center; align-items: center;">
                <apexchart v-if="pieSeries.length" type="pie" width="350" :options="pieChartOptions"
                    :series="pieSeries"></apexchart>
                <div v-else class="text-center pt-10">No Data</div>
            </div>
        </el-col>
    </el-row>
</template>

<script>
import { getDataByOrderType } from "@/api/lookerStudioEasypostAdjustment";
import EventBus from "@/utilities/eventBus.js";
import moment from 'moment';
import VueApexCharts from 'vue3-apexcharts';

export default {
    name: "EasypostAdjustmentOverviewOrderTypeChart",

    components: { apexchart: VueApexCharts },

    data() {
        return {
            lastFilter: 0,
            series: [],
            chartOptions: {
                chart: {
                    type: 'bar',
                    height: 350,
                    stacked: true,
                    toolbar: {
                        show: false
                    },
                },
                responsive: [
                    {
                        breakpoint: 480,
                        options: {
                            legend: {
                                position: 'bottom',
                                offsetX: -10,
                                offsetY: 0
                            }
                        }
                    }
                ],
                plotOptions: {
                    bar: {
                        horizontal: false,
                        borderRadius: 3,
                        dataLabels: {
                            total: {
                                enabled: true,
                                style: {
                                    fontSize: '13px',
                                }
                            }
                        },
                    },
                },
                xaxis: {
                    type: 'string',
                    categories: [],
                },
                yaxis: {
                    labels: {
                        formatter: function (val) {
                            return Number(val).toLocaleString();
                        }
                    },
                },
                legend: {
                    position: 'top',
                },
                fill: {
                    opacity: 1
                },
                tooltip: {
                    shared: true,
                    intersect: false,
                    y: {
                        formatter: function (value) {
                            return Number(value).toLocaleString();
                        }
                    },
                },
                dataLabels: {
                    formatter: function (val) {
                        return Number(val).toLocaleString();
                    },
                },
            },
            pieSeries: [],
            pieChartOptions: {
                chart: {
                    type: 'pie',
                },
                labels: [],
                stroke: {
                    show: false,
                },
                legend: {
                    position: 'bottom',
                },
                tooltip: {
                    y: {
                        formatter: function (value, w) {
                            let total = w?.globals?.series.reduce((a, b) => a + b, 0);
                            let percent = (value / total * 100).toFixed(1);

                            return `${Number(value).toLocaleString()} (${percent}%)`;
                        }
                    }
                },
            },
            dataFilters: {
                warehouse_id: "",
                start: moment().subtract(1, 'months').startOf('month').format('YYYY-MM-DD'),
                end: moment().subtract(1, 'months').endOf('month').format('YYYY-MM-DD'),
            },
            isLoading: false,
        }
    },

    async created() {
        this.loadData();
        EventBus.$on("handleFilterChange", (params) => {
            this.dataFilters = { ...params };
            this.loadData();
        });
    },

    methods: {
        async loadData() {
            const currentTime = moment().valueOf();
            this.lastFilter = currentTime;
            this.isLoading = true;
            this.series = [];
            const response = await getDataByOrderType(this.dataFilters);

            if (this.lastFilter != currentTime) { return; }

            this.series = response.data?.column_chart?.series || [];
            this.chartOptions.xaxis.categories = response.data?.column_chart?.categories || [];
            this.pieSeries = response.data?.pie_chart?.series || [];
            this.pieChartOptions.labels = response.data?.pie_chart?.labels || [];
            this.isLoading = false;
        },
    }
}
</script>
