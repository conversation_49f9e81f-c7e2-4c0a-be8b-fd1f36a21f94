<template>
    <el-card shadow="always">
        <el-table ref="filterTable" :data="tableData" stripe height="320" style="width: 100%" v-loading="isLoading">
            <el-table-column type="index" width="50">
            </el-table-column>
            <el-table-column prop="adjustment_reason" label="Reason">
            </el-table-column>
            <el-table-column #default="scope" prop="adjustment_count" label="Quantity">
                <span v-if="scope.row.adjustment_count || scope.row.adjustment_count === 0">
                    {{ Number(scope.row.adjustment_count).toLocaleString() }}
                </span>
            </el-table-column>
            <el-table-column label="%">
                <template #default="scope">
                    <div v-if="scope.row?.rate_previous || scope.row?.rate_previous === 0"
                        :class="{ 'text-success': scope.row.rate_previous > 0, 'text-danger': scope.row.rate_previous < 0 }">
                        {{ `${Math.abs(scope.row.rate_previous)}%` }}
                        <span class="float-right" v-if="scope.row.rate_previous">
                            <icon class="p-1" :data="scope.row.rate_previous > 0 ? iconArrowUp : iconArrowDown" />
                        </span>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </el-card>
</template>

<script>
import { getDataByReason } from "@/api/lookerStudioEasypostAdjustment";
import EventBus from "@/utilities/eventBus.js";
import moment from 'moment';

export default {
    name: "EasypostAdjustmentOverviewTable",

    data() {
        return {
            lastFilter: 0,
            tableData: [],
            isLoading: false,
            dataFilters: {
                warehouse_id: "",
                start: moment().subtract(1, 'months').startOf('month').format('YYYY-MM-DD'),
                end: moment().subtract(1, 'months').endOf('month').format('YYYY-MM-DD'),
            },
        }
    },

    async created() {
        this.loadData();
        EventBus.$on("handleFilterChange", (dataFilters) => {
            this.dataFilters = dataFilters;
            this.loadData();
        });
    },

    methods: {
        async loadData() {
            const currentTime = moment().valueOf();
            this.lastFilter = currentTime;
            this.isLoading = true;
            this.series = [];
            const response = await getDataByReason(this.dataFilters);

            if (this.lastFilter != currentTime) { return; }

            this.tableData = response.data?.data || [];
            this.isLoading = false;
        },
    }
}
</script>
