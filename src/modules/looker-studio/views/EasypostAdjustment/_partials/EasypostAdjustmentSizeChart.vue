<template>
    <div v-loading="isLoading" style="min-height: 350px;">
        <apexchart v-if="series.length" type="bar" height="350" :options="chartOptions" :series="series"></apexchart>
        <div v-else class="text-center pt-10">No Data</div>
    </div>
</template>

<script>
import { getDataChart } from "@/api/lookerStudioEasypostAdjustmentSize";
import EventBus from "@/utilities/eventBus.js";
import moment from 'moment';
import VueApexCharts from 'vue3-apexcharts';

export default {
    name: "EasypostAdjustmentSizeChart",

    components: { apexchart: VueApexCharts },

    data() {
        return {
            lastFilter: 0,
            series: [],
            chartOptions: {
                chart: {
                    type: 'bar',
                    height: 350,
                    stacked: true,
                    toolbar: {
                        show: false
                    },
                },
                responsive: [
                    {
                        breakpoint: 480,
                        options: {
                            legend: {
                                position: 'bottom',
                                offsetX: -10,
                                offsetY: 0
                            }
                        }
                    }
                ],
                plotOptions: {
                    bar: {
                        horizontal: false,
                        borderRadius: 3,
                        dataLabels: {
                            total: {
                                enabled: true,
                                style: {
                                    fontSize: '13px',
                                }
                            }
                        },
                    },
                },
                xaxis: {
                    type: 'string',
                    categories: [],
                },
                yaxis: {
                    labels: {
                        formatter: function (val) {
                            return Number(val).toLocaleString();
                        }
                    },
                },
                legend: {
                    position: 'top',
                },
                fill: {
                    opacity: 1
                },
                tooltip: {
                    shared: true,
                    intersect: false,
                    y: {
                        formatter: function (value) {
                            return Number(value).toLocaleString();
                        }
                    },
                },
                dataLabels: {
                    formatter: function (val) {
                        return Number(val).toLocaleString();
                    },
                },
            },
            dataFilters: {
                warehouse_id: "",
                start: moment().subtract(1, 'months').startOf('month').format('YYYY-MM-DD'),
                end: moment().subtract(1, 'months').endOf('month').format('YYYY-MM-DD'),
            },
            isLoading: false,
        }
    },

    async created() {
        this.loadData();
        EventBus.$on("handleFilterChange", (params) => {
            this.dataFilters = { ...params };
            this.loadData();
        });
    },

    methods: {
        async loadData() {
            const currentTime = moment().valueOf();
            this.lastFilter = currentTime;
            this.isLoading = true;
            this.series = [];
            const response = await getDataChart(this.dataFilters);

            if (this.lastFilter != currentTime) { return; }

            this.series = response.data?.series || [];
            this.chartOptions.xaxis.categories = response.data?.categories || [];
            this.isLoading = false;
        },
    }
}
</script>
