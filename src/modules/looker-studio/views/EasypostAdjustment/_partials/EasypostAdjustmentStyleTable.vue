<template>
    <el-card shadow="always">
        <el-table ref="filterTable" :data="tableData" stripe height="320" style="width: 100%" v-loading="isLoading">
            <el-table-column type="index" width="50">
            </el-table-column>
            <el-table-column prop="product_style_name" label="Style">
            </el-table-column>
            <el-table-column label="Quantity">
                <template #default="scope">
                    {{ formatNumberWithCommas(scope.row.qty) }}
                </template>
            </el-table-column>
            <el-table-column label="%">
                <template #default="scope">
                    {{ `${scope.row.rate}%` }}
                </template>
            </el-table-column>
        </el-table>
    </el-card>
</template>

<script>
import { getDataTable } from "@/api/lookerStudioEasypostAdjustmentStyle";
import EventBus from "@/utilities/eventBus.js";
import moment from 'moment';

export default {
    name: "EasypostAdjustmentStyleTable",

    data() {
        return {
            lastFilter: 0,
            tableData: [],
            isLoading: false,
            dataFilters: {
                warehouse_id: "",
                start: moment().subtract(1, 'months').startOf('month').format('YYYY-MM-DD'),
                end: moment().subtract(1, 'months').endOf('month').format('YYYY-MM-DD'),
            },
        }
    },

    async created() {
        this.loadData();
        EventBus.$on("handleFilterChange", (dataFilters) => {
            this.dataFilters = dataFilters;
            this.loadData();
        });
    },
    methods: {
        async loadData() {
            const currentTime = moment().valueOf();
            this.lastFilter = currentTime;
            this.isLoading = true;
            this.series = [];
            const response = await getDataTable(this.dataFilters);

            if (this.lastFilter != currentTime) { return; }

            this.tableData = response.data?.data || [];
            this.isLoading = false;
        },

        formatNumberWithCommas(number) {
            return Number(number).toLocaleString();
        },
    }
}
</script>
