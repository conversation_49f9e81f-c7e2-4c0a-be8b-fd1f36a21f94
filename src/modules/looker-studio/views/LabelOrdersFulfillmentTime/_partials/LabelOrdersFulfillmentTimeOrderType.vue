<template>
    <el-row :gutter="80" class="px-5 mt-5">
        <el-col :span="24" class="mb-2">
            <div style="display: flex; justify-content: space-between;">
                <h3>Order Type</h3>
                <div>
                    <el-card shadow="always">
                        <div>
                            Total Orders: <b>{{ formatNumberWithCommas(total) }}</b>
                            <span v-if="totalRate !== null"
                                :class="{ 'text-success': totalRate > 0, 'text-danger': totalRate < 0 }">
                                <span class="float-right" v-if="totalRate">
                                    <icon class="p-1" :data="totalRate > 0 ? iconArrowUp : iconArrowDown" />
                                </span>
                                {{ `${totalRate}%` }}
                            </span>
                        </div>
                    </el-card>
                </div>
            </div>
        </el-col>
        <el-col :span="14">
            <div v-loading="isLoading" style="min-height: 350px;">
                <apexchart v-if="series.length" type="bar" height="350" :options="chartOptions" :series="series">
                </apexchart>
                <div v-else class="text-center pt-10">No Data</div>
            </div>
        </el-col>
        <el-col :span="10">
            <el-card shadow="always">
                <div v-loading="isLoading"
                    style="min-height: 300px; display: flex; justify-content: center; align-items: center;">
                    <apexchart v-if="pieOrderTypeSeries.length" type="pie" width="350"
                        :options="pieOrderTypeChartOptions" :series="pieOrderTypeSeries"></apexchart>
                    <div v-else class="text-center pt-10">No Data</div>
                </div>
            </el-card>
        </el-col>
        <el-col :span="24">
            <h3>Fulfillment Time</h3>
        </el-col>
        <el-col :span="14">
            <div v-loading="isLoading"
                style="min-height: 350px; display: flex; justify-content: left; align-items: center; padding-left: 15%;">
                <apexchart v-if="pieFulfillmentTimeSeries.length" type="pie" width="350"
                    :options="pieFulfillmentTimeChartOptions" :series="pieFulfillmentTimeSeries"></apexchart>
                <div v-else class="text-center pt-10">No Data</div>
            </div>
        </el-col>
        <el-col :span="10">
            <el-card shadow="always">
                <el-table ref="filterTable" :data="tableData" row-key="fulfillment_time_type" stripe height="280"
                    style="width: 100%" v-loading="isLoading">
                    <el-table-column type="index" width="50">
                    </el-table-column>
                    <el-table-column prop="fulfillment_time_type_label" label="Time">
                    </el-table-column>
                    <el-table-column prop="quantity" label="Quantity">
                    </el-table-column>
                    <el-table-column label="%">
                        <template #default="scope">
                            <div
                                :class="{ 'text-success': comparePreviousMonth(scope.row) > 0, 'text-danger': comparePreviousMonth(scope.row) < 0 }">
                                {{ `${scope.row.rate}%` }}
                                <span class="float-right" v-if="comparePreviousMonth(scope.row)">
                                    <icon class="p-1"
                                        :data="comparePreviousMonth(scope.row) > 0 ? iconArrowUp : iconArrowDown" />
                                </span>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </el-col>
    </el-row>
</template>
<script>

import { getOverview } from "@/api/lookerInsightLabelOrdersFulfillmentTime.js";
import EventBus from "@/utilities/eventBus.js";
import moment from 'moment';
import VueApexCharts from 'vue3-apexcharts';

export default {
    name: "LabelOrdersFulfillmentTimeOrderType",

    components: { apexchart: VueApexCharts },

    data() {
        return {
            series: [],
            chartOptions: {
                chart: {
                    type: 'bar',
                    height: 350,
                    stacked: true,
                    toolbar: {
                        show: false
                    },
                },
                responsive: [
                    {
                        breakpoint: 480,
                        options: {
                            legend: {
                                position: 'bottom',
                                offsetX: -10,
                                offsetY: 0
                            }
                        }
                    }
                ],
                plotOptions: {
                    bar: {
                        horizontal: false,
                        borderRadius: 3,
                        dataLabels: {
                            total: {
                                enabled: true,
                                style: {
                                    fontSize: '13px',
                                }
                            }
                        },
                    },
                },
                xaxis: {
                    type: 'string',
                    categories: [],
                },
                yaxis: {
                    labels: {
                        formatter: function (val) {
                            return Number(val).toLocaleString();
                        }
                    },
                },
                legend: {
                    position: 'top',
                },
                fill: {
                    opacity: 1
                },
                tooltip: {
                    shared: true,
                    intersect: false,
                    y: {
                        formatter: function (value) {
                            return Number(value).toLocaleString();
                        }
                    },
                },
                dataLabels: {
                    formatter: function (val) {
                        return Number(val).toLocaleString();
                    },
                },
            },
            total: 0,
            totalPrevious: null,
            pieOrderTypeSeries: [],
            pieOrderTypeChartOptions: {
                chart: {
                    type: 'pie',
                },
                labels: [],
                stroke: {
                    show: false,
                },
                legend: {
                    position: 'right',
                },
                tooltip: {
                    y: {
                        formatter: function (value, w) {
                            let total = w?.globals?.series.reduce((a, b) => a + b, 0);
                            let percent = (value / total * 100).toFixed(1);

                            return `${Number(value).toLocaleString()} (${percent}%)`;
                        }
                    }
                },
            },
            pieFulfillmentTimeSeries: [],
            pieFulfillmentTimeChartOptions: {
                chart: {
                    type: 'pie',
                },
                labels: [],
                stroke: {
                    show: false,
                },
                legend: {
                    position: 'bottom',
                },
                tooltip: {
                    y: {
                        formatter: function (value, w) {
                            let total = w?.globals?.series.reduce((a, b) => a + b, 0);
                            let percent = (value / total * 100).toFixed(1);

                            return `${Number(value).toLocaleString()} (${percent}%)`;
                        }
                    }
                },
            },
            tableData: [],
            tableDataPreviousMonth: [],
            dataFilters: {
                start: moment().startOf('week').format('YYYY-MM-DD'),
                end: moment().endOf('week').format('YYYY-MM-DD'),
            },
            isLoading: false,
            fulfillmentTimeLabels: [
                'Not Yet in Transit',
                '<= 24 Hours',
                '25-29 Hours',
                '30-47 Hours',
                '> 48 Hours',
            ],
            colors: [
                '#008FFB',
                '#00E396',
                '#FEB019',
                '#FF4560',
                '#775DD0',
            ],
        }
    },

    async created() {
        this.loadData();
        EventBus.$on("handleFilterChange", (params) => {
            this.dataFilters = { ...params };
            this.loadData();
        });
    },

    computed: {
        totalRate() {
            if (!this.totalPrevious) {
                return null;
            }

            let rate = (this.total - this.totalPrevious) / this.totalPrevious * 100;

            return Number(rate).toFixed(1);
        },
    },

    methods: {
        async loadData() {
            this.isLoading = true;
            this.total = 0;
            this.totalPrevious = null;
            this.series = [];
            this.pieFulfillmentTimeSeries = [];
            this.pieOrderTypeSeries = [];
            const pendingPreviousMonth = getOverview({ ...this.dataFilters, previous: true });
            const response = await getOverview(this.dataFilters);
            this.total = response.data?.total || 0;
            this.chartOptions.xaxis.categories = response.data?.column_chart?.categories || [];
            this.series = response.data?.column_chart?.series || [];
            this.pieOrderTypeChartOptions.labels = response.data?.pie_chart_order_type?.labels || [];
            this.pieOrderTypeChartOptions.colors = this.pieOrderTypeChartOptions.labels.map((label) => {
                return label == 'Multiple' ? '#008FFB' : '#00E396';
            })
            this.pieOrderTypeSeries = response.data?.pie_chart_order_type?.series || [];
            this.pieFulfillmentTimeSeries = response.data?.pie_chart_fulfillment_time_type?.series || [];
            this.pieFulfillmentTimeChartOptions.labels = response.data?.pie_chart_fulfillment_time_type?.labels || [];
            this.pieFulfillmentTimeChartOptions.colors = this.pieFulfillmentTimeChartOptions.labels.map(item => {
                let index = this.fulfillmentTimeLabels.findIndex(label => label == item);
                return this.colors[index];
            })
            this.tableData = response.data?.fulfillment_time_list || [];
            const responsePreviousMonth = (await pendingPreviousMonth).data
            this.tableDataPreviousMonth = responsePreviousMonth?.fulfillment_time_list || [];
            this.totalPrevious = responsePreviousMonth?.total || 0;
            this.isLoading = false;
        },

        comparePreviousMonth(current) {
            if (!this.tableDataPreviousMonth.length || !Object.keys(current).length) {
                return 0;
            }

            let previos = this.tableDataPreviousMonth.find(item => item.fulfillment_time_type == current.fulfillment_time_type);

            if (!previos) {
                return 0;
            }

            if (current.rate > previos.rate) {
                return 1;
            }

            return -1
        },

        formatNumberWithCommas(number) {
            return Number(number).toLocaleString();
        },
    },
}
</script>