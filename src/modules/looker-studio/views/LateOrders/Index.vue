<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
    <div>
        <div class="top-head mb-4">
            <div class="top-head-left">
                <h1>{{ $t('Late Orders') }}</h1>
                <p>Over 3 business days</p>
            </div>
        </div>
        <el-form>
            <div class="flex gap-3 px-5">
                <el-form-item>
                    <el-select class="w-full" v-model="filters.warehouse_id" @change="handleFilterChange()"
                        placeholder="Warehouse" clearable multiple filterable>
                        <el-option v-for="item in getWarehouses" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item>
                    <el-select class="w-full" v-model="filters.store_id" @change="handleFilterChange()"
                        placeholder="Store" clearable multiple filterable>
                        <el-option v-for="item in getStores" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item>
                    <el-button type="danger" plain @click="onReset()">Reset</el-button>
                </el-form-item>
            </div>
        </el-form>

        <el-row :gutter="80" class="px-5 mt-5">
            <el-col :span="24">
                <h3>Print Method</h3>
            </el-col>
            <el-col :span="14">
                <PrintMethodChartComponent />
            </el-col>
            <el-col :span="10">
                <PrintMethodTableComponent />
            </el-col>
        </el-row>

        <el-row :gutter="80" class="px-5 mt-20">
            <el-col :span="7">
                <h3>Order Type</h3>
                <OrderTypeChartComponent />
            </el-col>
            <el-col :span="7">
                <h3>Warehouse</h3>
                <WarehouseChartComponent />
            </el-col>
            <el-col :span="10">
                <ProductTableComponent />
            </el-col>
        </el-row>

        <el-row :gutter="80" class="px-5 mt-20 pb-20">
            <el-col :span="24">
                <LateOrdersTableComponent />
            </el-col>
        </el-row>
    </div>
</template>