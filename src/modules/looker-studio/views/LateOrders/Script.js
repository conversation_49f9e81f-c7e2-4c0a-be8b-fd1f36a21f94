import PrintMethodChartComponent from "./_partials/PrintMethodChartComponent.vue";
import PrintMethodTableComponent from "./_partials/PrintMethodTableComponent.vue";
import OrderTypeChartComponent from "./_partials/OrderTypeChartComponent.vue";
import WarehouseChartComponent from "./_partials/WarehouseChartComponent.vue";
import ProductTableComponent from "./_partials/ProductTableComponent.vue";
import LateOrdersTableComponent from "./_partials/LateOrdersTableComponent.vue";
import EventBus from "@/utilities/eventBus.js";
import { mapGetters } from 'vuex';

export default {
    name: "LookerStudio",

    components: {
        PrintMethodChartComponent,
        PrintMethodTableComponent,
        OrderTypeChartComponent,
        WarehouseChartComponent,
        ProductTableComponent,
        LateOrdersTableComponent,
    },

    data() {
        return {
            filters: {
                warehouse_id: "",
                store_id: "",
            },
            emitFnFilter: null,
        }
    },

    computed: {
        ...mapGetters([
            'getStores',
            'getWarehouses',
        ]),
    },

    created() {
        this.$store.dispatch('getStores', { without_pagination: 1 });
        this.$store.dispatch('getWarehouses');
    },

    methods: {
        handleFilterChange() {
            this.debounceFilter();
        },

        onReset() {
            this.filters = {
                warehouse_id: "",
                store_id: "",
            }
            this.debounceFilter();
        },

        debounceFilter() {
            clearTimeout(this.emitFnFilter);
            this.emitFnFilter = setTimeout(() => EventBus.$emit("handleFilterChange", this.filters), 1000);
        }
    },

    beforeUnmount() {
        EventBus.$off("handleFilterChange");
    },
}