<template>
    <el-row :gutter="50">
        <el-col :span="6">
            <el-input v-model="orderNumber" size="large" @clear="loadData" @keydown.enter="loadData"
                placeholder="Order Number" clearable class="input-filter">
            </el-input>
        </el-col>
        <el-col :span="4">
            <el-card shadow="always" v-loading="isLoading">
                <div>Total Orders</div>
                <div>{{ formatNumberWithCommas(qtyOrders) }}</div>
            </el-card>
        </el-col>
        <el-col :span="4">
            <el-card shadow="always" v-loading="isLoading">
                <div>Total Items</div>
                <div>{{ formatNumberWithCommas(qtyOrderItems) }}</div>
            </el-card>
        </el-col>
        <el-col :span="10" style="display: flex; align-items: end; justify-content: right;">
            <el-button :loading="exporting" type="primary" plain @click="handleBtnExportClick()">Export</el-button>
        </el-col>
        <el-col :span="24" class="mt-2">
            <el-card shadow="always">
                <div class="table-content">
                    <el-table ref="filterTable" :data="tableData" @sort-change="sortTable" :default-sort="{ prop: 'created_at', order: 'ascending' }" stripe style="width: 100%" v-loading="isLoading">
                        <el-table-column #default="scope" min-width="60">
                            <span style="white-space: nowrap;">{{ (panigation.pageCurrent - 1) * panigation.limit +
                                scope.$index + 1 }}</span>
                        </el-table-column>
                        <el-table-column prop="store_name" label="Client's Name" min-width="120">
                        </el-table-column>
                        <el-table-column prop="order_number" label="Order Number" min-width="160">
                        </el-table-column>
                        <el-table-column prop="sale_order_item_sku" label="Item SKU" min-width="180">
                        </el-table-column>
                        <el-table-column prop="created_at" sortable="custom" label="Order Date" min-width="100">
                          <template #default="scope">
                            <span>{{ scope.row.order_date }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column prop="sale_order_item_quantity" label="Quantity" min-width="100">
                        </el-table-column>
                        <el-table-column prop="order_status_label" label="Order Status" min-width="158">
                        </el-table-column>
                        <el-table-column prop="production_status_label" label="Production Status" min-width="140">
                        </el-table-column>
                      <el-table-column  label="Age" min-width="150">
                        <template #default="scope">
                          <span>{{ getAge(scope.row.created_at) }}</span>
                        </template>
                      </el-table-column>
                        <el-table-column prop="order_type" :label="$t('Order Type')" min-width="220">
                            <template #default="scope">
                                <el-tag type="info" class="rounded-xl mr-2" style="background-color: #c32e2e"
                                    effect="dark" round size="small" v-if="scope.row.order_type === 2">
                                    {{ $t('Pretreated') }}
                                </el-tag>
                                <el-tag type="info" class="rounded-xl mr-2" style="background-color: #43bc2f"
                                    effect="dark" round size="small" v-if="scope.row.order_type === 3">
                                    {{ $t('Blank') }}
                                </el-tag>
                                <el-tag type="info" class="rounded-xl mr-2" style="background-color: #009a9a"
                                    effect="dark" round size="small" v-if="scope.row.is_create_manual === 1">
                                    {{ $t('Manual Order') }}
                                </el-tag>
                                <el-tag type="info" class="rounded-xl mr-2 bg-sky-500" effect="dark" round size="small"
                                    v-if="scope.row.is_manual === 1">
                                    {{ $t('Manual Process') }}
                                </el-tag>
                                <el-tag type="info" class="rounded-xl mr-2" style="background-color: #232d3b"
                                    effect="dark" round size="small" v-if="scope.row.is_fba_order === 1">
                                    {{ $t('FBA') }}
                                </el-tag>
                                <el-tag type="info" class="rounded-xl mr-2 bg-sky-500" effect="dark" round size="small"
                                    v-if="scope.row.is_xqc === 1">
                                    {{ $t('XQC') }}
                                </el-tag>
                                <el-tag v-if="scope?.row?.shipping_method === 'express'" type="info"
                                    class="rounded-xl mr-2 bg-emerald-500" effect="dark" round size="small">
                                    {{ $t('Shipping Express') }}
                                </el-tag>
                                <el-tag v-else-if="scope?.row?.shipping_method === 'standard'" type="info"
                                    class="rounded-xl mr-2" effect="dark" round size="small">
                                    {{ $t('Shipping Standard') }}
                                </el-tag>
                                <el-tag v-else-if="scope.row.shipping_method === 'priority'" type="info"
                                    class="rounded-xl mr-2 bg-orange-500" effect="dark" round size="small">
                                    {{ $t('Shipping Priority') }}
                                </el-tag>
                                <el-tag v-else type="info" class="rounded-xl mr-2" effect="dark" round size="small"
                                    style="border: 1px solid yellow; background-color: #ffd600; color: black;">
                                    {{ $t(`${displayShippingMethodTag(scope.row.shipping_method)}`) }}
                                </el-tag>
                                <el-tag type="info" class="rounded-xl mr-2 !bg-[#CB3AB4] !text-emerald-50" round
                                    size="small" v-if="scope.row.order_type === 5">
                                    {{ $t('Label Order') }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column :label="$t('Tag')">
                            <template #default="scope">
                                <span v-for="(tag, index) in renderTag(scope.row.tag)" :key="index">
                                    <el-tooltip class="box-item" effect="dark" :content="tag.name"
                                        placement="top-start">
                                        <el-tag size="small" class="mr-2 mb-1 !px-[4px] h-5 !rounded-full" type="info"
                                            :color="tag.color" v-if="tag.color" style="color: #fff"></el-tag>
                                        <el-tag size="small" class="mr-2 mb-1 !px-[4px] h-5 !rounded-full" type="info"
                                            v-else></el-tag>
                                    </el-tooltip>
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="tracking_number" label="Tracking" min-width="260">
                        </el-table-column>
                    </el-table>
                    <div class="bottom">
                        <el-pagination size="small" background layout="prev, pager, next" :total="panigation.total"
                            @current-change="loadData" :page-size="panigation.limit"
                            v-model:current-page="panigation.page" :disabled="isLoading" />
                        <div class="limit" :disabled="isLoading">
                            <el-select v-model="panigation.limit" :placeholder="$t('Select')" size="mini"
                                @change="loadData">
                                <el-option v-for="item in limits" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </div>
                    </div>
                </div>
            </el-card>
        </el-col>
    </el-row>
</template>

<script>
import { getList, getDataOverviewTotal, exportExport } from "@/api/lookerStudioLateOrder";
import EventBus from "@/utilities/eventBus.js";
import { mapGetters } from 'vuex';
import moment from 'moment';
import dateMixin from '@/mixins/date';

export default {
    name: "LateOrdersTableComponent",
    mixins: [dateMixin],

    data() {
        return {
            lastFilter: 0,
            tableData: [],
            qtyOrders: "",
            qtyOrderItems: "",
            orderNumber: "",
            panigation: {
                total: 1,
                page: 1,
                limit: 25,
                pageCurrent: 0,
            },
            isLoading: false,
            saleOrderStatus: [
                {
                    label: "New Order",
                    value: "new_order",
                },
                {
                    label: "In Production",
                    value: "in_production",
                },
                {
                    label: "Shipped",
                    value: "shipped",
                },
                {
                    label: "Cancelled",
                    value: "cancelled",
                },
                {
                    label: "In Production Cancelled",
                    value: "in_production_cancelled",
                },
                {
                    label: "Rejected",
                    value: "rejected",
                },
                {
                    label: "On Hold",
                    value: "on_hold",
                },
                {
                    label: "Draft",
                    value: "draft",
                },
            ],
            sourceTag: 'sale_order',
            exporting: false,
            dataFilters: {},
        }
    },

    computed: {
        ...mapGetters(['getTags', 'getAllShippingMethod'])
    },

    async created() {
        this.fetchTag();
        this.fetchAllShippingMethods();
        await this.loadData();

        EventBus.$on("handleFilterChange", (dataFilters) => {
            this.dataFilters = dataFilters;
            this.loadData();
        });
    },

    methods: {
      sortTable(data) {
        let sortColumn = '';
        let sortBy = '';
        if (data.prop && data.order) {
          sortColumn = data.prop;

          if (data.order === 'ascending') {
            sortBy = 'ASC';
          } else if (data.order === 'descending') {
            sortBy = 'DESC';
          }
        }
        this.dataFilters.sort_column = sortColumn;
        this.dataFilters.sort_by = sortBy;
        this.$nextTick(() => {
          this.loadData();
        });
      },
        async loadData() {
            const currentTime = moment().valueOf();
            this.lastFilter = currentTime;
            const { limit, page } = this.panigation;
            let filter = { limit, page, order_number: this.orderNumber, ...this.dataFilters }
            this.isLoading = true;
            const pendingGetList = getList(filter);
            const pendingGetListTotal = getList({...filter, get_total: true});
            const pendingGetTotal = getDataOverviewTotal(filter);
            const responseGetList = await pendingGetList;
            const responGetListTotal = await pendingGetListTotal;
            const responseGetTotal = await pendingGetTotal;

            if (this.lastFilter != currentTime) { return; }

            const dataList = responseGetList.data?.data;
            const dataOverview = responseGetTotal.data?.data;
            this.tableData = dataList?.data || [];
            this.panigation.total = responGetListTotal.data;
            this.panigation.pageCurrent = dataList?.current_page
            this.qtyOrders = dataOverview?.qty_orders || "";
            this.qtyOrderItems = dataOverview?.qty_order_items || "";
            this.isLoading = false;
        },

        async handleBtnExportClick() {
            this.exporting = true;
            let filter = { order_number: this.orderNumber, ...this.dataFilters }
            const response = await exportExport(filter);
            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', `swiftpod_report_late_order_${moment().format('YYYYMMDD')}.xlsx`);
            document.body.appendChild(link);
            link.click();
            this.exporting = false;
        },

        async fetchTag() {
            await this.$store.dispatch('getTags', { source: this.sourceTag });
        },

        async fetchAllShippingMethods() {
            await this.$store.dispatch('getAllShippingMethod');
        },

        renderTag(tag) {
            if (!tag) {
                return [];
            }

            const tags = tag.split(',');
            const data = [];
            const length = tags.length;

            for (let i = 0; i < length; i++) {
                if (!tags[i]) {
                    continue;
                }

                const itemTag = this.getTags.find((item) => item.id == tags[i]);

                if (!itemTag) {
                    continue;
                }

                data.push(itemTag);
            }

            return data;
        },

        displayShippingMethodTag(type) {
            let result = '';
            const method = this.getAllShippingMethod.find(
                (item) => item.api_shipping_method === type
            );

            if (method) {
                result =
                    method.shipping_carrier + ' ' + method.shipping_carrier_service;
            }

            return result;
        },

        orderStatus(status) {
            const item = this.saleOrderStatus.find(
                (item) => item.value === status
            );

            return item?.label || "";
        },

        formatNumberWithCommas(number) {
            return Number(number).toLocaleString();
        },
    }
}
</script>


<style scoped>
.input-filter {
    height: 100%;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 12px 0px;
}

::v-deep.input-filter .el-input__inner {
    height: 100% !important;
}

::v-deep .cell {
    word-break: break-word;
}

::v-deep .el-button svg {
    margin: 0;
    top: 0;
}
</style>
