<template>
    <div v-loading="isLoading" style="min-height: 200px;">
        <apexchart v-if="series.length" type="pie" width="350" :options="chartOptions" :series="series"></apexchart>
        <div v-else class="text-center pt-10">No Data</div>
    </div>
</template>
<script>
import VueApexCharts from 'vue3-apexcharts';
import { getDataOrderType } from "@/api/lookerStudioLateOrder";
import EventBus from "@/utilities/eventBus.js";
import moment from 'moment';

export default {
    name: "OrderTypeChartComponent",

    components: { apexchart: VueApexCharts },

    data() {
        return {
            lastFilter: 0,
            series: [],
            chartOptions: {
                chart: {
                    type: 'pie',
                },
                labels: [],
                stroke: {
                    show: false,
                },
                legend: {
                    position: 'bottom',
                },
                tooltip: {
                    y: {
                        formatter: function (value, w) {
                            let total = w?.globals?.series.reduce((a, b) => a + b, 0);
                            let percent = (value / total * 100).toFixed(1);

                            return `${Number(value).toLocaleString()} (${percent}%)`;
                        }
                    }
                },
            },
            dataFilters: {},
            isLoading: false,
            colors: []
        }
    },

    async created() {
        await this.loadData();

        EventBus.$on("handleFilterChange", (dataFilters) => {
            this.dataFilters = dataFilters;
            this.loadData();
        });
    },

    methods: {
        async loadData() {
            const currentTime = moment().valueOf();
            this.lastFilter = currentTime;
            this.isLoading = true;
            this.series = [];
            const response = await getDataOrderType(this.dataFilters);

            if (this.lastFilter != currentTime) { return; }

            this.series = response.data?.series || [];
            this.chartOptions.labels = response.data?.labels || [];
            this.chartOptions.colors = this.chartOptions.labels.map((label) => {
                return label == 'Multiple' ? '#008FFB' : '#00E396';
            })
            this.isLoading = false;
        },
    }
}
</script>