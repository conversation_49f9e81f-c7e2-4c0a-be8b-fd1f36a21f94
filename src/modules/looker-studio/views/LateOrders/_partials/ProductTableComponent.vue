<template>
    <el-card shadow="always">
        <el-table ref="filterTable" :data="tableData" @sort-change="sortTable" stripe :default-sort="{ prop: 'qty_order_items', order: 'descending' }" height="250" style="width: 100%" v-loading="isLoading">
            <el-table-column type="index" width="50">
            </el-table-column>
            <el-table-column prop="name" label="Product" width="300">
            </el-table-column>
            <el-table-column prop="product_sku" label="Sku">
            </el-table-column>
            <el-table-column prop="qty_order_items" label="Items" width="100" sortable="custom">
                <template #default="scope">
                    {{ formatNumberWithCommas(scope.row.qty_order_items) }}
                </template>
            </el-table-column>
        </el-table>
    </el-card>
</template>
<script>
import { getDataProductSkuTable } from "@/api/lookerStudioLateOrder";
import EventBus from "@/utilities/eventBus.js";
import moment from 'moment';

export default {
    name: "ProductTableComponent",

    data() {
        return {
            lastFilter: 0,
            tableData: [],
            dataFilters: {},
            isLoading: false,
        }
    },

    async created() {
        await this.loadData();
        EventBus.$on("handleFilterChange", (dataFilters) => {
            this.dataFilters = dataFilters;
            this.loadData();
        });
    },

    methods: {
      sortTable(data) {
        let sortColumn = '';
        let sortBy = '';
        if (data.prop && data.order) {
          sortColumn = data.prop;

          if (data.order === 'ascending') {
            sortBy = 'ASC';
          } else if (data.order === 'descending') {
            sortBy = 'DESC';
          }
        }
        this.dataFilters.sort_column = sortColumn;
        this.dataFilters.sort_by = sortBy;
        this.$nextTick(() => {
          this.loadData();
        });
      },
        async loadData() {
            const currentTime = moment().valueOf();
            this.lastFilter = currentTime;
            this.isLoading = true;
            this.series = [];
            const response = await getDataProductSkuTable(this.dataFilters);

            if (this.lastFilter != currentTime) { return; }

            this.tableData = response.data?.data || [];
            this.isLoading = false;
        },

        formatNumberWithCommas(number) {
            return Number(number).toLocaleString();
        },
    },
}
</script>