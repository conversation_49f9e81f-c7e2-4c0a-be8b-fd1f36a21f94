<template>
    <div v-loading="isLoading" style="min-height: 200px;">
        <apexchart v-if="series.length" type="pie" width="350" :options="chartOptions" :series="series"></apexchart>
        <div v-else class="text-center pt-10">No Data</div>
    </div>
</template>
<script>
import VueApexCharts from 'vue3-apexcharts';
import { getDataOrderWarehouse } from "@/api/lookerStudioLateOrder";
import EventBus from "@/utilities/eventBus.js";
import moment from 'moment';
import { mapGetters } from 'vuex';

export default {
    name: "WarehouseChartComponent",

    components: { apexchart: VueApexCharts },

    computed: {
        ...mapGetters([
            'getWarehouses',
        ]),
    },

    data() {
        return {
            lastFilter: 0,
            series: [],
            chartOptions: {
                chart: {
                    type: 'pie',
                },
                labels: [],
                stroke: {
                    show: false,
                },
                legend: {
                    position: 'bottom',
                },
                tooltip: {
                    y: {
                        formatter: function (value, w) {
                            let total = w?.globals?.series.reduce((a, b) => a + b, 0);
                            let percent = (value / total * 100).toFixed(1);

                            return `${Number(value).toLocaleString()} (${percent}%)`;
                        }
                    }
                },
            },
            dataFilters: {},
            isLoading: false,
            colors: [
                '#008FFB',
                '#00E396',
                '#FEB019',
                '#FF4560',
                '#775DD0',
                '#3F51B5',
                '#03A9F4',
                '#4CAF50',
                '#F9CE1D',
                '#FF9800',
                '#33B2DF',
                '#546E7A',
                '#D4526E',
                '#13D8AA',
                '#A5978B',
                '#2B908F',
                '#F9A3A4',
                '#90EE7E',
                '#FA4443',
                '#69D2E7',
            ]
        }
    },

    async created() {
        await this.loadData();

        EventBus.$on("handleFilterChange", (dataFilters) => {
            this.dataFilters = dataFilters;
            this.loadData();
        });
    },

    methods: {
        async loadData() {
            const currentTime = moment().valueOf();
            this.lastFilter = currentTime;
            this.isLoading = true;
            this.series = [];
            const response = await getDataOrderWarehouse(this.dataFilters);

            if (this.lastFilter != currentTime) { return; }

            this.series = response.data?.series || [];
            this.chartOptions.labels = response.data?.labels || [];
            this.isLoading = false;
            this.chartOptions.colors = response.data?.data_raw.map(item => {
                let index = this.getWarehouses.findIndex(w => w.id == item.warehouse_id);
                return this.colors[index];
            });
        },
    }
}
</script>