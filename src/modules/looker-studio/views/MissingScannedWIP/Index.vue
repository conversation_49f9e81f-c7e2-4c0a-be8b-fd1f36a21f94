<script src="./Script.js"></script>
<template>
  <div v-loading="isLoading" class="pr-3">
    <div class="title mt-1 flex pl-4">
      <h1>{{ $t('Missing Scanned WIP') }}</h1>
    </div>
    <div class="flex mt-3 pl-4">
      <div>
        <el-date-picker
            :disabled-date="disabledDate"
            format="YYYY-MM-DD"
            v-model="dateFilter"
            type="daterange"
            range-separator="To"
            :start-placeholder="$t('Start date')"
            :end-placeholder="$t('End date')"
            @change="onChangeDate"
        >
        </el-date-picker>
      </div>
      <div class="ml-3">
        <el-select
            filterable
            v-model="filters.warehouse"
            value-key="name"
            :placeholder="$t('Select warehouse')"
            @change="onFilter"
        >
          <el-option
              v-if="warehouses?.length != 1"
              key="all"
              label="All"
              value=""
          ></el-option>
          <el-option
              v-for="item in warehouses"
              :key="item.id"
              :label="item.name"
              :value="String(item.id)"
          >
          </el-option>
        </el-select>
      </div>
      <div v-if="hasFilter" class="ml-3 whitespace-nowrap">
        <el-button
            size="mini"
            class="!text-red-400 !bg-[#fdf0f0] !border-red-400"
            @click="onClearFilter"
        >
          {{ $t('Reset') }}
        </el-button>
      </div>
    </div>

    <div class="flex mt-3">
      <div style="width: 49%;" class="px-4">
        <p class="text-base mb-2">Missing Scanned WIP DTG</p>
        <el-card>
          <MissingScannedWIPChart class="w-full" :dataChart="data" :color="colorDTG"/>
        </el-card>

      </div>
      <div style="width: 49%;" class="px-4">
        <p class="text-base mb-2">Missing Scanned WIP MUGS</p>
        <el-card>
          <MissingScannedWIPChart class="w-full" :dataChart="dataMUGS" :color="colorMUGS"/>
        </el-card>

      </div>
    </div>
    <div class="flex mt-6">
      <div style="width: 49%;" class="px-4">
        <p class="text-base mb-2">Missing Scanned WIP DTF</p>
        <el-card>
          <MissingScannedWIPChart class="w-full" :dataChart="dataDTF" :color="colorDTF"/>
        </el-card>

      </div>
      <div style="width: 49%;" class="px-4">
        <p class="text-base mb-2">Missing Scanned WIP UV</p>
        <el-card>
          <MissingScannedWIPChart class="w-full" :dataChart="dataUV" :color="colorUV"/>
        </el-card>

      </div>
    </div>
  </div>
</template>