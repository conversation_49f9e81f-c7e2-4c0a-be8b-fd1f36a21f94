import {mapGetters} from "vuex";
import MissingScannedWIPChart from "./components/MissingScannedWIPChart.vue";
import { report } from '@/api/missingScannedWIP';
import moment from "moment-timezone";
import warehouseMixin from '@/mixins/warehouse';
import dateMixin from '@/mixins/date';
import {equals} from "ramda";


export default {
    name: "MissingScannedWIP",
    mixins: [warehouseMixin, dateMixin],
    components: { MissingScannedWIPChart },
    data() {
        return {
            data: {},
            colorDTG: '#3670ed',
            colorMUGS: '#3db4ca',
            colorDTF: '#f8ab25',
            colorUV: '#4850b3',
            dataMUGS: {},
            dataDTF: {},
            dataUV: {},
            filters: this.setDefaultFilter(),
            dateFilter: "",
            isLoading: false,
            DTG: 'DTG',
            DTF: 'DTF',
            UV: 'UV',
            MUGS: 'MUGS',
            userWarehouseId: '',
            userWarehouse: {},
            warehouses: [],
        };
    },
    mounted() {
        this.fetchWarehouse();
        this.filters = this.setDefaultFilter();
        this.getReport();
        this.setDefaultDate();
    },
    computed: {
        ...mapGetters(['getUserProfile']),
        hasFilter() {
            const defaultFilter = this.setDefaultFilter();
            return !equals(defaultFilter, this.filters);
        },
    },
    methods: {
        onFilter() {
            this.getReport();
        },
        async fetchWarehouse() {
            this.warehouses = this.getUserProfile.warehouses ?? [];
        },
        setDefaultDate() {
            this.dateFilter = [
                this.formatDate(moment.tz('America/Los_Angeles').subtract(7, 'days'), false),
                this.formatDate(moment.tz('America/Los_Angeles'), false),
            ];
        },
        onClearFilter() {
            this.filters = this.setDefaultFilter();
            this.orderDate = '';
            this.setDefaultDate();
            this.getReport();
        },
        setDefaultFilter() {
            let params = {
                start_date: this.formatDate(moment().subtract(7, 'days'), false),
                end_date: this.formatDate(moment(), false),
                warehouse: '',
            };
            return params;
        },
        disabledDate(time) {
            return time.getTime() > Date.now();
        },
        onChangeDate() {
            if (this.dateFilter && this.dateFilter.length) {
                this.filters.start_date = this.formatDate(this.dateFilter[0], false);
                this.filters.end_date = this.formatDate(this.dateFilter[1], false);
            } else {
                this.filters.start_date = '';
                this.filters.end_date = '';
            }
            this.getReport();
        },
        async getReport() {
            try {
                console.log(2222, {
                    start_date: this.filters.start_date,
                    end_date: this.filters.end_date,
                    warehouse: this.filters.warehouse,
                    print_method: this.DTG,
                })

                const [resDTG, resMUGS, resDTF, resUV] = await Promise.all([
                    report({
                    start_date: this.filters.start_date,
                    end_date: this.filters.end_date,
                    warehouse: this.filters.warehouse,
                    print_method: this.DTG,
                }),
                    report({
                        start_date: this.filters.start_date,
                        end_date: this.filters.end_date,
                        warehouse: this.filters.warehouse,
                        print_method: this.MUGS,
                    }),
                    report({
                        start_date: this.filters.start_date,
                        end_date: this.filters.end_date,
                        warehouse: this.filters.warehouse,
                        print_method: this.DTF,
                    }),
                    report({
                        start_date: this.filters.start_date,
                        end_date: this.filters.end_date,
                        warehouse: this.filters.warehouse,
                        print_method: this.UV,
                    })
                ]);
                this.data = resDTG.data;
                this.dataMUGS = resMUGS.data;
                this.dataDTF = resDTF.data;
                this.dataUV = resUV.data;
            } catch (e) {
                this.notification(' error!', 'error', false, {duration: 5000});
            }
        },

    }
};
