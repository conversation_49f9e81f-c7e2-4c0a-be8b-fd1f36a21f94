<template>
  <div class="mt-3">
    <div class="mr-3 w-full" v-if="chartOptions">
      <apexchart width="100%" :key="randomKey" type="bar" :height="maxHeight" :options="chartOptions" :series="series">
      </apexchart>
    </div>
  </div>
</template>

<script>
import VueApexCharts from 'vue3-apexcharts';
import formatNumberMixin from '@/mixins/formatNumber.js';
import {makeid} from '@/utilities/helper.js';

export default {
  name: "MissingScannedWIPChart",
  components: {apexchart: VueApexCharts},
  props: [
    'dataChart',
    'color'
  ],
  mixins: [formatNumberMixin],
  data() {
    return {
      randomKey: makeid(8),
      average: 0,
      maxHeight: 350,
      totalShipments: [],
      series: [{
        name: 'Total items',
        data: []
      }],
      chartOptions: {
        chart: {
          height: 350,
          type: 'bar',
          toolbar: {
            show: true,
            tools: {
              download: false
            }
          }
        },
        colors: [this.color],
        annotations: {
          yaxis: [{
            y: 500,
            borderColor: '#000000',
            label: {
              borderColor: '#666666',
              position: 'left',
              offsetX: 50,
              offsetY: -5,
              style: {
                color: '#fff',
                background: '#666666',
              },
              text: 'Average'
            }
          }],
        },
        plotOptions: {
          bar: {
            borderRadius: 5,
            dataLabels: {
              position: 'top', // top, center, bottom
            },
          }
        },
        dataLabels: {
          enabled: true,
          formatter: function (val) {
            return val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
          },
          offsetY: -20,
          style: {
            fontSize: '12px',
            colors: []
          }
        },

        xaxis: {
          position: 'bottom',
          axisBorder: {
            show: false
          },
          axisTicks: {
            show: false
          },
          tooltip: {
            enabled: true,
          }
        },
        yaxis: {
          axisBorder: {
            show: false
          },
          axisTicks: {
            show: false,
          },
          labels: {
            show: true,
            formatter: function (val) {
              return val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            }
          }

        },
        title: {
          floating: true,
          offsetY: 330,
          align: 'center',
          style: {
            color: '#444'
          }
        }
      },
    }
  },
  watch: {
    dataChart(newVal) {
      this.chartOptions.xaxis.categories = [];
      this.series[0].data = [];
      this.totalShipments = [];
      let total = 0;
      newVal.forEach((el, index) => {
        this.chartOptions.xaxis.categories[index] = el[0];
        this.series[0].data.push(el[1]);
        total += parseInt(el[1]);
      });
      this.average = total / newVal.length;
      this.chartOptions.annotations.yaxis[0].y = this.average;
      this.chartOptions.annotations.yaxis[0].label.text = 'Average: ' +  this.formatNum(Math.round(this.average));
      this.randomKey = makeid(8);
    }
  },
  mounted() {
  },
  methods: {
    formatNum(num) {
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    formatTotal(data) {
      return data ? this.formatNum(data) : '0';
    },
    formatPercent(data) {
      return data == null ? '0' : `${data} %`;
    }
  }
}
</script>