<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head mb-4">
      <div class="top-head-left">
        <h1>{{ $t('Offenders by Department') }}</h1>
      </div>
    </div>
    <el-form>
      <div class="flex gap-3 px-5">
        <el-form-item>
          <el-select class="w-full" v-model="filters.warehouse_id" @change="handleFilterChange()"
            placeholder="All warehouse" clearable multiple>
            <el-option v-for="item in getWarehouses" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>        
      </div>      
    </el-form>
    <div class="px-5">The reporting time: {{ filters.start_date }} to {{ filters.end_date }} (during the last 15 days)</div>

    <el-row :gutter="80" class="px-5 mt-5">
      <el-col :span="14">
        <p class="!font-semibold mb-4">
          Offenders by Department
        </p>
        <Report />
      </el-col>
      <el-col :span="10">
        <p class="!font-semibold mb-3">
          Threshold of each production stage by warehouse
        </p>
        <ReportSetting />
      </el-col>
    </el-row>
  </div>
</template>
