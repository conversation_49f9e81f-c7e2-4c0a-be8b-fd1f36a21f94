import ReportSetting from "./_partials/ReportSetting.vue";
import Report from "./_partials/Report.vue";
import EventBus from "@/utilities/eventBus.js";
import { mapGetters } from 'vuex';
import moment from "moment/moment";
export default {
    name: "LookerStudioEsypostOffenderDepartment",

    components: {
        ReportSetting,
        Report,
    },

    data() {
        return {
            filters: {
                start_date: moment().subtract(15, 'days').format('YYYY-MM-DD'),
                end_date: moment().format('YYYY-MM-DD'),
            },
        }
    },

    computed: {
        ...mapGetters([
            'getWarehouses',
        ]),
    },

    created() {
        this.initFilter();
        this.$store.dispatch('getWarehouses');
    },

    methods: {
        handleFilterChange() {
            let params = {
                warehouse_id: this.filters.warehouse_id,               
            }
            
            this.debounceFilter(params)
        },

        initFilter() {
            this.filters = {
                warehouse_id: "",
                start_date: moment().subtract(15, 'days').format('YYYY-MM-DD'),
                end_date: moment().format('YYYY-MM-DD'),
            }
        },

        onReset() {
            this.initFilter();
            this.handleFilterChange();
        },

        debounceFilter(params) {
            clearTimeout(this.emitFnFilter);
            this.emitFnFilter = setTimeout(() => EventBus.$emit("handleFilterChange", params), 1000);
        }
    },

    beforeUnmount() {
        EventBus.$off("handleFilterChange");
    },
}