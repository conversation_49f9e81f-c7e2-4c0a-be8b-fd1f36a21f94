<template>
    <el-card shadow="always">
        <el-table ref="filterTable" :data="tableData" stripe height="500" style="width: 100%" v-loading="isLoading">
            <el-table-column type="index" width="50">
            </el-table-column>
            <el-table-column prop="warehouse_name" label="Warehouse">
            </el-table-column>
            <el-table-column prop="setting_key_label" label="Threshold">
            </el-table-column>
            <el-table-column prop="setting_value" label="Hours">
            </el-table-column>            
        </el-table>
    </el-card>
</template>

<script>
import { getReportSetting } from "@/api/lookerStudioOffendersByDepartment";
import EventBus from "@/utilities/eventBus.js";
import moment from 'moment';

export default {
    name: "ReportSetting",

    data() {
        return {
            lastFilter: 0,
            tableData: [],
            isLoading: false,
            dataFilters: {
                warehouse_id: "",               
            },
        }
    },

    async created() {
        this.loadData();
        EventBus.$on("handleFilterChange", (dataFilters) => {
            this.dataFilters = dataFilters;
            this.loadData();
        });
    },
    
    methods: {
        async loadData() {
            const currentTime = moment().valueOf();
            this.lastFilter = currentTime;
            this.isLoading = true;
            this.series = [];
            const response = await getReportSetting(this.dataFilters);

            if (this.lastFilter != currentTime) { return; }

            this.tableData = response.data || [];
            this.isLoading = false;
        },
    }
}
</script>