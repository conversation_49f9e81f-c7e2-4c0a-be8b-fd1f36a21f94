<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
    <div>
        <div class="top-head mb-4">
            <div class="top-head-left">
                <h1>{{ $t('Tiktok Orders - Fulfillment Time') }}</h1>
            </div>
        </div>
        <el-form>
            <div class="flex gap-3 px-5 filter-custom" style="align-items: start;">
                <div>
                    <el-form-item>
                        <el-date-picker v-model="filters.daterange" @change="handleFilterChange()" type="daterange"
                            range-separator="To" start-placeholder="Start date" clearable end-placeholder="End date">
                        </el-date-picker>
                    </el-form-item>
                    <div class="compare-label">{{ compareLabel }}</div>
                </div>
                <el-form-item>
                    <el-button type="danger" plain @click="onReset()">Reset</el-button>
                </el-form-item>
            </div>

        </el-form>
        <el-row :gutter="80" class="px-5">
            <el-col :span="24">
                <TiktokOrdersFulfillmentTimeOrderType />
            </el-col>
        </el-row>
        <el-row :gutter="80" class="px-5 mt-10">
            <el-col :span="24">
                <TiktokOrdersFulfillmentTimeTable />
            </el-col>
        </el-row>
    </div>
</template>

<style scoped>
::v-deep .filter-custom .el-form-item {
    margin-bottom: 0;
}

::v-deep .filter-custom .compare-label {
    opacity: 0.8;
    font-style: italic;
    color: gray;
}
</style>