import TiktokOrdersFulfillmentTimeOrderType from "./_partials/TiktokOrdersFulfillmentTimeOrderType.vue";
import TiktokOrdersFulfillmentTimeTable from "./_partials/TiktokOrdersFulfillmentTimeTable.vue";
import EventBus from "@/utilities/eventBus.js";
import moment from "moment";

export default {
    name: "TiktokOrdersFulfillmentTime",
    components: {
        TiktokOrdersFulfillmentTimeOrderType,
        TiktokOrdersFulfillmentTimeTable,
    },
    data() {
        return {
            filters: {},
            emitFnFilter: null,
        }
    },

    computed: {
        compareLabel() {
            let startDate = this.filters?.daterange?.[0];
            let endDate = this.filters?.daterange?.[1];

            if (startDate && endDate) {
                let start = moment(startDate);
                let end = moment(endDate);

                if (start.clone().startOf('month').format('YYYYMMDD') === start.format('YYYYMMDD') &&
                    end.clone().endOf('month').format('YYYYMMDD') === end.format('YYYYMMDD')) {
                    start = end.clone().subtract(1, 'months').startOf('month').format('YYYY-MM-DD');
                    end = end.clone().subtract(1, 'months').endOf('month').format('YYYY-MM-DD');
                } else {
                    let days = moment(endDate).diff(moment(startDate), 'days');
                    start = moment(startDate).subtract(1, 'days').subtract(days, 'days').format('YYYY-MM-DD');
                    end = moment(startDate).subtract(1, 'days').format('YYYY-MM-DD');
                }

                return `Compared to: ${start} to ${end}`;
            }

            return '';
        }
    },

    created() {
        this.initFilter();
    },

    methods: {
        handleFilterChange() {
            let daterange = this.filters?.daterange || [];

            if (!daterange.length) {
                return;
            }

            let params = {
                start: moment(daterange[0]).format('YYYY-MM-DD'),
                end: moment(daterange[1]).format('YYYY-MM-DD'),
            }

            this.debounceFilter(params)
        },

        initFilter() {
            this.filters = {
                daterange: [
                    moment().startOf('week').format('YYYY-MM-DD'),
                    moment().endOf('week').format('YYYY-MM-DD'),
                ],
            }
        },

        onReset() {
            this.initFilter();
            this.handleFilterChange();
        },

        debounceFilter(params) {
            clearTimeout(this.emitFnFilter);
            this.emitFnFilter = setTimeout(() => EventBus.$emit("handleFilterChange", params), 500);
        }
    },

    beforeUnmount() {
        EventBus.$off("handleFilterChange");
    },
}