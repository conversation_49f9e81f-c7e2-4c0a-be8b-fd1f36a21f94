<template>
    <el-row :gutter="50">
        <div class="flex gap-8 justify-between px-6 w-full">
            <div class="w-full max-w-[400px]">
                <label class="ml-1">Search Order</label>
                <el-input v-model="orderNumber" size="large" @clear="loadData" @keydown.enter="loadData"
                    placeholder="Order Number" clearable class="input-filter">
                </el-input>
            </div>
            <div class="flex gap-4">
                <div class="flex gap-4 mt-5" v-for="performance in performanceData">
                    <div class="h-full w-full min-h-[50px] max-w-[300px] flex gap-8 items-center justify-between px-2 border border-gray-300 rounded-md">
                        <span class="text-md font-bold">
                            {{ performance?.time }} Hours - {{ performance?.total }} orders
                        </span>
                        <span class="text-lg font-bold text-green-800">
                            {{ performance?.percent }}%
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <el-col :span="24" class="mt-2">
            <el-card shadow="always" class="mb-5">
                <div class="table-content">
                    <el-table :data="tableData" stripe height="540" style="width: 100%" v-loading="isLoading">
                        <el-table-column #default="scope" min-width="60">
                            {{ (panigation.pageCurrent - 1) * panigation.limit + scope.$index + 1 }}
                        </el-table-column>
                        <el-table-column prop="order_number" label="Order Number" min-width="160">
                        </el-table-column>
                        <el-table-column prop="order_type_label" label="Order Type" min-width="100">
                        </el-table-column>
                        <el-table-column prop="order_quantity" label="Quantity" min-width="90">
                        </el-table-column>
                        <el-table-column prop="order_date" label="Order Date" min-width="100">
                        </el-table-column>
                        <el-table-column prop="label_printed_time" label="Label Printed At" min-width="100">
                        </el-table-column>
                        <el-table-column prop="in_transit_time" label="In Transit At" min-width="100"> 
                        </el-table-column>
                        <el-table-column prop="fulfillment_time_label" label="Fulfillment Time" min-width="130">
                        </el-table-column>
                        <el-table-column prop="fulfillment_time" label="Hours">
                        </el-table-column>
                    </el-table>
                    <div class="bottom">
                        <el-pagination size="small" background layout="prev, pager, next" :total="panigation.total"
                            @current-change="loadData" :page-size="panigation.limit"
                            v-model:current-page="panigation.page" :disabled="isLoading" />
                        <div class="limit" :disabled="isLoading">
                            <el-select v-model="panigation.limit" :placeholder="$t('Select')" size="mini"
                                @change="loadData">
                                <el-option v-for="item in limits" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </div>
                    </div>
                </div>
            </el-card>
        </el-col>
    </el-row>
</template>
<script>

import { getListData, getPerformance } from "@/api/lookerInsightTiktokOrdersFulfillmentTime.js";
import EventBus from "@/utilities/eventBus.js";
import moment from 'moment';

export default {
    name: "TiktokOrdersFulfillmentTimeTable",

    data() {
        return {
            tableData: [],
            performanceData: [],
            isLoading: false,
            orderNumber: "",
            dataFilters: {
                warehouse_id: "",
                start: moment().startOf('week').format('YYYY-MM-DD'),
                end: moment().endOf('week').format('YYYY-MM-DD'),
            },
            panigation: {
                total: 1,
                page: 1,
                limit: 25,
                pageCurrent: 0,
            },
        }
    },

    async created() {
        this.loadData();
        this.loadPerformance();
        EventBus.$on("handleFilterChange", (dataFilters) => {
            this.dataFilters = dataFilters;
            this.loadData();
            this.loadPerformance();
        });
    },

    methods: {
        async loadData() {
            const { limit, page } = this.panigation;
            let filter = { limit, page, order_number: this.orderNumber, ...this.dataFilters }
            this.isLoading = true;
            this.series = [];
            const response = await getListData(filter);
            const dataList = response.data?.data;
            this.panigation.total = dataList?.total
            this.panigation.pageCurrent = dataList?.current_page
            this.tableData = dataList?.data || [];
            this.isLoading = false;
        },

        async loadPerformance() {
            let filter = { ...this.dataFilters }
            this.isLoading = true;
            const response = await getPerformance(filter);
            this.performanceData = response.data;
            this.isLoading = false;
        },
    }
}
</script>

<style scoped>
.input-filter {
    height: 50px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 12px 0px;
}

::v-deep.input-filter .el-input__inner {
    height: 50px !important;
}


::v-deep .cell {
    word-break: break-word;
}
</style>
