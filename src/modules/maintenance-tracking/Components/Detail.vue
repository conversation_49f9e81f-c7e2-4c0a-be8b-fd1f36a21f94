<template>
  <div>
    <el-dialog
        v-model="showDetail"
        @close="resetData"
        width="800px"
        custom-class="el-dialog-custom custom-dialog border-bottom-none rounded-xl"
    >
      <template #title>
        <div class="flex items-center rounded-[10px] border px-[12px] py-[8px] max-w-fit">
          <div v-if="employee?.id" class="flex items-center">
            <img v-if="employee?.avatar" :src="generateAvatar(employee?.avatar)" class="w-[36px] h-[36px] mr-[12px] rounded-full" alt="">
            <img v-else src="@/assets/images/Avatar.png" class="w-[36px] h-[36px] mr-[12px] rounded-full" alt="">
            <span class="font-medium text-[16px] leading-[24px] text-[#0C0D0E]">
              {{ employee?.name }}
            </span>
            <el-button type="danger" @click="logout()" plain class="!bg-white !ml-[12px] !rounded-[10px]">{{ $t('Logout') }}</el-button>
          </div>
          <div v-else class="flex items-center cursor-pointer" @click="isOpenModalScanEmployee=true">
            <img src="@/assets/images/Avatar.png" class="w-[36px] h-[36px] mr-[12px] rounded-full" alt="">
            <span class="font-medium text-[16px] leading-[24px] text-[#0C0D0E]">
              {{ $t('Login to edit') }}
            </span>
          </div>

        </div>
      </template>
      <div class="flex break-words">
        <el-scrollbar class="w-7/12 pr-4" height="700px">
          <div >
            <div>
              <div class="flex justify-between !mt-2">
                <span class="font-medium text-[24px] word-break-custom"
                >Ticket #{{
                    ticketSelected.maintenance_tracking_number
                  }}</span
                >
              </div>
            </div>
            <div class="flex mt-4 items-center">
              <span class="w-[150px] text-neutral-500">Urgency</span>
              <div class="flex">
                <div :class="['flex border rounded-full py-[3px] px-4 border-[#DBDBDB] text-[#515961]']" >
                  <div :class="['w-3 h-3 border mr-2 rounded-full  my-auto', generateClass()]"></div>
                  <span>{{ generateUrgency(ticketSelected.urgency) }}</span>
                </div>
              </div>
            </div>
            <div class="flex mt-4 items-center">
              <span class="w-[150px] text-neutral-500">Status</span>
              <div class="flex relative">
                <div @click="toggleUpdateStatus" :class="['flex border rounded-full py-[3px] px-4 text-white cursor-pointer', generateBGStatus()]" >
                  <span>{{ generateStatus(ticketSelected.status) }}</span>
                  <icon class="!w-[10px] !mt-[1px] !ml-[10px]" :data="iconArrowDown" />
                </div>
                <div v-show="isChangeStatus" class="absolute rounded-[8px] p-[12px] top-[28px] w-[270px] z-50 shadow-custom">
                  <div v-if="ticketSelected.status == 'unresolved'" @click="processTicket" class="items-center flex justify-between cursor-pointer">
                    <span class="text-[14px] text-[#777]">{{ $t('Transition to') }}</span>
                    <div class="flex items-center ">
                      <icon class="!text-[#3B71E6] !w-[24px] !h-[24px]" :data="iconArrowRight"></icon>
                      <div class="flex border rounded-full ml-[10px] py-[3px] px-4 text-white bg-[#3B71E6]" >
                        <span class="mr-auto text-[16px]">{{ $t('In progress') }}</span>
                      </div>
                    </div>

                  </div>
                  <div @click="openModelResole" :class="['items-center flex justify-between cursor-pointer', ticketSelected.status == 'unresolved' ? 'pt-[12px]' : '']">
                    <span class="text-[14px] text-[#777]">{{ $t('Transition to') }}</span>
                    <div class="flex items-center">
                      <icon class="!text-[#3B71E6] !w-[24px] !h-[24px]" :data="iconArrowRight"></icon>
                      <div class="flex border rounded-full ml-[10px] py-[3px] px-4 text-white bg-[#75C045]" >
                        <span class="mr-auto text-[16px]">{{ $t('Resolved') }}</span>
                      </div>
                    </div>

                  </div>
                </div>
              </div>
            </div>
            <div class="flex mt-4 items-center">
              <span class="w-[150px] text-neutral-500">Assigned to</span>
              <el-select
                  filterable
                  :disabled="ticketSelected.status === 'resolved'"
                  :placeholder="$t('Select employee')"
                  class="type w-1/2"
                  v-model="ticketSelected.in_progressed_by"
                  @change="assignTicket"
              >
                <el-option
                    v-for="item in maintenanceEmployees"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                >
                </el-option>
              </el-select>
            </div>

            <div class="flex mt-4 items-center">
              <span class="w-[150px] text-neutral-500">Date</span>
              <div class="flex">
                <span>{{ formatTime(ticketSelected.created_at, 'YYYY-MM-DD HH:mm:ss') }}</span>
              </div>
            </div>
            <div>
              <p class="text-neutral-500 !mt-4">{{ $t('Machine info') }}</p>
              <div class="rounded-xl border mt-4 p-4">


                <div>
                  <div class="flex mb-3">
                    <span class="w-[135px] text-neutral-500">
                      Name
                    </span>
                      <span class="flex-1">
                      {{ ticketSelected?.machine?.name ?? NA }}
                    </span>
                  </div>
                  <div class="flex mb-3">
                  <span class="w-[135px] text-neutral-500">
                    Serial number
                  </span>
                    <span class="flex-1">
                    {{ ticketSelected?.machine?.series ?? NA }}
                  </span>
                  </div>
                  <div class="flex mb-3">
                  <span class="w-[135px] text-neutral-500">
                    Model
                  </span>
                    <span class="flex-1">
                    {{ ticketSelected?.machine?.model ?? NA }}
                  </span>
                  </div>
                  <div class="flex">
                  <span class="w-[135px] text-neutral-500">
                    Location
                  </span>
                    <span class="flex-1">
                    {{ ticketSelected?.machine?.location?.name ?? NA}}
                  </span>
                  </div>
                </div>
              </div>
            </div>
            <div class="rounded-xl border mt-4 p-4">

              <div class="flex items-center w-full">
                <span class="w-[135px] text-neutral-500">
                  Submitter
                </span>
                <div class="flex items-center">
                  <img v-if="ticketSelected?.employee_create?.avatar" :src="generateAvatar(ticketSelected?.employee_create?.avatar)" class="w-[24px] h-[24px] mr-2 rounded-full" alt="">
                  <img v-else src="@/assets/images/Avatar.png" class="w-[24px] h-[24px] mr-2 rounded-full" alt="">

                  <span>
                  {{ ticketSelected?.employee_create?.name ?? NA }}
                </span>
                </div>
                <div class="ml-auto">
                  <button v-if="showDetailEmployeeSubmit" @click="showDetailEmployeeSubmit = ! showDetailEmployeeSubmit">
                    <icon :data="iconArrowUp" class="mr-2" />
                  </button>
                  <button v-else @click="showDetailEmployeeSubmit = ! showDetailEmployeeSubmit">
                    <icon :data="iconArrowDown" class="mr-2" />
                  </button>
                </div>
              </div>
              <el-divider v-if="showDetailEmployeeSubmit" class="!my-4" />

              <div v-if="showDetailEmployeeSubmit">
                <div class="flex mb-3">
                  <span class="w-[135px] text-neutral-500">
                    Department
                  </span>
                  <span class="flex-1">
                    {{ ticketSelected?.employee_create?.department ?? NA }}
                  </span>
                </div>
                <div class="flex mb-3">
                  <span class="w-[135px] text-neutral-500">
                    Email
                  </span>
                  <span class="flex-1">
                    {{ ticketSelected?.creator_email ?? NA }}
                  </span>
                </div>
                <div class="flex">
                  <span class="w-[135px] text-neutral-500">
                    Phone number
                  </span>
                  <span>
                    {{ ticketSelected?.employee_create?.phone ?? NA}}
                  </span>
                </div>
              </div>
            </div>
            <div v-if="ticketSelected.employee_picking_up" class="rounded-xl border mt-4 p-4">

              <div class="flex items-center w-full">
              <span class="w-[135px] text-neutral-500">
                Assignee
              </span>
                <div class="flex items-center">
                  <img v-if="ticketSelected?.employee_picking_up?.avatar" :src="generateAvatar(ticketSelected?.employee_picking_up?.avatar)" class="w-[24px] h-[24px] mr-2 rounded-full" alt="">
                  <img v-else src="@/assets/images/Avatar.png" class="w-[24px] h-[24px] mr-2 rounded-full" alt="">
                  <span>
                  {{ ticketSelected?.employee_picking_up?.name ?? NA }}
                </span>
                </div>
                <div class="ml-auto">
                  <button v-if="showDetailEmployeeAssignee" @click="showDetailEmployeeAssignee = ! showDetailEmployeeAssignee">
                    <icon :data="iconArrowUp" class="mr-2" />
                  </button>
                  <button v-else @click="showDetailEmployeeAssignee = ! showDetailEmployeeAssignee">
                    <icon :data="iconArrowDown" class="mr-2" />
                  </button>
                </div>
              </div>
              <el-divider v-if="showDetailEmployeeAssignee" class="!my-4" />

              <div v-if="showDetailEmployeeAssignee">
                <div class="flex mb-3">
                  <span class="w-[135px] text-neutral-500">
                    Department
                  </span>
                  <span>
                    {{ ticketSelected?.employee_picking_up?.department ?? NA }}
                  </span>
                </div>
                <div class="flex mb-3">
                  <span class="w-[135px] text-neutral-500">
                    Email
                  </span>
                  <span>
                    {{ ticketSelected?.employee_picking_up?.email ?? NA }}
                  </span>
                </div>
                <div class="flex">
                  <span class="w-[135px] text-neutral-500">
                    Phone number
                  </span>
                  <span>
                    {{ ticketSelected?.employee_picking_up?.phone ?? NA}}
                  </span>
                </div>
              </div>
            </div>
            <div v-if="ticketSelected.employee_resolve" class="rounded-xl border mt-4 p-4">

              <div class="flex items-center w-full">
              <span class="w-[135px] text-neutral-500">
                Resolved by
              </span>
                <div class="flex items-center">
                  <img v-if="ticketSelected?.employee_resolve?.avatar" :src="generateAvatar(ticketSelected?.employee_resolve?.avatar)" class="w-[24px] h-[24px] mr-2 rounded-full" alt="">
                  <img v-else src="@/assets/images/Avatar.png" class="w-[24px] h-[24px] mr-2 rounded-full" alt="">
                  <span>
                  {{ ticketSelected?.employee_resolve?.name ?? NA }}
                </span>
                </div>
                <div class="ml-auto">
                  <button v-if="showDetailEmployeeResolve" @click="showDetailEmployeeResolve = ! showDetailEmployeeResolve">
                    <icon :data="iconArrowUp" class="mr-2" />
                  </button>
                  <button v-else @click="showDetailEmployeeResolve = ! showDetailEmployeeResolve">
                    <icon :data="iconArrowDown" class="mr-2" />
                  </button>
                </div>
              </div>
              <el-divider v-if="showDetailEmployeeResolve" class="!my-4" />

              <div v-if="showDetailEmployeeResolve">
                <div class="flex mb-3">
                  <span class="w-[135px] text-neutral-500">
                    Department
                  </span>
                  <span>
                    {{ ticketSelected?.employee_resolve?.department ?? NA }}
                  </span>
                </div>
                <div class="flex mb-3">
                  <span class="w-[135px] text-neutral-500">
                    Email
                  </span>
                  <span>
                    {{ ticketSelected?.employee_resolve?.email ?? NA }}
                  </span>
                </div>
                <div class="flex">
                  <span class="w-[135px] text-neutral-500">
                    Phone number
                  </span>
                  <span>
                    {{ ticketSelected?.employee_resolve?.phone ?? NA}}
                  </span>
                </div>
              </div>
            </div>
            <el-divider class="!my-4" />
            <div class="">
              <div class="mb-3">
              <span class="text-neutral-500">
                Description
              </span>
                <p class=" !mt-3">
                  {{ ticketSelected?.description ?? NA }}
                </p>
              </div>
            </div>
            <el-divider class="!my-4" />
            <div class="">
              <div class="mb-3">
              <span class="text-neutral-500">
                Attachments
              </span>
                <p v-if="ticketSelected?.maintenance_tracking_files?.length > 0"
                   @click="download(item.file)"
                   class=" !mt-3 flex" v-for="(item, index) in ticketSelected.maintenance_tracking_files">
                  <el-link :underline="false" class="custom-flex" type="primary">
                    <svg class="mt-[4px] mr-2" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M7.5 18.3334H12.5C16.6667 18.3334 18.3333 16.6667 18.3333 12.5001V7.50008C18.3333 3.33341 16.6667 1.66675 12.5 1.66675H7.5C3.33333 1.66675 1.66667 3.33341 1.66667 7.50008V12.5001C1.66667 16.6667 3.33333 18.3334 7.5 18.3334Z" stroke="#515961" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M10.1666 9.83327L8.99154 11.0083C8.34154 11.6583 8.34154 12.7166 8.99154 13.3666C9.64154 14.0166 10.6999 14.0166 11.3499 13.3666L13.1999 11.5166C14.4999 10.2166 14.4999 8.10828 13.1999 6.79995C11.8999 5.49995 9.79155 5.49995 8.48322 6.79995L6.46657 8.8166C5.3499 9.93327 5.3499 11.7416 6.46657 12.8583" stroke="#515961" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    {{ item.file }} </el-link>
                </p>
              </div>
            </div>

            <el-divider v-if="ticketSelected?.note" class="!my-4" />
            <div v-if="ticketSelected?.note" class="">
              <div class="mb-3">
              <span class="text-neutral-500">
                Resolved note
              </span>
                <p class=" !mt-3">
                  {{ ticketSelected?.note }}
                </p>
              </div>
            </div>
          </div>

        </el-scrollbar>
        <div class="w-5/12 pl-4 bg-[#EEF2F7] leading-[24px] relative">
          <el-scrollbar height="530px" class="">
            <div class="!max-w-[300px]">
              <div class="pt-[12px] !max-w-[300px]" v-for="(item, index) in ticketSelected.histories">
                <div class="flex items-center">
                  <img v-if="item?.employee?.avatar" :src="generateAvatar(item?.employee?.avatar)" class="w-[36px] h-[36px] mr-2 rounded-full" alt="">
                  <img v-else src="@/assets/images/Avatar.png" class="w-[36px] h-[36px] mr-2 rounded-full" alt="">
                  <div>
                    <p class="font-semibold text-[16px] leading-[24px] text-[#0C0D0E]">
                      {{ item?.employee?.name }}
                    </p>
                    <p class="text-[14px] font-normal leading-[24px] text-[#505050]">
                      {{ formatTime(item?.created_at, 'YYYY-MM-DD HH:mm:ss') }}
                    </p>
                  </div>
                </div>
                <div class="!max-w-[300px] !overflow-x-hidden">
                  <span class="text-[#0C0D0E] font-normal text-[16px] "> {{ item.message }}</span>
                </div>
              </div>
            </div>
          </el-scrollbar>

          <div class="absolute bottom-0.5 width-90 mr-0 bottom-4">
            <el-input
                @click="checkLogin"
                class="w-full relative"
                v-model="comment"
                @keyup.exact.enter="onSubmit"
                :rows="6"
                type="textarea"
            >

            </el-input>
            <el-button @click="onSubmit" class="absolute bottom-2 right-2 !rounded-md" type="primary">
              {{ $t('Comment') }}
            </el-button>
          </div>
        </div>


      </div>
<!--      <template #footer>-->
<!--        <span class="dialog-footer">-->
<!--          <el-button @click="showDetail = false">Close</el-button>-->
<!--          <el-button-->
<!--              v-if="ticketSelected.status != 'resolved'"-->
<!--              type="primary"-->
<!--              @click="-->
<!--              showDetail = false;-->
<!--            "-->
<!--          >Resolve</el-button-->
<!--          >-->
<!--        </span>-->
<!--      </template>-->
    </el-dialog>
    <el-dialog
        v-model="isOpenModalScanEmployee"
        :title="$t(`Scan Employee Code`)"
        custom-class="min-h-40 min-w-[400px] custom-dialog rounded-xl"
        @close="closeModalScanEmployee"
        destroy-on-close
        :close-on-click-modal="false"
        width="30%"
    >
      <template #default>
        <el-input
            v-if="!employee?.id"
            placeholder="Scan Employee Code"
            class="employee-input-scan"
            @keyup.enter="
                  scanCodeEmployee()
                "
            v-model="employeeCode"
        >
        </el-input>
      </template>
      <template #footer>
        <div class="flex justify-end">
          <el-button type="primary" :disabled="!employeeCode" @click="scanCodeEmployee()"
          >Confirm</el-button
          >
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="isOpenModalResolve"
        :title="$t(`Resolve Ticket`)"
        custom-class="min-h-40 min-w-[400px] custom-dialog rounded-xl"
        @close="closeModalResolve"
        destroy-on-close
        :close-on-click-modal="false"
        width="30%"
    >
      <template #default>
        <div v-if="!employee?.id">
          <div class="mb-[12px]">
            <span>{{ $t('Employee ID') }}</span>
            <span class="required-icon"></span>
          </div>
          <el-input
              placeholder="Scan Employee Code"
              class="employee-input-scan"
              @keyup.enter="scanCodeEmployee()"
              v-model="employeeCode"
          >
          </el-input>
        </div>
        <div>
          <div class="mb-[12px] mt-[12px]">
            <span>{{ $t('Note') }}</span>
            <span class="required-icon"></span>
          </div>
          <el-input
              :rows="5"
              type="textarea"
              v-model="note"
          >
          </el-input>
        </div>

      </template>
      <template #footer>
        <div class="flex justify-end">
          <el-button type="primary" :disabled="!employeeCode" @click="resolveTicket()"
          >Confirm</el-button
          >
        </div>
      </template>
    </el-dialog>


  </div>
</template>

<script>
  import EventBus from "@/utilities/eventBus";
  import dateMixin from "@/mixins/date";
  import {comment, detail, assignTicket, updateStatus} from "@/api/maintenanceTracking";
  import { getByCode } from '@/api/employee.js';
  import {isEmpty} from "ramda";
  import mixinFocusByElClass from '@/mixins/focusByElClass';
  import { S3_URL, STORAGE_URL } from "@/utilities/constants";


  export default {
    name: "DetailMaintenanceTracking",
    props: {
      urgency: {
        type: Array,
        default: () => []
      },
      employees: {
        type: Array,
        default: () => []
      },
    },
    mixins: [dateMixin, mixinFocusByElClass],
    data() {
      return {
        S3_URL: S3_URL,
        employeeAssignId: null,
        note: '',
        isChangeStatus: false,
        isOpenModalResolve: false,
        formDataReceiveRequest: {
          employeeCode: null,
        },
        formReceiveRules: {
          employeeCode: [
            {
              required: true,
              message: this.$t('This field cannot be left blank.'),
              trigger: ['blur'],
            },
          ]
        },
        showDetail: false,
        employeeCode: '',
        showNote: false,
        isOpenModalScanEmployee: false,
        showDetailEmployeeSubmit: false,
        showDetailEmployeeAssignee: false,
        showDetailEmployeeResolve: false,
        ticketSelected: {},
        employee: {},
        comment: '',
        status: [
          {
            label: 'Unresolved',
            value: 'unresolved'
          },
          {
            label: 'In Progress',
            value: 'in_progress'
          },
          {
            label: 'Resolved',
            value: 'resolved'
          }
        ],
        NA: 'N/A'
      }
    },
    computed: {
      maintenanceEmployees() {
        return this.employees.filter(
            (item) =>
                item.department && item.department.toLowerCase() === 'maintenance'
        );
      },
    },
    created() {

    },
    mounted() {

      EventBus.$on('showDetailMaintenanceTracking', async (id) => {
        const { data } = await detail(id);
        this.ticketSelected = data ?? {};
        this.showDetail = true;
      })
    },
    beforeUnmount() {
      EventBus.$off('showDetailMaintenanceTracking');
    },
    methods: {
      download(filePath) {
        return window.open(`${filePath}`, '_blank');
      },
      generateAvatar(avatar) {
        if (avatar) {
          return S3_URL + '/' + avatar;
        }
        return '@/assets/images/Avatar.png';
      },
      logout() {
        this.employee = {};
        this.employeeCode = '';
      },
      async assignTicket() {
        this.employeeAssignId = this.ticketSelected.in_progressed_by;
        if (isEmpty(this.employee)) {
          this.isOpenModalScanEmployee = true;
          this.focusByElClass('employee-input-scan');

          return
        }
        this.isLoading = true;
        try {
          const response = await assignTicket({
            maintenance_tracking_id: this.ticketSelected.id,
            employee_id: this.employee.id,
            in_progressed_by: this.ticketSelected.in_progressed_by
          });
          if (response.status === 200) {
            this.ticketSelected = response.data.data;
            this.notification(response.data.message, 'success');
            this.$emit('updateTicket', this.ticketSelected);
          }
        } catch (e) {
          this.isLoading = false;
          this.notification(e, 'error');
        } finally {
          this.isLoading = false;
          this.employeeAssignId = null;
        }
      },
      async fetchData() {
        try {
          this.isLoading = true;
          const res = await detail(this.ticketSelected.id);
          this.ticketSelected = res.data.data;
          this.isLoading = false;
        } catch (e) {
          this.isLoading = false;
          this.notification(e, 'error');
        }
      },
      async scanCodeEmployee() {
        try {
          this.isLoading = true;
          const res = await getByCode(this.employeeCode);
          if (!res.data.data) {
            this.codeEmployee = '';
            this.notification(
                'Scan employee code error, please scan again.',
                'error'
            );
            this.focusByElClass('employee-input-scan');
            this.isLoading = false;
            return;
          }
          this.employee = res.data.data;
          this.isLoading = false;
          this.isOpenModalScanEmployee = false;
          if (this.employeeAssignId) {
            await this.assignTicket();
          }
        } catch (e) {
          this.isLoading = false;
          this.notification(e, 'error');
        }
      },
      closeModalScanEmployee() {
        this.isOpenModalScanEmployee = false;
      },
      closeModalResolve() {
        this.showNote = false;
        this.isOpenModalResolve = false;
      },
      checkLogin() {
        if (isEmpty(this.employee)) {
          this.isOpenModalScanEmployee = true;
          this.focusByElClass('employee-input-scan');
        }
      },
      async onSubmit() {
        this.comment = this.comment.trim();
        if (isEmpty(this.employee)) {
          this.isOpenModalScanEmployee = true;
          this.focusByElClass('employee-input-scan');
          return
        }
        if (isEmpty(this.comment)) {
          return;
        }
        this.isLoading = true;
        try {
          let param = {
            maintenance_tracking_id: this.ticketSelected.id,
            employee_id: this.employee.id,
            message: this.comment
          };
          this.comment = '';
          const response = await comment(param);
          if (response.status === 200) {
            this.ticketSelected = response.data.data;
            this.$emit('updateTicket', this.ticketSelected);
          }
        } catch (e) {
          this.isLoading = false;
          this.notification(e, 'error');
        }
      },
      resetData() {
        this.ticketSelected = {};
        this.showDetail = false;
        this.isChangeStatus = false;
        this.showNote = false;
        this.employee = {};
        this.employeeCode = '';
      },
      toggleUpdateStatus() {
        if (this.ticketSelected.status !== 'resolved') {
          this.isChangeStatus = !this.isChangeStatus;
        } else {
          this.isChangeStatus = false;
        }
      },
      async processTicket() {
        if (isEmpty(this.employee)) {
          this.isOpenModalScanEmployee = true;
          this.focusByElClass('employee-input-scan');
          return;
        }
        this.isLoading = true;
        try {

          const response = await updateStatus({
            maintenance_tracking_id: this.ticketSelected.id,
            employee_id: this.employee.id,
            status: 'in_progress'
          });
          if (response.status === 200) {
            this.ticketSelected = response.data.data;
            this.isChangeStatus = false;
            this.notification(response.data.message, 'success');
            this.$emit('updateTicket', this.ticketSelected);
          }
        } catch (e) {
          this.isLoading = false;
          this.notification(e, 'error');
        }
      },
      openModelResole() {
        this.isOpenModalResolve = true;
      },
      async resolveTicket() {
        if (isEmpty(this.employee)) {
          this.isOpenModalScanEmployee = true;
          this.focusByElClass('employee-input-scan');
          return;
        }
        this.isLoading = true;
        try {

          const response = await updateStatus({
            maintenance_tracking_id: this.ticketSelected.id,
            employee_id: this.employee.id,
            status: 'resolved',
            note: this.note,
          });
          if (response.status === 200) {
            this.ticketSelected = response.data.data;
            this.isChangeStatus = false;
            this.closeModalResolve();
            this.notification(response.data.message, 'success');
            this.$emit('updateTicket', this.ticketSelected);
            this.note = '';
          }
        } catch (e) {
          this.isLoading = false;
          this.notification(e, 'error');
        }
      },
      generateClass() {
        switch (this.ticketSelected.urgency) {
          case 'urgent':
            return 'bg-[#F07674]';
            break;
          case 'high':
            return 'bg-[#F39545]';
            break;
          case 'medium':
            return 'bg-[#6B64EF]';
            break;
          case 'low':
            return 'bg-[#c1bebb]';
            break;
        }
      },
      generateBGStatus() {
        switch (this.ticketSelected.status) {
          case 'unresolved':
            return 'bg-[#E87D1A]';
            break;
          case 'in_progress':
            return 'bg-[#3B71E6]';
            break;
          case 'resolved':
            return 'bg-[#75C045]';
            break;
        }
      },
      generateStatus(status) {
        switch (status) {
          case 'unresolved':
            return 'Unresolved';
            break;
          case 'in_progress':
            return 'In Progress';
            break;
          case 'resolved':
            return 'Resolved';
            break;
        }
      },
      generateUrgency(urgency) {
        switch (urgency) {
          case 'urgent':
            return 'Urgent';
            break;
          case 'high':
            return 'High';
            break;
          case 'medium':
            return 'Medium';
            break;
          case 'low':
            return 'Low';
            break;
        }
      },
    }
  }
</script>