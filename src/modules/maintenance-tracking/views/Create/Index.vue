<style src="./Style.scss" lang="scss"></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
      v-model="openDialogCreate"
      custom-class="el-dialog-custom custom-dialog rounded-xl"
      @close="resetData"
      :close-on-click-modal="false"
      width="800px"
    >
      <template #title>
        <span class="font-bold text-xl">Create equipment maintenance ticket</span>
      </template>
      <template #default>
        <div class=" border-top my-4">

          <el-form
            status-icon
            ref="formData"
            :model="data"
            :rules="dataRules"
            @submit.prevent="createTicket"
            label-width="130px"
            :label-position="'top'"
            class="flex"
          >
            <div class="w-7/12 border-r pr-4">
              <div class="bg-[#F9FAFB] mb-4">
                <div v-if="employee" class="bg-gray-50 p-3 border rounded-xl">
                  <div class="flex justify-between">
                    <b style="font-size: 18px" class="mr-2"
                    >{{ $t('Hi') }} {{ employee.name + ',' }}
                      {{ $t(' Have a nice day!') }}</b
                    >
                    <el-link
                        type="danger"
                        class="ml-3"
                        @click="resetEmployee"
                        :underline="false"
                    >{{ $t('Logout') }}</el-link
                    >
                  </div>
                </div>
                <div v-if="!employee" class="border rounded-xl p-3">
                  <div  class="mb-1 required-icon">
                    Scan Employee ID
                  </div>
                  <el-form
                      :error="employeeError"
                      @submit.prevent.native="getScanCodeEmloyee"
                  >
                    <el-input
                        :placeholder="$t('Employee ID')"
                        class="el-form-item-scan-employee border-radius-10 !w-full"
                        ref="employeeCode"
                        v-model="codeEmployee"
                    />
                  </el-form>
                </div>
              </div>
              <div>
                <el-form-item :label="$t('Ticket name')" prop="name">
                  <el-input
                      :placeholder="$t('Eg.ticket#1')"
                      v-model="data.name"
                      class="border-radius-10"
                      :class="{
                      'border border-red-400':
                      errorValidator && errorValidator.name,
                  }"
                  ></el-input>
                </el-form-item>
                <div
                    class="text-danger text-[12px]"
                    v-if="errorValidator && errorValidator.name"
                >
                  {{ errorValidator.name[0] }}
                </div>
              </div>
              <div class="rounded-xl border p-4">
                <el-form-item prop="machine_id" class="flex custom-justify">
                  <span class="required-icon">Machine name</span>
                  <el-select
                      :placeholder="$t('Select machine')"
                      class="type w-1/2"
                      filterable
                      v-model="data.machine_id"
                      @change="selectMachine"
                  >
                    <el-option
                        v-for="item in machines"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-divider></el-divider>
                <div class="flex justify-between">
                  <span>
                    Serial number
                  </span>
                  <span>
                    {{ machineSelected?.series ?? NA}}
                  </span>
                </div>
                <el-divider></el-divider>

                <div class="flex justify-between">
                  <span>
                    Model
                  </span>
                  <span>
                    {{ machineSelected?.model ?? NA}}
                  </span>
                </div>
                <el-divider></el-divider>

                <div class="flex justify-between">
                  <span>
                    Location
                  </span>
                  <span>
                    {{ machineSelected?.location?.name ?? NA}}
                  </span>
                </div>
              </div>
              <div class="mt-4">
                <p class=" pb-2">Department</p>
                <p class=" leading-[30px] pl-[16px] w-full !border rounded-[10px] min-h-[30px]"> {{ employee?.department }}</p>
              </div>
              <div class="mt-4">
                <p class=" pb-2">Urgency</p>
                <div class="flex">
                  <div :class="['flex border rounded-full py-[3px] px-4 mr-auto cursor-pointer', data.urgency == item.value ? 'border-[#1A73E8] text-black' : 'text-[#515961]']" v-for="(item, index) in urgency" @click="selectUrgent(item)">
                    <div :class="['w-3 h-3 border mr-2 rounded-full  my-auto',generateClass(item.value)]"></div>
                    <span>{{ item.label }}</span>
                  </div>
                </div>
              </div>
              <div class="mt-4">
                <el-form-item :label="$t('Email')" prop="email">
                  <el-input
                      :placeholder="$t('Email')"
                      v-model="data.email"
                      class="border-radius-10"
                      :class="{
                    'border border-red-400 border-radius-10':
                      errorValidator && errorValidator.creator_email,
                  }"
                  ></el-input>
                </el-form-item>
                <div
                    class="text-danger text-[12px]"
                    v-if="errorValidator && errorValidator.creator_email"
                >
                  {{ errorValidator.creator_email[0] }}
                </div>
              </div>
            </div>
            <div class="w-5/12 pl-4">
              <div class="">
                <el-form-item :label="$t('Description')" prop="description">
                  <el-input
                      :placeholder="$t('Describe more about your issue')"
                      v-model="data.description"
                      class="border-radius-10"
                      :rows="4"
                      type="textarea"
                      :class="{
                    'border border-red-400':
                      errorValidator && errorValidator.description,
                  }"
                  ></el-input>
                </el-form-item>
                <div
                    class="text-danger text-[12px]"
                    v-if="errorValidator && errorValidator.description"
                >
                  {{ errorValidator.description[0] }}
                </div>
              </div>
              <div>
                <div>
                  <p>{{ $t('Attachments') }}</p>
                  <div
                      class="justify-space-between layout-default custom-upload-file"
                  >
                    <el-upload
                        class="upload-demo"
                        drag
                        multiple
                        :auto-upload="false"
                        :on-change="handleChange"
                        :data="listUpload"
                    >
                      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                      <div class="el-upload__text">
                        Drop file here or <em>click to upload</em>
                      </div>
                    </el-upload>
                  </div>
                </div>
                <div v-if="errorAttachment.length > 0">
                  <div
                      v-for="error in errorAttachment"
                      class="text-danger text-[12px]"
                  >
                    <p>{{ $t(error) }}</p>
                  </div>
                </div>

                <div
                    v-for="(name, index) in fileSelected"
                    :key="index"
                    class="relative hover:bg-gray-100 rounded"
                >
              <span class="ml-1"> {{ name }} </span
              ><icon
                    @click="removeFile(name)"
                    class="absolute right-1 cursor-pointer"
                    :data="iconCancel"
                />
                </div>
              </div>

            </div>
          </el-form>
        </div>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <el-button
              @click="resetData()"
          >Cancel</el-button
          >
          <el-button
            type="primary"
            @click="createTicket"
            :disabled="isLoading || !employee"
            :loading="isLoading"
          >
            {{ $t('Submit') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>