import { create } from '@/api/maintenanceTracking.js';
import EventBus from '@/utilities/eventBus.js';
import { STORAGE_URL } from '@/utilities/constants';
import { isEmpty } from 'ramda';
import focusMixin from '@/mixins/helpers.js';
import {
  employeeLogoutTimeChecking,
  employeeTimeChecking,
} from '@/api/employee.js';
import { list } from '@/api/department.js';

export default {
  name: 'CreateMaintenanceTracking',
  mixins: [focusMixin],
  data() {
    return {
      openDialogCreate: false,
      employee: null,
      time_checking_id: null,
      codeEmployee: '',
      employeeError: '',
      job_type: 'create_ticket',
      departments: [],
      isLoading: false,
      hasErrorAttachment: false,
      errorValidator: {},
      images: [],
      files: [],
      videos: [],
      image: null,
      file: null,
      video: null,
      listUpload: [],
      fileSelected: [],
      data: {
        urgency: 'medium',
      },
      urlStorage: STORAGE_URL,
      dataRules: {
        urgency: [this.commonRule()],
        machine_id: [this.commonRule()],
        name: [this.commonRule()],
        description: [this.commonRule()],
        email: [this.commonRule()],
      },
      requiredMessage: '',
      mapKey: [],
      time: 0,
      errorAttachment: [],
      machineSelected: {},
      NA: 'N/A'
    };
  },
  props: {
    machines: {
      type: Array,
      default: [],
    },
    status: {
      default: [],
      type: Array,
    },
    urgency: {
      default: [],
      type: Array,
    },
  },
  watch: {
    openDialogCreate() {
      if (!this.openDialogCreate) {
        this.listUpload = [];
        this.fileSelected = [];
        this.files = [];
        this.$emit('forceRender');
      }
    },
  },
  async created() {
    await this.fetchDepartments();
    EventBus.$on('showCreateInternalTicket', () => {
      this.openDialogCreate = true;
    });
  },

  methods: {
    selectUrgent(item) {
        this.data.urgency = item.value;
    },
    generateClass(item) {
      switch (item) {
        case 'urgent':
          return 'bg-[#F07674]';
          break;
        case 'high':
          return 'bg-[#F39545]';
          break;
        case 'medium':
          return 'bg-[#6B64EF]';
          break;
        case 'low':
          return 'bg-[#c1bebb]';
          break;
      }
    },
    selectMachine(id) {
      this.machineSelected = this.machines.find((item) => item.id === id);
    },
    validateImage(value, attr) {
      if (value) {
        this.errorValidator[attr][0] = 'Invalid value. Example: 16x16';
        return false;
      } else if (this.errorValidator.hasOwnProperty(attr)) {
        delete this.errorValidator[attr];
      }
      return true;
    },
    async fetchDepartments() {
      this.isLoading = true;
      try {
        const response = await list();
        this.departments = response.data;
      } catch (e) {
        this.notification('Department not found', 'error');
      }
      this.isLoading = false;
    },
    async resetEmployee() {
      await employeeLogoutTimeChecking(this.time_checking_id);
      this.employee = null;
      this.employeeError = '';
      this.codeEmployee = '';
      this.time = 0;
      this.time_checking_id = null;
      this.focusByElClassScanEmployee();
    },
    focusByElClassScanEmployee(elClass = 'el-form-item-scan-employee') {
      this.$nextTick(() => {
        const el = document.getElementsByClassName(elClass);
        if (!el || !el.length) {
          return;
        }
        const firtsElError = (el && el[0]) || undefined;
        const input = firtsElError.querySelector('input');
        document.body.scrollTop = (input.offsetTop - 120) | 0;
        input.focus();
      });
    },
    async getScanCodeEmloyee() {
      try {
        this.isLoading = true;
        const res = await employeeTimeChecking({
          code: Number(this.codeEmployee),
          job_type: this.job_type,
        });
        if (!res.data.data) {
          this.codeEmployee = '';
          this.employeeError = 'Scan employee code error, please scan again.';
          this.notification(
            'Scan employee code error, please scan again.',
            'error'
          );
          this.focusByElClassScanEmployee();
          return;
        }
        this.employee = res.data.data;
        if (this.employee?.email) {
            this.data.email = this.employee.email;
        }
        this.time_checking_id = res.data.id_time_checking;
        this.focusByElClass();
        this.isLoading = false;
      } catch (e) {
        const data = e.response.data;
        let message = this.$t('Not found');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.isLoading = false;
        this.notification(message, 'error');
      }
    },
    // handleFileUpload(type) {
    //   switch (type) {
    //     case 'file':
    //       this.files.push(this.$refs.file.input.files[0]);
    //       if (
    //         !this.fileSelected.includes(this.$refs.file.input.files[0].name)
    //       ) {
    //         this.fileSelected.push(this.$refs.file.input.files[0].name);
    //       }
    //       break;
    //     case 'image':
    //       this.images.push(this.$refs.image.input.files[0]);
    //       if (
    //         !this.fileSelected.includes(this.$refs.image.input.files[0].name)
    //       ) {
    //         this.fileSelected.push(this.$refs.image.input.files[0].name);
    //       }
    //       break;
    //     case 'video':
    //       this.videos.push(this.$refs.video.input.files[0]);
    //       if (
    //         !this.fileSelected.includes(this.$refs.video.input.files[0].name)
    //       ) {
    //         this.fileSelected.push(this.$refs.video.input.files[0].name);
    //       }
    //       break;
    //   }
    // },
    commonRule() {
      return {
        required: true,
        message: this.$t('This field cannot be left blank.'),
        trigger: 'change',
      };
    },
    async resetData() {
      this.openDialogCreate = false;
      this.errorValidator = {};
      this.data = {};
      this.requiredMessage = '';
      this.listUpload = [];
      this.errorAttachment = [];
      this.machineSelected = {};
      await this.$refs['formData'].resetFields();
      await this.resetEmployee();
    },
    handleChange(file, listFile) {
      this.listUpload = listFile;
    },
    async createTicket() {
      const isValid = await this.$refs['formData'].validate();
      if (!isValid) return;
      this.isLoading = true;
      let formData = new FormData();
      formData.append('name', this.data.name);
      formData.append('machine_id', this.data.machine_id ?? '');
      if (this.data.urgency) {
        formData.append('urgency', this.data.urgency);
      }
      formData.append('description', this.data.description);
      formData.append('created_by', this.employee.id);
      formData.append('creator_email', this.data.email ?? '');
      try {
        for (let i = 0; i < this.listUpload.length; i++) {
          let file = this.listUpload[i].raw;
          formData.append('file[' + i + ']', file);
        }
        await create(formData);
        const notification = this.$t(
          'Create maintenance tracking ticket successfully',
          'success'
        );
        this.data = {};
        this.$refs['formData'].resetFields();
        this.notification(notification);
        this.$emit('createdTicket');
        this.openDialogCreate = false;
      } catch (e) {
        this.errorValidator = e.response.data.errors;
        this.mapKey = Object.keys(this.errorValidator);
        this.hasErrorAttachment = this.mapKey.some((item) => {
          return (
            item.indexOf('file') !== -1
          );
        });
        this.errorAttachment = [];
        for (const property in this.errorValidator) {
          if (property.indexOf('file') !== -1) {
            let name =
              this.errorValidator[property][0].match(/\s(.*)\.(\d)+\s/);
            let stringReplace = name[0].split('.')[0] + ' ';
            let error = this.errorValidator[property][0].replace(
              name[0],
              stringReplace
            );
            this.errorAttachment.push(error);
          }
        }
      }
      this.isLoading = false;
    },
    removeFile(name) {
      // this.images = this.images.filter((item) => {
      //   this.image = null;
      //   return item.name != name;
      // });
      // this.videos = this.videos.filter((item) => {
      //   this.video = null;
      //   return item.name != name;
      // });
      // this.files = this.files.filter((item) => {
      //   this.file = null;
      //   return item.name != name;
      // });
      this.fileSelected = this.fileSelected.filter((item) => item != name);
    },
  },
};
