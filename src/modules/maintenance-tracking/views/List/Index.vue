<style src="./Style.scss" lang="scss"></style>
<script src="./Script.js"></script>
<template>
  <div class="break-words">
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ $t('Maintenance tracking') }}</h1>
      </div>
      <div class="top-head-right">
        <span class="mr-3">{{ $t('Total Tickets: ') }}{{ totalTicket }}</span>
        <span class="mr-3">{{ $t('Resolved: ') }}{{ totalResolved }}</span>
        <span class="mr-3">{{ $t('In Progress: ') }}{{ totalInProgress }}</span>
        <span class="mr-3 text-red-500"
          >{{ $t('Unresolved: ') }}{{ totalUnresolved }}</span
        >
        <el-button type="primary" @click="create">
          <span class="icon-margin-right"><icon :data="iconAdd" /></span
          >{{ $t('Create') }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <div class="filter">
        <div class="label">{{ $t('Filter by:') }}</div>
        <div class="filter-item ml-2">
          <el-dropdown
            ref="ticket_number"
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('ticket_number') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('ticket_number')">
                <el-tooltip
                  effect="dark"
                  :content="$t('Ticket Number')"
                  placement="top-start"
                >
                  <span>{{ filter.ticket_number }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t('Ticket Number') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input
                  :placeholder="$t('Enter search Ticket Number')"
                  class="search"
                  v-model="filter.ticket_number"
                  @keydown.enter="onFilter('ticket_number')"
                  clearable
                  @clear="clearFilterItem('ticket_number')"
                />
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item ml-2">
          <el-dropdown
              ref="machine_name"
              trigger="click"
              class="el-dropdown-filter-item"
              :class="{ 'is-active': hasChangeFilterByItem('machine_id') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('machine_id')">
                <el-tooltip
                    effect="dark"
                    :content="$t('Machine name')"
                    placement="top-start"
                >
                  <span>{{ getMachine(filter.machine_id) }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t('Machine name') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-select
                    filterable
                    v-model="filter.machine_id"
                    :placeholder="$t('Select machine name')"
                    @change="onFilter('machine_id')"
                >
                  <el-option
                      v-for="item in machines"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                  >
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item ml-2">
          <el-dropdown
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('urgency') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('urgency')">
                <el-tooltip
                  effect="dark"
                  :content="$t('Urgency')"
                  placement="top-start"
                >
                  <span>{{ getUrgency(filter.urgency) }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(' Urgency ') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-select
                  filterable
                  v-model="filter.urgency"
                  :placeholder="$t('Select Urgency')"
                  @change="onFilter"
                >
                  <el-option
                    v-for="item in urgency"
                    :key="item.value"
                    :label="item.label"
                    :value="String(item.value)"
                  >
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item ml-2">
          <el-dropdown
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('status') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('status')">
                <el-tooltip
                  effect="dark"
                  :content="$t('Status')"
                  placement="top-start"
                >
                  <span>{{ getStatus(filter.status) }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(' Status ') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-select
                  filterable
                  v-model="filter.status"
                  :placeholder="$t('Select Status')"
                  @change="onFilter"
                >
                  <el-option
                    v-for="item in status"
                    :key="item.value"
                    :label="item.label"
                    :value="String(item.value)"
                  >
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item ml-2">
          <el-dropdown
            ref="employee_id"
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('employee_id') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('employee_id')">
                <el-tooltip
                  effect="dark"
                  :content="$t('Submitter')"
                  placement="top-start"
                >
                  <span>{{ getEmployee(filter.employee_id) }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t('Submitter') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-select
                    filterable
                    v-model="filter.employee_id"
                    :placeholder="$t('Select submitter')"
                    @change="onFilter('employee_id')"
                >
                  <el-option
                      v-for="item in employees"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                  >
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item ml-2">
          <el-dropdown
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': date && date.length }"
          >
            <span class="el-dropdown-link">
              <template v-if="date && date.length">
                <el-tooltip
                  effect="dark"
                  :content="$t('Issued Date')"
                  placement="top-start"
                >
                  <span> {{ templateDateRange(date[0], date[1]) }}</span>
                </el-tooltip>
              </template>
              <template v-else> {{ $t('Issued Date') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-date-picker
                  format="YYYY-MM-DD"
                  v-model="date"
                  type="daterange"
                  range-separator="To"
                  :start-placeholder="$t('Start Date')"
                  :end-placeholder="$t('End Date')"
                  @change="onChangeDate"
                >
                </el-date-picker>
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item" v-if="hasFilter">
          <el-link type="danger" @click="onClearFilter" :underline="false">
            {{ $t('Clear') }}
          </el-link>
        </div>
      </div>

      <el-table
        stripe
        border
        size="small"
        :data="items"
        style="width: 100%"
        :max-height="maxHeight"
        v-loading="isLoading"
        element-loading-text="Loading..."
        @row-click="selectedRow"
      >
        <el-table-column
          class-name="cursor-pointer"
          prop="ticket_number"
          :label="$t('Ticket Number')"
        >
          <template class="text-cyan-400 hover:" #default="scope">
            <el-link :underline="false" type="primary">
              {{ scope.row.maintenance_tracking_number }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column prop="ticket_name" :label="$t('Ticket Name')">
          <template #default="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>

        <el-table-column prop="machine" :label="$t('Machine name')">
          <template #default="scope">
            {{ scope.row?.machine?.name }}
          </template>
        </el-table-column>

        <el-table-column
          prop="urgency"
          class-name="text-center"
          :label="$t('Urgency')"
        >
          <template #default="scope">
            <el-tag
              type="info"
              :class="getClassUrgency(scope.row.urgency)"
              class="rounded-xl"
              effect="dark"
              round
              size="small"
            >
              {{ getOrderStatusByValue(scope.row.urgency, urgency) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="status"
          class-name="text-center"
          :label="$t('Status')"
        >
          <template class="text-cyan-400 hover:" #default="scope">
            <el-tag
              :type="
                scope.row.status == 'unresolved'
                  ? 'error'
                  : scope.row.status == 'resolved'
                  ? 'success'
                  : ''
              "
            >
              {{ getOrderStatusByValue(scope.row.status, status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="employee_name" :label="$t('Submitter')">
          <template #default="scope">
            {{ scope.row?.employee_create?.name }}
          </template>
        </el-table-column>
<!--        <el-table-column prop="creator_email" :label="$t('Submitter email')">-->
<!--          <template #default="scope">-->
<!--            {{ scope.row?.creator_email }}-->
<!--          </template>-->
<!--        </el-table-column>-->

        <el-table-column prop="employee_name" :label="$t('Resolved by')">
          <template #default="scope">
            {{ scope.row?.employee_resolve?.name }}
          </template>
        </el-table-column>

        <el-table-column prop="assign_to" :label="$t('Assign')">
          <template #default="scope">
            <el-tooltip
              v-if="scope.row?.employee_picking_up"
              effect="dark"
              :content="
                convertUtcToLocalTime(
                  scope.row.in_progressed_at,
                  'YYYY-MM-DD HH:mm:ss'
                )
              "
              placement="top-start"
            >
              {{ scope.row?.employee_picking_up?.name }}
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column
          prop="created_at"
          :label="$t('Issued Date')"
        >
          <template #default="scope">
            {{
              formatTime(scope.row.created_at, 'YYYY-MM-DD HH:mm:ss')
            }}
          </template>
        </el-table-column>
        <el-table-column prop="age" :label="$t('Age')">
          <template #default="scope">
            {{ scope.row?.age ?? '' }}
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom">
        <div class="total">
          {{ $t('Total:') }} {{ items.length ? formatNumber(total) : 0 }}
        </div>
        <el-pagination
          :disabled="isLoading"
          background
          layout="prev, pager, next"
          :page-size="filter.limit"
          :total="total"
          @current-change="changePage"
          v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
            v-model="filter.limit"
            :placeholder="$t('Select')"
            size="mini"
            @change="onFilter"
          >
            <el-option
              v-for="item in limits"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
    <el-dialog
      v-model="assignDialog"
      @close="closeAssignDialog"
      :show-close="false"
      width="30%"
      custom-class="dialog"
    >
      <template #title>
        <span class="font-bold text-xl word-break-custom">Assign To Dev</span>
      </template>
      <div class="custom-dialog">
        <div>
          <div class="mb-2">
            <div v-if="employee" class="bg-gray-50 p-3 border rounded">
              <div class="flex justify-between">
                <b style="font-size: 18px" class="mr-2"
                  >{{ $t('Hi') }} {{ employee.name + ',' }}
                  {{ $t(' Have a nice day!') }}</b
                >
                <el-link
                  type="danger"
                  class="ml-3"
                  @click="resetEmployee"
                  :underline="false"
                  >{{ $t('Logout') }}
                </el-link>
              </div>
            </div>
          </div>
          <div v-if="!employee" class="mb-1 required-icon">
            Scan Employee ID
          </div>
          <div
            v-show="!employee"
            :error="employeeError"
            @keyup.enter="getScanCodeEmloyee"
          >
            <el-input
              :placeholder="$t('Employee ID')"
              class="el-form-item-scan-employee max-w-fit"
              ref="employeeCode"
              v-model="codeEmployee"
            />
          </div>
        </div>
        <el-form
          class="mt-4"
          status-icon
          ref="formData"
          :model="data"
          :rules="dataRules"
          @submit.prevent="onSubmit"
          label-width="130px"
          :label-position="'top'"
        >
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="assignDialog = false">Close</el-button>
          <el-button type="primary" :disabled="!employee" @click="assignTicket"
            >Assign</el-button
          >
        </span>
      </template>
    </el-dialog>
    <div v-if="renderCreatePopup">
      <CreateMaintenanceTracking
        @createdTicket="fetchData"
        :status="status"
        :urgency="urgency"
        :machines="machines"
        :vendors="vendors"
        @forceRender="forceRender"
      />
    </div>
    <DetailMaintenanceTracking
        :urgency="urgency"
        :employees="employees"
        @updateTicket="updateTicket"
    />
  </div>
</template>
<style scoped>
.word-break-custom {
  word-break: break-word;
}
.el-dialog__body {
  @apply py-2;
}
</style>
