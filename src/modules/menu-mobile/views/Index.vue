<style src="./Style.scss" lang="scss" scoped>
</style>
<script src="./Script.js"></script>
<template>
  <div class="page-menu">
    <div class="header-title">
      MENU
    </div>
    <div class="menu-list">
      <div class="menu-item" v-for="item in MenuMobile" @click="routerRedirect(item)">
        <icon :data="renderIcon(item)" width="60" height="60"></icon>
        <p>{{ $t(item.name) }}</p>
      </div>

    </div>
  </div>
</template>
