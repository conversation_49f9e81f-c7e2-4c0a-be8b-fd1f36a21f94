import http from "@/utilities/http.js";
import MenuMobile from "@/utilities/MenuMobile.js";

export default {
  name: "Menu",
  minxins: [],
  data() {
    return {
      MenuMobile,
      isLoading: false,
    };
  },
  methods: {
    routerRedirect(item) {
      const routerName = item.router.name || "";
      if (!routerName) {
        return;
      }
      return this.$router.push({ name: routerName });
    },
    renderIcon(item) {
      return (item.icon && this[item.icon]) || "";
    },
  },
};
