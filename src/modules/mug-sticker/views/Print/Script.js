import {convertPdf, countMug, downloadPdf, historyPdf, listPdf} from "@/api/mugs3DConvert.js";
import {getByCode} from "@/api/employee.js";
import {ElMessage} from 'element-plus';
import {S3_URL} from "@/utilities/constants";
//import {appFireBase, database} from '@/utilities/firebase';
//import { ref, onValue, set } from 'firebase/database';

export default {
    name: "3d-printing",
    data() {
        return {
            page: 1,
            dataCount: {},
            stylePicked: null,
            dialogVisible: false,
            valueInput: 0,
            employee_number: null,
            clickConfirm: false,
            pendingList: {},
            activeTab: 'convert',
            dialogVisibleDownload: false,
            pdf: '',
            pdfDownload: {},
            limit: 11,
            historyList: {},
            pageHistory: 1,
            dialogVisibleDownloadHistory: false,
            pdfDownloadHistory: {},
            pdfHistory: '',
            passwordRedownload: '',
            password: null,
            refreshListPdf: null,
            refreshCount: null,
            onThisPage: true,
            searchLabelId: '',
            unlockHistory: false,
            clientId: '',
            print_method: 'UV3DS'
        }
    },

    mounted() {
        this.countMugConvert();
        this.getListPdf();
        this.getHistoryPdf();

        let clientId = localStorage.getItem('clientId');
        if (!clientId) {
            clientId = this.generateClientId();
            localStorage.setItem('clientId', clientId);
        }
        this.clientId = clientId;
    },

    methods: {
        generateClientId() {
            return Math.random().toString(36).substr(2, 9);
        },
        async searchLabelDownloaded() {
            this.page = 1;
            await this.getHistoryPdf();
        },
        saveClientId() {
            localStorage.setItem('clientId', this.clientId);
        },
        async countMugConvert() {
            let params = {
                page: this.page,
                print_method: this.print_method,
            }

            try {
                const res = await countMug(params);
                this.dataCount = res.data.data;
            } catch (e) {
                this.serverErrors = e.response.data.errors;
                this.notification(e.response.data.message, 'error', false, {duration: 10000});
            }

            clearTimeout(this.refreshCount);
            let x = setTimeout(() => {
                if (this.onThisPage) {
                    this.countMugConvert()
                }
            }, 10 * 1000);
            this.refreshCount = x;
        },

        setConvert(item) {
            this.stylePicked = item;
            this.valueInput = item.count >= 30 ? 30 : item.count;
        },

        confirmConvertBtn() {
            this.employee_number = null;
            if (this.stylePicked == null) {
                ElMessage({
                    type: 'warning',
                    message: this.$t('Please select the option to convert'),
                })
                return false
            }
            if (this.valueInput == 0) {
                ElMessage({
                    type: 'warning',
                    message: this.$t('Limit must be greater than zero'),
                })
                return false
            }

            this.dialogVisible = true;
        },

        async confirmStaff() {
            this.clickConfirm = true;
            if (!this.employee_number) {
                ElMessage({
                    type: 'warning',
                    message: this.$t('Please enter the staff number to confirm the convert pdf'),
                })
                this.clickConfirm = false;
                return false
            }
            const res = await getByCode(this.employee_number)
            if (!res.data.data) {
                ElMessage({
                    type: 'error',
                    message: this.$t('The staff number does not exist'),
                })
                this.clickConfirm = false;
                return false
            }

            try {
                let params = {
                    employee_id: res.data.data.id,
                    product_id: this.stylePicked.product_id,
                    quantity: this.valueInput,
                    print_method: this.print_method,
                }
                await convertPdf(params);
                this.getListPdf();
                this.countMugConvert();
            } catch (e) {
                this.notification(e.response.data.message, 'error', false, {duration: 10000});
            }
            this.dialogVisible = false;
        },

        async getListPdf() {
            let params = {
                page: this.page,
                limit: this.limit,
                print_method: this.print_method
            };

            try {
                const res = await listPdf(params);
                this.pendingList = res.data.data;
            } catch (e) {
                this.serverErrors = e.response.data.errors;
                this.notification(e.response.data.message, 'error', false, {duration: 10000});
            }

            clearTimeout(this.refreshListPdf);
            let x = setTimeout(() => {
                if (this.onThisPage) {
                    this.getListPdf()
                }
            }, 10 * 1000);
            this.refreshListPdf = x;
        },

        async getHistoryPdf() {
            let params = {
                page: this.pageHistory,
                limit: this.limit,
                label_id: this.searchLabelId,
                print_method: this.print_method
            };

            try {
                const res = await historyPdf(params);
                this.historyList = res.data.data;
            } catch (e) {
                this.serverErrors = e.response.data.errors;
                this.notification(e.response.data.message, 'error', false, {duration: 10000});
            }
        },

        popupDownloadPdf(item) {
            this.dialogVisibleDownload = true;
            this.pdfDownload = item;
            this.pdf = `${S3_URL}/3d/${item.id}.pdf?v=` + Math.floor(Math.random() * 1000);
        },

        popupDownloadPdfHistory(item) {
            this.password = null;
            let today = new Date();
            let md = String(today.getMonth() + 1).padStart(2, '0') + String(today.getDate()).padStart(2, '0');
            this.passwordRedownload = md;
            this.dialogVisibleDownloadHistory = true;
            this.pdfDownloadHistory = item;
            this.pdfHistory = `${S3_URL}/3d/${item.id}.pdf?v=` + Math.floor(Math.random() * 1000);
        },

        async confirmDownloadedBtn() {
            try {

                let res = await this.convertPts(this.pdfDownload);

                if (res.status == 200) {
                    this.notification(res.data.message, 'success', false, {duration: 10000});
                    await downloadPdf({
                        barcode_printed_id: this.pdfDownload.id,
                        is_history: false
                    })

                    this.getListPdf();
                    this.getHistoryPdf();
                    this.countMugConvert();
                } else {
                    this.notification(res.data.error, 'error', false, {duration: 10000});
                }

            } catch (e) {
                this.notification(e.response.data.status, 'error', false, {duration: 10000});
            }
        },

        async confirmDownloadedHistoryBtn() {
            if (this.passwordRedownload != this.password) {
                ElMessage({
                    type: 'error',
                    message: this.$t('The password is incorrect'),
                })
            } else {
                let res = await this.convertPts(this.pdfDownloadHistory)
                if (res.status != 200) {
                    this.notification(res.data.error, 'error', false, {duration: 10000});
                    return;
                } else {
                    this.notification(res.data.status, 'success', false, {duration: 10000});
                }
                this.unlockHistory = true;
            }
        },

        handleCloseHistory() {
            this.dialogVisibleDownloadHistory = false;
            this.unlockHistory = false;
        },

        changePage(page) {
            this.page = page;
            this.$nextTick(() => {
                this.getListPdf();
            });
        },

        changePageHistory(pageHistory) {
            this.pageHistory = pageHistory;
            this.$nextTick(() => {
                this.getHistoryPdf();
            });
        },

        async convertPts(item) {
            let msg = {
                id: item.id,
                sku: item.product.sku,
                staff: item.employee_convert.name,
                file: S3_URL + '/3d/' + item.id + '.png',
                status: 'pending',
                date: item.converted_at
            }

            let res = await fetch('http://localhost:3030/api/sendMessage', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    room: 'uv3d',
                    message: msg
                })
            });


            return {status: res.status, data: await res.json()};
        }
    },

    beforeUnmount() {
        clearTimeout(this.refreshListPdf);
        clearTimeout(this.refreshCount);
    },

    unmounted() {
        clearTimeout(this.refreshCount);
        clearTimeout(this.refreshListPdf);
    },

    beforeRouteLeave(to, from, next) {
        this.onThisPage = false;
        next();
    },


}