<style src="./Style.scss" lang="scss"></style>
<script src="./Script.js"></script>

<template>
  <div class="mug-convert">
    <div class="border-b pb-4 flex flex-row items-center">
      <div class="mr-5 ">
        <h3>{{ $t("MUGS WIP") }}</h3>
      </div>

      <div class="flex items-center gap-1">
        <div v-for="item in priorityStores" :key="item.id" @click="toggleSelectStore(item.id)"
          class="!mr-2 flex items-center gap-1 cursor-pointer px-2 h-[32px] border border-gray-300 rounded-md text-sm"
          :label="item.id" :class="{ '!border-blue-400': storeId === item.id }">
          <div>
            <div
              class="w-3 h-3 border border-gray-300 rounded-full relative after:absolute after:content-[''] after:w-[4px] after:h-[4px] after:bg-white after:rounded-full after:top-1/2 after:left-1/2 after:-translate-x-1/2 after:-translate-y-1/2"
              :class="{ '!border-blue-400 bg-blue-500': storeId === item.id }">
            </div>
          </div>
          <div class="text-sm" :class="{ 'text-primary': storeId === item.id }">
            {{ item.code || storeNA }}({{ item.count }})
          </div>
        </div>
      </div>

      <div class="text-right grow ">{{ $t("Total Pending: ") }}
        <span class="font-semibold">{{ all.total ?? 0 }}</span>
      </div>
    </div>

    <div class="flex flex-row">
      <div class="basis-1/5 border-r">
        <div v-if="styles?.data?.length == 0">
          <el-empty :description="$t('no data')"></el-empty>
        </div>

        <el-scrollbar style="height: calc(100vh - 150px);">
          <div v-if="styles?.data" v-for="item in styles.data" :key="item.product_id" @click="setConvert(item)"
            class="scrollbar-item bg-gradient-to-r from-cyan-200 to-blue-300 hover:from-cyan-300 hover:to-blue-400 my-3 mr-3 p-3"
            :class="{ 'border-4 border-blue-500/75': item?.product_id == stylePicked?.product_id }">
            <div class="name">{{ item.style_name + ' ' + item.size + 'Z ' + item.color }}</div>
            <div class="sub">
              {{ $t("Pending: ") }}<strong>{{ item.count }}</strong><br> {{ $t("Last Created: ") }}
              {{ item.last_created_at ? utcToLocalTime(item.last_created_at).format('lll') : 'not yet' }}
            </div>
          </div>
        </el-scrollbar>
      </div>

      <div class="basis-1/5 border-r">
        <div class="item-xqc m-3 p-3 bg-gradient-to-r from-rose-300 to-red-500 hover:from-rose-400	hover:to-red-600"
          @click="setXQC()" :class="{ 'border-4 border-red-500/75': is_xqc == 1 }">
          <div class="name">{{ $t("XQC") }}</div>
          <div class="sub">
            {{ $t("Pending Label: ") }}<strong>{{ new Intl.NumberFormat().format(xqc?.total) }}</strong><br>
            {{ $t("Last Created: ") }}
            {{ xqc?.printed_at ? utcToLocalTime(xqc.printed_at).format('lll') : 'not yet' }}
          </div>
        </div>

        <div
          class="item-xqc m-3 p-3 bg-gradient-to-r from-orange-400 to-orange-600 hover:from-orange-500	hover:to-orange-700"
          @click="setEps()" :class="{ 'border-4 border-orange-700/75': is_eps == 1 }">
          <div class="name">{{ $t("EXPRESS") }}</div>
          <div class="sub">
            {{ $t("Pending Label: ") }}<strong>{{ new Intl.NumberFormat().format(eps?.total) }}</strong><br>
            {{ $t("Last Created: ") }}
            {{ eps?.printed_at ? utcToLocalTime(eps.printed_at).format('lll') : 'not yet' }}
          </div>
        </div>

        <div v-if="isPriorityStore" @click="setAll()" :class="{ 'border-4 border-yellow-500/75': is_all == 1 }"
          class="item-xqc m-3 p-3 bg-gradient-to-r from-amber-200 to-orange-400	 hover:from-amber-300	hover:to-orange-500 ">
          <div class="name">{{ $t('ALL') }}</div>
          <div class="sub">
            {{ $t("Pending Label: ") }}<strong>{{ new Intl.NumberFormat().format(all?.total) }}</strong><br>
            {{ $t("Last Created: ") }}
            {{ all?.printed_at ? utcToLocalTime(all.printed_at).format('lll') : 'not yet' }}
          </div>
        </div>

        <div
          class="item-xqc m-3 p-3 bg-gradient-to-r from-fuchsia-200 to-pink-400 hover:from-fuchsia-300	hover:to-pink-500"
          @click="setReprint()" :class="{ 'border-4 border-pink-500/75': is_reprint == 1 }">
          <div class="name">{{ $t('RE-PRINT') }}</div>
          <div class="sub">
            {{ $t("Pending Label: ") }}<strong>{{ new Intl.NumberFormat().format(reprint?.total) }}</strong><br>
            {{ $t("Last Created: ") }}
            {{ reprint?.printed_at ? utcToLocalTime(reprint.printed_at).format('lll') : 'not yet' }}
          </div>
        </div>

        <div
          class="item-xqc m-3 p-3 bg-gradient-to-r from-yellow-200 to-yellow-400 hover:from-yellow-300	hover:to-yellow-500"
          @click="setManualProcess()" :class="{ 'border-4 border-yellow-500/75': is_manual == 1 }">
          <div class="name">{{ $t('MANUAL') }}</div>
          <div class="sub">
            {{ $t("Pending Label: ") }}<strong>{{ new Intl.NumberFormat().format(manualProcess?.total) }}</strong><br>
            {{ $t("Last Created: ") }}
            {{ manualProcess?.printed_at ? utcToLocalTime(manualProcess.printed_at).format('lll') : 'not yet' }}
          </div>
        </div>

        <div
          class="item-xqc m-3 p-3 bg-gradient-to-r from-green-300 to-green-500 hover:from-green-400 hover:to-green-600"
          @click="setReroute()" :class="{ 'border-4 border-green-500/75': is_reroute == 1 }">
          <div class="name">{{ $t('REROUTE') }}</div>
          <div class="sub">
            {{ $t("Pending Label: ") }}<strong>{{ new Intl.NumberFormat().format(reroute?.total) }}</strong><br>
            {{ $t("Last Created: ") }}
            {{ reroute?.printed_at ? utcToLocalTime(reroute.printed_at).format('lll') : 'not yet' }}
          </div>
        </div>

        <div class="item-xqc m-3 p-3 bg-gradient-to-r from-cyan-200 to-blue-300 hover:from-cyan-300  hover:to-blue-400"
          @click="setTiktok()" :class="{ 'border-4 border-blue-500/75 from-cyan-300 to-blue-400': is_tiktok == 1 }">
          <div class="name">{{ $t('TIKTOK') }}</div>
          <div class="sub">
            {{ $t("Pending Label: ") }}<strong>{{ new Intl.NumberFormat().format(tiktok?.total) }}</strong><br>
            {{ $t("Last Created: ") }}
            {{ tiktok?.printed_at ? utcToLocalTime(tiktok?.printed_at).format('lll') : 'not yet' }}
          </div>
        </div>

        <div
          class="item-xqc m-3 p-3 bg-gradient-to-r from-violet-200 to-violet-400 hover:from-violet-300 hover:to-violet-500"
          @click="setFba()" :class="{ 'border-4 border-violet-500/75': is_fba == 1 }">
          <div class="name">{{ $t('FBA') }}</div>
          <div class="sub">
            {{ $t("Pending Label: ") }}<strong>{{ new Intl.NumberFormat().format(fba?.total) }}</strong><br>
            {{ $t("Last Created: ") }}
            {{ fba?.printed_at ? utcToLocalTime(fba.printed_at).format('lll') : 'not yet' }}
          </div>
        </div>

        <div class="item-xqc m-3 p-3 bg-gradient-to-r from-lime-200 to-lime-400 hover:from-lime-300 hover:to-lime-500"
          @click="setBulkOrder()" :class="{ 'border-4 border-lime-500/75': is_bulk_order == 1 }">
          <div class="name">{{ $t('Bulk Order') }}</div>
          <div class="sub">
            {{ $t("Pending Label: ") }}<strong>{{ new Intl.NumberFormat().format(bulkOrder?.total) }}</strong><br>
            {{ $t("Last Printed: ") }}
            {{ bulkOrder?.printed_at ? utcToLocalTime(bulkOrder.printed_at).format('lll') : 'not yet' }}
          </div>
        </div>
      </div>

      <div class="basis-3/4">
        <div class="p-3">
          <el-input-number v-model="valueInput" class="mr-3" style="width: 150px" :min="0"
            :max="maxItems <= 300 ? maxItems : 300" />
          <el-button @click="confirmConvertBtn" type="primary">{{ $t("Confirm") }}</el-button>
        </div>
        <el-dialog v-model="dialogVisible" :title="$t('Assign To Staff')" width="30%">
          <div>
            <el-input class="m-0" v-model="employee_number" size="large" :placeholder="$t('Enter The Staff Number')">
            </el-input>
          </div>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="dialogVisible = false">{{ $t("Cancel") }}</el-button>
              <el-button type="primary" :disabled="clickConfirm" @click="confirmStaff()">{{ $t("Confirm") }}</el-button>
            </span>
          </template>
        </el-dialog>
        <el-tabs v-model="activeTab" class="ml-3">
          <el-tab-pane :label="$t('Convert')" name="convert">
            <el-table stripe :data="pendingList.data" style="width: 100%">
              <el-table-column prop="id" :label="$t('ID')" width="120" />
              <el-table-column :label="$t('Created at')" width="180">
                <template #default="scope">
                  {{ utcToLocalTime(scope.row.created_at).format('lll') }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('Filter')" width="200">
                <template #default="scope">
                  <el-tag v-if="scope.row?.style_sku && scope.row?.product?.size && scope.row?.product?.color"
                    class="mr-2 mb-1" size="small" type="success">
                    {{ scope.row.style_sku + ' ' + scope.row.product.size + 'Z ' + scope.row.product.color }}
                  </el-tag>
                  <el-tag v-if="scope.row.is_xqc == 1" class="mr-2 mb-1" size="small" type="danger">
                    {{ $t("Xqc") }}
                  </el-tag>

                  <el-tag v-if="scope.row.is_eps == 1" class="mr-2 mb-1" size="small" type="danger">
                    {{ $t("Express") }}
                  </el-tag>

                  <el-tag v-if="+scope.row.is_reprint == 1" class="mr-2 mb-1" size="small" type="danger">
                    {{ $t("Reprint") }}
                  </el-tag>

                  <el-tag v-if="scope.row.is_manual == 1" class="mr-2 mb-1" size="small" type="warning">
                    {{ $t("Manual") }}
                  </el-tag>

                  <el-tag v-if="scope.row.is_reroute == 1" class="mr-2 mb-1" size="small" type="warning">
                    {{ $t("Reroute") }}
                  </el-tag>
                  <el-tag v-if="scope.row.is_tiktok == 1" class="mr-2 mb-1" size="small" type="danger">
                    {{ $t("Tiktok") }}
                  </el-tag>
                  <el-tag v-if="scope.row.is_fba == 1" class="mr-2 mb-1" size="small" type="warning">
                    {{ $t("FBA") }}
                  </el-tag>
                  <el-tag v-if="scope.row.is_bulk_order == 1" class="mr-2 mb-1" size="small"
                    style="background-color: #D5F895; color: #000">
                    {{ $t("Bulk Order") }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column prop="employee_name" :label="$t('Staff')">
                <template #default="scope">
                  {{ scope.row.employee_convert.name }}
                </template>
              </el-table-column>
              <el-table-column prop="quantity" :label="$t('Quantity')" width="100">
                <template #default="scope">
                  {{ scope.row.convert_percent }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('Status')" width="160">
                <template #default="scope">
                  <el-tag v-if="+scope.row.convert_status == 1" type="success">{{ $t("Completed") }}</el-tag>
                  <el-tag v-else-if="+scope.row.convert_status == 2" type="danger">{{ $t("Error") }}</el-tag>
                  <el-tag v-else type="warning">{{ $t("Pending") }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column width="250" :label="$t('Action')">
                <template #default="scope">
                  <template v-if="scope.row.print_status == 0">
                    <el-button size="small" :disabled="scope.row.convert_status !== 1" type="primary"
                      @click="popupDownloadPdf(scope.row)">
                      {{ $t("Download") }}
                    </el-button>
                    <el-button size="small" :disabled="scope.row.convert_status !== 1" type="success"
                               @click="sendFileToPrinter(scope.row)">
                      {{ $t("Send To App Printing") }}
                    </el-button>
                  </template>
                  <template v-if="scope.row.print_status == 1">
                    <el-button size="small" :disabled="scope.row.convert_status !== 1" type="info">{{ $t("Downloaded")
                      }}
                    </el-button>
                  </template>
                </template>
              </el-table-column>
            </el-table>
            <div class="mt-3 items-center">
              <el-pagination background :page-size="limit" :pager-count="10" layout="prev, pager, next"
                :total="pendingList.total" @current-change="changePage">
              </el-pagination>
            </div>
          </el-tab-pane>
          <el-tab-pane :label="$t('History')" name="history">
            <el-table stripe :data="historyList.data" style="width: 100%">
              <el-table-column prop="id" :label="$t('ID')" width="120" />
              <el-table-column :label="$t('Created at')" width="180">
                <template #default="scope">
                  {{ utcToLocalTime(scope.row.created_at).format('lll') }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('Filter')">
                <template #default="scope">
                  <el-tag class="mr-2 mb-1" size="small" type="success">
                    {{ scope.row.style_sku + ' ' + scope.row.product.size + 'Z ' + scope.row.product.color }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="employee_name" :label="$t('Staff')">
                <template #default="scope">
                  {{ scope.row.employee_convert.name }}
                </template>
              </el-table-column>
              <el-table-column prop="quantity" :label="$t('Quantity')" width="100">
                <template #default="scope">
                  {{ scope.row.convert_percent }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('Status')" width="160">
                <template #default="scope">
                  <el-tag v-if="+scope.row.convert_status == 1" type="success">{{ $t("Completed") }}</el-tag>
                  <template v-else>
                    <el-tag type="warning">{{ $t("Pending") }}</el-tag>
                  </template>
                </template>
              </el-table-column>
              <el-table-column width="400" :label="$t('Action')">
                <template #default="scope">
                  <el-button size="small" type="primary" @click="popupDownloadPdfHistory(scope.row)">
                    {{ $t("Re-Download") }}
                  </el-button>
                  <el-button size="small" :disabled="scope.row.convert_status !== 1" type="success"
                             @click="sendFileToPrinter(scope.row, false)">
                    {{ $t("Send To App Printing") }}
                </el-button>
                  <el-button
                      size="small"
                      :disabled="scope.row.convert_status !== 1"
                      type="warning"
                      @click="convertAi(scope.row)"
                  >
                    {{ $t("Convert To .ai") }}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="mt-3 flex items-center">
              <div class="flex">
                <el-input v-model="searchLabelId" :placeholder="$t('Search Label ID')">
                  <template #append>
                    <el-button @click="searchLabelDownloaded">{{ $t("Search") }}</el-button>
                  </template>
                </el-input>
              </div>
              <el-pagination class="ml-5" background :page-size="limit" :pager-count="10" layout="prev, pager, next"
                :total="historyList.total" @current-change="changePageHistory">
              </el-pagination>
            </div>
          </el-tab-pane>
        </el-tabs>
        <el-dialog v-model="dialogVisibleDownload" :title="$t('Preview MUGS')" width="60%" top="5vh">
          <iframe ref="theFrame" class="m-0 p-0" :src="pdf + '#view=Fit'" style="height: 500px; width: 100%;"></iframe>
          <div class="mt-3">
            <el-button type="primary" @click="confirmDownloadedBtn($event)">{{ $t("Confirm") }}
            </el-button>
          </div>
        </el-dialog>
        <el-dialog v-model="dialogVisibleDownloadHistory" :before-close="handleCloseHistory" :title="$t('Preview MUGS')"
          width="60%" top="5vh">
          <div v-if="!unlockHistory" class="bg-slate-200" style="height: 450px"></div>
          <iframe v-else ref="theFrame" class="m-0 p-0" :src="pdfHistory + '#view=Fit'"
            style="height: 500px; width: 100%;"></iframe>
          <h2 class="mt-2 mb-2"><strong>{{ $t("Enter Password To Unlock Download") }}</strong></h2>
          <el-input v-model="password" />
          <div class="mt-3">
            <el-button type="primary" @click="confirmDownloadedHistoryBtn($event)">{{ $t("Confirm") }}
            </el-button>
          </div>
        </el-dialog>
      </div>
    </div>
  </div>
</template>