import { countMug, listPdf, convertPdf, downloadPdf, historyPdf, fetchPdf, countPendingPriorityStore, print, checkAppMugPrintingIsOpen, convertToAi } from "@/api/mugsConvert.js";
import { getByCode } from "@/api/employee.js";
import { ElMessage } from 'element-plus';
import { S3_URL, NOT_APPLICABLE } from "@/utilities/constants";

export default {
  name: "Mugs-convert",

  data() {
    return {
      page: 1,
      dataCount: {},
      stylePicked: null,
      dialogVisible: false,
      valueInput: 0,
      employee_number: null,
      clickConfirm: false,
      pendingList: {},
      activeTab: 'convert',
      dialogVisibleDownload: false,
      pdf: '',
      pdfDownload: {},
      limit: 11,
      historyList: {},
      pageHistory: 1,
      dialogVisibleDownloadHistory: false,
      pdfDownloadHistory: {},
      pdfHistory: '',
      passwordRedownload: '',
      password: null,
      refreshListPdf: null,
      refreshCount: null,
      onThisPage: true,
      searchLabelId: '',
      unlockHistory: false,
      priorityStores: [],
      storeSelected: null,
      storeNA: NOT_APPLICABLE,
      storeId: null,
      isSelectStore: false,
      isPriorityStore: false,
      tiktok: {},
      fba: {},
      reroute: {},
      manualProcess: {},
      reprint: {},
      xqc: {},
      eps: {},
      styles: {},
      all: {},
      is_reprint: null,
      is_manual: null,
      is_reroute: null,
      is_fba: null,
      is_eps: null,
      is_tiktok: null,
      style_sku: null,
      is_xqc: null,
      is_all: null,
      is_style_sku: null,
      is_bulk_order: null,
      maxItems: 0,
    }
  },

  mounted() {
    this.countMugConvert();
    this.getListPdf();
    this.getHistoryPdf();
    this.countPendingPriorityStore();
    this.checkAppMugPrintingIsOpen()
  },

  methods: {
    toggleSelectStore(store_id) {
      if (this.storeId == store_id) {
        this.storeId = null;
        this.isPriorityStore = false;

      } else {
        this.storeId = store_id;
        this.isPriorityStore = true;
      }

      this.countMugConvert();
    },

    async countPendingPriorityStore() {
      const res = await countPendingPriorityStore()

      this.priorityStores = res?.data?.data || [];

      if (this.isPriorityStore && this.priorityStores.length == 0) {
        this.isPriorityStore = false;
        this.storeId = null;
      }
    },

    async triggerCount(store_id) {
      if (this.isSelectStore) {
        this.storeId = store_id;
      } else {
        this.storeId = null;
      }

      this.countMugConvert();
    },

    async searchLabelDownloaded() {
      this.page = 1;

      await this.getHistoryPdf();
    },

    async countMugConvert() {
      let params = {
        page: this.page,
        store_id: this.storeId ?? null
      }

      try {
        const res = await countMug(params);

        this.dataCount = res.data.data;
        this.styles = this.dataCount.style;
        this.tiktok = this.dataCount.tiktok;
        this.fba = this.dataCount.fba;
        this.reroute = this.dataCount.reroute;
        this.manualProcess = this.dataCount.manual;
        this.reprint = this.dataCount.reprint;
        this.xqc = this.dataCount.xqc;
        this.eps = this.dataCount.eps;
        this.bulkOrder = this.dataCount.bulk_order;
        let total = 0;
        let lastPrinted = null;

        for (const property in this.dataCount) {
          total += this.dataCount[property]?.total;
          if (property == 'style' && this.dataCount[property]?.data?.length > 0) {
            this.dataCount[property].data.forEach(item => {
              if (!lastPrinted || lastPrinted < item?.last_created_at) {
                lastPrinted = item?.last_created_at
              }
            });
          }
        }

        this.all.printed_at = lastPrinted;
        this.all.total = total;
      } catch (e) {
        this.serverErrors = e.response.data.errors;
        this.notification(e.response.data.message, 'error', false, { duration: 10000 });
      }

      clearTimeout(this.refreshCount);

      let x = setTimeout(() => {
        if (this.onThisPage) {
          this.countMugConvert()
          this.countPendingPriorityStore()
        }
      }, 10 * 1000);

      this.refreshCount = x;
    },

    setConvert(item) {
      this.resetFilter()
      this.stylePicked = item;
      this.is_style_sku = 1;
      this.maxItems = item.count
      this.valueInput = item.count >= 90 ? 90 : item.count;
    },

    confirmConvertBtn() {
      this.employee_number = null;

      if (
        this.style_sku == null
        && this.is_xqc == null
        && this.all == 0
        && this.is_reprint == null
        && this.is_manual == null
        && this.is_reroute == null
        && this.is_fba == null
        && this.is_eps == null
        && this.is_tiktok == null
        && this.is_bulk_order == null
      ) {
        ElMessage({
          type: 'warning',
          message: this.$t('Please select the option to convert'),
        })

        return false
      }

      if (this.valueInput == 0) {
        ElMessage({
          type: 'warning',
          message: this.$t('Limit must be greater than zero'),
        })

        return false
      }

      this.dialogVisible = true;
    },

    async confirmStaff() {
      this.clickConfirm = true;

      if (!this.employee_number) {
        ElMessage({
          type: 'warning',
          message: this.$t('Please enter the staff number to confirm the convert pdf'),
        })
        this.clickConfirm = false;

        return false
      }

      const res = await getByCode(this.employee_number)

      if (!res.data.data) {
        ElMessage({
          type: 'error',
          message: this.$t('The staff number does not exist'),
        })
        this.clickConfirm = false;

        return false
      }

      try {
        let params = {
          employee_id: res.data.data.id,
          product_id: this.stylePicked?.product_id,
          quantity: this.valueInput,
          store_id: this.storeId ?? null,
          is_fba: this.is_fba,
          is_tiktok: this.is_tiktok,
          is_reroute: this.is_reroute,
          is_manual: this.is_manual,
          is_reprint: this.is_reprint,
          is_xqc: this.is_xqc,
          is_eps: this.is_eps,
          is_all: this.is_all,
          is_style_sku: this.is_style_sku,
          is_bulk_order: this.is_bulk_order,
        }

        this.dialogVisible = false;

        await convertPdf(params);

        this.getListPdf();
        this.countMugConvert();
        this.countPendingPriorityStore()
      } catch (e) {
        this.notification(e.response.data.message, 'error', false, { duration: 10000 });
      }
      this.clickConfirm = false;
    },

    async getListPdf() {
      let params = {
        page: this.page,
        limit: this.limit
      };

      try {
        const res = await listPdf(params);

        this.pendingList = res.data.data;
      } catch (e) {
        this.serverErrors = e.response.data.errors;
        this.notification(e.response.data.message, 'error', false, { duration: 10000 });
      }

      clearTimeout(this.refreshListPdf);
      let x = setTimeout(() => {
        if (this.onThisPage) {
          this.getListPdf()
        }
      }, 10 * 1000);

      this.refreshListPdf = x;
    },

    async getHistoryPdf() {
      let params = {
        page: this.pageHistory,
        limit: this.limit,
        label_id: this.searchLabelId
      };

      try {
        const res = await historyPdf(params);

        this.historyList = res.data.data;
      } catch (e) {
        this.serverErrors = e.response.data.errors;
        this.notification(e.response.data.message, 'error', false, { duration: 10000 });
      }
    },

    popupDownloadPdf(item) {
      this.dialogVisibleDownload = true;
      this.pdfDownload = item;
      this.pdf = `${S3_URL}/mugs/${item.id}.pdf?v=` + Math.floor(Math.random() * 1000);
    },
    async sendFileToPrinter(item, is_update_barcode = true) {
      try {
        this.notification(this.$t(`Sending file with format: ${item.id}.pdf to the printing app`), 'info');
        this.pdf = `${S3_URL}/mugs/${item.id}.pdf?v=` + Math.floor(Math.random() * 1000);
        await print({
          url: this.pdf,
          file: item.id + '.pdf',
        });
        if(is_update_barcode){
          await downloadPdf({
            barcode_printed_id: item.id,
            is_history: false
          })
        }
        this.getListPdf();
        this.getHistoryPdf();
        this.countMugConvert();
        this.notification(this.$t("Send successfully"));
      } catch (e) {
        this.notification(this.$t("Send Failed"), 'error');
      } finally {
        this.isLoading = false;
      }
    },
    async convertAi(item) {
      try {
        const { id, positions } = item;
        const msg = { id, positions };
        const res = await fetch('http://localhost:3030/api/sendMessage', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ room: 'convert-ai', message: msg })
        });
        if (res.status !== 200) {
          const dataRes = await res.json();
          this.notification(dataRes.error, 'error', false, { duration: 10000 });
          return ;
        }
        this.notification(this.$t("Send successfully"));
      } catch (e) {
        this.notification(e.response.data.message, 'error', false, { duration: 5000 });
        this.notification("Error, please try again", 'error', false, { duration: 5000 });
      }
    },
    async checkAppMugPrintingIsOpen() {
      checkAppMugPrintingIsOpen().then((res) => {
        if (res.data?.status == 'success') {
          this.printers = res.data.data
        }
      }).catch((error) => {
        this.notification(this.$t('Please turn on the SWIFTPOD MUG PRINTTING app'), 'warning');
      });
    },
    popupDownloadPdfHistory(item) {
      this.password = null;
      let today = new Date();
      let md = String(today.getMonth() + 1).padStart(2, '0') + String(today.getDate()).padStart(2, '0');
      this.passwordRedownload = md;
      this.dialogVisibleDownloadHistory = true;
      this.pdfDownloadHistory = item;
      this.pdfHistory = `${S3_URL}/mugs/${item.id}.pdf?v=` + Math.floor(Math.random() * 1000);
    },

    async confirmDownloadedBtn() {
      try {
        window.open(this.pdf);

        await downloadPdf({
          barcode_printed_id: this.pdfDownload.id,
          is_history: false
        })

        this.dialogVisibleDownload = false;
        this.getListPdf();
        this.getHistoryPdf();
        this.countMugConvert();
      } catch (e) {
        this.notification(e.response.data.message, 'error', false, { duration: 10000 });
      }
    },

    async confirmDownloadedHistoryBtn() {
      if (this.passwordRedownload != this.password) {
        ElMessage({
          type: 'error',
          message: this.$t('The password is incorrect'),
        })
      } else {
        this.unlockHistory = true;
      }
    },

    handleCloseHistory() {
      this.dialogVisibleDownloadHistory = false;
      this.unlockHistory = false;
    },

    changePage(page) {
      this.page = page;
      this.$nextTick(() => {
        this.getListPdf();
      });
    },

    changePageHistory(pageHistory) {
      this.pageHistory = pageHistory;
      this.$nextTick(() => {
        this.getHistoryPdf();
      });
    },

    setAll() {
      this.resetFilter();
      this.valueInput = this.all.total >= 90 ? 90 : this.all.total;
      this.maxItems = this.all.total
      this.is_all = 1;
    },

    setXQC() {
      this.resetFilter();
      this.valueInput = this.xqc.total >= 90 ? 90 : this.xqc.total;
      this.maxItems = this.xqc.total
      this.is_xqc = 1;
    },

    setEps() {
      this.valueInput = this.eps.total >= 90 ? 90 : this.eps.total;
      this.resetFilter();
      this.maxItems = this.eps.total
      this.is_eps = 1;
    },

    setReprint() {
      this.valueInput = this.reprint.total >= 90 ? 90 : this.reprint.total;
      this.resetFilter();
      this.maxItems = this.reprint.total
      this.is_reprint = 1;
    },

    setManualProcess() {
      this.valueInput = this.manualProcess.total >= 90 ? 90 : this.manualProcess.total;
      this.resetFilter();
      this.maxItems = this.manualProcess.total
      this.is_manual = 1;
    },

    setReroute() {
      this.valueInput = this.reroute.total >= 90 ? 90 : this.reroute.total;
      this.resetFilter();
      this.maxItems = this.reroute.total
      this.is_reroute = 1;
    },

    setTiktok() {
      this.resetFilter();
      this.valueInput = this.tiktok.total >= 90 ? 90 : this.tiktok.total;
      this.maxItems = this.tiktok.total
      this.is_tiktok = 1;
    },

    setFba() {
      this.valueInput = this.fba.total >= 90 ? 90 : this.fba.total;
      this.resetFilter();
      this.maxItems = this.fba.total
      this.is_fba = 1;
    },

    setBulkOrder() {
      this.valueInput = this.bulkOrder.total >= 90 ? 90 : this.bulkOrder.total;
      this.resetFilter();
      this.maxItems = this.bulkOrder.total
      this.is_bulk_order = 1;
    },

    resetFilter() {
      this.style_sku = null;
      this.is_xqc = null;
      this.is_reprint = null;
      this.is_manual = null;
      this.is_all = null;
      this.is_reroute = null;
      this.is_fba = null;
      this.is_eps = null;
      this.is_tiktok = null;
      this.is_bulk_order = null;
      this.stylePicked = {};
      this.maxItems = 0;
    },
  },

  beforeUnmount() {
    clearTimeout(this.refreshListPdf);
    clearTimeout(this.refreshCount);
  },

  unmounted() {
    clearTimeout(this.refreshCount);
    clearTimeout(this.refreshListPdf);
  },

  beforeRouteLeave(to, from, next) {
    this.onThisPage = false;
    next();
  },
}