<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div class="top-head mb-3">
    <div class="top-head-left">
      <h1>{{ $t('Multiple Staging') }}</h1>
    </div>
  </div>
  <div class="flex gap-6 table-content">
    <div>
      <div class="demo-ruleForm" style="min-width: 400px">
        <div class="scan-employee">
          <div class="mb-3">
            <div
              v-if="employee.length > 0"
              style="min-width: 400px"
              class="flex"
            >
              <div
                class="bg-gray-50 p-3 border rounded mr-2"
                style="max-width: 400px"
              >
                <div class="flex justify-between">
                  <b style="font-size: 18px" class="mr-2"
                    >{{ $t('Hi') }} {{ totalNameEmployee }}
                    {{ $t(' Have a nice day!') }}</b
                  >
                  <el-link
                    type="danger"
                    class="ml-3"
                    @click="resetEmployee"
                    :underline="false"
                    >{{ $t('Logout') }}</el-link
                  >
                </div>
                <div class="text-lg text-fuchsia-500">
                  <IncrementTimer />
                </div>
              </div>
              <div class="m-auto">
                <el-button
                  @click="addEmployee()"
                  class="bg-[#1bae9f]"
                  type="primary"
                >
                  <span class="text-3xl font-bold text-white"
                    >+</span
                  ></el-button
                >
              </div>
            </div>
          </div>
          <div style="min-width: 400px" class="flex flex-col">
            <template v-for="(input, k) in itemsArray" :key="k">
              <div class="flex pb-3.5">
                <el-input
                  :placeholder="$t('Scan code employee')"
                  class="el-form-item-scan-employee border-stone-500 mr-2"
                  size="large"
                  ref="employeeCode"
                  v-model="input.codeEmployeeValue"
                  @keyup.enter="getScanCodeEmployee(k)"
                />
                <div class="m-auto" v-if="employee.length > 0">
                  <el-button
                    @click="removeEmployeeInList(k)"
                    class="bg-[#1bae9f] w-11"
                    type="danger"
                    ><span class="text-3xl font-bold text-white"
                      >-</span
                    ></el-button
                  >
                </div>
                <template v-else>
                  <div class="m-auto" v-if="k > 0">
                    <el-button
                      @click="removeEmployeeInList(k)"
                      class="bg-[#1bae9f] w-11"
                      type="danger"
                      ><span class="text-3xl font-bold text-white"
                        >-</span
                      ></el-button
                    >
                  </div>
                  <div class="m-auto" v-else>
                    <el-button
                      @click="addEmployee()"
                      class="bg-[#1bae9f] w-11"
                      type="primary"
                    >
                      <span class="text-3xl font-bold text-white"
                        >+</span
                      ></el-button
                    >
                  </div>
                </template>
              </div>
            </template>
          </div>
        </div>
        <div class="scan-sku mb-4">
          <div>
            <el-input
              :placeholder="$t('Scan QR code')"
              class="mt-2 el-form-item-tracking-number border-stone-500"
              style="max-width: 400px"
              size="large"
              ref="skuQualityControl"
              v-model="filter.qrCode"
              @keyup.enter="getScanQRCode()"
            />
          </div>
        </div>
        <div class="mb-4">
          <div class="font-semibold text-3xl text-slate-700">
            <strong>{{ sku }}</strong>
          </div>
        </div>
        <hr />
        <div class="mb-4">
          <div
            v-if="nameStore"
            class="font-semibold text-6xl text-slate-700 mr-5"
          >
            {{ nameStore }}
          </div>
        </div>
        <div class="mb-4">
          <div
            v-if="items?.shipping_method === 'express'"
            class="font-semibold text-6xl text-slate-700 mr-5 text-white bg-pink-500 rounded-lg p-5"
          >
            {{ $t('Express') }}
          </div>
          <div
            v-if="items?.shipping_method === 'priority'"
            class="font-semibold text-6xl text-slate-700 mr-5 text-white bg-orange-400 rounded-lg p-5"
          >
            {{ $t('Priority') }}
          </div>
        </div>
        <div class="mb-4">
          <div
            v-if="isXqc === 1"
            class="font-semibold text-6xl text-slate-700 mr-5 text-white bg-sky-500 rounded-lg p-5"
          >
            {{ $t('XQC') }}
          </div>
        </div>
      </div>
    </div>
    <div class="border rounded bg w-full">
      <template v-if="items">
        <PieChart :chartData="dataConfigChart" :height="maxHeight" />
      </template>
      <template v-else>
        <el-empty :description="$t('Please scan QR code')">
          <template #image>
            <img src="@/assets/images/scan-label-id.png" />
          </template>
        </el-empty>
      </template>
    </div>
  </div>
</template>
