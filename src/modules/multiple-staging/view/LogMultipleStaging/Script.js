import {getInfo} from "@/api/multipleStaging.js";
import {isEmpty} from "ramda";
import {Pie<PERSON><PERSON>} from "vue-chart-3";
import {Chart, registerables} from "chart.js";
import { employeeLogoutTimeCheckingMultipleStaging, employeeTimeChecking } from "@/api/employee.js";
import IncrementTimer from "@/components/IncrementTimer.vue";

Chart.register(...registerables);

export default {
    name: "MultipleStaging",
    components: {
        PieChart,
        IncrementTimer
    },
    data() {
        return {
            isLoading: false,
            filter: this.setDefaultFilter(),
            countOrderItem: null,
            countOrderItemScan: null,
            dataConfigChart: {
                // labels: ["Paris", "Nîmes", "Toulon", "Perpignan", "Autre"],
                datasets: [
                    {
                        data: [1, 1, 1],
                        backgroundColor: [],
                    },
                ],
            },
            items: null,
            sku: "",
            codeEmployee: "",
            employee: [],
            employeeError: '',
            skuError: '',
            job_type: 'multiple_staging',
            time_checking_id: null,
            isEps: null,
            isXqc: null,
            nameStore: '',
            itemsArray: [{
                codeEmployeeValue: ''
            }],
            totalNameEmployee: '',
            idParentTimeChecking: '',
            dataTimeCheckingId: [],
            employeeIdParentTimeChecking:'',
            arrCodeEmployee:[]
        };
    },
    created() {
        // this.focusByElClass();
        this.focusByElClassScanEmployee()
    },
    mounted() {
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 180);
        }
    },
    methods: {
        focusByElClass(elClass = "el-form-item-qrcode") {
            this.$nextTick(() => {
                const el = document.getElementsByClassName(elClass);
                if (!el || !el.length) {
                    return;
                }
                const firtsElError = (el && el[0]) || undefined;
                const input = firtsElError.querySelector("input");
                document.body.scrollTop = (input.offsetTop - 120) | 0;
                input.focus();
            });
        },
        setDefaultFilter() {
            return {
                qrCode: "",
            };
        },
        async getScanQRCode() {
            if(!this.employeeIdParentTimeChecking){
                const mess = "Employee code field cannot be left blank.";
                this.notification(mess, "error");
                this.filter.qrCode = ""
                this.focusByElClassScanEmployee()
                return;
            }
            this.employeeError = "";
            this.items = null;
            if (this.filter.qrCode === "") {
                this.focusByElClass();
                return;
            }
            this.sku = ''
            this.isEps = ''
            this.isXqc = ''
            this.nameStore = ''
            this.isLoading = true;
            try {
                let message = `Scan QR code ${this.filter.qrCode} success`
                const res = await getInfo({
                    label: this.filter.qrCode,
                    employee_id: this.employeeIdParentTimeChecking,
                    id_time_checking : this.dataTimeCheckingId
                });
                this.sku = this.filter.qrCode
                this.isEps =  res.data.data.is_eps
                this.isXqc =  res.data.data.is_xqc
                this.nameStore =  res.data.data.name
                this.countOrderItem = res.data.total_item
                this.countOrderItemScan = res.data.total_item_scan
                this.items = res.data.data
                this.dataConfigChart.datasets[0].data = this.arrayData()
                this.dataConfigChart.datasets[0].backgroundColor = this.backgroundColor()
                this.notification(message, "success");
                this.filter.qrCode = "";
                this.focusByElClass();
                this.isLoading = false;
            } catch (e) {
                const data = e.response.data;
                let message = this.$t("Not found QR code");
                if (!isEmpty(data)) {
                    const keyFirstData = Object.keys(data)[0];
                    const firstData = data[keyFirstData];
                    message = firstData[0];
                }
                this.filter.qrCode = "";
                this.focusByElClass();
                this.isLoading = false;
                this.notification(message, "error");
            }
        },
        backgroundColor() {
            var dataArrColor = [];

            if (this.countOrderItem === this.countOrderItemScan) {
                for (var i = 0; i < this.countOrderItem; i++) {
                    dataArrColor.push("#38bdf8")
                }
                return dataArrColor
            }

            for (var i = 0; i < this.countOrderItem; i++) {
                if (i < this.countOrderItemScan) {
                    dataArrColor.push("#a8a29e")
                } else {
                    dataArrColor.push("#f8fafc")
                }
            }
            return dataArrColor
        },
        arrayData() {
            var dataArr = [];
            for (var i = 0; i < this.countOrderItem; i++) {
                dataArr.push(1)
            }
            return dataArr;
        },
        focusByElClassScanEmployee(elClass = "el-form-item-scan-employee") {
            this.$nextTick(() => {
                const el = document.getElementsByClassName(elClass);
                if (!el || !el.length) {
                    return;
                }
                const firtsElError = (el && el[0]) || undefined;
                const input = firtsElError.querySelector("input");
                document.body.scrollTop = (input.offsetTop - 120) | 0;
                input.focus();
            });
        },
        async getScanCodeEmployee(index){
            try {
                this.isLoading = true;
                const code = this.itemsArray[index].codeEmployeeValue
                const checkCode = this.arrCodeEmployee.find(element => element === code);
                if(checkCode){
                    this.itemsArray[index].codeEmployeeValue = ''
                    let message = this.$t('Employee scanned');
                    this.notification(message, "error");
                    return
                }

                if(this.idParentTimeChecking === '') {
                    var data = {
                        code: Number(code),
                        job_type: this.job_type
                    }
                } else {
                    var data = {
                        code: Number(code),
                        job_type: this.job_type,
                        id_parent: this.idParentTimeChecking
                    }
                }
                const res = await employeeTimeChecking(data)
                if (!res.data.data) {
                    this.codeEmployee = "";
                    this.employeeError = "Scan employee code error, please scan again."
                    this.focusByElClassScanEmployee()
                    return;
                }
                this.itemsArray.splice(this.itemsArray[index], 1);
                if( this.idParentTimeChecking === '') {
                    this.idParentTimeChecking = res.data.id_time_checking
                    this.employeeIdParentTimeChecking = res.data.data.id
                }
                const dataEmployee = {
                    data:  res.data.data,
                    time_checking_id :res.data.id_time_checking
                }
                this.employee.push(dataEmployee);
                this.arrCodeEmployee.push(code)
                this.makeDataForMulStaging(this.employee)
                this.itemsArray.map((item)=> {item.codeEmployeeValue = ''})
                this.focusByElClass()
                this.isLoading = false;
                this.$refs.skuQualityControl.focus();
            } catch (e) {
                const data = e.response.data;
                let message = this.$t('Not found');
                if (!isEmpty(data)) {
                    const keyFirstData = Object.keys(data)[0];
                    const firstData = data[keyFirstData];
                    message = firstData[0];
                }
                this.itemsArray.map((item)=> {item.codeEmployeeValue = ''})
                this.isLoading = false;
                this.notification(message, "error");
            }
        },
        async resetEmployee(){
            const res = await employeeLogoutTimeCheckingMultipleStaging({id:this.dataTimeCheckingId})
            this.employee = []
            this.employeeError = ""
            this.codeEmployee =  ""
            this.time_checking_id = null
            this.idParentTimeChecking = ''
            this.totalNameEmployee = ''
            this.dataTimeCheckingId = []
            this.itemsArray= [{
                codeEmployeeValue: ''
            }]
            this.isEps =  null,
            this.isXqc =  null,
            this.nameStore =  ''
            this.sku = ''
            this.countOrderItem = null,
            this.countOrderItemScan = null
            this.items = null
            this.arrCodeEmployee = []
            this.focusByElClassScanEmployee()
        },
        addEmployee(){
            this.itemsArray.push({
                value: ''
            });
        },
        removeEmployeeInList(index){
            this.itemsArray.splice(index, 1)
        },
        makeDataForMulStaging(allEmployee){
            this.totalNameEmployee = ''
            this.dataTimeCheckingId = []
            allEmployee.forEach((item)=>{
                this.totalNameEmployee = this.totalNameEmployee + item.data.name + ', '
                this.dataTimeCheckingId.push(item.time_checking_id)
            })

       },


    }
};
