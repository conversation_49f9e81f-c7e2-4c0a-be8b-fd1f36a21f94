<style src="./Style.scss" lang="scss">

</style>
<script src="./Script.js"></script>
<template>
  <div class="ornament-convert">
    <div class="border-b pb-4 flex flex-row items-center">
      <div class="mr-5">
        <h3>{{ $t('Ornaments Convert') }}</h3>
      </div>
    </div>
    <div class="flex flex-row">
      <div class="basis-1/5 border-r">
        <div v-if="dataCount?.data?.length == 0">
          <el-empty :description="$t('no data')"></el-empty>
        </div>
        <el-scrollbar style="height: calc(100vh - 150px)">
          <div v-for="item in dataCount.data" :key="item.product_id" @click="setConvert(item)" class="
              scrollbar-item
              bg-gradient-to-r
              from-cyan-200
              to-blue-300
              hover:from-cyan-300 hover:to-blue-400
              my-3
              mr-3
              p-3
            " :class="{
              'border-4 border-blue-500/75':
                item?.product_id == productPicked?.product_id,
            }">
            <div class="name">{{ item.sku }}</div>
            <div class="sub">
              {{ $t('Last Created: ') }}
              {{
              item.last_created_at
              ? utcToLocalTime(item.last_created_at).format(
              'lll'
              )
              : 'not yet'
              }}
            </div>
          </div>
        </el-scrollbar>
        <el-dialog v-model="visibleDialogGen" @close="closeDialogGen" title="Generate Layout" width="65%" top="5vh">
          <template #default>
            <div class="flex">
              <div class="basis-1/4 border-r">
                <div class="scan-sku">
                  <el-form-item >
                    <el-input
                      :placeholder="$t('Scan Qr Code')"
                      class="el-form-item-tracking-number border-stone-500" style="max-width: 200px"
                      size="default"
                      ref="skuQualityControl"
                      v-model="label"
                      :disabled="loadingScan"
                      @keyup.enter="getScanBarcodeQualityControl()"
                    />
                  </el-form-item>
                </div>
                <div v-if="images.length > 0" class="list-labels">
                  <el-row v-for="(item, index) in images" :key="index">
                    <div class="mt-1 text-rose-600 w-10">{{ index + 1 }}: </div>
                    <div class="border-t mt-1 text-lime-600">{{ item.label }}</div>
                  </el-row>
                </div>
              </div>
              <div class="basis-3/4 ml-6">
                <div class="top-head" :style="{width: preset_size.width + 'px'}">
                  <h3 class="top-head-left">Preview Layout</h3>
                  <el-row class="top-head-right">
                    <el-button @click="submitGenerate" type="primary">Generate</el-button>
                  </el-row>
                </div>
                <el-card 
                  class="box-card relative mt-6"
                  :style="{width: preset_size.width + 'px', height: preset_size.height + 'px'}"
                >
                  <div v-for="(item, index) in images" :key="index">
                    <el-tooltip
                      class="box-item"
                      effect="dark"
                      :content="index + 1"
                      placement="bottom"
                    >
                      <img class="pr-1 pb-1 absolute" :class="preset_size.is_circle ? 'rounded-full' : ''" :src="item.image" :style="{
                        width: item.width + 'px',
                        height: item.height + 'px',
                        top: item.top + 'px',
                        left: item.left + 'px',
                      }">
                    </el-tooltip>
                    <span v-if="item.image_id != null" class="absolute text-red-600" style="font-size: 20px; cursor: pointer;" 
                      :style="{left: (item.left + preset_size.width_item - 15) + 'px', top: (item.top - 10) + 'px'}"
                      @click="removeImage(item, index)"
                    >
                      <icon :data="iconDelete" />
                    </span>
                  </div>
                </el-card>
              </div>
            </div>
            
          </template>
        </el-dialog>
      </div>
      <div class="basis-4/5">
        <div class="p-3">
          <el-input v-model="employee_number" class="mr-3" style="width: 200px" :placeholder="$t('Enter The Staff Number')" />
          <el-button @click="confirmStaff()" type="primary">{{
            $t('Generate Layout')
          }}</el-button>
        </div>
        <el-tabs v-model="activeTab" class="ml-3">
          <el-tab-pane :label="$t('Printing')" name="printing">
            <el-table stripe :data="pendingList.data" style="width: 100%">
              <el-table-column prop="id" :label="$t('ID')" width="120" />
              <el-table-column :label="$t('Created at')" width="180">
                <template #default="scope">
                  {{ utcToLocalTime(scope.row.created_at).format('lll') }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('Filter')">
                <template #default="scope">
                  <el-tag class="mr-2 mb-1" size="small" type="success">
                    {{ scope.row.product.sku }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="employee_name" :label="$t('Staff')">
                <template #default="scope">
                  {{ scope.row.employee_convert.name }}
                </template>
              </el-table-column>
              <el-table-column prop="quantity" :label="$t('Quantity')" width="100">
                <template #default="scope">
                  {{ scope.row.convert_percent }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('Status')" width="160">
                <template #default="scope">
                  <el-tag v-if="+scope.row.convert_status == 1" type="success">{{ $t('Completed') }}</el-tag>
                  <el-tag v-else-if="+scope.row.convert_status == 2" type="danger">{{ $t('Error') }}</el-tag>
                  <el-tag v-else type="warning">{{ $t('Pending') }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column width="150" :label="$t('Action')">
                <template #default="scope">
                  <template v-if="scope.row.download_status == 0">
                    <el-button size="small" :disabled="scope.row.convert_status !== 1" type="primary"
                      @click="popupPrintPdf(scope.row)">
                      {{ $t('Download') }}
                    </el-button>
                  </template>
                  <template v-if="scope.row.download_status == 1">
                    <el-button size="small" :disabled="scope.row.convert_status !== 1" type="info">{{ $t('Printed') }}
                    </el-button>
                  </template>
                </template>
              </el-table-column>
            </el-table>
            <div class="mt-3 items-center fixed">
              <el-pagination background :page-size="limit" :pager-count="10" layout="prev, pager, next"
                :total="pendingList.total" @current-change="changePage">
              </el-pagination>
            </div>
          </el-tab-pane>
          <el-tab-pane :label="$t('History')" name="history">
            <el-table stripe :data="historyList.data" style="width: 100%">
              <el-table-column prop="id" :label="$t('ID')" width="120" />
              <el-table-column :label="$t('Created at')" width="180">
                <template #default="scope">
                  {{ utcToLocalTime(scope.row.created_at).format('lll') }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('Filter')">
                <template #default="scope">
                  <el-tag class="mr-2 mb-1" size="small" type="success">
                    {{ scope.row.product.sku }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="employee_name" :label="$t('Staff')">
                <template #default="scope">
                  {{ scope.row.employee_convert.name }}
                </template>
              </el-table-column>
              <el-table-column prop="quantity" :label="$t('Quantity')" width="100">
                <template #default="scope">
                  {{ scope.row.convert_percent }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('Status')" width="160">
                <template #default="scope">
                  <el-tag v-if="+scope.row.convert_status == 1" type="success">{{ $t('Completed') }}</el-tag>
                  <template v-else>
                    <el-tag type="warning">{{ $t('Pending') }}</el-tag>
                  </template>
                </template>
              </el-table-column>
              <el-table-column width="150" :label="$t('Action')">
                <template #default="scope">
                  <el-button size="small" type="primary" @click="popupPrintPdfHistory(scope.row)">
                    {{ $t('Re-Download') }}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="mt-3 flex items-center">
              <div class="flex">
                <el-input v-model="searchLabelId" :placeholder="$t('Search Label ID')">
                  <template #append>
                    <el-button @click="searchLabelDownloaded">{{
                    $t('Search')
                    }}</el-button>
                  </template>
                </el-input>
              </div>
              <el-pagination class="ml-5" background :page-size="limit" :pager-count="10" layout="prev, pager, next"
                :total="historyList.total" @current-change="changePageHistory">
              </el-pagination>
            </div>
          </el-tab-pane>
        </el-tabs>
        <el-dialog v-model="dialogVisibleDownload" :title="$t('Download Ornaments')" width="60%" top="5vh">
          <iframe class="m-0 p-0" :src="pdf" style="height: 450px; width: 100%"></iframe>
          <div class="mt-3">
            <el-button type="primary" @click="confirmPrintedBtn($event)">{{ $t("Confirm") }}
            </el-button>
          </div>
        </el-dialog>
        <el-dialog v-model="dialogVisibleDownloadHistory" :before-close="handleCloseHistory"
          :title="$t('Redownload Ornaments')" width="60%" top="5vh">
          <div v-if="!unlockHistory" class="bg-slate-200" style="height: 450px"></div>
          <iframe v-else class="m-0 p-0" :src="pdfHistory"
            style="height: 450px; width: 100%"></iframe>
          <h2 class="mt-2 mb-2">
            <strong>{{ $t('Enter Password To Unlock Redownload') }}</strong>
          </h2>
          <el-input v-model="password" />
          <div class="mt-3">
            <el-button type="primary" @click="confirmPrintedHistoryBtn($event)">{{ $t('Confirm') }}
            </el-button>
          </div>
        </el-dialog>
      </div>
    </div>
  </div>
</template>