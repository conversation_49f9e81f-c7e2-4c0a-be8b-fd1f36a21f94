import { count, convert, list, history, getPreset, convertToAi } from "@/api/sticker.js";
import { scan, download } from "@/api/ornament.js";
import { getByCode } from "@/api/employee.js";
import { STORAGE_URL, S3_URL } from "@/utilities/constants";
import { ElMessage } from 'element-plus';

export default {
  name: "Sticker",
  data() {
    return {
      visibleDialogGen: false,
      codeEmployee: "",
      employeeError: "",
      employee: null,
      time_checking_id: null,
      isLoading: false,
      images: [],
      label: '',
      productPicked: null,
      refreshCount: null,
      dataCount: {},
      clickConfirm: false,
      employee_number: null,
      refreshList: null,
      page: 1,
      pageHistory: 1,
      limit: 11,
      searchLabelId: '',
      activeTab: 'printing',
      pendingList: {},
      historyList: {},
      preset_size: {},
      loadingScan: false,
      dialogVisibleDownload: false,
      pngDownload: {},
      dialogVisibleDownloadHistory: false,
      pngDownloadHistory: {},
      png: '',
      pngHistory: '',
      passwordRedownload: '',
      password: null,
      unlockHistory: false,
      onThisPage: true,
      heightTemplate: 500
    }
  },

  mounted() {
    this.countSticker();
    this.getList();
    this.getHistory();
  },

  methods: {
    async searchLabelDownloaded() {
      this.page = 1;
      await this.getHistory();
    },

    async setConvert(item) {
      try {
        const res = await getPreset({product_id: item.product_id});
        this.preset_size = res.data.data;
        this.productPicked = item;
      } catch (e) {
        this.notification(e.response.data.message, 'error', false, { duration: 5000 });
      }
    },

    async getList() {
      let params = {
        page: this.page,
        limit: this.limit
      };

      try {
        const res = await list(params);
        this.pendingList = res.data.data;
      } catch (e) {
        this.notification(e.response.data.message, 'error', false, { duration: 5000 });
      }

      clearTimeout(this.refreshList);
      let x = setTimeout(() => {
        if (this.onThisPage) {
          this.getList()
        }
      }, 10 * 1000);
      this.refreshList = x;
    },

    async getHistory() {
      let params = {
        page: this.pageHistory,
        limit: this.limit,
        label_id: this.searchLabelId
      };

      try {
        const res = await history(params);
        this.historyList = res.data.data;
      } catch (e) {
        this.serverErrors = e.response.data.errors;
        this.notification(e.response.data.message, 'error', false, { duration: 5000 });
      }
    },

    async countSticker() {
      let params = {
        page: this.page,
      }

      try {
        const res = await count(params);
        this.dataCount = res.data.data;
      } catch (e) {
        this.serverErrors = e.response.data.errors;
        this.notification(e.response.data.message, 'error', false, { duration: 5000 });
      }

      clearTimeout(this.refreshCount);
      let x = setTimeout(() => {
        if (this.onThisPage) {
          this.countSticker()
        }
      }, 10 * 1000);
      this.refreshCount = x;
    },

    async confirmStaff() {
      if (this.productPicked == null) {
        this.notification(this.$t('Please select the style before!'), 'error', false, { duration: 5000 });
        return false
      }

      this.clickConfirm = true;
      if (!this.employee_number) {
        this.notification(this.$t('Please enter the staff number to confirm the convert sticker'), 'error', false, { duration: 5000 });
        this.clickConfirm = false;
        return false
      }
      const res = await getByCode(this.employee_number)
      if (!res.data.data) {
        this.notification(this.$t('The staff number does not exist'), 'error', false, { duration: 5000 });
        this.clickConfirm = false;
        return false
      }
      this.visibleDialogGen = true;
      this.employee = res.data.data;
      this.employee_number = null;
    },

    closeDialogGen() {
      this.images = [];
      this.label = '';
      this.productPicked = null;
      this.employee = null;
    },

    focusByElClass(elClass = "el-form-item-tracking-number") {
      this.$nextTick(() => {
        const el = document.getElementsByClassName(elClass);
        if (!el || !el.length) {
          return;
        }
        const firtsElError = (el && el[0]) || undefined;
        const input = firtsElError.querySelector("input");
        document.body.scrollTop = (input.offsetTop - 120) | 0;
        input.focus();
      });
    },

    async getScanBarcodeQualityControl() {
      this.loadingScan = true;
      let label = this.label;
      let array_label = label.split('-');
      if (array_label.length == 5) {
        label = label + '-0';
      }

      if (label.split('-').length <= 5) {
        this.loadingScan = false;
        this.notification('Please config the scanner before!', 'error', false, { duration: 5000 });
        this.label = '';
        return false;
      }

      if (this.images.find(i => i.label == label)) {
        this.loadingScan = false;
        this.notification('Label ID already exists!', 'error', false, { duration: 5000 });
        this.label = '';
        return false;
      }

      let params = {
        label_id: label,
        employee_id: this.employee.id,
        product_id: this.productPicked.product_id
      };
      try {
        const res = await scan(params);
        let maxItemOnRow = this.preset_size.max_item_on_row;
        let row = this.images.length % maxItemOnRow == 0 ? Math.ceil(this.images.length / maxItemOnRow) + 1 : Math.ceil(this.images.length / maxItemOnRow);
        this.heightTemplate = row * this.preset_size.height_item;
        this.images.push({
          label: label,
          image: res.data.data.image_url,
          width: this.preset_size.width_item,
          height: this.preset_size.height_item,
          left: this.preset_size.width_item * (this.images.length % maxItemOnRow),
          top: this.preset_size.height_item * (row - 1),
          image_id: res.data.data.image_id,
          side: res.data.data.side
        });
      } catch (e) {
        this.notification(e.response.data.message, 'error', false, { duration: 5000 });
      }
      this.focusByElClass()
      this.label = '';
      this.loadingScan = false;
      let listLabelEl = document.getElementById('list-labels');
      listLabelEl.scrollIntoView({ behavior: 'smooth', block: 'end' });
      let listImagesEl = document.getElementById('list-images');
      listImagesEl.scrollIntoView({ behavior: 'smooth', block: 'end' });
    },

    removeImage(index) {
      this.images.splice(index, 1);
      let newImages = [];
      this.images.forEach((item, key) => {
        let maxItemOnRow = this.preset_size.max_item_on_row;
        let row = key % maxItemOnRow == 0 ? Math.ceil(key / maxItemOnRow) + 1 : Math.ceil(key / maxItemOnRow);
        this.heightTemplate = row * this.preset_size.height_item;
        newImages.push({
          label: item.label,
          image: item.image,
          width: item.width,
          height: item.height,
          left: this.preset_size.width_item * (key % maxItemOnRow),
          top: this.preset_size.height_item * (row - 1),
          image_id: item.image_id,
          side: item.side
        });
      })

      this.images = newImages;
    },

    async submitGenerate() {
      let options = this.images.map(i => ({
        label_id: i.label == null ? null : i.label.trim().split('-').slice(0, 5).join('-'),
        image_id: i.image_id,
        side: i.side,
      }));
      let params = {
        employee_id: this.employee.id,
        product_id: this.productPicked.product_id,
        options: options
      };
      try {
        await convert(params);
        this.getList();
        this.countSticker();
        this.visibleDialogGen = false;
      } catch (e) {
        let data = e.response.data;
        if (e.response.status == 422) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          this.notification(firstData[0], 'error', false, { duration: 5000 });
        }
        this.notification(data.message, 'error', false, { duration: 5000 });
      }
    },

    popupPrintPng(item) {
      this.dialogVisibleDownload = true;
      this.pngDownload = item;
      this.png = `${S3_URL}/sticker/${item.id}.png?v=` + Math.floor(Math.random() * 1000);
    },

    popupPrintPngHistory(item) {
      this.password = null;
      let today = new Date();
      let md = String(today.getMonth() + 1).padStart(2, '0') + String(today.getDate()).padStart(2, '0');
      this.passwordRedownload = md;
      this.dialogVisibleDownloadHistory = true;
      this.pngDownloadHistory = item;
      this.pngHistory = `${S3_URL}/sticker/${item.id}.png?v=` + Math.floor(Math.random() * 1000);
    },

    async confirmPrintedBtn() {
      try {
        window.open(this.png);
        await download({
          pdf_converted_id: this.pngDownload.id
        })

        this.dialogVisibleDownload = false;
        this.getList();
        this.getHistory();
        this.countSticker();
      } catch (e) {
        this.notification(e.response.data.message, 'error', false, { duration: 5000 });
      }
    },

    async confirmPrintedHistoryBtn() {
      if (this.passwordRedownload != this.password) {
        ElMessage({
          type: 'error',
          message: this.$t('The password is incorrect'),
        })
      } else {
        this.unlockHistory = true;
        this.dialogVisibleDownloadHistory = false;
        window.open(this.pngHistory, '_blank');
      }
    },

    async convertAi(item) {
      try {
        await convertToAi({
          pdf_converted_id: item.id
        })
      } catch (e) {
        this.notification(e.response.data.message, 'error', false, { duration: 5000 });
      }
    },

    handleCloseHistory() {
      this.dialogVisibleDownloadHistory = false;
      this.unlockHistory = false;
    },

    changePage(page) {
      this.page = page;
      this.$nextTick(() => {
        this.getList();
      });
    },

    changePageHistory(pageHistory) {
      this.pageHistory = pageHistory;
      this.$nextTick(() => {
        this.getHistory();
      });
    },
  },

  beforeUnmount() {
    clearTimeout(this.refreshList);
    clearTimeout(this.refreshCount);
  },
  
  unmounted() {
    clearTimeout(this.refreshCount);
    clearTimeout(this.refreshList);
  },

  beforeRouteLeave(to, from, next) {
    this.onThisPage = false;
    next();
  },
}