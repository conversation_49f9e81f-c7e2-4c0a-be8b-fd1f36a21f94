<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head mb-4">
      <div class="top-head-left">
        <h1>{{ part_number }}{{ $t("'s history") }}</h1>
      </div>
    </div>
    <div class="table-content">
      <div class="filter-top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-date-picker
              v-model="filter.date"
              type="daterange"
              unlink-panels
              range-separator="To"
              :start-placeholder="$t('Start date')"
              :end-placeholder="$t('End date')"
              style="width: 100%"
              value-format="YYYY/MM/DD"
            >
            </el-date-picker>
          </el-col>
          <el-col :span="4">
            <el-select
              v-model="filter.action"
              filterable
              :placeholder="$t('Select action')"
              class="w-full"
            >
              <el-option
                v-for="(item, key) in actionOptions"
                :key="item"
                :label="item"
                :value="key"
              />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select
              v-model="filter.type"
              filterable
              :placeholder="$t('Select type')"
              class="w-full"
            >
              <el-option
                v-for="(item, key) in typeOptions"
                :key="item"
                :label="key"
                :value="item"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <div class="btn-filter">
              <el-button type="primary" @click="onFilter">
                <span class="icon-margin-right">
                  <icon :data="iconFilter" /></span
                >{{ $t('Filter') }}
              </el-button>
              <el-button @click="resetFilter">
                <span class="icon-margin-right">{{ $t('Reset') }}</span>
              </el-button>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-table
        border
        stripe
        size="small"
        :data="items"
        :max-height="maxHeight"
        v-loading="isLoading"
        element-loading-text="Loading..."
      >
        <el-table-column :label="$t('Date')" min-width="200">
          <template #default="scope">
            {{ formatTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Action')" min-width="250">
          <template #default="scope">
            {{ scope.row.action && actionOptions[scope.row.action] ? actionOptions[scope.row.action] : '' }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Type')" min-width="200">
          <template #default="scope">
            {{ scope.row.type == typeOptions.Import ? 'Import' : 'Export' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="quantity"
          :label="$t('Quantity')"
          min-width="200"
        >
        </el-table-column>
        <el-table-column prop="balance" :label="$t('Balance')" min-width="200">
        </el-table-column>
        <el-table-column :label="$t('User')" min-width="200">
          <template #default="scope">
            {{ scope.row.employee?.name ?? '' }}
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom">
        <div class="total">
          {{ $t('Total:') }} {{ items.length ? formatNumber(total) : 0 }}
        </div>
        <el-pagination
          :disabled="isLoading"
          background
          layout="prev, pager, next"
          :page-size="filter.limit"
          :total="total"
          @current-change="changePage"
          v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
            v-model="filter.limit"
            :placeholder="$t('Select')"
            size="mini"
            @change="onFilter"
          >
            <el-option
              v-for="item in limits"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
  </div>
</template>
