import { history } from '@/api/partNumberOverview';
import { ACTION, TYPE } from '@/utilities/partNumber.js';
import dateMixin from "@/mixins/date.js";
export default {
  name: 'PartNumberHistory',
  components: {},
  mixins : [dateMixin],
  data() {
    return {
      items: [],
      isLoading: false,
      filter: this.setDefaultFilter(),
      part_number: this.$route.params.part_number,
      actionOptions: ACTION,
      typeOptions: TYPE,
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 189);
    },
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    setDefaultFilter() {
      return {
        limit: 25,
        page: 1,
        date: [],
        action: '',
        type: '',
      };
    },
    async fetchData() {
      this.isLoading = true;
      this.setRouteParam();
      const { data } = await history(this.filter, this.part_number);
      this.items = data.data;
      this.total = data.total;
      this.isLoading = false;
    },
    changePage(page) {
      this.filter.page = page;
      this.fetchData();
    },
    onFilter() {
      this.filter.page = 1;
      this.fetchData();
    },
    resetFilter() {
      this.filter = this.setDefaultFilter();
      this.fetchData();
    },
  },
};
