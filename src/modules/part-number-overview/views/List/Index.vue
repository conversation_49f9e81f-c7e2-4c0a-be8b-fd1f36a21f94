<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head mb-4">
      <div class="top-head-left">
        <h1>{{ $t('Part Number Overview') }}</h1>
      </div>
      <div class="top-head-right">
        <el-button plain type="primary" @click="exportExcel" class="ml-2" :loading="isLoadingExport">
          <span class="icon-margin-right"><icon :data="iconExport" /></span
          >{{ $t('Export') }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <div class="filter-top">
        <el-row :gutter="20">
          <el-col :span="4">
            <el-input
              :placeholder="$t('Search Part number/Fabric content')"
              v-model="filter.key_search"
              @keyup.enter="onFilter"
            />
          </el-col>
          <el-col :span="4">
            <el-select
              v-model="filter.style"
              filterable
              :placeholder="$t('Select style')"
              class="w-full"
            >
              <el-option
                v-for="item in styleOptions"
                :key="item.id"
                :label="item.name"
                :value="item.name"
              />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select
              :placeholder="$t('Select Country of origin')"
              class="el-select-country w-full el-form-item-country-distributor"
              v-model="filter.country"
              filterable
            >
              <el-option
                v-for="item in countries"
                :key="item.iso2"
                :label="item.name"
                :value="item.iso2"
              >
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="6">
            <div class="btn-filter">
              <el-button type="primary" @click="onFilter">
                <span class="icon-margin-right">
                  <icon :data="iconFilter" /></span
                >{{ $t('Filter') }}
              </el-button>
              <el-button @click="resetFilter">
                <span class="icon-margin-right">{{ $t('Reset') }}</span>
              </el-button>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-table
        border
        stripe
        size="small"
        :data="items"
        :max-height="maxHeight"
        v-loading="isLoading"
        element-loading-text="Loading..."
        class="break-words"
      >
        <el-table-column
          prop="part_number"
          :label="$t('Part Number')"
          min-width="200"
        ></el-table-column>
        <el-table-column prop="product" :label="$t('Style')" min-width="100">
          <template #default="scope">
            {{ scope.row?.product?.style ?? '' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="fabric_content"
          :label="$t('Fabric content')"
          min-width="200"
        >
          <template #default="scope">
            {{ scope.row?.product_spec?.fabric_content ?? '' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="country"
          :label="$t('Country of origin')"
          min-width="200"
        >
          <template #default="scope">
            {{ scope.row.part_number_country?.name ?? '' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="total_import"
          :label="$t('Total import')"
          min-width="200"
        >
          <template #default="scope">
            {{ scope.row.total_import ?? 0 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="total_export"
          :label="$t('Total export')"
          min-width="200"
        >
          <template #default="scope">
            {{ scope.row.total_export ?? 0 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="total_balance"
          :label="$t('Total balance')"
          min-width="200"
        >
          <template #default="scope">
            {{ scope.row.total_balance ?? 0 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="action"
          :label="$t('Action')"
          fixed="right"
          width="100"
          align="center"
        >
          <template #default="scope">
            <router-link :to="{ name: 'part_number_history', params: { part_number: scope.row.part_number } }">
              <b>View history</b>
            </router-link>
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom">
        <div class="total">
          {{ $t('Total:') }} {{ items.length ? formatNumber(total) : 0 }}
        </div>
        <el-pagination
          :disabled="isLoading"
          background
          layout="prev, pager, next"
          :page-size="filter.limit"
          :total="total"
          @current-change="changePage"
          v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
            v-model="filter.limit"
            :placeholder="$t('Select')"
            size="mini"
            @change="onFilter"
          >
            <el-option
              v-for="item in limits"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
  </div>
</template>
