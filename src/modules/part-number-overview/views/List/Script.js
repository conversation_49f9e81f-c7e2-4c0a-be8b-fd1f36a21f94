import { fetch, exportCsv } from '@/api/partNumberOverview';
import { fetchAll } from '@/api/productStyle';
import { countries } from '@/api/default.js';
import moment from 'moment';

export default {
  name: 'PartNumberOverview',
  components: {},
  data() {
    return {
      items: [],
      isLoading: false,
      isLoadingExport: false,
      filter: this.setDefaultFilter(),
      countries: [],
      styleOptions: [],
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 189);
    },
  },
  mounted() {
    this.fetchData();
    this.fetchCountries();
    this.fetchProductStyle();
  },
  methods: {
    setDefaultFilter() {
      return {
        limit: 25,
        page: 1,
        key_search: '',
        style: '',
        country: '',
      };
    },
    async fetchData() {
      this.filter = this.getRouteParam();
      await this.fetchList();
    },
    async fetchList() {
      this.isLoading = true;
      this.setRouteParam();
      const { data } = await fetch(this.filter);
      this.items = data.data;
      this.total = data.total;
      this.isLoading = false;
    },
    async fetchProductStyle() {
      const { data } = await fetchAll();
      this.styleOptions = data || [];
    },
    async fetchCountries() {
      const res = await countries();
      this.countries = res.data || [];
    },
    changePage(page) {
      this.filter.page = page;
      this.fetchList();
    },
    onFilter() {
      this.filter.page = 1;
      this.fetchList();
    },
    resetFilter() {
      this.filter = this.setDefaultFilter();
      this.fetchList();
    },
    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);
      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }
      filter.page = +filter.page || 1;
      return filter;
    },
    async exportExcel() {
      this.isLoadingExport = true;
      const config = {
        responseType: 'arraybuffer',
        onDownloadProgress: (progressEvent) => {
          this.percentage = parseInt(
            Math.round((progressEvent.loaded / progressEvent.total) * 100)
          );
        },
      };
      try {
        const date = moment().format('YYYY-MM-DD');
        const response = await exportCsv(this.filter, config);
        console.log(response);
        var blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
        var downloadElement = document.createElement('a');
        var href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        downloadElement.download = `part_number_overview_${date}.xlsx`;
        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);
      } catch (error) {
        console.log(error);
        this.notification('Export fail.', 'error');
      }
      this.isLoadingExport = false;
    },
  },
};
