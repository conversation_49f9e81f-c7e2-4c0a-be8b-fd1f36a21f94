import EventBus from "@/utilities/eventBus";
import {create} from "@/api/partNumber"
import {getProductByParams, getProductByGTIn, getProductAttributes, getProductBySku} from "@/api/product"

export default {
    name: "CreatePartNumber",
    props: ['styles', 'attributes', 'listCountry'],
    data() {
        return {
            dialogCreate: false,
            data: this.setDefaultData(),
            serverErrors: [],
            colors: [],
            sizes: [],
            isLoading: false
        }
    },
    created() {
        EventBus.$on('createPartNumber', () => {
            this.dialogCreate = true;
        })
    },
    beforeUnmount() {
        EventBus.$off('createPartNumber');
    },
    methods: {
        selectStyle() {
            this.data.size = '';
            this.data.color = '';
            if (!this.data.style) {
                this.colors = [];
                this.sizes = [];
                return;
            }
            const currentStyle = this.attributes[this.data.style];
            let colors = currentStyle.colors;
            colors =
                colors.length &&
                colors.map((item) => {
                    return {
                        label: item,
                        value: item,
                    };
                });
            let sizes = currentStyle.sizes;
            sizes =
                sizes.length &&
                sizes.map((item) => {
                    return {
                        label: item,
                        value: item,
                    };
                });
            this.colors = colors;
            this.sizes = sizes;
        },

        async filterProduct() {
            if (!this.data.style || !this.data.color || !this.data.size) {
                return
            } else {
                let params = {
                    style: this.data.style,
                    color: this.data.color,
                    size: this.data.size,
                }
                const res = await getProductByParams(params);
                this.isLoading = false;
                this.data.sku = res.data.sku ?? '';
                this.data.fabric_content = res.data?.product_spec?.fabric_content;
            }
        },
        async filterBySKU() {
            if (this.data.sku) {
                let params = {
                    sku: this.data.sku
                }
                const res = await getProductBySku(params);
                this.isLoading = false;
                this.data.style = res.data.style ?? '';
                await this.selectStyle()
                this.data.color = res.data.color ?? '';
                this.data.size = res.data.size ?? '';
                this.data.fabric_content = res.data.product_spec.fabric_content ?? '';
            }
        },
        setDefaultData() {
            return {
                style: '',
                color: '',
                size: '',
                sku: '',
                fabric_content: '',
                part_number: '',
                country: ''
            };
        },
        isError(field) {
            return !!this.serverErrors[field];
        },
        getErrorMessage(field) {
            return this.serverErrors[field][0];
        },
        async onSubmit() {
            this.isLoading = true
            try {
                let params = {
                    sku: this.data.sku,
                    part_number: this.data.part_number,
                    country: this.data.country,
                    fabric_content: this.data.fabric_content,
                }
                const response = await create(params);
                this.notification(this.$t("Successfully created part number."), "success");
                this.dialogCreate = false;
                this.$emit("refresh");
            } catch (e) {
                if (e.response.data.errors) {
                    this.serverErrors = e.response.data.errors
                    this.notification(e.response.data.message ?? 'Data invalid', "error");
                } else if (e.response.data.message) {
                    this.notification(e.response.data.message, "error");
                }
                return;
            }
            this.isLoading = false
        },
        close() {
            this.data = this.setDefaultData();
            this.serverErrors= [];
            this.colors = [];
            this.sizes = [];
        }
    }

}