<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <el-dialog
      v-model="dialogVisible"
      :title="$t('Import Part Number')"
      custom-class="el-dialog-custom"
      width="50%"
      @close="close"
  >
    <p class="mb-2">
      Upload file CSV/XLS/XLSX (<el-link :underline="false" type="primary" @click="downloadCsvTemplate">{{ $t('Download example') }}</el-link>)
    </p>
    <el-upload
        class="el-upload-drag-full mb-1"
        ref="uploadPartNumber"
        name="file"
        limit="1"
        accept=".csv,.xlsx,.xls"
        drag
        :headers="headerInfo"
        :on-success="onSuccess"
        :on-error="onError"
        :action="uploadApi"
        :on-change="onchange"
    >
      <el-icon class="el-icon--upload">
        <icon :data="iconPlus" />
      </el-icon>
      <div class="el-upload__text">
        Drop file here or <em>{{ $t('click to upload') }}</em>
      </div>
    </el-upload>
    <div v-if="uploadResponse.status" class="flex mb-2">
      <span class="mr-2">Total: {{ uploadResponse.total_row }}</span>
      <span class="text-[#16A34A] mr-2">Valid: {{ uploadResponse.total_success }}</span>
      <span class="mr-2 text-danger">Invalid: {{ uploadResponse.total_error }}</span>
    </div>
    <template v-if="uploadResponse?.invalid_data?.length">
      <el-table :data="uploadResponse?.invalid_data" border size="small" class="w-full mb-3" max-height="260">
        <el-table-column width="160" prop="part_number" :label="$t('Part Number')" />
        <el-table-column width="160" prop="sku" :label="$t('SKU')" />
        <el-table-column width="160" prop="fabric_content" :label="$t('Fabric content')" />
        <el-table-column width="160" prop="country" :label="$t('Country')" />
        <el-table-column prop="reason" :label="$t('Reason')">
        </el-table-column>
      </el-table>
    </template>
    <el-button :disabled="!uploadResponse.status || uploadResponse.valid_data.length == 0" type="primary" :loading="isLoading" @click="importCsvFile()">{{ $t('Import') }}</el-button>
  </el-dialog>
</template>
