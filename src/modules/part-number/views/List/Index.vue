<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head mb-4">
      <div class="top-head-left">
        <h1>{{ $t('Part Number') }}</h1>
      </div>
      <div class="top-head-right">
        <el-button type="primary" @click="openDialogImport()">
          {{ $t('Import') }}
        </el-button>
        <el-button type="primary" @click="openDialogCreate()">
           <span class="icon-margin-right">
            <icon :data="iconAdd" />
           </span>
          {{ $t('Create') }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <div class="filter-top">
        <el-row :gutter="20">

          <el-col :span="3">
            <el-input :placeholder="$t('Part Number')" v-model="filter.part_number" />
          </el-col>
          <el-col :span="3">
            <el-input :placeholder="$t('Fabric Content')" v-model="filter.fabric_content" />
          </el-col>
          <el-col :span="3">
            <el-select
                v-model="filter.style"
                filterable
                :placeholder="$t('Select style')"
                class="w-full"
            >
              <el-option
                  v-for="item in styleOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.name"
              />
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-select
                v-model="filter.color"
                filterable
                :placeholder="$t('Select color')"
                class="w-full"
            >
              <el-option
                  v-for="item in colorOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.name"
              />
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-select
                v-model="filter.size"
                filterable
                :placeholder="$t('Select size')"
                class="w-full"
            >
              <el-option
                  v-for="item in sizeOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.name"
              />
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-select
                v-model="filter.country"
                filterable
                :placeholder="$t('Select country')"
                class="w-full"
            >
              <el-option
                  v-for="item in listCountry"
                  :key="item.id"
                  :label="item.name"
                  :value="item.iso2"
              />
            </el-select>
          </el-col>
          <el-col :span="5">
            <div class="btn-filter">
              <el-button type="primary" @click="onFilter">
                <span class="icon-margin-right">
                  <icon :data="iconFilter" />
                </span>
                {{ $t("Filter") }}
              </el-button>
              <el-button v-if="checkHasFilter" @click="onClearFilter">
                <span class="icon-margin-right">{{ $t("Clear") }}</span>
              </el-button>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-table
          border
          stripe size="small" :data="listPartNumber"
          class="table-cus"
          style="width: 100%"
          :max-height="maxHeight"
          v-loading="isLoading"
          element-loading-text="Loading..."
      >
        <el-table-column prop="part_number" :label="$t('Part Number')"></el-table-column>
        <el-table-column prop="product.sku" :label="$t('SKU')"></el-table-column>
        <el-table-column prop="product.style" :label="$t('Style')"></el-table-column>
        <el-table-column prop="product.color" :label="$t('Color')"></el-table-column>
        <el-table-column prop="product.size" :label="$t('Size')"></el-table-column>
        <el-table-column prop="product_spec.fabric_content" :label="$t('Fabric Content')"></el-table-column>
        <el-table-column prop="country.name" :label="$t('Country of origin')"></el-table-column>
        <el-table-column prop="action" :label="$t('Action')" width="60">
          <template #default="scope">
            <el-link
                class="el-link-edit"
                :underline="false"
                type="primary"
                @click="openDialogUpdate(scope.row)"
            ><icon :data="iconEdit"
            /></el-link>
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom">
        <div class="total">{{$t('Total:')}} {{ listPartNumber.length ? formatNumber(total) : 0 }}</div>
        <el-pagination
            :disabled="isLoading"
            background
            layout="prev, pager, next"
            :page-size="filter.limit"
            :total="total"
            @current-change="changePage"
            v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
              v-model="filter.limit"
              :placeholder="$t('Select')"
              size="mini"
              @change="onFilter"
          >
            <el-option
                v-for="item in limits"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
    <CreatePartNumber
        :styles="styles || []"
        :attributes="attributes || []"
        :list-country="listCountry || []"
        @refresh="fetchPartNumber"
    />
    <UpdatePartNumber
        @refresh="fetchPartNumber"
    />
    <ImportPartNumber
        @refresh="fetchPartNumber"
    />
  </div>
</template>