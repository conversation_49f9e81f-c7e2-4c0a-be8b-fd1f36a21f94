import {getList} from "@/api/partNumber";
import { fetchAll as getStyles } from "@/api/productStyle";
import { fetchAll as getSizes } from "@/api/productSize";
import { fetchAll as getColors } from "@/api/productColor";
import { getProductAttributes } from "@/api/product";
import { countries } from "@/api/default";
import EventBus from "@/utilities/eventBus.js";
import CreatePartNumber from "@/modules/part-number/views/Create/Index.vue"
import UpdatePartNumber from "@/modules/part-number/views/Update/Index.vue"
import ImportPartNumber from "@/modules/part-number/views/Import/Index.vue"
import {equals} from "ramda";
import filterMixin from "@/mixins/filter";

export default {
    name: 'PartNumberList',
    components: {
        CreatePartNumber,
        UpdatePartNumber,
        ImportPartNumber,
    },
    mixins: [filterMixin],
    data() {
        return {
            filter: {
                fabric_content: '',
                country: '',
                style: '',
                color: '',
                size: '',
                part_number: '',
                limit: 25,
                page: 1
            },
            styles: [],
            attributes: [],
            styleOptions: [],
            sizeOptions: [],
            colorOptions: [],
            listCountry: [],
            listPartNumber: [],
            serverErrors: [],
            total: null,
            isLoading: false,
            dialogImport: false,

        }
    },
    created() {
        this.filter = this.getRouteParam();
        this.fetchPartNumber();
        this.fetchProductColor();
        this.fetchProductSize();
        this.fetchProductAttributes();
        this.fetchProductStyle();
        this.fetchCountries();
    },
    computed: {
        checkHasFilter() {
            let defaultFilter = {
                fabric_content: '',
                country: '',
                style: '',
                color: '',
                size: '',
                part_number: '',
                limit: 25,
                page: 1
            }
            return !equals(defaultFilter, this.filter);
        },
    },
    methods: {
        downloadTemplate() {
            return
        },
        changePage(page) {
            this.filter.page = page;
            this.$nextTick(() => {
                this.fetchPartNumber();
            });
        },
        openDialogCreate() {
            EventBus.$emit("createPartNumber");
        },
        openDialogImport() {
            console.log(1111)
            EventBus.$emit("importPartNumber");
        },
        openDialogUpdate(partNumber) {
            EventBus.$emit("updatePartNumber", partNumber);
        },
        onFilter() {
            this.filter.page = 1;
            this.$nextTick(() => {
                this.fetchPartNumber();
            });
        },
        async onClearFilter() {
            this.filter = this.setDefaultFilter();
            await this.fetchPartNumber();
        },
        setRouteParam() {
            const params = this.filter;
            this.$router.replace({ name: 'part_number', query: params });
        },
        isError(field) {
            return !!this.serverErrors[field];
        },
        getErrorMessage(field) {
            return this.serverErrors[field][0];
        },
        getRouteParam() {
            let routeQuery = this.$route.query || {};
            let filter = this.setDefaultFilter();
            filter = {
                ...filter,
                ...routeQuery,
            };
            filter.limit = +filter.limit || 10;
            const scopeLimit = this.limits.map((item) => item.value);
            if (!scopeLimit.includes(filter.limit)) {
                filter.limit = 10;
            }
            filter.page = +filter.page || 1;
            return filter;
        },
        async fetchPartNumber() {
            this.isLoading = true;
            this.setRouteParam();
            let { data } = await getList(this.filter);
            this.listPartNumber = data.data || [];
            this.total = data.total || 0;
            this.isLoading = false;
        },
        async fetchProductColor() {
            let { data } = await getColors();
            this.colorOptions = data || [];
        },
        async fetchProductSize() {
            let { data } = await getSizes();
            this.sizeOptions = data || [];
        },
        async fetchProductStyle() {
            const { data } = await getStyles();
            this.styleOptions = data || [];
        },
        async fetchProductAttributes() {
            const res = await getProductAttributes();
            const data = res.data || '';
            let styles = data && Object.keys(data);
            styles =
                styles.length &&
                styles.map((item) => {
                    return {
                        label: item,
                        value: item,
                    };
                });
            this.styles = styles;
            this.attributes = data;
        },

        async fetchCountries() {
            const { data } = await countries();
            this.listCountry = data || [];
        },
        setDefaultFilter() {
            return this.filter = {
                fabric_content: '',
                country: '',
                style: '',
                color: '',
                size: '',
                part_number: '',
                limit: 25,
                page: 1
            }
        }
    }
}