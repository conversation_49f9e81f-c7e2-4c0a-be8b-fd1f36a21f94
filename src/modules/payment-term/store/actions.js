import { list } from "@/api/paymentTerm.js";

export const actions = {
  async getPaymentTerms(context) {
    try {
      const res = await list();
      let items = (res.data && res.data) || [];      
     
      items = items.map((item) => {
        return {
          ...item,
          nameCache: item.name,
        };
      });
      context.commit("setPaymentTerms", items);

      return items;
    } catch (e) {
      return e;
    }
  },
};
