export const mutations = {
  setPaymentTerms(state, paymentTerms) {
    state.paymentTerms = paymentTerms;
  },
  addPaymentTerm(state, paymentTerm) {
    paymentTerm = {
      ...paymentTerm,
      nameCache: paymentTerm.name,
    };
    state.paymentTerms.push(paymentTerm);
  },
  updatePaymentTerm(state, paymentTerm) {
    state.paymentTerms.forEach((item) => {
      if (item.id === paymentTerm.id) {
        item.name = paymentTerm.name;
        item.nameCache = paymentTerm.name;
      }
    });
  },
  deletePaymentTerm(state, id) {
    const index = state.paymentTerms.findIndex((item) => item.id === id);
    state.paymentTerms.splice(index, 1);
  },
  resetDataPaymentTerms(state) {
    state.paymentTerms = state.paymentTerms.map((item) => {
      return {
        ...item,
        name: item.nameCache,
        is_edit: false,
      };
    });
  },
};
