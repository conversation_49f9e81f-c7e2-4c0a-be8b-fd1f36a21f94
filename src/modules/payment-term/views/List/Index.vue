<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog v-model="openDialogPaymentTerm" :title="$t('Payment Term Management')"
      custom-class="el-dialog-custom el-dialog-payment-term" @close="closePaymentTermList"
      :close-on-click-modal="false">
      <template #default>
        <div class="add-payment-term">
          <el-form status-icon ref="addPaymentTermForm" :model="addData" :rules="paymentTermRules"
            @submit.prevent="addPaymentTerm('addPaymentTermForm')">
            <el-form-item prop="name" @keyup.enter="addPaymentTerm('addPaymentTermForm')"
              :class="{ 'is-error': addPaymentTermError && addPaymentTermError['name'] }">
              <el-input v-model="addData.name" placeholder="Name"></el-input>
              <div v-if="addPaymentTermError && addPaymentTermError['name']" class="el-form-item__error">
                {{ addPaymentTermError["name"][0] }}
              </div>
            </el-form-item>

            <el-form-item prop="due_days" @keyup.enter="addPaymentTerm('addPaymentTermForm')"
              :class="{ 'is-error': addPaymentTermError && addPaymentTermError['due_days'] }">
              <el-input v-model="addData.due_days" placeholder="Due in fixed number of days"></el-input>
              <div v-if="addPaymentTermError && addPaymentTermError['due_days']" class="el-form-item__error">
                {{ addPaymentTermError["due_days"][0] }}
              </div>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="addPaymentTerm('addPaymentTermForm')" :disabled="isLoadingAdd"
                :loading="isLoadingAdd">{{ $t('Add') }}</el-button>
            </el-form-item>
          </el-form>
        </div>

        <div class="list-payment-term">
          <h1 class="title">{{ $t('List payment term') }}</h1>
          <template v-if="getPaymentTerms && getPaymentTerms.length">
            <div class="item" v-for="(item, index) in getPaymentTerms" :key="index">
              <div class="item-input">
                <div v-if="item.is_edit" class="add-payment-term">
                  <el-form status-icon @submit.prevent="updatePaymentTerm('addPaymentTermForm')">
                    <el-form-item
                      :class="{ 'is-error': !item.name || (editPaymentTermError && editPaymentTermError['name']) }">
                      <el-input class="item-input-edit-payment-term" v-model="item.name"
                        :id="'inputEdit' + item.id"></el-input>
                      <div v-if="!item.name" class="el-form-item__error">
                        This field cannot be left blank.
                      </div>
                      <div v-if="editPaymentTermError && editPaymentTermError['name']" class="el-form-item__error">
                        {{ editPaymentTermError["name"][0] }}
                      </div>
                    </el-form-item>
                    <el-form-item
                      :class="{ 'is-error': (!item.due_days && item.due_days !== 0) || (editPaymentTermError && editPaymentTermError['due_days']) }">
                      <el-input class="item-input-edit-payment-term" v-model="item.due_days"
                        :id="'inputEdit' + item.id"></el-input>
                      <div v-if="!item.due_days && item.due_days !== 0" class="el-form-item__error">
                        This field cannot be left blank.
                      </div>
                      <div v-if="editPaymentTermError && editPaymentTermError['due_days']" class="el-form-item__error">
                        {{ editPaymentTermError["due_days"][0] }}
                      </div>
                    </el-form-item>
                    <div class="item-input-group-btn">
                      <el-button size="mini" type="success" @click="updatePaymentTerm(item)"
                        :disabled="isLoadingEdit['id'] === item.id" :loading="isLoadingEdit['id'] === item.id" circle>
                        <icon :data="iconCheck" />
                      </el-button>
                      <el-button size="mini" type="info" @click="clearEditPaymentTerm(item)" circle>
                        <icon :data="iconClose" />
                      </el-button>
                    </div>
                  </el-form>
                </div>
                <div v-else @click="editPaymentTerm(item)">
                  {{ item.name }}
                </div>
              </div>
              <div class="btn-group" v-if="!item.is_edit">
                <el-button size="mini" type="primary" @click="editPaymentTerm(item)">
                  <icon :data="iconEdit" />
                </el-button>
                <el-popconfirm title="Are you sure to delete this?" @confirm="deletePaymentTerm(item)">
                  <template #reference>
                    <el-button size="mini" type="danger" :disabled="isLoadingDelete['id'] === item.id"
                      :loading="isLoadingDelete['id'] === item.id">
                      <icon :data="iconDelete" />
                    </el-button>
                  </template>
                </el-popconfirm>
              </div>
            </div>
          </template>
          <template v-else>{{ $t(' No records. ') }}</template>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
