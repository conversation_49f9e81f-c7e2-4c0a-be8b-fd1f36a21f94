import EventBus from "@/utilities/eventBus.js";
import { mapGetters } from "vuex";
import { add, destroy, update } from "@/api/paymentTerm.js";

export default {
  name: "PaymentTerm",

  data() {
    return {
      openDialogPaymentTerm: false,
      addData: {
        name: "",
        due_days: 1,
      },
      paymentTermRules: {
        name: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: "blur",
          },
        ],

        due_days: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: "blur",
          },
        ],
      },
      isLoadingAdd: false,
      isLoadingEdit: {},
      isLoadingDelete: {},
      addPaymentTermError: {},
      editPaymentTermError: {},
    };
  },

  computed: {
    ...mapGetters(["getPaymentTerms"]),
  },

  created() {
    EventBus.$on("showPaymentTermManagement", () => {
      this.openDialogPaymentTerm = true;
    });
  },

  mounted() {
    this.fetchPaymentTerms();
  },

  methods: {
    fetchPaymentTerms() {
      this.$store.dispatch("getPaymentTerms");
    },

    resetAddData() {
      this.$refs.addPaymentTermForm.resetFields();
      this.addData.name = "";
      this.addData.due_days = 1;
      this.addPaymentTermError = {};
      this.editPaymentTermError = {};
      this.$store.commit("resetDataPaymentTerms");
    },

    closePaymentTermList() {
      this.resetAddData();
    },

    async addPaymentTerm(formName) {
      const isValid = await this.$refs[formName].validate();

      if (!isValid) {
        return;
      }

      this.isLoadingAdd = true;

      try {
        const res = await add(this.addData);
        this.isLoadingAdd = false;
        const data = res.data || undefined;

        if (data) {
          this.notification(this.$t('Add payment term successfully.'));
          this.$store.commit("addPaymentTerm", data);
          this.resetAddData();
        }
      } catch (e) {
        this.isLoadingAdd = false;
        this.addPaymentTermError = e.response?.data?.errors || [];
        this.notification(this.$t('Add payment term error.'), "error");
      }
    },

    async deletePaymentTerm(item) {
      this.isLoadingDelete = { id: item.id };
      await destroy(item.id);
      this.isLoadingDelete = {};
      this.$store.commit("deletePaymentTerm", item.id);
      this.notification(this.$t('Delete payment term successfully.'));
    },

    editPaymentTerm(item) {
      item.is_edit = true;
      this.$nextTick(() => {
        const refInput = "inputEdit" + item.id;
        document.getElementById(refInput).focus();
      });
    },

    async updatePaymentTerm(item) {
      if (!item.name) {
        return;
      }

      this.isLoadingEdit = {
        id: item.id,
        due_days: item.due_days,
      };

      try {
        const params = {
          id: item.id,
          name: item.name,
          due_days: item.due_days,
        };

        await update(params);
        this.isLoadingEdit = {};
        this.notification(this.$t('Update payment term successfully.'));
        this.$store.commit("updatePaymentTerm", item);
        item.is_edit = false;
      } catch (e) {
        this.isLoadingEdit = {};
        this.editPaymentTermError = e.response?.data?.errors || [];
        this.notification(this.$t('Update payment term error.'), "error");
      }
    },

    clearEditPaymentTerm(item) {
      item.is_edit = false;
      item.name = item.nameCache;
    },
  },
};
