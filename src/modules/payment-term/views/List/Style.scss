.add-payment-term {
  position: sticky;
  top: 0;
  background-color: #fff;
  z-index: 2;
  margin-left: -20px;
  padding: 20px 20px 10px 20px;
  width: 100%;

  form {
    display: flex;

    .el-form-item {
      &:nth-of-type(1) {
        flex: 2;
        margin-right: 20px;
      }

      &:nth-of-type(2) {
        flex: 1;
        margin-right: 20px;
      }
    }
  }
}

.list-payment-term {
  .title {
    margin-top: 0;
  }

  position: relative;

  .item {
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--el-color-primary-light-9);
    margin-bottom: 8px;
    padding-bottom: 8px;

    &:last-of-type {
      margin-bottom: 0;
      padding-bottom: 0;
      border-bottom: none;
    }

    .item-input {
      flex: 1;
      position: relative;


      .el-form-item__error {
        margin-top: 5px;
      }
    }
  }

  .add-payment-term {
    margin: 0;
    padding: 0;

    .el-form-item__error {
      position: unset;
    }
  }
}

:deep .list-payment-term .add-payment-term .el-form-item--default {
  margin-bottom: 0;
}

:deep .list-payment-term .add-payment-term .el-input__inner {
  padding: 0 11px;
}

:deep .list-payment-term .add-payment-term .el-form-item__content {
  align-items: start;
}