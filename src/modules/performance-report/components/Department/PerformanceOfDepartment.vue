<template>
  <el-table v-if="jobTypes && jobTypes.length > 1" @sort-change="sortTable" border stripe size="small" :data="items"
    v-loading="isLoading" element-loading-text="Loading..." :max-height="maxHeight" default-expand-all>
    <el-table-column type="expand" min-width="48">
      <template #default="props">
        <el-table :data="props.row.employees" style="width: calc(100% - 47px); margin-left: 47px" size="small"
          class="el-table-border-none el-table-sub-department" :show-header="false">
          <el-table-column :label="$t('Employee')" min-width="160"><template #default="scope">{{ scope.row.employee_name
          }}
            </template></el-table-column>
          <el-table-column v-if="showTotalInk" prop="total_ink" sortable :label="$t('Total Ink')" min-width="140"
            :sort-method="sortTotalInk">
            <template #default="scope">
              {{ formatNumberFloat(scope?.row?.total_ink) }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('Total Output')" min-width="140" prop="total_output" sortable
            :sort-method="sortOutput"><template #default="scope">
              {{ formatNumberFloat(scope.row.output) }}</template></el-table-column>
          <el-table-column :label="$t('Total Hours')" min-width="140" prop="total_hours" sortable :sort-method="sortHours">
            <template #default="scope">
              {{
                  formatNumberFloat(scope.row.hours)
              }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('Average')" min-width="140" prop="avg_per_hour" sortable :sort-method="sortAvg">
            <template #default="scope">{{
                formatNumberFloat(scope.row.average)
            }}
            </template>
          </el-table-column>
          <el-table-column v-if="showTotalInk" :label="$t('Average Ink')" min-width="140" prop="avg_ink" sortable
            :sort-method="sortAvgInk">
            <template #default="scope">{{
                formatNumberFloat(scope.row.avg_ink)
            }}
            </template>
          </el-table-column>
        </el-table>
      </template>
    </el-table-column>
    <el-table-column prop="job_type" :label="$t('Job Type')" min-width="160">
      <template #default="scope">
        <span class="font-semibold">{{ renderJobType(scope.row.job_type) }}</span>
      </template>
    </el-table-column>
    <el-table-column v-if="showTotalInk" prop="total_ink" :label="$t('Total Ink')" min-width="140" sortable="custom">
      <template #default="scope">
        <span class="font-semibold">{{ formatNumberFloat(scope?.row?.total_ink) }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="output" :label="$t('Total Output')" min-width="140" sortable="custom">
      <template #default="scope">
        <span class="font-semibold">{{ formatNumberFloat(scope?.row?.outputInv) }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="hours" :label="$t('Total Times')" min-width="140" sortable="custom">
      <template #default="scope">
        <span class="font-semibold">{{ formatNumberFloat(scope?.row?.hoursInv) }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="average" :label="$t('Average')" min-width="140" sortable="custom">
      <template #default="scope">
        <span class="font-semibold">{{ formatNumberFloat(scope?.row?.averageInv) }}</span>
      </template>
    </el-table-column>
  </el-table>

  <el-table v-else border stripe :data="items?.[0]?.employees || []" size="small" v-loading="isLoading"
    element-loading-text="Loading..." :max-height="maxHeight">
    <el-table-column :label="$t('Employee')" min-width="160"><template #default="scope">{{ scope.row.employee_name
    }}
      </template></el-table-column>
    <el-table-column v-if="showTotalInk" prop="total_ink" min-width="140" sortable :label="$t('Total Ink')"
      :sort-method="sortTotalInk">
      <template #default="scope">
        {{ formatNumberFloat(scope?.row?.total_ink) }}
      </template>
    </el-table-column>
    <el-table-column :label="$t('Total Output')" prop="total_output" min-width="140" sortable :sort-method="sortOutput">
      <template #default="scope">
        {{ formatNumberFloat(scope.row.output) }}</template>
    </el-table-column>
    <el-table-column :label="$t('Total Hours')" prop="total_hours" min-width="140" sortable :sort-method="sortHours">
      <template #default="scope">
        {{
            formatNumberFloat(scope.row.hours)
        }}
      </template>
    </el-table-column>
    <el-table-column :label="$t('Average')" prop="avg_per_hour" min-width="140" sortable :sort-method="sortAvg">
      <template #default="scope">{{
          formatNumberFloat(scope.row.average)
      }}
      </template>
    </el-table-column>
    <el-table-column v-if="showTotalInk" :label="$t('Average Ink')" min-width="140" prop="avg_ink" sortable
      :sort-method="sortAvgInk">
      <template #default="scope">{{
          formatNumberFloat(scope.row.avg_ink)
      }}
      </template>
    </el-table-column>
  </el-table>
</template>
<script>
import formatNumberMixin from "@/mixins/formatNumber.js";
import EventBus from "@/utilities/eventBus.js";
import { performanceReportManager } from '@/api/performanceReport.js';
import performanceReportMixin from "@/modules/performance-report/mixins/performanceReport.js";
import { mapGetters } from 'vuex';
export default {
  name: "PerformanceOfDepartment",
  props: ['jobTypes'],
  mixins: [formatNumberMixin, performanceReportMixin],
  data() {
    return {
      isLoading: false,
      items: [],
      departmentId: this.$route.query.department_id,
      filter: this.setDefaultFilter(),
    };
  },
  watch: {
    '$route.query.department_id': {
      handler(val) {
        if (!val) return;
        this.filter = this.setDefaultFilter();
        this.departmentId = val;
        this.fetchData();
      },
    },
  },
  created() {
    EventBus.$on('performanceReportChangeFilter', (filter) => {
      this.filter = {
        ...this.filter,
        ...filter
      }
      this.fetchData();
    })
  },
  computed: {
    ...mapGetters(['getPerformanceReportDepartments']),
    maxHeight() {
      return parseInt(window.innerHeight - 189);
    },
    showTotalInk() {
      if (!this.department) return;
      return !!(this.department.name === 'Printing');
    },
    department() {
      return this.getPerformanceReportDepartments.find(
        (item) => +item.id === +this.departmentId
      )
    }
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    toFloat(num) {
      if (!num) return;
      num = parseFloat(num);
      return num;
    },
    sortTable(data) {
      let sortColumn = "";
      let sortBy = "";
      if (data.prop && data.order) {
        sortColumn = data.prop;
        sortBy = data.order;
      }

      for (let i = 0; i < this.items.length; i++) {
        const item = this.items[i];
        if(!item?.employees?.length) continue;
        item.employees.sort((a, b) => {
          if (!sortColumn) {
            return a.average - b.average;
          }
          if (sortBy === "ascending") {
            return this.toFloat(a[sortColumn]) - this.toFloat(b[sortColumn]);
          } else if (sortBy === "descending") {
            return this.toFloat(b[sortColumn]) - this.toFloat(a[sortColumn]);
          }
          return "null";
        });
      }
    },
    sortOutput(a, b) {
      return this.toFloat(a.output) - this.toFloat(b.output);
    },
    sortHours(a, b) {
      return this.toFloat(a.hours) - this.toFloat(b.hours);
    },
    sortAvg(a, b) {
      return this.toFloat(a.average) - this.toFloat(b.average);
    },
    sortAvgInk(a, b) {
      return this.toFloat(a.avg_ink) - this.toFloat(b.avg_ink);
    },
    sortTotalInk(a, b) {
      return this.toFloat(a.total_ink) - this.toFloat(b.total_ink);
    },
    async fetchData() {
      if (!this.departmentId) return;
      this.isLoading = true;
      try {
        let params = Object.assign({}, this.filter);
        params.department_id = this.departmentId;
        const res = await performanceReportManager(params);
        this.items = res.data?.employees || [];
        for (let i = 0; i < this.items.length; i++) {
          const item = this.items[i];
          if(!item?.employees?.length) continue;
          item.employees.sort((a, b) => {
            return a.average - b.average;
          });
        }
        this.isLoading = false;
      } catch (e) { }
    },
  },
};
</script>

<style scoped>

</style>
