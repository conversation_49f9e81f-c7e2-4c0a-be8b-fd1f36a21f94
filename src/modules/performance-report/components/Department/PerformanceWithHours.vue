<template>
  <el-table @sort-change="sortTable" border stripe size="small" :data="items"
    v-loading="isLoading" element-loading-text="Loading..." :max-height="maxHeight">
    <el-table-column type="expand" min-width="48">
      <template #default="props">
        <el-table :data="props.row.data" style="width: calc(100% - 47px); margin-left: 47px" size="small"
          class="el-table-border-none el-table-sub-department" :show-header="false">
          <el-table-column :label="$t('Work date')" min-width="160"><template #default="scope">{{ scope.row.work_date
          }}
            </template></el-table-column>
          <el-table-column :label="$t('Total output')" min-width="140" prop="total_tasks" sortable
            ><template #default="scope">
              {{ formatNum(scope.row.total_tasks) }}</template></el-table-column>
          <el-table-column :label="$t('Total hours')" min-width="140" prop="total_hours_worked">
            <template #default="scope">
              {{ formatNumberFloat(scope?.row?.total_hours_worked) }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('Average')" min-width="140" prop="total_tasks" sortable>
            <template #default="scope">{{
                formatNumbers(scope?.row?.total_tasks / scope?.row?.total_hours_worked)
            }}
            </template>
          </el-table-column>
        </el-table>
      </template>
    </el-table-column>
    <el-table-column prop="employee_name" :label="$t('Employee')" min-width="160">
      <template #default="scope">
        <span class="font-semibold">{{ scope.row.employee_name }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="total_outputs" :label="$t('Total Output')" min-width="140" sortable="custom">
      <template #default="scope">
        <span class="font-semibold">{{ formatNum(scope?.row?.total_outputs) }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="total_hours_worked" :label="$t('Total hours')" min-width="140" sortable="custom">
      <template #default="scope">
        <span class="font-semibold">{{ formatNumberFloat(scope?.row?.total_hours_worked) }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="average" :label="$t('Average (per hour)')" min-width="140" sortable="custom">
      <template #default="scope">
        <span class="font-semibold">{{ formatNumbers(scope?.row?.total_outputs / scope?.row?.total_hours_worked) }}</span>
      </template>
    </el-table-column>
  </el-table>
</template>
<script>
import formatNumberMixin from "@/mixins/formatNumber.js";
import EventBus from "@/utilities/eventBus.js";
import { performanceReportManagerV2 } from '@/api/performanceReport.js';
import performanceReportMixin from "@/modules/performance-report/mixins/performanceReport.js";
import { mapGetters } from 'vuex';
export default {
  name: "PerformanceWithHours",
  props: ['jobTypes', 'jobType', 'date', 'employee'],
  mixins: [formatNumberMixin, performanceReportMixin],
  data() {
    return {
      isLoading: false,
      items: [],
      departmentId: this.$route.query.department_id,
      filter: this.setDefaultFilter(this.departmentId),
      job_type: ''
    };
  },
  watch: {
    '$route.query.department_id': {
      handler(val) {
        if (!val) return;
        this.departmentId = val;
        this.filter = this.setDefaultFilter();
      },
    },
  },
  created() {
    EventBus.$off("performanceReportChangeFilter");
    EventBus.$on('performanceReportChangeFilter', (filter) => {
      this.filter = {
        ...this.filter,
        ...filter
      }
      this.fetchData();
    })
  },
  computed: {
    ...mapGetters(['getPerformanceReportDepartments']),
    maxHeight() {
      return parseInt(window.innerHeight - 189);
    },
    showTotalInk() {
      if (!this.department) return;
      return !!(this.department.name === 'Printing');
    },
    department() {
      return this.getPerformanceReportDepartments.find(
        (item) => +item.id === +this.departmentId
      )
    }
  },
  mounted() {
    this.job_type = this.jobType ?? this.jobTypes[0]?.job_type;
    this.filter.start_date = this.date?.[0] ?? this.filter.start_date;
    this.filter.end_date = this.date?.[1] ?? this.filter.end_date;
    this.filter.employee_id = this.employee ?? this.filter.employee_id;
    this.fetchData();
  },

  unmounted() {
  },
  methods: {
    toFloat(num) {
      if (!num) return;
      num = parseFloat(num);
      return num;
    },
    sortTable(data) {
      let sortColumn = "";
      let sortBy = "";
      if (data.prop && data.order) {
        sortColumn = data.prop;
        sortBy = data.order;
      }
      if(!this.items?.length) return;
      this.items.sort((a, b) => {
        if (!sortColumn) {
          return a?.total_outputs / a?.total_hours_worked - b?.total_outputs / b?.total_hours_worked
        }
        if (sortBy === "ascending") {
          if (sortColumn === "average") {
            return a?.total_outputs / a?.total_hours_worked - b?.total_outputs / b?.total_hours_worked;
          }
          return this.toFloat(a[sortColumn]) - this.toFloat(b[sortColumn]);
        } else if (sortBy === "descending") {
          if (sortColumn === "average") {
            return b?.total_outputs / b?.total_hours_worked - a?.total_outputs / a?.total_hours_worked;
          }
          return this.toFloat(b[sortColumn]) - this.toFloat(a[sortColumn]);
        }
        return "null";
      });
    },
    sortOutput(a, b) {
      return this.toFloat(a.output) - this.toFloat(b.output);
    },

    async fetchData() {
      if (!this.departmentId) return;
      this.isLoading = true;
      try {
        let params = Object.assign({}, this.filter);
        params.department_id = this.departmentId;
        params.job_type = params.job_type ? params.job_type : (this.jobType ? this.jobType : this.jobTypes[0]?.job_type);
        params.start_date = this.formatDate(params.start_date, false);
        params.end_date = this.formatDate(params.end_date, false);
        const res = await performanceReportManagerV2(params);
        this.items = res.data?.employees || [];
        this.items.sort((a, b) => {
          return b?.total_outputs / b?.total_hours_worked - a?.total_outputs / a?.total_hours_worked;
        });
        this.isLoading = false;
      } catch (e) { }
    },
  },
};
</script>

<style scoped>

</style>
