<template>
  <el-card class="box-card">
    <h3 class="mb-3 font-semibold uppercase">{{ $t("top, Lowest employees") }}</h3>
    <div>
      <div class="mb-3">
        <span class="mr-2 uppercase">Show</span>
        <el-select placeholder="All Employee" v-model="filterEmployee" @change="changeEmployee">
          <el-option v-for="item in optionEmployees" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </div>
      <div>
        <el-table border stripe size="small" :data="items" v-loading="isLoading" element-loading-text="Loading..."
          :max-height="maxHeight">
          <el-table-column :label="$t('Employee')" min-width="160">
            <template #default="scope">
              {{ scope.row.employee_name }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('Job Type')" min-width="140">
            <template #default="scope">
              {{ renderJobType(scope.row.job_type) }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('Performance')" min-width="140">
            <template #default="scope">
              {{ formatNumberFloat(scope?.row?.average) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </el-card>
</template>
<script>
import { topEmployeeOfMonth } from "@/api/performanceReport.js";
import formatNumberMixin from "@/mixins/formatNumber.js";
import EventBus from "@/utilities/eventBus.js";
import performanceReportMixin from "@/modules/performance-report/mixins/performanceReport.js";
export default {
  name: "TopEmployee",
  mixins: [formatNumberMixin, performanceReportMixin],
  data() {
    return {
      isLoading: false,
      value: "",
      items: [],
      departmentId: this.$route.query.department_id,
      filterEmployee: "10asc",
      optionEmployees: [
        {
          limit: "",
          sort_by: "",
          label: this.$t("All Employee"),
          value: "all",
        },
        {
          limit: 10,
          sort_by: "desc",
          label: this.$t("Top 10 Employees"),
          value: "10desc",
        },
        {
          limit: 5,
          sort_by: "desc",
          label: this.$t("Top 5 Employees"),
          value: "5desc",
        },
        {
          limit: 10,
          sort_by: "asc",
          label: this.$t("Lowest 10 Employees"),
          value: "10asc",
        },
        {
          limit: 5,
          sort_by: "asc",
          label: this.$t("Lowest 5 Employees"),
          value: "5asc",
        },
      ],
      filter: this.setDefaultFilter(),
    };
  },
  watch: {
    '$route.query.department_id': {
      handler(val) {
        if(!val) return;
        this.departmentId = val;
        this.filterEmployee = "10asc";
        this.filter = this.setDefaultFilter();
        this.fetchData();
      },
    },
  },
  created() {
    EventBus.$on('performanceReportChangeFilter', (filter) => {
      this.filter = {
        ...this.filter,
        ...filter
      }
      this.fetchData();
    })
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 289);
    },
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      if (!this.departmentId) return;
      this.isLoading = true;
      const filterEmployee = this.optionEmployees.find(
        (item) => item.value === this.filterEmployee
      );
      let filter = Object.assign({}, this.filter);
      if (this.filterEmployee) {
        filter.limit = filterEmployee.limit;
        filter.sort_by = filterEmployee.sort_by;
      }
      filter.department_id = this.departmentId;
      try {
        const res = await topEmployeeOfMonth(filter);
        this.items = res.data || [];
        this.isLoading = false;
      } catch (e) { }
    },
    changeEmployee() {
      this.fetchData();
    },
  },
};
</script>

<style scoped>

</style>
