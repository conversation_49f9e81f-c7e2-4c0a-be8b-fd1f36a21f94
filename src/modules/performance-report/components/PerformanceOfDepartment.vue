<template>
  <div class="p-3 border w-1/2">
    <div class="top-head">
      <div class="top-head-left">
        <el-select
          v-model="filter.department_ids"
          multiple
          placeholder="All Department"
          @change="changeDepartment()"
          class="w-full"
        >
          <el-option
            v-for="item in options"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </div>
    </div>
    <apexchart
      v-if="isLoading"
      v-loading="isLoading"
      class="flex justify-center"
      width="100%"
      type="line"
      :options="chartOptions"
      :series="series"
      :key="random"
    ></apexchart>
    <apexchart
      v-else
      class="flex justify-center"
      width="100%"
      type="line"
      :options="chartOptions"
      :series="series"
      :key="random"
    ></apexchart>
  </div>
</template>

<script>
import { reportDepartmentsForManage } from "@/api/performanceReport.js";
import VueApexCharts from "vue3-apexcharts";
import EventBus from "@/utilities/eventBus.js";
import { listForPerformanceReport } from "@/api/department.js";
import formatNumberMixin from "@/mixins/formatNumber.js";
import { makeid } from "@/utilities/helper.js";
export default {
  name: "PerformanceOfDepartment",
  mixins: [formatNumberMixin],
  components: { apexchart: VueApexCharts },
  data() {
    return {
      random: makeid(8),
      series: [
        // {
        //   name: "Pulling",
        //   data: [1110, "5901", 155, 33],
        // },
        // {
        //   name: "Folding",
        //   data: [110, "901", 155, "330"],
        // },
      ],
      chartOptions: {
        chart: {
          id: "chartPerformanceOfDepartment",
          height: 350,
          type: "line",
          dropShadow: {
            enabled: true,
            color: "#000",
            top: 18,
            left: 7,
            blur: 10,
            opacity: 0.2,
          },
          toolbar: {
            show: false,
          },
        },
        // colors: ["#77B6EA", "#545454"],
        dataLabels: {
          enabled: true,
        },
        stroke: {
          curve: "smooth",
        },
        // title: {
        //   text: 'Average High & Low Temperature',
        //   align: 'left'
        // },
        grid: {
          borderColor: "#e7e7e7",
          row: {
            colors: ["#f3f3f3", "transparent"], // takes an array which will be repeated on columns
            opacity: 0.5,
          },
        },
        markers: {
          size: 1,
        },
        xaxis: {
          // categories: ["2022-06-07", "2022-06-06", "2022-06-05", "2022-06-04"],
          categories: [],
          title: {
            text: "Department",
          },
        },
        yaxis: {
          // title: {
          //   text: 'Temperature'
          // },
          // min: 5,
          // max: 40
        },
        // legend: {
        //   position: "top",
        //   horizontalAlign: "right",
        //   floating: true,
        //   offsetY: -25,
        //   offsetX: -5,
        // },
      },
      options: [],
      filter: this.setDefaultFilter(),
      isLoading: true,
    };
  },
  watch: {},
  computed: {},
  created() {
    EventBus.$on("performanceOfDepartment", async (filter) => {
      this.filter = {
        ...this.filter,
        ...filter,
      };
      await this.getDataForChart();
    });
  },
  mounted() {
    this.fetchDataDepartment();
  },
  methods: {
    setDefaultFilter() {
      let params = {
        start_date: "",
        end_date: "",
        department_ids: [],
      };
      return params;
    },
    async fetchDataDepartment() {
      try {
        const res = await listForPerformanceReport();
        this.options = res?.data || [];
      } catch (e) {}
    },
    async changeDepartment() {
      await this.getDataForChart();
    },
    async getDataForChart() {
      // call Api get data
      this.isLoading = true;
      const res = await reportDepartmentsForManage(this.filter);
      const data = res.data;
      this.series = [];
      let convertData = {};
      data.forEach((item) => {
        item.departments.forEach((d) => {
          if (!convertData[d.name]) {
            convertData[d.name] = {
              name: d.name,
              data: [this.formatNumberFloat(d.average)],
            };
          } else {
            convertData[d.name]["data"].push(this.formatNumberFloat(d.average));
          }
        });
      });
      this.chartOptions = {
        ...this.chartOptions,
        ...{
          xaxis: {
            categories: data.map((item) => item.date),
          },
        },
      };
      Object.keys(convertData).forEach((key) => {
        this.series.push(convertData[key]);
      });
      this.random = makeid(8);
      this.isLoading = false;
    },
  },
};
</script>

<style scoped></style>
