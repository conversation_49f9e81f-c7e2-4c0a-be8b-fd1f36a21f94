<template>
  <div class="p-3 border w-full">
    <div class="top-head mb-3">
      <div class="top-head-left">
        <h2 class="uppercase bold">
          {{ $t("Employee") }}
        </h2>
      </div>
    </div>
    <div>
      <el-table
        border
        stripe
        size="small"
        :data="data"
        v-loading="isLoading"
        element-loading-text="Loading..."
        :max-height="400"
      >
        <el-table-column prop="employee" :label="$t('Employee')">
          <template #default="scope">
            <span>{{ scope.row.employee_name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="output" :label="$t('Total Output')">
          <template #default="scope">
            {{ formatNumberFloat(scope?.row?.output) }}
          </template>
        </el-table-column>
        <el-table-column prop="hours" :label="$t('Total Hours')">
          <template #default="scope">
            {{ formatNumberFloat(scope?.row?.hours) }}
          </template>
        </el-table-column>
        <el-table-column prop="average" :label="$t('Average')">
          <template #default="scope">
            {{ formatNumberFloat(scope?.row?.average) }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { reportAllEmployeesForLeader } from "@/api/performanceReport.js";
import formatNumberMixin from "@/mixins/formatNumber.js";
import EventBus from "@/utilities/eventBus.js";
export default {
  name: "ReportAllEmployeesForLeader",
  mixins: [formatNumberMixin],
  components: {},
  data() {
    return {
      isLoading: true,
      filter: {
      },
      data: [],
    };
  },
  watch: {},
  computed: {},
  created() {
    EventBus.$on("reportAllEmployeesForLeader", async (filter) => {
      this.filter = {
        ...this.filter,
        ...filter,
      };
      await this.fetchData();
    });
  },
  mounted() {},
  methods: {
    async fetchData() {
      this.isLoading = true;
      const res = await reportAllEmployeesForLeader(this.filter);
      const data = res.data;
      this.data = data;
      this.isLoading = false;
    },
  },
};
</script>

<style scoped></style>
