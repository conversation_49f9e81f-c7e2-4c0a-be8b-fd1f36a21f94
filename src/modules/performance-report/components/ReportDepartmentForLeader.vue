<template>
  <div class="p-3 border w-full">
    <div class="top-head mb-3">
      <div class="top-head-left">
        <h2 class="uppercase bold">
          {{ $t("Department Chart") }}
        </h2>
      </div>
    </div>
    <div>
      <apexchart
        v-if="isLoading"
        v-loading="isLoading"
        class="flex justify-center"
        width="100%"
        type="line"
        :options="chartOptions"
        :series="series"
        :key="random"
      ></apexchart>
      <apexchart
        v-else
        class="flex justify-center"
        width="100%"
        type="line"
        :options="chartOptions"
        :series="series"
        :key="random"
      ></apexchart>
    </div>
  </div>
</template>

<script>
import { reportDepartmentForLeader } from "@/api/performanceReport.js";
import formatNumberMixin from "@/mixins/formatNumber.js";
import EventBus from "@/utilities/eventBus.js";
import { makeid } from "@/utilities/helper.js";
import VueApexCharts from "vue3-apexcharts";

export default {
  name: "ReportDepartmentForLeader",
  mixins: [formatNumberMixin],
  components: { apexchart: VueApexCharts },
  data() {
    return {
      isLoading: true,
      filter: {},
      series: [],
      chartOptions: {
        chart: {
          height: 350,
          type: "line",
          dropShadow: {
            enabled: true,
            color: "#000",
            top: 18,
            left: 7,
            blur: 10,
            opacity: 0.2,
          },
          toolbar: {
            show: false,
          },
        },
        dataLabels: {
          enabled: true,
        },
        stroke: {
          curve: "smooth",
        },
        grid: {
          borderColor: "#e7e7e7",
          row: {
            colors: ["#f3f3f3", "transparent"], // takes an array which will be repeated on columns
            opacity: 0.5,
          },
        },
        markers: {
          size: 1,
        },
        xaxis: {
          categories: [],
          title: {
            text: "Department",
          },
        },
      },
      random: makeid(8),
    };
  },
  watch: {},
  computed: {},
  created() {
    EventBus.$on("reportDepartmentForLeader", async (filter) => {
      this.filter = {
        ...this.filter,
        ...filter,
      };
      await this.fetchData();
    });
  },
  mounted() {},
  methods: {
    async fetchData() {
      this.isLoading = true;
      const res = await reportDepartmentForLeader(this.filter);
      const data = res.data;
      this.series = [];
      let convertData = {};
      data.forEach((item) => {
        item.departments.forEach((d) => {
          if (!convertData[d.name]) {
            convertData[d.name] = {
              name: d.name,
              data: [this.formatNumberFloat(d.average)],
            };
          } else {
            convertData[d.name]["data"].push(this.formatNumberFloat(d.average));
          }
        });
      });
      this.chartOptions = {
        ...this.chartOptions,
        ...{
          xaxis: {
            categories: data.map((item) => item.date),
          },
        },
      };
      Object.keys(convertData).forEach((key) => {
        this.series.push(convertData[key]);
      });
      this.random = makeid(8);
      this.isLoading = false;
    },
  },
};
</script>

<style scoped></style>
