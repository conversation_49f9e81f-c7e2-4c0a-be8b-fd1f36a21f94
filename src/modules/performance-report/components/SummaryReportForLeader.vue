<template>
  <div class="p-3 border w-full">
    <div class="top-head mb-3">
      <div class="top-head-left">
        <h2 class="uppercase bold">
          {{ $t("Summary") }}
        </h2>
      </div>
    </div>
    <div>
      <el-row :gutter="20">
        <el-col :span="6" v-for="(item, key, index) in data" :key="index">
          <el-card class="box-card">
            <template #header>
              <div class="card-header justify-between">
                <span class="card-header-title">{{ mapLabel[key] }}</span>
              </div>
            </template>
            <div class="min-h-[60px]" v-loading="isLoading">
              <div class="text-3xl">
                {{ formatNumberFloat(item.value) || 0 }}
              </div>
              <div class="text-md">
                <span
                  :class="{
                    'text-success': item.percent > 0,
                    'text-danger': item.percent < 0,
                  }"
                >
                  <template v-if="item.percent > 0">+</template>
                  <!-- <template v-else-if="item.percent < 0">-</template> -->
                  {{ formatNumberFloat(item.percent) }}%
                </span>
                <span class="ml-2">{{ $t("This period") }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { summaryReportForLeader } from "@/api/performanceReport.js";
import formatNumberMixin from "@/mixins/formatNumber.js";
import EventBus from "@/utilities/eventBus.js";
export default {
  name: "SummaryReportForLeader",
  mixins: [formatNumberMixin],
  components: {},
  data() {
    return {
      isLoading: true,
      filter: {},
      mapLabel: {
        average: "Performance",
        hours: "Total Hour",
        members: "Member",
        output: "Total Output",
      },
      data: {},
    };
  },
  watch: {},
  computed: {},
  created() {
    EventBus.$on("summaryReportForLeader", async (filter) => {
      this.filter = {
        ...this.filter,
        ...filter,
      };
      await this.fetchData();
    });
  },
  mounted() {},
  methods: {
    async fetchData() {
      this.isLoading = true;
      const res = await summaryReportForLeader(this.filter);
      const data = res.data;
      this.data = data;
      this.isLoading = false;
    },
  },
};
</script>

<style scoped></style>
