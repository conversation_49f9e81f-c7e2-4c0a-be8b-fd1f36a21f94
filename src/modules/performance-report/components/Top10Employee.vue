<template>
  <div class="p-3 border w-1/2">
    <div class="top-head mb-3">
      <div class="top-head-left">
        <h2 class="uppercase bold">
          {{ $t("top, Lowest employees") }}
        </h2>
      </div>
    </div>
    <div>
      <div class="mb-3">
        <span class="mr-2">Show</span>
        <el-select
          placeholder="All Employee"
          v-model="filterEmployee"
          @change="changeEmployee"
        >
          <el-option
            v-for="item in optionEmployees"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
      <div>
        <el-table
          border
          stripe
          size="small"
          :data="items"
          v-loading="isLoading"
          element-loading-text="Loading..."
        >
          <el-table-column :label="$t('Employee')" min-width="100">
            <template #default="scope">
              {{ scope.row.employee_name }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('Department')" min-width="100">
            <template #default="scope">
              {{ scope.row.department }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('Performance')" min-width="100">
            <template #default="scope">
              {{ formatNumberFloat(scope?.row?.average) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import { topEmployeeOfMonth } from "@/api/performanceReport.js";
import { list } from "@/api/employee.js";
import EventBus from "@/utilities/eventBus.js";
import formatNumberMixin from "@/mixins/formatNumber.js";

export default {
  name: "Top10Employee",
  mixins: [formatNumberMixin],
  data() {
    return {
      isLoading: false,
      options: [],
      value: "",
      items: [],
      filter: this.setDefaultFilter(),
      filterEmployee: "10asc",
      optionEmployees: [
        {
          limit: "",
          sort_by: "",
          label: this.$t("All Employee"),
          value: "all",
        },
        {
          limit: 10,
          sort_by: "desc",
          label: this.$t("Top 10 Employees"),
          value: "10desc",
        },
        {
          limit: 5,
          sort_by: "desc",
          label: this.$t("Top 5 Employees"),
          value: "5desc",
        },
        {
          limit: 10,
          sort_by: "asc",
          label: this.$t("Lowest 10 Employees"),
          value: "10asc",
        },
        {
          limit: 5,
          sort_by: "asc",
          label: this.$t("Lowest 5 Employees"),
          value: "5asc",
        },
      ],
    };
  },
  created() {
    EventBus.$on("top10Employee", (data) => {
      this.filter.start_date = data.start_date;
      this.filter.end_date = data.end_date;
      this.fetchData();
    });
  },
  mounted() {
    this.fetchDataEmployee();
  },
  methods: {
    setDefaultFilter() {
      let params = {
        start_date: "",
        end_date: "",
        limit: "",
        sort_by: "",
      };
      return params;
    },
    async fetchData() {
      this.isLoading = true;
      const filterEmployee = this.optionEmployees.find(
        (item) => item.value === this.filterEmployee
      );
      if (this.filterEmployee) {
        this.filter.limit = filterEmployee.limit;
        this.filter.sort_by = filterEmployee.sort_by;
      }
      try {
        const res = await topEmployeeOfMonth(this.filter);
        this.items = res.data;
        this.isLoading = false;
      } catch (e) {}
    },
    async fetchDataEmployee() {
      this.isLoading = true;
      try {
        const res = await list();
        this.options = res?.data?.data || [];
      } catch (e) {}
    },
    changeEmployee() {
      this.fetchData();
    },
  },
};
</script>

<style scoped></style>
