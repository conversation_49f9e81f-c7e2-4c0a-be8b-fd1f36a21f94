import dateMixin from '@/mixins/date.js';
import moment from "moment-timezone";
export default {
  mixins: [dateMixin],
  methods: {
    renderJobType(string) {
      if (!string) return;
      const stringNew = string.split('_').join(' ');
      return stringNew.charAt(0).toUpperCase() + stringNew.slice(1);
    },
    setDefaultDate() {
      return [
        this.formatDate(moment().subtract(7, 'days'), false),
        this.formatDate(moment(), false),
      ];
    },
    setDefaultFilter(department_id = '') {
      let params = {
        start_date: this.formatDate(moment().subtract(7, 'days'), false),
        end_date: this.formatDate(moment(), false),
        job_type: '',
        department_id: department_id,
        employee_id: null,
      };
      return params;
    },
  },
};
