<style src="./Style.scss" lang="scss"></style>
<script src="./Script.js"></script>
<template>
  <div class="sale-order">
    <div class="top-head">
      <div class="top-head-left">
        <h1>
          {{ $t('Performance Report For Manager') }}:
          {{ department.name || '' }}
        </h1>
      </div>
    </div>
    <div class="mt-3">
      <div class="mb-5 flex items-center">
        <div class="whitespace-nowrap">
          {{ $t('Date Report') }}
        </div>
        <div class="mx-2">
          <el-date-picker
            format="YYYY-MM-DD"
            v-model="date"
            type="daterange"
            range-separator="To"
            :clearable="false"
            :start-placeholder="$t('Start date')"
            :end-placeholder="$t('End date')"
            @change="onChangeDate"
          >
          </el-date-picker>
        </div>
        <div class="mx-2">
          <el-select
              filterable
              v-model="filter.employee_id"
              :placeholder="$t('Employee')"
              @change="onFilter"
          >
            <el-option
                v-for="item in employees"
                :key="item.id"
                :label="item.name"
                :value="String(item.id)"
            >
            </el-option>
          </el-select>
        </div>
        <div v-if="hasFilter" class="ml-2">
          <el-link type="danger" @click="onClearFilter" :underline="false">
            {{ $t('Reset') }}
          </el-link>
        </div>
      </div>
      <div>
        <el-tabs
            class="el-tab-filter"
            v-model="jobType"
            @tab-change="onChangeTab"
            v-if="jobTypes.length > 1"
            type="card"
        >
          <el-tab-pane
              :label="tab.job_type"
              :name="tab.job_type"
              v-for="(tab, index) in jobTypes"
              :key="index"
          >
            <template #label>
              <span class="custom-tabs-label">
                <span>{{ tab.name }}</span>
              </span>
            </template>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div>
        <el-row :gutter="20">
          <el-col :span="24">
            <PerformanceWithEmployee v-if="checkJobTypeCalculateWithDay" :jobTypes="jobTypes" :date="date" :employee="filter.employee_id" :jobType="jobType"/>
            <PerformanceWithHours v-else-if="checkJobTypeCalculateWithHour" :jobTypes="jobTypes" :date="date" :employee="filter.employee_id" :jobType="jobType" />
            <PerformanceOfDepartment v-else :jobTypes="jobTypes" />
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>
