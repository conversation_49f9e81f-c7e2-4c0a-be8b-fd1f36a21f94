import { equals } from 'ramda';
import TopEmployee from '@/modules/performance-report/components/Department/TopEmployee.vue';
import PerformanceOfDepartment from '@/modules/performance-report/components/Department/PerformanceOfDepartment.vue';
import PerformanceWithEmployee from '@/modules/performance-report/components/Department/PerformanceWithEmployee.vue';
import PerformanceWithHours from '@/modules/performance-report/components/Department/PerformanceWithHours.vue';
import { mapGetters } from 'vuex';
import { capitalizeFirstLetter } from '@/utilities/helper.js';
import EventBus from '@/utilities/eventBus.js';
import dateMixin from '@/mixins/date.js';
import performanceReportMixin from '@/modules/performance-report/mixins/performanceReport.js';
import {list} from "@/api/employee";

export default {
  name: 'DepartmentPerformance',
  components: {
    TopEmployee,
    PerformanceOfDepartment,
    PerformanceWithEmployee,
    PerformanceWithHours,
  },
  mixins: [dateMixin, performanceReportMixin],
  data() {
    return {
      date: '',
      filter: this.setDefaultFilter(),
      isLoading: false,
      jobTypes: [],
      department: {},
      departmentCalculateWithDay: ['pulling'],
      departmentCalculateWithHour: ['qc', 'qa', 'printing'],
      jobTypeCalculateWithDay: ['pulling', 'mugs_print', 'ornament_print', 'confirm_internal_request', 'adjust_pulling_shelves', 'fulfill_internal_request', 'stock_transfer_fulfill', 'addition', 'test_count', 'rbt_pulling'],
      jobTypeCalculateWithHour: ['dragon_fruit_print', 'all_in_one_print', 'neck_print', 'qc', 'qa', 'folding', 'label', 'machine_folding', 'printing_embroidery', 'dtf_print', 'latex_print'],
      // tabs: ['dragon_fruit_print', 'all_in_one_print', 'neck_print', 'mugs_print', 'qc', 'qa'],
      tabs: [
        {
          name: 'DTG (Dragon fruit)',
          key: 'dragon_fruit_print',
        },
        {
          name: 'DTG (All in one)',
          key: 'all_in_one_print',
        },
        {
          name: 'Neck',
          key: 'neck_print',
        },
        {
          name: 'Mugs',
          key: 'mugs_print',
        },
        {
          name: 'QC',
          key: 'qc',
        },
        {
          name: 'QA',
          key: 'qa',
        },
        {
          name: 'Pulling',
          key: 'pulling',
        },
        {
          name: 'Traditional folding',
          key: 'folding',
        },
        {
          name: 'Machine Folding',
          key: 'machine_folding',
        },
        {
          name: 'Label',
          key: 'label',
        },
        {
          name: 'DTF (Jack fruit)',
          key: 'dtf_print',
        },
        {
          name: 'EMB (Chili)',
          key: 'printing_embroidery',
        },
        {
          name: 'Ornament',
          key: 'ornament_print',
        },
        {
          name: 'Ajdust Pulling Shelves',
          key: 'adjust_pulling_shelves',
        },
        {
          name: 'Confirm Internal Request',
          key: 'confirm_internal_request',
        },
        {
          name: 'RBT Pulling',
          key: 'rbt_pulling',
        },

        {
          name: 'Fulfill Internal Request',
          key: 'fulfill_internal_request',
        },
        {
          name: 'Stock Transfer Fulfill',
          key: 'stock_transfer_fulfill',
        },
        {
          name: 'Test Count',
          key: 'test_count',
        },
        {
          name: 'Addition',
          key: 'addition',
        },
        {
          name: 'Latex',
          key: 'latex_print',
        }
      ],
      // checkDepartmentCalculateWithDay: false,
      // checkDepartmentCalculateWithHour: false,
      checkJobTypeCalculateWithDay: false,
      checkJobTypeCalculateWithHour: false,
      employees: [],
      jobType: '',
    };
  },
  watch: {
    '$route.query.department_id': {
      handler() {
        this.jobType = '';
        this.jobTypes = [];
        this.setDefaultData();
      },
    },
    'department.name': {
      handler() {
        this.fetchEmployee();
      },
      deep: true,
    },
    'jobType': {
      handler() {
        console.log(this.jobType)
        this.checkJobTypeCalculateWithDay = false;
        this.checkJobTypeCalculateWithHour = false;
        this.checkJobTypeCalculateWithDay = this.jobTypeCalculateWithDay.includes(this?.jobType?.toLowerCase());
        this.checkJobTypeCalculateWithHour = this.jobTypeCalculateWithHour.includes(this?.jobType?.toLowerCase());
        this.filter.job_type = this.jobType;
        this.onFilter();

      },
      deep: true,
    },
    getPerformanceReportDepartments: {
      handler() {
        this.setDefaultData();
      },
    },
  },
  computed: {
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      const filter = Object.assign({}, this.filter);
      delete filter.department_id;
      return !equals(defaultFilter, filter);
    },
    ...mapGetters(['getPerformanceReportDepartments', 'getEmployees']),


  },
  mounted() {
    this.setDefaultData();
  },
  methods: {
    async onChangeTab(tab = null) {
      if (tab?.props) {
        this.jobType = tab.props.label;
      }
    },
    async fetchEmployee() {
      let params = {
        department: this.department?.name,
      }
      const res = await list(params);
      this.employees = (res.data && res.data.data) || [];
    },
    setDefaultData() {
      this.date = this.setDefaultDate();
      this.filter = this.setDefaultFilter();
      this.filter.department_id = this.getDepartmentId();
      this.setRouteQuery();
      this.getDepartment();
      this.buildJobTypes();
    },
    setRouteQuery() {
      if (
        this.$route?.query?.department_id ||
        !['performance_report'].includes(this.$route.name)
      ) {
        return;
      }
      this.$router.replace({
        name: 'performance_report',
        query: {
          department_id: this.filter.department_id,
        },
      });
    },
    getDepartmentId() {
      let departmentId = '';
      if (this?.$route?.query?.department_id) {
        departmentId = this.$route.query.department_id;
      } else if (this.getPerformanceReportDepartments?.length) {
        departmentId = this.getPerformanceReportDepartments[0].id;
      }
      return departmentId;
    },
    getDepartment() {
      if (
        !this.filter?.department_id ||
        !this.getPerformanceReportDepartments.length
      ) {
        this.department = '';
        return;
      }
      const department = this.getPerformanceReportDepartments.find(
        (item) => +item.id === +this.filter.department_id
      );
      this.department = department;
      // this.checkDepartmentCalculateWithHour = this.departmentCalculateWithHour.includes(this?.department?.name?.toLowerCase());

    },
    buildJobTypes() {
      if (!this.department?.job_types?.length) {
        this.jobTypes = [];
        return;
      }
      let jobTypes = this.department.job_types;
      let data = [];
      jobTypes.forEach((item) => {
        let index = this.tabs.findIndex((x) => x.key === item.job_type);
        if (index !== -1) {
          data.push({
            name:
            this.tabs[index].name,
            ...item,
          });
        }
      });
      this.jobTypes = data;
      this.jobType = data[0]?.job_type;
      this.filter.job_type = data[0]?.job_type;
      this.onFilter();
    },
    onChangeDate() {
      if (this.date && this.date.length) {
        this.filter.start_date = this.formatDate(this.date[0], false);
        this.filter.end_date = this.formatDate(this.date[1], false);
      } else {
        this.filter.start_date = '';
        this.filter.end_date = '';
      }
      this.onFilter();
    },
    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.date = this.setDefaultDate();
      this.onFilter();
    },
    onFilter() {
      EventBus.$emit('performanceReportChangeFilter', this.filter);
    },
  },
};
