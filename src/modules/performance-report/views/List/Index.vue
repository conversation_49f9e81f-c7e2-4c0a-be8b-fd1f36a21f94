<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ $t('Performance Report For Manager') }}</h1>
      </div>
    </div>
    <div class="table-content mb-3">
      <div class="filter-top">
        <el-row :gutter="20">
          <el-col :span="16" class="pr-2">
            <div class="flex items-center">
              <div class="mr-5">Date Report</div>
              <div>
                <el-date-picker
                  v-model="date"
                  type="daterange"
                  unlink-panels
                  range-separator="To"
                  start-placeholder="Start date"
                  end-placeholder="End date"
                  @change="onChangeDate"
                  :shortcuts="shortcuts"
                >
                </el-date-picker>
              </div>
            </div>
          </el-col>
          <el-col :span="8" class="text-right">
            <!-- <el-button type="primary">
              <span class="icon-margin-right"> {{ $t("Export") }}</span>
            </el-button> -->
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="flex flex-row gap-2 mb-4">
      <div class="p-3 border bg-slate-50 w-3/5">
        <el-table
          :data="dataPerformanceReport"
          border
          stripe
          size="small"
          element-loading-text="Loading..."
          v-loading="isLoading"
          :flexible="false"
          :resizable="false"
        >
          <el-table-column type="expand" width="48">
            <template #default="props">
              <!-- start -->
              <el-table
                :data="props.row.employees"
                style="width: 100%"
                size="small"
                class="el-table-border-none"
                :flexible="false"
                :resizable="false"
              >
                <el-table-column type="expand" width="48">
                  <template #default="props">
                    <el-table
                      :data="props.row.employees"
                      style="width: 100%; margin-left: 47px"
                      size="small"
                      class="el-table-border-none"
                    >
                      <el-table-column :label="$t('Employee')" width="200"
                        ><template #default="scope"
                          >{{ scope.row.employee_name }}
                        </template></el-table-column
                      >
                      <el-table-column
                        :label="$t('Total Output')"
                        width="200"
                        prop="total_output"
                        sortable
                        :sort-method="inventorySortOutput"
                        ><template #default="scope">
                          {{ formatNumberFloat(scope.row.output) }}</template
                        ></el-table-column
                      >
                      <el-table-column
                        :label="$t('Total Hours')"
                        width="200"
                        prop="total_hours"
                        sortable
                        :sort-method="inventorySortHours"
                        ><template #default="scope">{{
                          formatNumberFloat(scope.row.hours)
                        }}</template></el-table-column
                      >
                      <el-table-column
                        :label="$t('Avg Per Hour')"
                        :width="props.row.job_type == 'printing' ? 120 : 200"
                        prop="avg_per_hour"
                        sortable
                        :sort-method="inventorySortAvg"
                        ><template #default="scope"
                          >{{ formatNumberFloat(scope.row.average) }}
                        </template></el-table-column
                      >
                      <el-table-column
                        v-if="props.row.job_type == 'printing'"
                        prop="avg_ink_per_print"
                        :label="$t('Avg Ink Per Print')"
                        width="140"
                        sortable
                        :sort-method="inventorySortAvgInk"
                      >
                        <template #default="scope">
                          {{ formatNumberFloat(scope.row.avg_ink) }}
                        </template>
                      </el-table-column>
                    </el-table>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('Job Type')" width="200"
                  ><template #default="scope"
                    >{{ renderJobType(scope.row.job_type) }}
                  </template></el-table-column
                >
                <el-table-column
                  :label="$t('Total Output')"
                  width="200"
                  sortable
                  :sort-method="inventoryParentSortOutput"
                  ><template #default="scope"
                    >{{ formatNumberFloat(scope?.row?.outputInv) }}
                  </template></el-table-column
                >
                <el-table-column
                  :label="$t('Total Hours')"
                  width="200"
                  sortable
                  :sort-method="inventoryParentSortHours"
                  ><template #default="scope">{{
                    formatNumberFloat(scope?.row?.hoursInv)
                  }}</template></el-table-column
                >
                <el-table-column
                  :label="$t('Avg Per Hour')"
                  width="200"
                  sortable
                  :sort-method="inventoryParentSortAvg"
                  ><template #default="scope">{{
                    formatNumberFloat(scope?.row?.averageInv)
                  }}</template></el-table-column
                >
              </el-table>
              <!-- <template v-else-if="props.row.department === 'Printing'">
                <el-table
                  :data="props.row.employees"
                  style="width: 100%; margin-left: 47px"
                  size="small"
                  class="el-table-border-none"
                >
                  <el-table-column :label="$t('Employee')" width="200">
                    <template #default="scope">
                      {{ scope.row.employee_name }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('Total Output')"
                    width="200"
                    prop="total_output"
                    sortable
                    :sort-method="printingSortOutput"
                  >
                    <template #default="scope">
                      {{ formatNumberFloat(scope.row.output) }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('Total Hours')"
                    width="200"
                    prop="total_hours"
                    sortable
                    :sort-method="printingSortHours"
                  >
                    <template #default="scope">
                      {{ formatNumberFloat(scope.row.hours) }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('Avg Per Hour')"
                    width="120"
                    prop="avg_per_hour"
                    sortable
                    :sort-method="printingSortAvg"
                  >
                    <template #default="scope">
                      {{ formatNumberFloat(scope.row.average) }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="avg_ink_per_print"
                    :label="$t('Avg Ink Per Print')"
                    width="140"
                    sortable
                    :sort-method="printingSortAvgInk"
                  >
                    <template #default="scope">
                      {{ formatNumberFloat(scope.row.avg_ink) }}
                    </template>
                  </el-table-column>
                </el-table>
              </template>
              <template v-else>
                <el-table
                  :data="props.row.employees"
                  style="width: 100%; margin-left: 47px"
                  size="small"
                  class="el-table-border-none"
                  :default-sort="defaultSort"
                >
                  <el-table-column :label="$t('Employee')" width="200">
                    <template #default="scope">
                      {{ scope.row.employee_name }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('Total Output')"
                    width="200"
                    prop="output"
                    sortable
                    :sort-method="sortOutput"
                  >
                    <template #default="scope">
                      {{ formatNumberFloat(scope.row.output) }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('Total Hours')"
                    prop="hours"
                    sortable
                    :sort-method="sortHours"
                    width="200"
                  >
                    <template #default="scope">
                      {{ formatNumberFloat(scope.row.hours) }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('Avg Per Hour')"
                    width="200"
                    prop="average"
                    sortable
                    :sort-method="sortAvg"
                  >
                    <template #default="scope">
                      {{ formatNumberFloat(scope.row.average) }}
                    </template>
                  </el-table-column>
                </el-table>
              </template> -->
              <!-- end -->
            </template>
          </el-table-column>
          <el-table-column
            prop="department"
            :label="$t('Department')"
            width="200"
          >
            <template #default="scope">
              <span class="font-bold">{{ scope.row.department }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="output"
            :label="$t('Total Output')"
            width="200"
            sortable
            :sort-method="parentSortOuput"
          >
            <template #default="scope">
              {{ formatNumberFloat(scope?.row?.output) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="hours"
            :label="$t('Total Hours ')"
            width="200"
            sortable
            :sort-method="parentSortHours"
          >
            <template #default="scope">
              {{ formatNumberFloat(scope?.row?.hours) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="average"
            :label="$t('Avg Per Hour')"
            sortable
            :sort-method="parentSortAvg"
          >
            <template #default="scope">
              {{ formatNumberFloat(scope?.row?.average) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="p-3 border bg-slate-50 w-2/5">
        <div class="flex justify-center mb-3 w-full gap-3">
          <el-card class="box-card">
            <template #header>
              <div class="card-header justify-between">
                <span class="card-header-title text-primary">{{
                  $t('Average Output')
                }}</span>
              </div>
            </template>
            <div
              class="text-3xl text-center min-h-[60px] text-primary"
              v-loading="isLoading"
            >
              {{ formatNumberFloat(average?.average_output) || 0 }}
            </div>
          </el-card>
          <el-card class="box-card">
            <template #header>
              <div class="card-header justify-between text-success">
                <span class="card-header-title">{{ $t('Average Hours') }}</span>
              </div>
            </template>
            <div
              class="text-3xl text-center min-h-[60px] text-success"
              v-loading="isLoading"
            >
              {{ formatNumberFloat(average?.average_hours) || 0 }}
            </div>
          </el-card>
          <el-card class="box-card">
            <template #header>
              <div class="card-header justify-between text-warning">
                <span class="card-header-title">{{
                  $t('Average Performance')
                }}</span>
              </div>
            </template>
            <div
              class="text-3xl text-center min-h-[60px] text-warning"
              v-loading="isLoading"
            >
              {{ formatNumberFloat(average?.average_performance) || 0 }}
            </div>
          </el-card>
        </div>
        <div class="w-full" v-if="!isLoading && dataPerformanceReport">
          <apexchart
            class="flex justify-center"
            width="100%"
            height="350px"
            type="bar"
            :options="chartPerformanceDepartment"
            :series="seriesPerformanceDepartment"
          ></apexchart>
          <div class="flex justify-center text-xl font-bold">
            Performance of department
          </div>
        </div>
      </div>
    </div>
    <div class="flex flex-row gap-6 mb-4">
      <performance-of-department />
      <top10-employee />
    </div>
  </div>
</template>
