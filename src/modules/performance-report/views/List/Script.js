import { equals } from 'ramda';
import VueApexCharts from 'vue3-apexcharts';
import { performanceReportManager } from '@/api/performanceReport.js';
import PerformanceOfDepartment from '../../components/PerformanceOfDepartment.vue';
import Top10Employee from '../../components/Top10Employee.vue';
import EventBus from '@/utilities/eventBus.js';
import formatNumberMixin from '@/mixins/formatNumber.js';
import dateMixin from '@/mixins/date.js';
export default {
  name: 'PerformanceReportManager',
  mixins: [formatNumberMixin, dateMixin],
  components: {
    apexchart: VueApexCharts,
    PerformanceOfDepartment,
    Top10Employee,
  },
  data() {
    return {
      items: [],
      isLoading: false,
      filter: this.setDefaultFilter(),
      chartPerformanceDepartment: {
        chart: {
          id: 'vuechart-example',
        },
        xaxis: {
          categories: [],
        },
      },
      seriesPerformanceDepartment: [],
      dataPerformanceReport: [],
      average: {},
      date: '',
      defaultSort: { prop: 'average', order: 'ascending' },
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 305);
    },
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },
  },
  beforeUnmount() {
    EventBus.$off('performanceOfDepartment');
    EventBus.$off('top10Employee');
  },
  async mounted() {
    this.setDefaultDate();
    this.fetchPerformanceReportManager();
  },
  methods: {
    toFloat(num) {
      if (!num) return;
      // num = String(num);
      num = parseFloat(num);
      return num;
      // return parseFloat(num.replace(".", "").replace(",", "."));
    },
    inventorySortOutput(a, b) {
      return this.toFloat(a.output) - this.toFloat(b.output);
    },
    inventorySortHours(a, b) {
      return this.toFloat(a.hours) - this.toFloat(b.hours);
    },
    inventorySortAvg(a, b) {
      return this.toFloat(a.average) - this.toFloat(b.average);
    },
    inventorySortAvgInk(a, b) {
      return this.toFloat(a.avg_ink) - this.toFloat(b.avg_ink);
    },
    printingSortOutput(a, b) {
      return this.toFloat(a.output) - this.toFloat(b.output);
    },
    printingSortHours(a, b) {
      return this.toFloat(a.hours) - this.toFloat(b.hours);
    },
    printingSortAvg(a, b) {
      return this.toFloat(a.average) - this.toFloat(b.average);
    },
    printingSortAvgInk(a, b) {
      return this.toFloat(a.avg_ink) - this.toFloat(b.avg_ink);
    },
    sortOutput(a, b) {
      return this.toFloat(a.output) - this.toFloat(b.output);
    },
    sortHours(a, b) {
      return this.toFloat(a.hours) - this.toFloat(b.hours);
    },
    sortAvg(a, b) {
      return this.toFloat(a.average) - this.toFloat(b.average);
    },
    parentSortOutput(a, b) {
      return this.toFloat(a.output) - this.toFloat(b.output);
    },
    parentSortHours(a, b) {
      return this.toFloat(a.hours) - this.toFloat(b.hours);
    },
    parentSortAvg(a, b) {
      return this.toFloat(a.average) - this.toFloat(b.average);
    },
    inventoryParentSortOutput(a, b) {
      return this.toFloat(a.outputInv) - this.toFloat(b.outputInv);
    },
    inventoryParentSortHours(a, b) {
      return this.toFloat(a.hoursInv) - this.toFloat(b.hoursInv);
    },
    inventoryParentSortAvg(a, b) {
      return this.toFloat(a.averageInv) - this.toFloat(b.averageInv);
    },
    sortTable(data) {
      let sortColumn = '';
      let sortBy = '';
      if (data.prop && data.order) {
        sortColumn = data.prop;
        if (data.order === 'ascending') {
          sortBy = 'ASC';
        } else if (data.order === 'descending') {
          sortBy = 'DESC';
        }
      }
      console.log(sortBy, sortColumn);
    },
    setDefaultDate() {
      const today = new Date();
      const priorDate = new Date(new Date().setDate(today.getDate() - 30));
      this.filter.start_date = this.formatDate(today, false);
      this.filter.end_date = this.formatDate(today, false);
      this.date = [
        this.formatDate(today, false),
        this.formatDate(today, false),
      ];
      return (
        (today &&
          priorDate && [
            this.formatDate(priorDate, false),
            this.formatDate(today, false),
          ]) ||
        ''
      );
    },
    async onChangeDate() {
      if (this.date && this.date.length) {
        this.filter.start_date = this.formatDate(this.date[0], false);
        this.filter.end_date = this.formatDate(this.date[1], false);
      } else {
        this.filter.start_date = '';
        this.filter.end_date = '';
      }
      this.fetchPerformanceReportManager();
    },
    setDefaultFilter() {
      let params = {
        start_date: '',
        end_date: '',
      };
      return params;
    },
    setRouteParam() {
      const params = this.filter;
      this.$router.replace({
        name: 'performance_report_for_manager',
        query: params,
      });
    },
    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      if (filter.start_date && filter.end_date) {
        this.date = [filter.start_date, filter.end_date];
      }
      return filter;
    },
    async fetchPerformanceReportManager() {
      this.isLoading = true;
      this.setRouteParam();
      this.seriesPerformanceDepartment = [];
      let arrData = new Array();
      const res = await performanceReportManager(this.filter);
      // if(res.length > 0) {
      this.dataPerformanceReport = this.setDefaultSort(res.data.performance);
      this.average = res.data.average;
      if (res?.data?.chart) {
        res?.data?.chart.forEach((item) => {
          this.chartPerformanceDepartment.xaxis.categories.push(
            item.department
          );
          let performancePercent = 0;
          if (item?.performance_percent) {
            performancePercent = this.formatNumberFloat(
              item?.performance_percent.toFixed(0)
            );
          }
          arrData.push(performancePercent);
        });
      }

      this.seriesPerformanceDepartment.push({
        name: 'Performance',
        data: arrData,
      });
      // }
      EventBus.$emit('performanceOfDepartment', this.filter);
      EventBus.$emit('top10Employee', this.filter);
      this.isLoading = false;
    },
    setDefaultSort(data) {
      if (!data || !data.length) return data;
      const length = data.length;
      for (let i = 0; i < length; i++) {
        const item = data[i];
        switch (item.department) {
          // case "Inventory":
          //   item.employees =
          //     (item?.employees?.length &&
          //       item.employees.sort((a, b) => {
          //         return this.toFloat(b.averageInv) - this.toFloat(a.averageInv);
          //       })) ||
          //     [];
          //   item.employees.forEach((iItem) => {
          //     iItem.employees =
          //       (iItem?.employees?.length &&
          //         iItem.employees.sort((a, b) => {
          //           return this.toFloat(b.average) - this.toFloat(a.average);
          //         })) ||
          //       [];
          //   });
          //   break;
          default:
            // item.employees =
            //   (item?.employees?.length &&
            //     item.employees.sort((a, b) => {
            //       return this.toFloat(b.average) - this.toFloat(a.average);
            //     })) ||
            //   [];
            item.employees =
              (item?.employees?.length &&
                item.employees.sort((a, b) => {
                  return (
                    this.toFloat(b.averageInv) - this.toFloat(a.averageInv)
                  );
                })) ||
              [];
            item.employees.forEach((iItem) => {
              iItem.employees =
                (iItem?.employees?.length &&
                  iItem.employees.sort((a, b) => {
                    return this.toFloat(b.average) - this.toFloat(a.average);
                  })) ||
                [];
            });
            break;
        }
      }
      // data = data.sort((a, b) => {
      //   return this.toFloat(b.average) - this.toFloat(a.average);
      // });
      return data;
    },
    renderJobType(string) {
      const stringNew = string.split('_').join(' ');
      return stringNew.charAt(0).toUpperCase() + stringNew.slice(1);
    },
  },
};
