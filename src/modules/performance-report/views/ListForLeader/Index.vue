<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ $t("Performance Report For Leader") }}</h1>
      </div>
    </div>
    <div class="table-content mb-3">
      <div class="filter-top">
        <el-row :gutter="20">
          <el-col :span="16" class="pr-2">
            <div class="flex items-center">
              <div class="mr-5">Date Report</div>
              <div>
                <el-date-picker
                  v-model="date"
                  type="daterange"
                  unlink-panels
                  range-separator="To"
                  start-placeholder="Start date"
                  end-placeholder="End date"
                  @change="onChangeDate"
                  :shortcuts="shortcuts"
                >
                </el-date-picker>
              </div>
            </div>
          </el-col>
          <el-col :span="8" class="text-right">
            <!-- <el-button type="primary">
              <span class="icon-margin-right"> {{ $t("Export") }}</span>
            </el-button> -->
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="mb-4">
      <SummaryReportForLeader class="mb-4" />
      <ReportDepartmentForLeader class="mb-4" />
      <ReportAllEmployeesForLeader class="mb-4" />
      <ReportAllEmployeesByTimeForLeader />
    </div>
  </div>
</template>
