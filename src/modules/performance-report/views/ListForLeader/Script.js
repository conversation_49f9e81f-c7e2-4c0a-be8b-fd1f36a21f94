import ReportAllEmployeesByTimeForLeader from "../../components/ReportAllEmployeesByTimeForLeader.vue";
import ReportAllEmployeesForLeader from "../../components/ReportAllEmployeesForLeader.vue";
import ReportDepartmentForLeader from "../../components/ReportDepartmentForLeader.vue";
import SummaryReportForLeader from "../../components/SummaryReportForLeader.vue";
import EventBus from "@/utilities/eventBus.js";
import dateMixin from "@/mixins/date.js";
export default {
  name: "PerformanceReportManagerForLeader",
  mixins: [dateMixin],
  components: {
    ReportAllEmployeesByTimeForLeader,
    ReportAllEmployeesForLeader,
    ReportDepartmentForLeader,
    SummaryReportForLeader,
  },
  data() {
    return {
      filter: this.setDefaultFilter(),
      date: "",
    };
  },
  computed: {},
  beforeUnmount() {
    EventBus.$off("reportAllEmployeesByTimeForLeader");
    EventBus.$off("reportAllEmployeesForLeader");
    EventBus.$off("reportDepartmentForLeader");
    EventBus.$off("summaryReportForLeader");
  },
  async mounted() {
    this.setDefaultDate();
    this.fetchData();
  },
  methods: {
    setDefaultDate() {
      const today = new Date();
      const priorDate = new Date(new Date().setDate(today.getDate() - 30));
      this.filter.start_date = this.formatDate(today, false);
      this.filter.end_date = this.formatDate(today, false);
      this.date = [
        this.formatDate(today, false),
        this.formatDate(today, false),
      ];
      return (
        (today &&
          priorDate && [
            this.formatDate(priorDate, false),
            this.formatDate(today, false),
          ]) ||
        ""
      );
    },
    async onChangeDate() {
      if (this.date && this.date.length) {
        this.filter.start_date = this.formatDate(this.date[0], false);
        this.filter.end_date = this.formatDate(this.date[1], false);
      } else {
        this.filter.start_date = "";
        this.filter.end_date = "";
      }
      this.fetchData();
    },
    setDefaultFilter() {
      let params = {
        start_date: "",
        end_date: "",
      };
      return params;
    },
    setRouteParam() {
      const params = this.filter;
      this.$router.replace({
        name: "performance_report_for_leader",
        query: params,
      });
    },
    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      if (filter.start_date && filter.end_date) {
        this.date = [filter.start_date, filter.end_date];
      }
      return filter;
    },
    async fetchData() {
      this.setRouteParam();
      EventBus.$emit("reportAllEmployeesByTimeForLeader", this.filter);
      EventBus.$emit("reportAllEmployeesForLeader", this.filter);
      EventBus.$emit("reportDepartmentForLeader", this.filter);
      EventBus.$emit("summaryReportForLeader", this.filter);
    },
  },
};
