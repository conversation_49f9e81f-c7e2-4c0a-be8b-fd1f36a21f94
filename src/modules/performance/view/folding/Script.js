import { equals, isEmpty } from 'ramda';
import { scanSkuForFolding, livePerformance } from '@/api/performance.js';
import {
  employeeLogoutTimeChecking,
  employeeTimeChecking,
} from '@/api/employee.js';
import IncrementTime from '@/components/IncrementTime.vue';
import { getLinkManualMockup } from '@/api/saleOrderItemImage';
import { STORAGE_URL, NOT_APPLICABLE } from '@/utilities/constants';
import { getListPrinters } from '@/api/label.js';
import warehouseMixin from '@/mixins/warehouse';
import formatNumberMixin from '@/mixins/formatNumber';
import { ArrowDownBold, ArrowUpBold, Medal, Trophy } from '@element-plus/icons-vue';

import PrintingCard from '../../components/PrintingCard.vue';
import SignTag from '@/components/SignTag.vue';
import {getBacklogWithJobType} from "@/api/backlog";
export default {
  name: 'Folding',
  components: {
    IncrementTime,
    PrintingCard,
    SignTag,
    ArrowDownBold,
    ArrowUpBold,
    Medal,
    Trophy
  },
  mixins: [warehouseMixin, formatNumberMixin],
  data() {
    return {
      isLoading: false,
      filter: this.setDefaultFilter(),
      front: null,
      back: null,
      employee_id: '',
      qualityControlId: '',
      label: '',
      product: '',
      sku: '',
      codeEmployee: '',
      employee: null,
      employeeError: '',
      skuError: '',
      job_type: 'folding',
      department: 17,
      time_checking_id: null,
      dataFromTracking: '',
      code: '',
      folded: '',
      iconProduct: '',
      urlStorage: STORAGE_URL,
      orderInsert: null,
      printers: [],
      orderID: '',
      storeNA: NOT_APPLICABLE,
      total_item: 0,
      avg_item_by_hour: 0,
      avg_item_last_year: 0,
      ave_item_current_year: 0,
      performanceHistory: {},
      warehouse_id: '',
      age: '',
      is15Minutes: true,
      isCollapsed: true, // Collapsible state
      countPerformanceReport: 0,
      totalBacklog: 0,
      intervalId: null,

    };
  },
  created() {
    this.focusByElClassScanEmployee();
  },
  mounted() {
    this.listPrinters();
    this.warehouse_id = this.getWarehouseId();
    this.fetchBacklog();
    this.intervalId = setInterval( () => {
      this.fetchBacklog();
    }, 60000 )
  },
  beforeUnmount() {
    this.clearPerformanceInterval();
},
unmounted() {
    this.stopInterval();
  this.clearPerformanceInterval();
},
  beforeDestroy() {
    this.clearPerformanceInterval();
  },
  computed: {
    performanceColor() {
      if(this.countPerformanceReport < 2) {
        return '';
      }

      let percentageDifference = (this.avg_item_by_hour / this.ave_item_current_year) * 100 - 100;
      if (percentageDifference < -30) {
        return 'bg-red-200'; // Change background to red if below 30%
      } else if (percentageDifference < -20) {
        return 'bg-yellow-200'; // Change background to yellow if below 20%
      } else {
        return ''; // Default background color
      }
    },
    performanceColorBorder() {
      if(this.countPerformanceReport < 2) {
        return '';
      }

      let percentageDifference = (this.avg_item_by_hour / this.ave_item_current_year) * 100 - 100;
  
      if (percentageDifference < -30) {
        return 'border border-red-500'; // Change background to red if below 30%
      } else if (percentageDifference < -20) {
        return 'border border-yellow-200'; // Change background to yellow if below 20%
      } else {
        return ''; // Default background color
      }
    },

    performanceColorBorderBottom() {
      if(this.countPerformanceReport < 2) {
        return '';
      }

      let percentageDifference = (this.avg_item_by_hour / this.ave_item_current_year) * 100 - 100;
  
      if (percentageDifference < -30) {
        return 'border-b border-b-red-500'; // Change background to red if below 30%
      } else if (percentageDifference < -20) {
        return 'border-b border-b-yellow-500'; // Change background to yellow if below 20%
      } else {
        return ''; // Default background color
      }
    },
    performanceColorBorderRight() {
      let percentageDifference = (this.avg_item_by_hour / this.ave_item_current_year) * 100 - 100;
  
      if (percentageDifference < -30 && this.countPerformanceReport >= 2) {
        return 'pr-8 border-r border-r-red-500'; // Change background to red if below 30%
      } else if (percentageDifference < -20 && this.countPerformanceReport >= 2) {
        return 'pr-8 border-r border-r-yellow-500'; // Change background to yellow if below 20%
      } else if(this.countPerformanceReport < 2){
        return 'pr-8 border-r border-r-grey-50'; // Default background color
      }
    },


    collapseIcon() {
      return this.isCollapsed ? ArrowUpBold : ArrowDownBold;
    },
  },
  methods: {
    stopInterval() {
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
      }
    },
    async fetchBacklog() {
      let params = {
        'job_type': this.job_type,
      }
      const res = await getBacklogWithJobType(params);
      this.totalBacklog = res.data?.total;
    },
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed;
    },
    getImageUrl(item) {
      return this.urlStorage + '/IconProduct/' + item;
    },
    getLinkManualMockup(printSide) {
      const link = getLinkManualMockup(this.sku, printSide);
      return window.open(link);
    },
    onClear() {
      this.front = '';
      this.back = '';
      this.label = '';
      this.product = '';
      this.dataFromTracking = '';
      this.code = '';
      this.folded = '';
      this.iconProduct = '';
      this.orderInsert = null;
      this.orderID = '';
    },
    setDefaultFilter() {
      return {
        label: '',
      };
    },
    async getScanSKU() {
      if (!this.codeEmployee) {
        this.codeEmployee = '';
        this.employeeError = 'Employee code field cannot be left blank.';
        this.filter.label = '';
        this.focusByElClassScanEmployee();
        return;
      }

      if (!this.employee) {
        this.codeEmployee = '';
        this.employeeError = 'Scan Employee code again';
        this.focusByElClassScanEmployee();
        return;
      }

      this.employeeError = '';

      if (this.filter.label === '') {
        this.focusByElClass();
        return;
      }
      this.onClear();
      this.isLoading = true;
      try {
        const res = await scanSkuForFolding({
          label: this.filter.label,
          employee_id: this.employee.id,
          id_time_checking: this.time_checking_id,
        });
        this.code = res.data.code;
        if (res.data.code === 'label') {
          this.front = res.data.data.front;
          this.back = res.data.data.back;
          this.label = res.data.data.label_id;
          this.product = res.data.data.name;
          this.sku = res.data.data.sku;
          this.folded = res.data.folded;
          if (res.data.folded) {
            this.notification('Duplicate label id', 'error');
          }
        }
        if (res.data.code === 'tracking') {
          this.dataFromTracking = res.data.data;
          this.folded = res.data.folded;
          this.iconProduct = res.data.iconProduct;
          this.orderInsert = res.data?.orderInsert;
          this.orderID = res.data?.data?.order_id;
          if (res.data.folded) {
            this.notification('Duplicate tracking number', 'error');
          }
        }
        if(!res.data.folded || res.data.folded != 1) {
          this.total_item++;
        }
        this.$refs.skuQualityControl?.select();
        this.focusByElClass();
        this.isLoading = false;
      } catch (e) {
        const data = e.response.data;
        let message = this.$t('Not found');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.$refs.skuQualityControl?.select();
        this.focusByElClass();
        this.isLoading = false;
        this.notification(message, 'error');
      }
    },
    focusByElClass(elClass = 'el-form-item-tracking-number') {
      this.$nextTick(() => {
        const el = document.getElementsByClassName(elClass);
        if (!el || !el.length) {
          return;
        }
        const firtsElError = (el && el[0]) || undefined;
        const input = firtsElError.querySelector('input');
        document.body.scrollTop = (input.offsetTop - 120) | 0;
        input.focus();
      });
    },
    focusByElClassScanEmployee(elClass = 'el-form-item-scan-employee') {
      this.$nextTick(() => {
        const el = document.getElementsByClassName(elClass);
        if (!el || !el.length) {
          return;
        }
        const firtsElError = (el && el[0]) || undefined;
        const input = firtsElError.querySelector('input');
        document.body.scrollTop = (input.offsetTop - 120) | 0;
        input.focus();
      });
    },
    async getScanCodeEmloyee() {
      try {
        this.isLoading = true;
        const res = await employeeTimeChecking({
          code: Number(this.codeEmployee),
          job_type: this.job_type,
        });
        if (!res.data.data) {
          this.codeEmployee = '';
          this.employeeError = 'Scan employee code error, please scan again.';
          this.focusByElClassScanEmployee();
          return;
        }
        this.employee = res.data.data;
        this.time_checking_id = res.data.id_time_checking;
        this.focusByElClass();
        await this.fetchLivePerformance();
        this.startPerformanceInterval();
        this.isLoading = false;
      } catch (e) {
        const data = e.response.data;
        let message = this.$t('Not found');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.isLoading = false;
        this.notification(message, 'error');
      }
    },
    async resetEmployee() {
      const res = await employeeLogoutTimeChecking(this.time_checking_id);
      this.employee = null;
      this.employeeError = '';
      this.codeEmployee = '';
      (this.time_checking_id = null), this.onClear();
      this.focusByElClassScanEmployee();
      window.location.reload();
    },
    async listPrinters() {
      getListPrinters().then((res) => {
        if (res.data?.status == 'Success') {
          this.printers = res.data.data;
        }
      });
    },
    async fetchLivePerformance() {
      try {
        const resPerformance = await livePerformance({
          employee_id: this.employee.id,
          job_type: this.job_type,
          department_id: this.department,
          time_tracking_id: this.time_checking_id,
          warehouse_id: this.warehouse_id,
        });
        if (resPerformance.data) {
          const performance = resPerformance.data;
          this.avg_item_by_hour = performance.average_user;
          this.ave_item_current_year = performance.average_current_year;
          this.avg_item_last_year = performance.average_last_year;
          this.performanceHistory = performance;

          if(performance.age) {
            this.age = performance.age
          }
          this.is15Minutes = true;
          this.countPerformanceReport++;
        }
      } catch (e) {
        console.error('Failed to fetch live performance:', e);
      }
    },
    startPerformanceInterval() {
      this.clearPerformanceInterval();
      this.performanceInterval = setInterval(async () => {
        if (this.employee) {
          await this.fetchLivePerformance();
        }
      }, 15 * 60 * 1000); // 5 minutes
    },
    clearPerformanceInterval() {
      if (this.performanceInterval) {
        clearInterval(this.performanceInterval);
        this.performanceInterval = null;
      }
    },
    convertItem(data) {
      if(data > 1) {
        return data +' items/hour'
      } else {
        return data +' item/hour'
      }
    },
    convertOutPut(data) {
      if (data > 1) {
          return data + ' items'
      } else {
          return data + ' item'
      }
    }
  },
};
