<template>
  <div class="">
    <el-dialog
      v-model="showModalExport"
      custom-class="el-dialog-custom el-dialog-custom2 el-dialog-200"
      title="Export Pretreat Preset KSU"
      width="300px"
    >
      <span>Select Mode</span>
      <el-select filterable class="w-full" v-model="filter.mode">
        <el-option
          v-for="mode in dataMode"
          :key="mode.value"
          :label="mode.name"
          :value="mode.value"
        />
      </el-select>
      <el-button
        type="primary"
        @click="handleExport"
        :disabled="isLoading"
        :loading="isLoading"
        class="!mt-2"
      >
        {{ $t('Export') }}
      </el-button>
    </el-dialog>
  </div>
</template>
<script>
import EventBus from '@/utilities/eventBus.js';
import { exportCsv } from '@/api/pretreatPresetSku';
import moment from 'moment';

export default {
  name: 'ExportPresetSKU',
  data() {
    return {
      showModalExport: false,
      isLoading: false,
      filter: this.setDefaultFilter(),
      dataMode: [
        {
          name: 'All',
          value: 'all',
        },
        {
          name: 'Product without preset',
          value: 'not-exist',
        },
        {
          name: 'Product with preset',
          value: 'exist',
        },
      ],
    };
  },
  created() {
    EventBus.$on('showModalExportPreset', () => {
      this.showModalExport = true;
      this.focusByElClass();
    });
  },
  methods: {
    setDefaultFilter() {
      return {
        mode: 'all',
      };
    },
    async handleExport() {
      this.isLoading = true;
      const config = {
        responseType: 'arraybuffer',
        onDownloadProgress: (progressEvent) => {
          this.percentage = parseInt(
            Math.round((progressEvent.loaded / progressEvent.total) * 100)
          );
        },
      };
      try {
        const date = moment().format('YYYY-MM-DD');
        const modeName = this.dataMode.find((item) => item.value === this.filter.mode);
        const response = await exportCsv(this.filter, config);
        console.log(response);
        var blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
        var downloadElement = document.createElement('a');
        var href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        downloadElement.download = `pretreat_preset_sku_${date}_${modeName.name.replaceAll(' ', '-')}.xlsx`;
        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);
      } catch (error) {
        this.notification('Export fail.', 'error');
      }
      this.isLoading = false;
    },
  },
};
</script>
