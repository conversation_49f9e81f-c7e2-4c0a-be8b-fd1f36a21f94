<template>
  <div class="">
    <el-dialog
      v-model="openDialogImportReroute"
      custom-class="el-dialog-custom el-dialog-custom2 el-dialog-800"
      title="Import Pretreat Preset SKU"
      :close-on-click-modal="false"
      width="80%"
      @close="closeModal"
    >
      <div v-loading="isLoading" v-if="step === 1">
        <div>
          <div class="mb-1 flex items-center">
            Upload file CSV/XLS/XLSX
            <span class="ml-1"
              >(
              <el-link
                type="primary"
                :underline="false"
                @click="downloadCsvTemplate"
              >
                {{ $t('Download Example') }}
              </el-link>
              )
            </span>
          </div>
          <el-upload
            accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
            class="upload-import-reroute"
            drag
            :auto-upload="false"
            :on-change="uploadFile"
            v-model:file-list="file"
            :multiple="false"
          >
            <icon :data="iconAdd" class="!w-12 !h-12 !text-gray-400 mb-1" />
            <div class="el-upload__text">
              Drop file here or <em>click to upload</em>
            </div>
          </el-upload>
        </div>
      </div>

      <div v-if="step === 2" v-loading="isLoading">
        <div class="mb-3">
          <div class="text-base flex">
            <div class="mr-8">
              {{ $t('Total Pretreat Preset SKU') }}: {{ data?.total || 0 }}
            </div>
            <div class="text-success mr-8">
              {{ $t('Valid') }}: {{ data?.count_valid || 0 }}
            </div>
            <div class="text-danger">
              {{ $t('Invalid') }}: {{ data?.count_invalid || 0 }}
            </div>
          </div>
        </div>
        <div v-if="data?.invalid?.length" class="mb-3">
          <el-table
            border
            stripe
            size="small"
            :data="data.invalid"
            :max-height="400"
            element-loading-text="Loading..."
          >
            <el-table-column :label="$t('Preset Name')" width="200">
              <template #default="scope">
                {{ scope.row.row.preset_name }}
              </template>
            </el-table-column>
            <el-table-column :label="$t('Reason')">
              <template #default="scope">{{ scope.row.reason }}</template>
            </el-table-column>
          </el-table>
        </div>
        <div class="flex justify-end">
          <el-button @click="back">{{ $t('Reset') }}</el-button>
          <el-button type="primary" @click="insertData">{{
            $t('Import')
          }}</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EventBus from '@/utilities/eventBus.js';
import { verifyImport, importCsv } from '@/api/pretreatPresetSku';
import { STORAGE_URL } from "@/utilities/constants";
import { isEmpty } from 'ramda';

export default {
  name: 'ImportReroute',
  data() {
    return {
      openDialogImportReroute: false,
      file: [],
      data: {},
      step: 1,
      isLoading: false,
      uploadFileRaw: '',
    };
  },
  created() {
    EventBus.$on('showModalImportPreset', () => {
      this.openDialogImportReroute = true;
      this.focusByElClass();
    });
  },
  methods: {
    downloadCsvTemplate() {
      window.open(`${STORAGE_URL}/csv-template/import-pretreat-preset-sku.csv`, '_blank');
    },
    reset() {
      this.step = 1;
      this.data = {};
      this.uploadFileRaw = '';
    },
    closeModal() {
      this.reset();
      this.openDialogImportReroute = false;
    },
    back() {
      this.reset();
    },
    async uploadFile(uploadFile) {
      try {
        this.isLoading = true;
        let formData = new FormData();
        formData.append('file', uploadFile.raw);
        this.uploadFileRaw = uploadFile.raw;
        const res = await verifyImport(formData);
        this.data = res.data || {};
        this.step = 2;
        this.$forceUpdate();
        this.isLoading = false;
      } catch (e) {
        const data = e.response.data;
        this.isLoading = false;
        let message = this.$t('Import Error.');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.notification(message, 'error');
      }
    },
    async insertData() {
      try {
        this.isLoading = true;
        let formData = new FormData();
        formData.append('file', this.uploadFileRaw);

        const res = await importCsv(formData);
        if (res.status === 200) {
          this.notification('Import Successfully.');
          this.isLoading = false;
          this.openDialogImportReroute = false;
          this.$emit('refresh');
        } else {
          this.notification('Import Error.', 'error');
        }
      } catch (e) {
        const data = e.response.data;
        this.isLoading = false;
        let message = this.$t('Import Error.');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.notification(message, 'error');
      }
    },
  },
};
</script>
<style lang="scss">
.upload-import-reroute {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
    }
  }

  &.disabled {
    .el-upload {
      pointer-events: none;
      background-color: var(--el-disabled-bg-color);

      .el-upload-dragger {
        background-color: var(--el-disabled-bg-color);
        pointer-events: none;
      }
    }
  }
}
</style>
