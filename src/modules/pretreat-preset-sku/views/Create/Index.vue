<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
        v-model="dialogVisible"
        :title="$t('Create Pretreat Preset SKU')"
        custom-class="el-dialog-custom"
        @close="onClose()"
        width="50%"
        :destroy-on-close="true"
    >
      <el-form
          status-icon
          ref="createPretreatPreset"
          :model="data"
          :rules="dataRules"
          @submit.prevent="onSubmit"
          :label-position="'top'"
          class="w-full"
      >
        <el-form-item
            :label="$t('Preset Name')"
            class="w-full"
            prop="pretreat_preset_id"
            required
        >
          <el-select
              filterable
              class="w-full"
              :class="{
                'border border-red-400 rounded':
                  errorValidator && errorValidator.color,
              }"
              v-model="data.pretreat_preset_id"
              size="large"
          >
            <el-option
                v-for="item in pretreatPresets"
                :key="item.id"
                :label="item.preset_name"
                :value="item.id"
            />
          </el-select>
          <div class="text-danger text-[12px]" v-if="errorValidator && errorValidator.pretreat_preset_id">
            {{ errorValidator.pretreat_preset_id[0] }}
          </div>
        </el-form-item>
        <div class="flex">
          <el-form-item
              :label="$t('Product Style')"
              class="mr-2 w-full"
              prop="style"
              required
          >
            <el-select
                filterable
                class="w-full"
                :class="{
                'border border-red-400 rounded':
                  errorValidator && errorValidator.style,
              }"
                v-model="data.style"
                size="large"
            >
              <el-option
                  v-for="itemStyle in styles"
                  :key="itemStyle.id"
                  :label="itemStyle.name"
                  :value="itemStyle.sku"
              />
            </el-select>
            <div class="text-danger text-[12px]" v-if="errorValidator && errorValidator.style">
              {{ errorValidator.style[0] }}
            </div>
          </el-form-item>
          <el-form-item
              :label="$t('Product Color')"
              class="w-full"
              prop="color"
              required
          >
            <el-select
                filterable
                class="w-full"
                :class="{
                'border border-red-400 rounded':
                  errorValidator && errorValidator.color,
              }"
                v-model="data.color"
                size="large"
            >
              <el-option
                  v-for="itemColor in colors"
                  :key="itemColor.id"
                  :label="itemColor.name"
                  :value="itemColor.sku"
              />
            </el-select>
            <div class="text-danger text-[12px]" v-if="errorValidator && errorValidator.color">
              {{ errorValidator.color[0] }}
            </div>
          </el-form-item>
        </div>
        <el-button
            type="primary"
            @click="onSubmit"
            :disabled="isLoading"
            :loading="isLoading"
        >{{ $t('Create') }}</el-button>
      </el-form>
    </el-dialog>
  </div>
</template>
