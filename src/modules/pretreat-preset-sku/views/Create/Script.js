import EventBus from "@/utilities/eventBus";
import { create } from "@/api/pretreatPresetSku";

export default {
    name: "CreatePrintingPreset",
    props: {
        styles: {
            default: [],
            type: Array,
        },
        colors: {
            default: [],
            type: Array,
        },
        pretreatPresets: {
            default: [],
            type: Array,
        },
    },
    data() {
        return {
            dialogVisible: false,
            data: this.setDefaultData(),
            isLoading: false,
            dataRules: {
                pretreat_preset_id: [this.commonRule()],
                color: [this.commonRule()],
                style:[this.commonRule()]
            },
            errorValidator: [],
        }
    },
    created() {
        EventBus.$on("showCreatePretreatPreset", () => {
            this.data = this.setDefaultData();
            this.dialogVisible = true;
        });
    },
    beforeUnmount() {
        EventBus.$off("showCreatePretreatPreset");
    },
    methods: {
        commonRule() {
            return {
                required: true,
                message: this.$t('This field cannot be left blank.'),
                trigger: "change",
            };
        },
        setDefaultData() {
            return {
                color: "",
                style: "",
                pretreat_preset_id: ''
            };
        },
        async onSubmit() {
            this.errorValidator = [];
            if (this.isLoading) return;
            const isValid = await this.$refs.createPretreatPreset.validate();
            if (!isValid) {
                return;
            }
            this.isLoading = true;
            try {
                const res = await create(this.data);
                this.dialogVisible = false;
                this.notification(res.data.message);
                this.$emit("refresh");
            } catch (e) {
                this.errorValidator = e.response.data.errors

                let message = e.response.data.message;
                this.notification(message, "error");
            } finally {
                this.isLoading = false;
            }
        },
        onClose() {
            this.errorValidator = [];
        }
    }
}
