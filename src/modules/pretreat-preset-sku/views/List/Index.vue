<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head mb-4">
      <div class="top-head-left"><h1>{{ $t('Pretreat Preset SKU') }}</h1></div>
      <div class="top-head-right">
        <el-button @click="showModalExport" plain type="primary">
          <span class="icon-margin-right"><icon :data="iconExport" /></span>{{ $t('Export Csv') }}
        </el-button>
        <el-button type="primary" @click="showModalImport">
          <span class="icon-margin-right"><icon :data="iconAdd" /></span>{{ $t('Import Csv') }}
        </el-button>
        <el-button type="primary" @click="createPretreatPreset">
          <span class="icon-margin-right"><icon :data="iconAdd" /></span>{{ $t('Create') }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <div class="filter">
        <el-select
            filterable
            class="mr-3"
            placeholder="Select preset name"
            v-model="filter.pretreat_preset_id"
        >
          <el-option
              v-for="item in pretreatPresets"
              :key="item.id"
              :label="item.preset_name"
              :value="item.id"
          />
        </el-select>
        <el-select
            filterable
            class="mr-3"
            placeholder="Select style"
            v-model="filter.style"
        >
          <el-option
              v-for="item in styles"
              :key="item.id"
              :label="item.name"
              :value="item.sku"
          />
        </el-select>
        <el-select
            filterable
            class="mr-3"
            placeholder="Select color"

            v-model="filter.color"
        >
          <el-option
              v-for="item in colors"
              :key="item.id"
              :label="item.name"
              :value="item.sku"
          />
        </el-select>
        <div class="btn-filter">
          <template v-if="hasFilter">
            <el-link type="danger" @click="onClearFilter" :underline="false">{{ $t('Clear') }}</el-link>
          </template>
          <el-button type="primary" @click="onFilter">
            <span class="icon-margin-right"><icon :data="iconFilter" /></span>{{ $t('Filter') }}
          </el-button>
        </div>
      </div>
      <el-table
          border
          stripe size="small" :data="items"
          :max-height="maxHeight"
          v-loading="isLoading"
          element-loading-text="Loading..."
      >
        <el-table-column prop="id" :label="$t('ID')" min-width="100"></el-table-column>
        <el-table-column prop="preset.preset_name" :label="$t('Preset Name')" min-width="100"></el-table-column>
        <el-table-column prop="product_style.name" :label="$t('Style')" min-width="100"></el-table-column>
        <el-table-column prop="product_color.name" :label="$t('Color')" min-width="100"></el-table-column>
        <el-table-column prop="user.username" :label="$t('User')" min-width="100"></el-table-column>
        <el-table-column :label="$t('Date')" min-width="150">
          <template #default="scope">
            {{ listViewDateFormat(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="action" :label="$t('Action')" fixed="right" width="100">
          <template #default="scope">
            <el-link
                class="el-link-edit"
                :underline="false"
                type="primary"
                @click="updatePretreatPreset(scope.row)"
            ><icon :data="iconEdit"
            /></el-link>
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom">
        <div class="total">{{$t('Total:')}} {{ items.length ? formatNumber(total) : 0 }}</div>
        <el-pagination
            :disabled="isLoading"
            background
            layout="prev, pager, next"
            :page-size="filter.limit"
            :total="total"
            @current-change="changePage"
            v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
              v-model="filter.limit"
              :placeholder="$t('Select')"
              size="mini"
              @change="onFilter"
          >
            <el-option
                v-for="item in limits"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
  </div>
  <create-pretreat-preset :styles="styles" :pretreatPresets="pretreatPresets" :colors="colors" @refresh="getPretreatPresetSkuList" />
  <update-pretreat-preset :styles="styles" :pretreatPresets="pretreatPresets" :colors="colors" @refresh="getPretreatPresetSkuList" />
  <import @refresh="getPretreatPresetSkuList" />
  <export />
</template>