import { getList } from "@/api/pretreatPresetSku";
import { fetchAll as fetchAllStyle } from '@/api/productStyle';
import { fetchAll as fetchAllPreset } from '@/api/pretreatPreset';
import { fetchAll as fetchAllColor } from '@/api/productColor';
import EventBus from "@/utilities/eventBus";
import dateMixin from "@/mixins/date";
import CreatePretreatPreset from "@/modules/pretreat-preset-sku/views/Create/Index.vue";
import UpdatePretreatPreset from "@/modules/pretreat-preset-sku/views/Update/Index.vue";
import Import from "@/modules/pretreat-preset-sku/components/Import.vue";
import Export from "@/modules/pretreat-preset-sku/components/Export.vue";


export default {
    name: "PretreatPresetList",
    mixins: [dateMixin],
    components: {
        CreatePretreatPreset,
        UpdatePretreatPreset,
        Import,
        Export
    },
    data() {
        return {
            items: [],
            isLoading: false,
            filter: this.setDefaultFilter(),
            styles: [],
            colors: [],
            pretreatPresets: [],
        }
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 189);
        }
    },
    mounted() {
        this.filter = this.getRouteParam();
        this.getPretreatPresetSkuList();
        this.fetchAllStyle();
        this.fetchAllColor();
        this.fetchAllPreset();
    },
    methods: {
        async fetchAllStyle() {
            const { data } = await fetchAllStyle();
            this.styles = data;
        },
        async fetchAllColor() {
            const { data } = await fetchAllColor();
            this.colors = data;
        },
        async fetchAllPreset() {
            const { data } = await fetchAllPreset();
            this.pretreatPresets = data;
        },
        setDefaultFilter() {
            return {
                limit: 25,
                page: 1,
                pretreat_preset_id: '',
                style: '',
                color: '',
            };
        },
        async getPretreatPresetSkuList() {
            this.isLoading = true;
            this.setRouteParam();
            const { data } = await getList(this.filter);
            this.items = data.data;
            this.total = data.total;
            this.isLoading = false;
        },
        changePage(page) {
            this.filter.page = page;
            this.getPretreatPresetSkuList();
        },
        onFilter() {
            this.filter.page = 1;
            this.getPretreatPresetSkuList();
        },
        onClearFilter() {
            this.filter = this.setDefaultFilter();
            this.$nextTick(() => {
                this.getPretreatPresetSkuList();
            });
        },
        createPretreatPreset() {
            EventBus.$emit("showCreatePretreatPreset");
        },
        updatePretreatPreset(item) {
            EventBus.$emit("showUpdatePretreatPreset", item);
        },
        showModalImport() {
            EventBus.$emit("showModalImportPreset");
        },
        showModalExport() {
            EventBus.$emit("showModalExportPreset");
        }
    }
}