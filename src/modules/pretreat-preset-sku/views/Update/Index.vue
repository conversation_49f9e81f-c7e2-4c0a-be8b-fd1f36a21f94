<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
        v-model="dialogVisible"
        :title="$t('Update Pretreat Preset SKU')"
        custom-class="el-dialog-custom"
        width="50%"
        @close="onClose()"
        :destroy-on-close="true"
    >
      <el-form
          status-icon
          ref="updatePretreatPreset"
          :model="data"
          :rules="dataRules"
          @submit.prevent="onSubmit"
          :label-position="'top'"
          class="w-full"
      >
        <el-form-item
            :label="$t('Preset Name')"
            class="w-full"
            prop="preset_name"
            required
        >
          <el-select
              filterable
              class="w-full"
              @change="changePreset"
              :class="{
                'border border-red-400 rounded':
                  errorValidator && errorValidator.color,
              }"
              v-model="data.preset_name"
              size="large"
          >
            <el-option
                v-for="item in pretreatPresets"
                :key="item.id"
                :label="item.preset_name"
                :value="item.id"
            />
          </el-select>
          <div class="text-danger text-[12px]" v-if="errorValidator && errorValidator.preset_name">
            {{ errorValidator.preset_name[0] }}
          </div>
        </el-form-item>

        <div class="flex">
          <el-form-item
              :label="$t('Product Style')"
              class="mr-2 w-full"
              prop="style"
              required
          >
            <el-select
                filterable
                class="w-full"
                :class="{
                'border border-red-400 rounded':
                  errorValidator && errorValidator.style,
              }"
                v-model="data.style"
                size="large"
            >
              <el-option
                  v-for="itemStyle in styles"
                  :key="itemStyle.id"
                  :label="itemStyle.name"
                  :value="itemStyle.sku"
              />
            </el-select>
            <div class="text-danger text-[12px]" v-if="errorValidator && errorValidator.style">
              {{ errorValidator.color[0] }}
            </div>
          </el-form-item>
          <el-form-item
              :label="$t('Product Color')"
              class="w-full"
              prop="color"
              required
          >
            <el-select
                filterable
                class="w-full"
                :class="{
                'border border-red-400 rounded':
                  errorValidator && errorValidator.color,
              }"
                v-model="data.color"
                size="large"
            >
              <el-option
                  v-for="itemColor in colors"
                  :key="itemColor.id"
                  :label="itemColor.name"
                  :value="itemColor.sku"
              />
            </el-select>
            <div class="text-danger text-[12px]" v-if="errorValidator && errorValidator.color">
              {{ errorValidator.color[0] }}
            </div>
          </el-form-item>
        </div>
        <el-button
            type="primary"
            @click="onSubmit"
            :disabled="isLoading"
            :loading="isLoading"
        >{{ $t('Update') }}</el-button>
      </el-form>
    </el-dialog>
  </div>
</template>
