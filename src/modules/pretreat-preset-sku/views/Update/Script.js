import EventBus from "@/utilities/eventBus";
import { update } from "@/api/pretreatPresetSku";
import {clone} from "ramda";

export default {
    name: "UpdatePrintingPreset",
    props: {
        styles: {
            default: [],
            type: Array,
        },
        colors: {
            default: [],
            type: Array,
        },
        pretreatPresets: {
            default: [],
            type: Array,
        },
    },
    data() {
        return {
            pretreatPresetId: null,
            dialogVisible: false,
            data: this.setDefaultData(),
            isLoading: false,
            dataRules: {
                preset_name: [this.commonRule()],
                color: [this.commonRule()],
                style:[this.commonRule()]
            },
            errorValidator: [],
        }
    },
    created() {
        EventBus.$on("showUpdatePretreatPreset", (item) => {
            this.pretreatPresetId = item.id;
            let preset = clone(item);
            this.data.preset_name = preset?.preset?.preset_name;
            this.data.pretreat_preset_id = preset?.preset?.id;
            this.data.color = preset.color;
            this.data.style = preset.style;
            this.dialogVisible = true;
        });
    },
    beforeUnmount() {
        EventBus.$off("showUpdatePretreatPreset");
    },
    methods: {
        changePreset(id) {
            this.data.pretreat_preset_id = id;
        },
        commonRule() {
            return {
                required: true,
                message: this.$t('This field cannot be left blank.'),
                trigger: "change",
            };
        },
        setDefaultData() {
            return {
                preset_name: "",
                color: "",
                style: "",
                pretreat_preset_id: "",
            };
        },
        async onSubmit() {
            this.errorValidator = [];
            if (this.isLoading) return;
            const isValid = await this.$refs.updatePretreatPreset.validate();
            if (!isValid) {
                return;
            }
            this.isLoading = true;
            try {
                const res = await update(this.pretreatPresetId, this.data);
                this.dialogVisible = false;
                this.notification(res.data.message);
                this.$emit("refresh");
            } catch (e) {
                this.errorValidator = e.response.data.errors
                let message = e.response.data.message;
                this.notification(message, "error");
            } finally {
                this.isLoading = false;
            }
        },
        onClose() {
            this.errorValidator = [];
        }
    }
}
