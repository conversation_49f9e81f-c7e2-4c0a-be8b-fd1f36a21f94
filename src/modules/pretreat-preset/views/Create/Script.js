import EventBus from '@/utilities/eventBus';
import { create } from '@/api/pretreatPreset';

export default {
  name: 'CreatePretreatPreset',
  data() {
    return {
      dialogVisible: false,
      data: this.setDefaultData(),
      isLoading: false,
      dataRules: {
        preset_name: [this.commonRule()],
        density: [
          {
            type: 'number',
            min: 0.01,
            message: this.$t('Value must be greater than or equal to 0.01'),
            trigger: ['blur', 'change'],
          },
          this.commonRule()
        ],
        cure_temperature: [
          {
            type: 'number',
            message: this.$t('Value must be equal to 0 or greater than or equal to 32'),
            trigger: ['blur', 'change'],
          },
          {
            validator: (rule, value, callback) => {
              if (value == 0 || value >= 32) {
                callback();
              } else {
                callback(new Error(this.$t('Value must be equal to 0 or greater than or equal to 32')));
              }
            }, trigger: ['blur', 'change']
          },
          this.commonRule()
        ],
        print_cure_temperature: [
          {
            type: 'number',
            min: 32,
            message: this.$t('Value must be greater than or equal to 32'),
            trigger: ['blur', 'change'],
          },
          this.commonRule()
        ],
        print_cure_time: [
          {
            type: 'number',
            min: 1,
            message: this.$t('Value must be greater than or equal to 1'),
            trigger: ['blur', 'change'],
          },
          this.commonRule()
        ],
        print_time: [
          {
            type: 'number',
            min: 1,
            message: this.$t('Value must be greater than or equal to 1'),
            trigger: ['blur', 'change'],
          },
          this.commonRule()
        ],
        press_temperature: [
          {
            type: 'number',
            min: 32,
            message: this.$t('Value must be greater than or equal to 32'),
            trigger: ['blur', 'change'],
          },
          this.commonRule()
        ],
        cure_time: [
          {
            type: 'number',
            min: 0,
            message: this.$t('Value must be greater than or equal to 0'),
            trigger: ['blur', 'change'],
          },
          this.commonRule()
        ],
        press_time: [
          {
            type: 'number',
            min: 1,
            message: this.$t('Value must be greater than or equal to 1'),
            trigger: ['blur', 'change'],
          },
          this.commonRule()
        ],
        pressure: [
          {
            type: 'number',
            min: 1,
            message: this.$t('Value must be greater than or equal to 1'),
            trigger: ['blur', 'change'],
          },
          this.commonRule()
        ],
      },
      errorValidator: {},
    };
  },
  created() {
    EventBus.$on('showCreatePretreatPreset', () => {
      this.data = this.setDefaultData();
      this.dialogVisible = true;
    });
  },
  methods: {
    commonRule() {
      return {
        required: true,
        message: this.$t('This field cannot be left blank.'),
        trigger: ['change', 'blur'],
      };
    },
    setDefaultData() {
      return {
        preset_name: '',
        density: 0.01,
        cure_temperature: 0,
        press_temperature: 32,
        cure_time: 0,
        press_time: 1,
        pressure: 1,
        print_cure_temperature: 32,
        print_cure_time: 1,
        print_time: 1,
      };
    },
    async onSubmit() {
      if (this.isLoading) return;
      const isValid = await this.$refs.createPretreatPreset.validate();
      if (!isValid) {
        return;
      }
      this.isLoading = true;
      try {
        const res = await create(this.data);
        this.dialogVisible = false;
        this.notification(res.data.message);
        this.$emit('refresh');
      } catch (e) {
        let error = e.response.data;
        if (error.errors && error.errors.style) {
          this.notification(error.errors.style[0], 'error');
        } else {
          this.notification(error.message, 'error');
        }
      } finally {
        this.isLoading = false;
      }
    },
  },
};
