<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head mb-4">
      <div class="top-head-left"><h1>{{ $t('Pretreat Preset') }}</h1></div>
      <div class="top-head-right">
        <el-button @click="handleExport" plain type="primary">
          <span class="icon-margin-right"><icon :data="iconExport" /></span>{{ $t('Export Csv') }}
        </el-button>
        <el-button type="primary" @click="showModalImport">
          <span class="icon-margin-right"><icon :data="iconAdd" /></span>{{ $t('Import Csv') }}
        </el-button>
        <el-button type="primary" @click="createPretreatPreset">
          <span class="icon-margin-right"><icon :data="iconAdd" /></span>{{ $t('Create') }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <div class="filter">
        <el-input
            :placeholder="$t('Search preset name')"
            class="search mr-3"
            v-model="filter.preset_name"
            @keyup.enter="onFilter"
        />
        <div class="btn-filter">
          <template v-if="hasFilter">
            <el-link type="danger" @click="onClearFilter" :underline="false">{{ $t('Clear') }}</el-link>
          </template>
          <el-button type="primary" @click="onFilter">
            <span class="icon-margin-right"><icon :data="iconFilter" /></span>{{ $t('Filter') }}
          </el-button>
        </div>
      </div>
      <el-table
          border
          stripe size="small" :data="items"
          :max-height="maxHeight"
          v-loading="isLoading"
          element-loading-text="Loading..."
      >
        <el-table-column prop="preset_name" :label="$t('Preset Name')" min-width="150" align="center"></el-table-column>
<!--        <el-table-column prop="color" :label="$t('Product Color')" min-width="200" align="center">-->
<!--          <template #default="scope">-->
<!--            {{ scope.row.product_color ? scope.row.product_color.name : '' }}-->
<!--          </template></el-table-column>-->
<!--          <el-table-column prop="sku" :label="$t('SKU')" min-width="100" align="center">-->
<!--          <template #default="scope">-->
<!--            {{ (scope.row.product_style && scope.row.product_style) ? `${scope.row.product_style.sku}${scope.row.product_color.sku}` : '' }}-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column prop="density" :label="$t('Density (gram)')" min-width="120" align="center"></el-table-column>
        <el-table-column prop="cure_temperature" :label="$t('Cure temp (°F)')" min-width="100" align="center"></el-table-column>
        <el-table-column prop="cure_time" :label="$t('Cure time (s)')" min-width="100" align="center"></el-table-column>
        <el-table-column prop="press_time" :label="$t('Press time (s)')" min-width="100" align="center"></el-table-column>
        <el-table-column prop="pressure" :label="$t('Pressure (PSI)')" min-width="100" align="center"></el-table-column>
        <el-table-column prop="press_temperature" :label="$t('Press temp (°F)')" min-width="120" align="center"></el-table-column>
        <!-- <el-table-column class-name="break-words" prop="print_time" :label="$t('Print time (s)')" min-width="100" align="center"></el-table-column> -->
        <el-table-column class-name="break-words" prop="print_cure_temperature" :label="$t('Print Cure temp (°F)')" min-width="100" align="center"></el-table-column>
        <el-table-column class-name="break-words" prop="print_cure_time" :label="$t('Print Cure time (s)')" min-width="100" align="center"></el-table-column>
        <el-table-column prop="action" :label="$t('Action')" fixed="right" width="100" align="center">
          <template #default="scope">
            <el-link
                class="el-link-edit"
                :underline="false"
                type="primary"
                @click="updatePretreatPreset(scope.row)"
            ><icon :data="iconEdit"
            /></el-link>
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom">
        <div class="total">{{$t('Total:')}} {{ items.length ? formatNumber(total) : 0 }}</div>
        <el-pagination
            :disabled="isLoading"
            background
            layout="prev, pager, next"
            :page-size="filter.limit"
            :total="total"
            @current-change="changePage"
            v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
              v-model="filter.limit"
              :placeholder="$t('Select')"
              size="mini"
              @change="onFilter"
          >
            <el-option
                v-for="item in limits"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
  </div>
  <create-pretreat-preset @refresh="getPretreatPreset" />
  <update-pretreat-preset @refresh="getPretreatPreset" />
  <import @refresh="getPretreatPreset" />
</template>