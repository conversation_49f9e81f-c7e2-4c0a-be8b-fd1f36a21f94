import { fetchAll as fetchAllStyle } from '@/api/productStyle';
import { fetchAll as fetchAllColor } from '@/api/productColor';
import {exportCsv, getList} from "@/api/pretreatPreset";
import EventBus from "@/utilities/eventBus";
import CreatePretreatPreset from "@/modules/pretreat-preset/views/Create/Index.vue";
import UpdatePretreatPreset from "@/modules/pretreat-preset/views/Update/Index.vue";
import Import from "@/modules/pretreat-preset/components/Import.vue";
import moment from "moment/moment";

export default {
    name: "PretreatPresetList",
    components: {
        CreatePretreatPreset,
        UpdatePretreatPreset,
        Import,
    },
    data() {
        return {
            items: [],
            styles: [],
            colors: [],
            isLoading: false,
            filter: this.setDefaultFilter(),
        }
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 189);
        }
    },
    mounted() {
        this.filter = this.getRouteParam();
        this.getPretreatPreset();
    //     this.fetchAllStyle();
    //     this.fetchAllColor();
    },
    methods: {
        setDefaultFilter() {
            return {
                limit: 25,
                page: 1,
                preset_name: '',
                // style: '',
                // color: '',
            };
        },
        async getPretreatPreset() {
            this.isLoading = true;
            this.setRouteParam();
            const { data } = await getList(this.filter);
            this.items = data.data;
            this.total = data.total;
            this.isLoading = false;
        },
        async fetchAllStyle() {
            const { data } = await fetchAllStyle();
            this.styles = data;
        },
        async fetchAllColor() {
            const { data } = await fetchAllColor();
            this.colors = data;
        },
        changePage(page) {
            this.filter.page = page;
            this.getPretreatPreset();
        },
        onFilter() {
            this.filter.page = 1;
            this.getPretreatPreset();
        },
        onClearFilter() {
            this.filter = this.setDefaultFilter();
            this.getPretreatPreset();
        },
        createPretreatPreset() {
            EventBus.$emit("showCreatePretreatPreset");
        },
        updatePretreatPreset(item) {
            EventBus.$emit("showUpdatePretreatPreset", item);
        },
        showModalImport() {
            EventBus.$emit("showModalImport");
        },
        async handleExport() {
            this.isLoading = true;
            const config = {
                responseType: 'arraybuffer',
                onDownloadProgress: (progressEvent) => {
                    this.percentage = parseInt(
                        Math.round((progressEvent.loaded / progressEvent.total) * 100)
                    );
                },
            };
            try {
                const date = moment().format('YYYY-MM-DD');
                const response = await exportCsv({
                    mode: 'all',
                }, config);
                console.log(response);
                var blob = new Blob([response.data], {
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                });
                var downloadElement = document.createElement('a');
                var href = window.URL.createObjectURL(blob);
                downloadElement.href = href;
                downloadElement.download = `${date}_All.xlsx`;
                document.body.appendChild(downloadElement);
                downloadElement.click();
                document.body.removeChild(downloadElement);
                window.URL.revokeObjectURL(href);
            } catch (error) {
                this.notification('Export fail.', 'error');
            }
            this.isLoading = false;
        },
    }
}