<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :title="$t('Update Pretreat Preset')"
      custom-class="el-dialog-custom"
      width="50%"
      :destroy-on-close="true"
    >
      <el-form
        status-icon
        ref="updatePrintingPreset"
        :model="data"
        :rules="dataRules"
        @submit.prevent="onSubmit"
        :label-position="'top'"
        class="w-full"
      >
        <el-form-item :label="$t('Preset Name')" prop="preset_name">
          <el-input
            v-model="data.preset_name"
            @keyup.enter="onSubmit"
            autocomplete="off"
            :precision="2"
          ></el-input>
        </el-form-item>
        <div class="flex flex-nowrap justify-between gap-1 items-center">
          <el-form-item :label="$t('Density (gram)')" prop="density" class="w-1/3">
            <el-input-number
              v-model="data.density"
              @keyup.enter="onSubmit"
              autocomplete="off"
              :precision="2"
            ></el-input-number>
          </el-form-item>
          <el-form-item
            :label="$t('Cure Temperature (°F)')"
            prop="cure_temperature"
            class="w-1/3"
          >
            <el-input-number
              v-model="data.cure_temperature"
              @keyup.enter="onSubmit"
              autocomplete="off"
              :precision="1"
            ></el-input-number>
          </el-form-item>
          <el-form-item :label="$t('Cure time (s)')" prop="cure_time" class="w-1/3">
            <el-input-number
              v-model="data.cure_time"
              @keyup.enter="onSubmit"
              autocomplete="off"
            ></el-input-number>
          </el-form-item>
        </div>
        <div class="flex flex-nowrap justify-between gap-1 items-center">
          <el-form-item :label="$t('Press time (s)')" prop="press_time" class="w-1/3">
            <el-input-number
              v-model="data.press_time"
              @keyup.enter="onSubmit"
              autocomplete="off"
            ></el-input-number>
          </el-form-item>
          <el-form-item :label="$t('Pressure (PSI)')" prop="pressure" class="w-1/3">
            <el-input-number
              v-model="data.pressure"
              @keyup.enter="onSubmit"
              autocomplete="off"
            ></el-input-number>
          </el-form-item>
          <el-form-item
            :label="$t('Press Temperature (°F)')"
            prop="press_temperature"
            class="w-1/3"
          >
            <el-input-number
              v-model="data.press_temperature"
              @keyup.enter="onSubmit"
              autocomplete="off"
              :precision="1"
            ></el-input-number>
          </el-form-item>
        </div>
        <div class="flex flex-nowrap justify-between gap-1 items-center">
          <!-- <el-form-item :label="$t('Print time (s)')" prop="print_time">
            <el-input-number
              v-model="data.print_time"
              @keyup.enter="onSubmit"
              autocomplete="off"
            ></el-input-number>
          </el-form-item> -->
          <el-form-item
            :label="$t('Print Cure Temperature (°F)')"
            prop="print_cure_temperature"
            class="w-1/3"
          >
            <el-input-number
              v-model="data.print_cure_temperature"
              @keyup.enter="onSubmit"
              autocomplete="off"
              :precision="1"
            ></el-input-number>
          </el-form-item>
          <el-form-item
            :label="$t('Print Cure time (s)')"
            prop="print_cure_time"
            class="w-1/3"
          >
            <el-input-number
              v-model="data.print_cure_time"
              @keyup.enter="onSubmit"
              autocomplete="off"
            ></el-input-number>
          </el-form-item>
          <el-form-item class="w-1/3"> </el-form-item>
        </div>
        <el-button
          type="primary"
          @click="onSubmit"
          :disabled="isLoading"
          :loading="isLoading"
          class="float-right"
          >{{ $t('Update') }}
        </el-button>
      </el-form>
    </el-dialog>
  </div>
</template>
