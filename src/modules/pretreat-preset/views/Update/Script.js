import EventBus from '@/utilities/eventBus';
import { update } from '@/api/pretreatPreset';

export default {
  name: 'UpdatePrintingPreset',
  data() {
    return {
      pretreatPresetId: 0,
      dialogVisible: false,
      data: this.setDefaultData(),
      isLoading: false,
      dataRules: {
        preset_name: [this.commonRule()],
        density: [
          {
            type: 'number',
            min: 0.01,
            message: this.$t('Value must be greater than or equal to 0.01'),
            trigger: ['blur', 'change'],
          },
          this.commonRule()
        ],
        cure_temperature: [
          {
            type: 'number',
            message: this.$t('Value must be equal to 0 or greater than or equal to 32'),
            trigger: ['blur', 'change'],
          },
          {
            validator: (rule, value, callback) => {
              if (value == 0 || value >= 32) {
                callback();
              } else {
                callback(new Error(this.$t('Value must be equal to 0 or greater than or equal to 32')));
              }
            }, trigger: ['blur', 'change']
          },
          this.commonRule()
        ],
        print_cure_temperature: [
          {
            type: 'number',
            min: 32,
            message: this.$t('Value must be greater than or equal to 32'),
            trigger: ['blur', 'change'],
          },
          this.commonRule()
        ],
        print_cure_time: [
          {
            type: 'number',
            min: 1,
            message: this.$t('Value must be greater than or equal to 1'),
            trigger: ['blur', 'change'],
          },
          this.commonRule()
        ],
        print_time: [
          {
            type: 'number',
            min: 1,
            message: this.$t('Value must be greater than or equal to 1'),
            trigger: ['blur', 'change'],
          },
          this.commonRule()
        ],
        press_temperature: [
          {
            type: 'number',
            min: 32,
            message: this.$t('Value must be greater than or equal to 32'),
            trigger: ['blur', 'change'],
          },
          this.commonRule()
        ],
        cure_time: [
          {
            type: 'number',
            min: 0,
            message: this.$t('Value must be greater than or equal to 0'),
            trigger: ['blur', 'change'],
          },
          this.commonRule()
        ],
        press_time: [
          {
            type: 'number',
            min: 1,
            message: this.$t('Value must be greater than or equal to 1'),
            trigger: ['blur', 'change'],
          },
          this.commonRule()
        ],
        pressure: [
          {
            type: 'number',
            min: 1,
            message: this.$t('Value must be greater than or equal to 1'),
            trigger: ['blur', 'change'],
          },
          this.commonRule()
        ],
      },
      errorValidator: {},
    };
  },
  created() {
    EventBus.$on('showUpdatePretreatPreset', (item) => {
      this.pretreatPresetId = item.id;
      this.data = Object.assign(this.data, item);
      this.dialogVisible = true;
    });
  },
  methods: {
    commonRule(field) {
      return {
        required: true,
        message: this.$t('This field cannot be left blank.'),
        trigger: ['change', 'blur'],
      };
    },
    setDefaultData() {
      return {
        preset_name: '',
        density: 0.01,
        cure_temperature: 32,
        press_temperature: 32,
        cure_time: 1,
        press_time: 1,
        pressure: 1,
      };
    },
    async onSubmit() {
      if (this.isLoading) return;
      const isValid = await this.$refs.updatePrintingPreset.validate();
      if (!isValid) {
        return;
      }
      this.isLoading = true;
      try {
        const dataUpdate = {
          preset_name: this.data.preset_name,
          density: this.data.density,
          cure_temperature: this.data.cure_temperature,
          press_temperature: this.data.press_temperature,
          cure_time: this.data.cure_time,
          press_time: this.data.press_time,
          pressure: this.data.pressure,
          print_cure_temperature: this.data.print_cure_temperature,
          print_cure_time: this.data.print_cure_time,
          print_time: this.data.print_time,
        };
        const res = await update(this.pretreatPresetId, dataUpdate);
        this.dialogVisible = false;
        this.notification(res.data.message);
        this.$emit('refresh');
      } catch (e) {
        let message = e.response.data.message;
        this.notification(message, 'error');
      } finally {
        this.isLoading = false;
      }
    },
  },
};
