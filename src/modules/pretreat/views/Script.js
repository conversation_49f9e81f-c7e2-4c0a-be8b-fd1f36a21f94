import { equals, isEmpty } from 'ramda';
import { scanLabel, updatePretreatSuccess, clearQueue, addQueue } from '@/api/pretreat.js';
import {
  employeeLogoutTimeChecking,
  employeeTimeChecking,
} from '@/api/employee.js';
import IncrementTimer from '@/components/IncrementTimer.vue';


export default {
  name: 'Pretreat',
  components: {
    IncrementTimer,
  },
  mixins: [],
  data() {
    return {
      isLoading: false,
      filter: this.setDefaultFilter(),
      employee_id: '',
      codeEmployee: '',
      employee: null,
      employeeError: '',
      image: null,
      printSides: [],
      currentSide: '',
      product: null,
      store: '',
      label: '',
      position: null,
      pretreatPreset: null,
      job_type: 'pretreat',
      time_checking_id: null,
      XMLData: '',
      productName: '',
      productColor: '',
      selectedSide: [],
      pretreatedTimes: 0,
      totalPretreat: 0,
    };
  },
  created() {
    this.focusByElClass('el-form-item-scan-employee');
  },
  computed: {},
  methods: {
    selectPrintSide(printSide) {
      this.selectedSide = printSide;
    },
    onClear() {
      this.image = null;
      this.printSides = [];
      this.pretreatedTimes = 0;
      this.totalPretreat = 0;
      this.currentSide = '';
      this.product = null;
      this.store = '';
      this.label = '';
      this.XMLData = '';
      this.position = null;
      this.pretreatPreset = null;
    },
    setDefaultFilter() {
      return {
        label: '',
      };
    },
    async getScanLabel() {
      if (!this.codeEmployee) {
        this.codeEmployee = '';
        this.employeeError = 'Employee code field cannot be left blank.';
        this.filter.label = '';
        this.focusByElClass('el-form-item-scan-employee');
        return;
      }
      if (!this.employee) {
        this.codeEmployee = '';
        this.employeeError = 'Scan Employee code again';
        this.focusByElClass('el-form-item-scan-employee');
        return;
      }
      this.employeeError = '';
      if (this.filter.label === '') {
        this.focusByElClass();
        return;
      }
      this.onClear();
      this.isLoading = true;
      try {
        const res = await scanLabel({
          label: this.filter.label,
          employee_id: this.employee.id,
          id_time_checking: this.time_checking_id,
        });
        this.printSides = res.data.data?.dtg_print_sides ?? [];
        this.totalPretreat = res.data.data?.total_pretreated_times ?? 0;
        this.pretreatedTimes = res.data.data?.pretreated_times ?? 0;
        this.selectedSide = this.printSides[0] ?? [];
        this.productName = res.data.data?.name;
        this.productColor = res.data.data?.color_code ?? '#ffffff';
        this.store = res.data.data?.store_code;
        this.label = res.data.data.label_id;
        this.filter.label = '';
        this.focusByElClass();
        this.isLoading = false;
        this.notification(this.$t('Success!'), 'success');
      } catch (e) {
        const data = e.response.data;
        let message = this.$t('Oops, something went wrong..');
        if (!isEmpty(data.message)) {
          message = data.message[0];
        }
        this.focusByElClass();
        this.isLoading = false;
        this.notification(message, 'error');
      }

    },
    // async connectPretreatMachine() {
    //   try {
    //     await clearQueue('http://127.0.0.1:16542/ClearQueue');
    //     await addQueue(
    //       'http://127.0.0.1:16542/AddToQueue',
    //       this.XMLData
    //     );
    //     let fluidGrams = this.pretreatPreset.density * this.position.pretreat_zone.width * this.position.pretreat_zone.height;
    //     const res = await updatePretreatSuccess({
    //       label: this.filter.label,
    //       employee_id: this.employee.id,
    //       id_time_checking: this.time_checking_id,
    //       fluid_grams: fluidGrams,
    //     });
    //   } catch (error) {
    //     this.notification('Connect pretreat app fail.', 'error');
    //   }
    // },
    focusByElClass(elClass = 'el-form-item-tracking-number') {
      this.$nextTick(() => {
        const el = document.getElementsByClassName(elClass);
        if (!el || !el.length) {
          return;
        }
        const firtsElError = (el && el[0]) || undefined;
        const input = firtsElError.querySelector('input');
        document.body.scrollTop = (input.offsetTop - 120) | 0;
        input.focus();
      });
    },
    async getScanCodeEmloyee() {
      try {
        this.isLoading = true;
        const res = await employeeTimeChecking({
          code: Number(this.codeEmployee),
          job_type: this.job_type,
        });
        if (!res.data.data) {
          this.codeEmployee = '';
          this.employeeError = 'Scan employee code error, please scan again.';
          this.focusByElClass('el-form-item-scan-employee');
          return;
        }
        this.employee = res.data.data;
        this.time_checking_id = res.data.id_time_checking;
        this.focusByElClass();
        this.isLoading = false;
      } catch (e) {
        const data = e.response.data;
        let message = this.$t('Oops, something went wrong..');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.isLoading = false;
        this.notification(message, 'error');
      }
    },
    async resetEmployee() {
      const res = await employeeLogoutTimeChecking(this.time_checking_id);
      this.employee = null;
      this.employeeError = '';
      this.codeEmployee = '';
      this.time_checking_id = null;
      this.onClear();
      this.focusByElClass('el-form-item-scan-employee');
    },
  },
};
