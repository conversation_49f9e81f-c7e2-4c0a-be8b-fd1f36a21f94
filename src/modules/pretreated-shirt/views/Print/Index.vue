<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div class="border-b pb-4 flex flex-row items-center">
    <div class="mr-5">
      <h3>{{ $t('Pretreated Shirt WIP') }}</h3>
    </div>
    <div class="flex items-center gap-1">
      <div v-for="item in priorityStores"  :key="item.id" @click="toggleSelectStore(item.id)" class="!mr-2 flex items-center gap-1 cursor-pointer px-2 h-[32px] border border-gray-300 rounded-md text-sm" :label="item.id"
           :class="{
            '!border-blue-400': storeId === item.id
          }"
      >
        <div>
          <div class="w-3 h-3 border border-gray-300 rounded-full relative after:absolute after:content-[''] after:w-[4px] after:h-[4px] after:bg-white after:rounded-full after:top-1/2 after:left-1/2 after:-translate-x-1/2 after:-translate-y-1/2"
               :class="{
            '!border-blue-400 bg-blue-500': storeId === item.id
          }"
          >
          </div>
        </div>

        <div class="text-sm" :class="{

            'text-primary': storeId === item.id
          }">{{ item.code || storeNA }}({{ item.count }})</div>
      </div>

    </div>
    <div class="text-right grow">
      {{ $t('Total Pending: ') }}
      <span class="font-semibold">{{ totalCount }}</span>
    </div>
  </div>
  <div class="flex flex-row">
    <div class="basis-1/5 border-r">
      <div v-if="styles.length == 0">
        <el-empty :description="$t('no data')"></el-empty>
      </div>
      <el-scrollbar style="height: calc(100vh - 150px)" v-else>
        <div
          v-for="item in styles"
          :key="item.name"
          @click="setStyle(item)"
          class="scrollbar-item bg-gradient-to-r from-cyan-200 to-blue-300 hover:from-cyan-300 hover:to-blue-400 my-3 mr-3 p-3"
          :class="{
            'border-4 border-blue-500/75': item.sku == filterPrint.style_sku,
          }"
        >
          <div class="name">{{ item.name }}</div>
          <div class="sub">
            {{ $t('Pending Label: ') }}<strong>{{ item.count }}</strong
            ><br />
            {{ $t('Last Printed: ') }}
            {{
              item.last_printed_at
                ? utcToLocalTime(item.last_printed_at).format('lll')
                : 'not yet'
            }}
          </div>
        </div>
      </el-scrollbar>
    </div>
    <div class="basis-3/4">
      <div class="p-3">
        <el-input-number
          class="mr-3"
          style="width: 150px"
          v-model="filterPrint.limit"
          :min="0"
          :max="maxAllowPrint"
        />
        <el-button @click="confirmPrintBtn" type="primary">{{
          $t('Confirm')
        }}</el-button>
      </div>
      <el-dialog
        v-model="dialogVisible"
        :title="$t('Assign To Staff')"
        width="30%"
      >
        <div>
          <el-input
            class="m-0"
            v-model="filterPrint.employee_number"
            size="large"
            :placeholder="$t('Enter The Staff Number')"
          ></el-input>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">{{
              $t('Cancel')
            }}</el-button>
            <el-button
              type="primary"
              :disabled="clickConfirm"
              @click="confirmStaff()"
              >{{ $t('Confirm') }}</el-button
            >
          </span>
        </template>
      </el-dialog>
      <el-tabs v-model="activeTab" class="ml-3">
        <el-tab-pane :label="$t('Printing')" name="printing">
          <el-table stripe :data="pendingList.data" style="width: 100%">
            <el-table-column prop="id" :label="$t('ID')" width="120" />
            <el-table-column :label="$t('Created at')" width="180">
              <template #default="scope">
                {{ utcToLocalTime(scope.row.created_at).format('lll') }}
              </template>
            </el-table-column>
            <el-table-column :label="$t('Filter')">
              <template #default="scope">
                <el-tag class="mr-2 mb-1" size="small" type="success">
                  {{ scope.row.style?.name }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="employee_name" :label="$t('Staff')">
              <template #default="scope">
                {{ scope.row.employee_convert.name }}
              </template>
            </el-table-column>
            <el-table-column
              prop="quantity"
              :label="$t('Quantity')"
              width="100"
            />
            <el-table-column :label="$t('Status')" width="160">
              <template #default="scope">
                <el-tag v-if="+scope.row.convert_status == 1" type="success">{{
                  $t('Completed')
                }}</el-tag>
                <template v-else>
                  <template v-if="scope.row.convert_percent > 0">
                    <el-progress :percentage="scope.row.percentage" />
                  </template>
                  <template v-else>
                    <el-tag
                      v-if="+scope.row.convert_status == 2"
                      type="danger"
                      >{{ $t('Error') }}</el-tag
                    >
                    <el-tag v-else type="warning">{{ $t('Pending') }}</el-tag>
                  </template>
                </template>
              </template>
            </el-table-column>
            <el-table-column width="100" :label="$t('Action')">
              <template #default="scope">
                <template v-if="scope.row.print_status == 0">
                  <el-button
                    size="small"
                    :disabled="scope.row.convert_status !== 1"
                    @click="printPDF(scope.row)"
                    type="primary"
                    >{{ $t('Print') }}
                  </el-button>
                </template>
                <template v-if="scope.row.print_status == 1">
                  <el-button
                    size="small"
                    :disabled="scope.row.convert_status !== 1"
                    @click="printPDF(scope.row)"
                    type="info"
                    >{{ $t('Printed') }}
                  </el-button>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane :label="$t('History')" name="history">
          <el-table stripe :data="printedList.data" style="width: 100%">
            <el-table-column prop="id" :label="$t('ID')" width="120" />
            <el-table-column :label="$t('Created at')" width="180">
              <template #default="scope">
                {{ utcToLocalTime(scope.row.created_at).format('lll') }}
              </template>
            </el-table-column>
            <el-table-column :label="$t('Filter')">
              <template #default="scope">
                <el-tag class="mr-2 mb-1" size="small" type="success">
                  {{ scope.row.style?.name }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="employee_name" :label="$t('Staff')">
              <template #default="scope">
                {{ scope.row.employee_convert.name }}
              </template>
            </el-table-column>
            <el-table-column
              prop="quantity"
              :label="$t('Quantity')"
              width="100"
            />
            <el-table-column :label="$t('Status')" width="160">
              <template #default="scope">
                <el-tag v-if="+scope.row.convert_status == 1" type="success">{{
                  $t('Completed')
                }}</el-tag>
                <template v-else>
                  <template v-if="scope.row.convert_percent > 0">
                    <el-progress :percentage="scope.row.percentage" />
                  </template>
                  <template v-else>
                    <el-tag
                      v-if="+scope.row.convert_status == 2"
                      type="danger"
                      >{{ $t('Error') }}</el-tag
                    >
                    <el-tag v-else type="warning">{{ $t('Pending') }}</el-tag>
                  </template>
                </template>
              </template>
            </el-table-column>
            <el-table-column width="100" :label="$t('Action')">
              <template #default="scope">
                <template v-if="scope.row.print_status == 0">
                  <el-button
                    size="small"
                    :disabled="scope.row.convert_status !== 1"
                    @click="printPDF(scope.row)"
                    type="primary"
                    >{{ $t('Print') }}
                  </el-button>
                </template>
                <template v-if="scope.row.print_status == 1">
                  <el-button
                    size="small"
                    :disabled="scope.row.convert_status !== 1"
                    @click="rePrintedBtn(scope.row)"
                    type="info"
                    >{{ $t('Reprint') }}
                  </el-button>
                </template>
              </template>
            </el-table-column>
          </el-table>
          <div class="mt-3 flex items-center">
            <div class="flex">
              <el-input
                v-model="filterBarcodePrinted.keyword"
                :placeholder="$t('Search SKU')"
              >
                <template #append>
                  <el-button @click="searchBarcodePrinted">{{
                    $t('Search')
                  }}</el-button>
                </template>
              </el-input>
            </div>
            <div class="ml-5">
              <el-pagination
                background
                :page-size="filterBarcodePrinted.limit"
                :pager-count="10"
                layout="prev, pager, next"
                :total="printedList.total"
                @current-change="changePage"
              >
              </el-pagination>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <el-dialog
        v-model="dialogVisiblePrint"
        @close="closeModalPrint"
        :title="$t('Print WIP')"
        width="30%"
        top="5vh"
      >
        <iframe
          class="m-0 p-0"
          :src="pdf + '#toolbar=0'"
          style="height: 450px; width: 100%"
        ></iframe>
        <div class="mt-3">
          <el-button :disabled="isLoading" v-if="!(itemSelected.printed_at || itemSelected.print_status == 1)" type="primary" @click="printWIP()">{{ $t("Print") }}
          </el-button>
          <div v-if="(itemSelected.printed_at || itemSelected.print_status == 1)">
            <h2 class="mt-2">{{ $t("Enter the password to unlock reprint") }}</h2>
            <el-input :placeholder="$t('Password')" class="mt-2" v-model="password"></el-input>
            <el-button :disabled="password != passwordReprint" class="mt-2" type="primary" @click="reprintWIP()">{{ $t("Reprint") }}
            </el-button>
          </div>
        </div><h2 class="mt-2">{{ $t('Confirm Printed') }}</h2>
        <el-input
          :placeholder="$t('Scan first label / Quét mã đầu tiên')"
          class="mt-2"
          v-model="skuPrinted.start"
        ></el-input>
        <el-input
          :placeholder="$t('Scan end label / Quét mã sau cùng')"
          class="mt-2"
          v-model="skuPrinted.end"
        ></el-input>
        <div class="mt-3">
          <el-button :disabled="!itemSelected.printed_at" type="primary" @click="confirmPrintedBtn()"
            >{{ $t('Confirm') }}
          </el-button>
        </div>
      </el-dialog>
      <el-dialog
        v-model="dialogVisibleRePrint"
        :title="$t('RePrint WIP')"
        width="30%"
        top="5vh"
        :before-close="handleCloseReprint"
      >
        <div
          v-if="lockRePrintStatus"
          class="bg-slate-200"
          style="height: 350px"
        ></div>
        <iframe
          v-else
          class="m-0 p-0"
          :src="pdf"
          style="height: 450px; width: 100%"
        ></iframe>
        <h2 class="mt-2">{{ $t('Enter the password to unlock print') }}</h2>
        <el-input
          :placeholder="$t('Password')"
          class="mt-2"
          v-model="password"
        ></el-input>
        <div class="mt-3">
          <el-button type="primary" @click="unLockReprint()"
            >{{ $t('Unlock') }}
          </el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
