import {
    countByOrderType,
    printBarcode,
    confirmPrinted,
    fetchByOrderType,
    historyByOrderType,
    confirmPrintByOrderType,
    countPendingByOrderTypePriorityStore, printBarcodeWIP, reprintBarcodeWIP
} from "@/api/barcode.js";
import { getByCode } from "@/api/employee.js";
import { ElMessageBox, ElMessage } from 'element-plus'
import moment from "moment-timezone";
import {STORAGE_URL, S3_URL, NOT_APPLICABLE} from "@/utilities/constants";
import date from "@/mixins/date";

export default {
    name: "Barcode",
    mixins: [date],
    data() {
        return {
            priorityStores: [],
            storeSelected: null,
            storeNA: NOT_APPLICABLE,
            storeId: null,
            isSelectStore: false,
            styles: [],
            totalCount: 0,
            stores: [],
            accounts: [],
            employees: {},
            itemSelected: {},
            warehouse: {},
            barcode: {},
            maxAllowPrint: 0,
            filterPrint: {
                style_sku: null,
                all: 0,
                employee_id: null,
                limit: 0,
                employee_number: null,
            },

            filterBarcodePrinted: {
                limit: 11,
                page: 1,
                keyword: ''
            },
            printedList: {},
            pendingList: {},
            isLoading: false,
            dialogVisible: false,
            preview_id: 0,

            activeTab: 'printing',
            pdf: '',
            dialogVisiblePrint: false,
            dialogVisibleRePrint: false,
            password: null,

            skuPrinted: {
                id: null,
                start: null,
                end: null
            },
            refreshCount: null,
            refreshPrinting: null,
            refreshPrinted: null,
            lockRePrintStatus: true,
            passwordReprint: '',
            clickConfirm: false,
            onThisPage: true,
            maxItems: 3000,
            PRETREATED_TYPE: 2,
        };
    },
    mounted() {
        let today = new Date();
        let dd = String(today.getDate()).padStart(2, '0');
        this.passwordReprint = dd;
        this.countPendingByOrderTypePriorityStore();
        this.countBarcode();
        this.fetchPrintedList();
        this.fetchPendingList();
    },

    watch: {
        dialogVisiblePrint: {
            handler: function (value) {
                if (!value) {
                    const iframes = document.querySelectorAll('.pdf-iframe');
                    iframes.forEach((iframe) => {
                        URL.revokeObjectURL(iframe.src);
                        document.body.removeChild(iframe);
                    });
                }
            },
            deep: true,
        },
        activeTab: {
            async handler() {
                this.filterBarcodePrinted.keyword = '';
            }
        }
    },

    methods: {
        async printWIP() {
            try {
                if (!this.itemSelected?.id) {
                    return;
                }
                this.isLoading = true;
                await printBarcodeWIP(this.itemSelected.id);
                await this.printPdfFromBlob(this.pdf);
                this.itemSelected.printed_at = moment().format('YYYY-MM-DD hh:mm:ss');
            } catch (e) {
                this.notification(e?.response?.data?.message ?? 'This WIP has been printed.', "error");

            } finally {
                this.isLoading = false;
            }
        },
        async reprintWIP() {
            try {
                if (!this.itemSelected?.id) {
                    return;
                }
                this.isLoading = true;
                await reprintBarcodeWIP(this.itemSelected.id);
                await this.printPdfFromBlob(this.pdf);
                this.itemSelected.printed_at = moment().format('YYYY-MM-DD hh:mm:ss');
            } catch (e) {
                this.notification(e?.response?.data?.message ?? 'Error', "error");
            } finally {
                this.isLoading = false;
            }
        },
        async countPendingByOrderTypePriorityStore() {
            let param = {
                order_type: this.PRETREATED_TYPE
            }
            const res = await countPendingByOrderTypePriorityStore(param)
            this.priorityStores = res?.data?.data || [];
        },
        toggleSelectStore(store_id) {
            if (this.storeId == store_id) {
                this.storeId = null;
            } else {
                this.storeId = store_id;
            }
            this.countBarcode();

        },
        async triggerCount(store_id) {
            if (this.isSelectStore) {
                this.storeId = store_id;
            } else {
                this.storeId = null;
            }
            this.countBarcode();
        },
        async searchBarcodePrinted() {
            this.filterBarcodePrinted.page = 1;
            await this.fetchPrintedList();
        },
        changePage(page) {
            this.filterBarcodePrinted.page = page;
            this.$nextTick(() => {
                this.fetchPrintedList();
            });
        },
        printPDF(item) {

            this.dialogVisiblePrint = true;
            this.skuPrinted.id = item.id
            this.itemSelected = item;
            // this.pdf = `${S3_URL}/barcode/${item.id}.pdf?v=` + Math.floor(Math.random() * 1000);
            this.pdf = item.wip_url;
        },
        setStyle(item) {
            this.maxAllowPrint = item.count > this.maxItems ? this.maxItems : item.count;
            this.filterPrint.style_sku = item.sku;
            this.filterPrint.limit = item.count;
            this.filterPrint.all = 0;
        },
        resetFilterPrint() {
            this.filterPrint.style_sku = null;
            this.filterPrint.all = 0;
        },
        async countBarcode() {
            let params = {
                page: this.filterBarcodePrinted.page,
                order_type: this.PRETREATED_TYPE,
                store_id: this.storeId ?? null
            }
            try {
                const res = await countByOrderType(params);
                this.styles = res.data.data.data.filter(({count}) => count > 0);
                this.totalCount = res.data.data.total;
            } catch (e) {
                this.notification(e.response.data.message, 'error', false, { duration: 10000 });
            }
            clearTimeout(this.refreshCount);
            let x = setTimeout(() => {
                if (this.onThisPage) {
                    this.countBarcode()
                    this.countPendingByOrderTypePriorityStore();
                }
            }, 10 * 1000);
            this.refreshCount = x;
        },

        async fetchPrintedList() {
            let params = {
                order_type: this.PRETREATED_TYPE,
                page: this.filterBarcodePrinted.pageHistory,
                limit: this.filterBarcodePrinted.limit,
                label_id: this.filterBarcodePrinted.keyword
            };

            try {
                const res = await historyByOrderType(params);
                this.printedList = res.data.data;
            } catch (e) {
                this.notification(e.response.data.message, 'error', false, { duration: 10000 });
            }
        },

        async fetchPendingList() {
            let params = {
                order_type: this.PRETREATED_TYPE,
                page: this.filterBarcodePrinted.page,
                limit: this.filterBarcodePrinted.limit
            };

            try {
                const res = await fetchByOrderType(params);
                this.pendingList = res.data.data;
            } catch (e) {
                this.notification(e.response.data.message, 'error', false, { duration: 10000 });
            }

            clearTimeout(this.refreshPrinting);
            let x = setTimeout(() => {
                if (this.onThisPage) {
                    this.fetchPendingList()
                }
            }, 10 * 1000);
            this.refreshPrinting = x;
        },

        async confirmStaff() {
            this.clickConfirm = true;
            if (!this.filterPrint.employee_number) {
                ElMessage({
                    type: 'warning',
                    message: this.$t('Please enter the staff number to confirm the printed label'),
                })
                this.clickConfirm = false;
                return false
            }

            const res = await getByCode(this.filterPrint.employee_number)

            if (!res.data.data) {
                ElMessage({
                    type: 'error',
                    message: this.$t('The staff number does not exist'),
                })
                this.clickConfirm = false;
                return false
            }

            try {
                let params = {
                    order_type: this.PRETREATED_TYPE,
                    employee_id: res.data.data.id,
                    style_sku: this.filterPrint.style_sku,
                    quantity: this.filterPrint.limit,
                    store_id: this.storeId ?? null,
                }
                await confirmPrintByOrderType(params);
                this.dialogVisible = false;
                this.filterPrint.employee_number = null;
                this.fetchPendingList();
                this.countBarcode();
            } catch (err) {
                this.dialogVisible = false;
                let data = err.response.data;
                if (err.response.status == 422) {
                    const keyFirstData = Object.keys(data)[0];
                    const firstData = data[keyFirstData];
                    this.notification(firstData[0], 'error', false, { duration: 10000 });
                } else {
                    this.notification(data.message, 'error', false, { duration: 10000 });
                }
            }
            this.clickConfirm = false;
        },

        confirmPrintBtn() {
            if (this.filterPrint.style_sku == null && this.filterPrint.all == 0) {
                ElMessage({
                    type: 'warning',
                    message: this.$t('Please select the option to print'),
                })
                return false
            }
            if (this.filterPrint.limit == 0) {
                ElMessage({
                    type: 'warning',
                    message: this.$t('Limit must be greater than zero'),
                })
                return false
            }

            this.dialogVisible = true;
        },

        confirmPrintedBtn() {
            if (this.skuPrinted.start == '' || this.skuPrinted.end == '') {
                ElMessage({
                    type: 'warning',
                    message: this.$t('Please scan the first SKU and the last SKU'),
                })
                return false
            }

            confirmPrinted(this.skuPrinted.id, this.skuPrinted).then((res) => {
                if (res.data.status == true) {
                    ElMessage({
                        type: 'success',
                        message: this.$t('Successful print confirmation'),
                    })
                    this.fetchPendingList()
                    this.fetchPrintedList()
                    this.dialogVisiblePrint = false
                    this.skuPrinted = {
                        id: null,
                        start: null,
                        end: null
                    }
                } else {
                    ElMessage({
                        type: 'error ',
                        message: this.$t('The first or the last SKU is incorrect'),
                    })
                }
            })
        },

        rePrintedBtn(item) {
            let today = new Date();
            let dd = String(today.getDate()).padStart(2, '0');
            this.dialogVisibleRePrint = true;
            this.passwordReprint = dd;
            // this.pdf = `${S3_URL}/barcode/${item.id}.pdf?v=` + Math.floor(Math.random() * 1000);
            this.pdf = item.wip_url;
        },
        unLockReprint() {
            if (this.passwordReprint != this.password) {
                ElMessage({
                    type: 'error',
                    message: this.$t('The password is incorrect'),
                })
            } else {
                this.lockRePrintStatus = false;
            }
        },
        handleCloseReprint() {
            this.lockRePrintStatus = true
            this.dialogVisibleRePrint = false
            this.password = null;
        },
        closeModalPrint() {
            this.password = null;
            this.dialogVisiblePrint = false;
            this.skuPrinted = {
                id: null,
                start: null,
                end: null
            }
        },

        moment(date) {
            return moment(date)
        },


    },

    beforeUnmount() {
        console.log('clear all refresh')
        clearTimeout(this.refreshCount);
        clearTimeout(this.refreshPrinting);
        //  clearTimeout(this.refreshPrinted);
    },
    unmounted() {
        console.log('unmounted')
        clearTimeout(this.refreshCount);
        clearTimeout(this.refreshPrinting);
    },
    beforeRouteLeave(to, from, next) {
        this.onThisPage = false;
        next();
    },
};
