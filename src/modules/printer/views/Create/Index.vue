<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :title="$t('Create Printer')"
      custom-class="el-dialog-custom-printer"
      width="60%"
      :destroy-on-close="true"
    >
      <el-form
        ref="createPrinter"
        :model="data"
        :rules="dataRules"
        label-width="120px"
        class="form-create"
        :label-position="'top'"
        >
        <div class="el-item-group">
          <el-form-item
              label="Printer Name"
              prop="name"
              class="w-1/2 pr-3"
          >
            <el-input
                :placeholder="$t('Enter printer name')"
                ref="name"
                v-model="data.name"
            ></el-input>
          </el-form-item>
          <el-form-item
              label="Device Name"
              prop="device_id"
              class="w-1/2"
          >
            <el-input
                :placeholder="$t('Enter device name')"
                ref="namePrinter"
                v-model="data.device_id"
            ></el-input>
          </el-form-item>
        </div>
        <el-form-item
            label="Style"
            prop="style"
            class="el-form-item-tracking-number w-full"
        >
          <el-select
              v-model="selectStyle"
              placeholder="Select"
              class="w-full"
              filterable
              @change="fetchColorByStyle"
          >
            <el-option
                v-for="item in styleOptions"
                :key="item.label"
                :label="item.label"
                :value="item.label">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <div class="flex justify-between w-full">
            <div class="box-item pr-5 w-1/2">
              <div class="box-item-info">
                <el-table
                    border
                    ref="multipleTableLeft"
                    :data="leftData"
                    style="width: 100%"
                    size="small"
                    :height="maxHeight"
                    @selection-change="handleSelectionChangeLeft"
                    @row-click="toggleSelectionTableLeft"
                    :header-cell-class-name="'text-center'"
                >
                  <template #empty >
                    <div class="custom-empty-text">
                      No data available.
                    </div>
                  </template>
                  <el-table-column
                      type="selection"
                      width="55"></el-table-column>
                  <el-table-column :label="$t('Style / Color')" min-width="150" >
                    <template #default="scope" >
                      <span class="cursor-pointer">
                        {{ scope.row.label }}
                      </span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
            <div class="box-item mt-auto mb-auto">
              <el-button size="small"
                         @click="changeToRight"
                         :disabled="multipleSelectionLeft.length === 0"
                         type="primary">
                {{'>>'}}
              </el-button>
              <br>
              <el-button size="small"
                         @click="changeToLeft"
                         :disabled="multipleSelectionRight.length === 0"
                         type="primary">
                {{'<<'}}
              </el-button>
            </div>
            <div class="box-item pl-5 w-1/2">
              <div class="flex flex-row">
              </div>
              <div class="box-item-info">
                <el-table
                    border
                    ref="multipleTableRight"
                    :data="rightData"
                    style="width: 100%"
                    size="small"
                    :height="maxHeight"
                    @selection-change="handleSelectionChangeRight"
                    @row-click="toggleSelectionTableRight"
                    :header-cell-class-name="'text-center'"
                >
                  <template #empty class="content-center">
                    <div class="mt-auto mb-auto">
                      No data available.
                    </div>
                  </template>
                  <el-table-column
                      type="selection"
                      width="55"></el-table-column>
                  <el-table-column class="text-center" :label="$t('Style / Color')" min-width="150" prop="scanGtin">
                    <template #default="scope" class="cursor-pointer">
                      {{ scope.row.label }}
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button
            v-if="isEdit"
            type="primary"
            @click="onEdit"
            :disabled="isLoading"
            :loading="isLoading"
        >{{ $t('Update') }}</el-button>
        <el-button
            v-else
            type="primary"
            @click="onSubmit"
            :disabled="isLoading"
            :loading="isLoading"
        >{{ $t('Save') }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>
