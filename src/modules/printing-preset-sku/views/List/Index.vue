<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head mb-4">
      <div class="top-head-left">
        <h1>{{ $t('Printing Preset Sku') }}</h1>
      </div>
      <div class="top-head-right">
        <el-button type="primary" @click="createPrintingPresetSku(null)">
          <span class="icon-margin-right"><icon :data="iconAdd" /></span
          >{{ $t('Create') }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <div class="filter">
        <div class="flex items-center mr-3">
          <label class="mr-1">{{ $t('Missing presets') }}</label>
          <el-switch
            active-value="1"
            inactive-value="0"
            v-model="filter.no_presets"
            @change="onFilter"
          />
        </div>
        <el-input
          :placeholder="$t('Search sku')"
          class="search mr-3"
          v-model="filter.sku"
          @keyup.enter="onFilter"
        />
        <div class="btn-filter">
          <template v-if="hasFilter">
            <el-link type="danger" @click="onClearFilter" :underline="false">{{
              $t('Clear')
            }}</el-link>
          </template>
          <el-button type="primary" @click="onFilter">
            <span class="icon-margin-right"><icon :data="iconFilter" /></span
            >{{ $t('Filter') }}
          </el-button>
        </div>
      </div>
      <el-table
        border
        stripe
        size="small"
        :data="items"
        :max-height="maxHeight"
        v-loading="isLoading"
        element-loading-text="Loading..."
        :cell-style="setColor"
        header-row-class-name="break-words"
      >
        <el-table-column
          prop="id"
          :label="$t('ID')"
          min-width="100"
        ></el-table-column>
        <el-table-column :label="$t('Date')" min-width="160">
          <template #default="scope">
            {{ listViewDateFormat(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="sku"
          :label="$t('Sku')"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="black_ink"
          :label="$t('Black Ink')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="white_ink"
          :label="$t('White Ink')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="purple_ink"
          :label="$t('Purple Ink')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="mix_ink"
          :label="$t('Mix Ink')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="black_ink_xqc"
          :label="$t('Black Ink Xqc')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="white_ink_xqc"
          :label="$t('White Ink Xqc')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="purple_ink_xqc"
          :label="$t('Purple Ink Xqc')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="mix_ink_xqc"
          :label="$t('Mix Ink Xqc')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="front_size"
          :label="$t('Front Size')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="back_size"
          :label="$t('Back Size')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="inner_neck_label_size"
          :label="$t('Neck Inner Size')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="outer_neck_label_size"
          :label="$t('Neck Outer Size')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="left_sleeve_size"
          :label="$t('Left Sleeve Size')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="right_sleeve_size"
          :label="$t('Right Sleeve Size')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="pocket_size"
          :label="$t('Pocket Size')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="platen_front_size"
          :label="$t('Platen Front Size')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="platen_back_size"
          :label="$t('Platen Back Size')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="platen_left_sleeve_size"
          :label="$t('Platen Left Sleeve Size')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="platen_right_sleeve_size"
          :label="$t('Platen Right Sleeve Size')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="platen_inner_neck_label_size"
          :label="$t('Platen Neck Inner Size')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="platen_outer_neck_label_size"
          :label="$t('Platen Neck Outer Size')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="platen_pocket_size"
          :label="$t('Platen Pocket Size')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="front_position"
          :label="$t('Front Position')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="left_sleeve_position"
          :label="$t('Left Sleeve Position')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="right_sleeve_position"
          :label="$t('Right Sleeve Position')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="inner_neck_label_position"
          :label="$t('Neck Inner Position')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="outer_neck_label_position"
          :label="$t('Neck Outer Position')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="pocket_position"
          :label="$t('Pocket Position')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="background_color"
          :label="$t('Background')"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="user.username"
          :label="$t('User')"
          min-width="150"
        ></el-table-column>
        <el-table-column
          prop="action"
          :label="$t('Action')"
          fixed="right"
          width="100"
        >
          <template #default="scope">
            <el-link
              class="el-link-edit"
              :underline="false"
              type="primary"
              @click="updatePrintingPresetSku(scope.row)"
              ><icon :data="iconEdit"
            /></el-link>
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom">
        <div class="total">
          {{ $t('Total:') }} {{ items.length ? formatNumber(total) : 0 }}
        </div>
        <el-pagination
          :disabled="isLoading"
          background
          layout="prev, pager, next"
          :page-size="filter.limit"
          :total="total"
          @current-change="changePage"
          v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
            v-model="filter.limit"
            :placeholder="$t('Select')"
            size="mini"
            @change="onFilter"
          >
            <el-option
              v-for="item in limits"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
  </div>
  <create-printing-preset-sku
    :defaultFormData="defaultFormData"
    :dynamicAreaList="dynamicAreaList"
    @refresh="getPrintingPresetSkuList"
  />
  <update-printing-preset-sku
    :defaultFormData="defaultFormData"
    :dynamicAreaList="dynamicAreaList"
    @refresh="getPrintingPresetSkuList"
  />
</template>
