import { getList } from '@/api/printingPresetSku';
import EventBus from '@/utilities/eventBus';
import CreatePrintingPresetSku from '@/modules/printing-preset-sku/views/Create/Index.vue';
import UpdatePrintingPresetSku from '@/modules/printing-preset-sku/views/Update/Index.vue';
import { getList as getPrintArea } from '@/api/productPrintSide';

export default {
  name: 'PrintingPresetSkuList',
  components: {
    CreatePrintingPresetSku,
    UpdatePrintingPresetSku,
  },
  data() {
    return {
      items: [],
      isLoading: false,
      filter: this.setDefaultFilter(),
      defaultFormData: {
        sku: '',
        black_ink: '',
        white_ink: '',
        purple_ink: '',
        mix_ink: '',
        black_ink_xqc: '',
        white_ink_xqc: '',
        purple_ink_xqc: '',
        mix_ink_xqc: '',
        background_color: '',
      },
      dynamicAreaList: [],
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 230);
    },
  },
  mounted() {
    this.filter = this.getRouteParam();
    this.getPrintingPresetSkuList();
    this.fetchProductPrintSide();
  },
  methods: {
    async fetchProductPrintSide() {
      try {
        this.isLoading = true;
        this.dynamicAreaList = [];
        const { data } = await getPrintArea();
        if (data.length > 0) {
          data.forEach((item) => {
            Object.assign(this.defaultFormData, {
              [item.code_name + '_size']: '',
              ['platen_' + item.code_name + '_size']: '',
              [item.code_name + '_position']: '',
            });
            this.dynamicAreaList = data;
          });
        }
        this.isLoading = false;
      } catch (error) {}
    },
    setDefaultFilter() {
      return {
        limit: 25,
        page: 1,
        no_presets: '0',
        sku: '',
      };
    },
    async getPrintingPresetSkuList() {
      this.isLoading = true;
      this.setRouteParam();
      const { data } = await getList(this.filter);
      this.items = data.data;
      this.total = data.total;
      this.isLoading = false;
    },
    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.getPrintingPresetSkuList();
    },
    changePage(page) {
      this.filter.page = page;
      this.getPrintingPresetSkuList();
    },
    onFilter() {
      this.filter.page = 1;
      this.getPrintingPresetSkuList();
    },
    createPrintingPresetSku(presetSku = null) {
      EventBus.$emit('showCreatePrintingPresetSku', presetSku);
    },
    updatePrintingPresetSku(item) {
      if (!item.id) {
        return this.createPrintingPresetSku(item.sku);
      }
      EventBus.$emit('showUpdatePrintingPresetSku', item);
    },
    setColor({ row, column, rowIndex, columnIndex }) {
      if (row.background_color !== null && columnIndex === 19) {
        if (!this.checkIsLightColor(row.background_color)) {
          return { backgroundColor: row.background_color, color: '#ffffff' };
        }
        return { backgroundColor: row.background_color };
      }
    },
    checkIsLightColor(color) {
      const hex = color.replace('#', '');
      const c_r = parseInt(hex.substr(0, 2), 16);
      const c_g = parseInt(hex.substr(2, 2), 16);
      const c_b = parseInt(hex.substr(4, 2), 16);
      const brightness = (c_r * 299 + c_g * 587 + c_b * 114) / 1000;
      return brightness > 155;
    },
  },
};
