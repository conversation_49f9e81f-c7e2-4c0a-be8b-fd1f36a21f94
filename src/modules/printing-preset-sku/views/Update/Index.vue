<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      destroy-on-close
      :title="$t('Update Printing Preset Sku')"
      custom-class="el-dialog-custom"
      width="50%"
      :destroy-on-close="true"
    >
      <el-form
        status-icon
        ref="updatePrintingPresetSku"
        :model="data"
        @submit.prevent="onSubmit"
        :label-position="'top'"
        :rules="initRules"
        class="w-full"
      >
        <el-form-item
          :label="$t('Sku')"
          prop="sku"
          class="font-bold"
          :class="{ 'is-error': isError('sku') }"
        >
          <el-input
            v-model="data.sku"
            :disabled="userCanNotEdit"
            @keyup.enter="onSubmit"
            autocomplete="off"
          ></el-input>
          <div v-if="isError('sku')" class="el-form-item__error">
            {{ getErrorMessage('sku') }}
          </div>
        </el-form-item>
        <div class="flex">
          <el-form-item
            :label="$t('Background Color')"
            prop="background_color"
            class="w-full"
            :class="{ 'is-error': isError('background_color') }"
          >
            <el-input
              v-model="data.background_color"
              :disabled="userCanNotEdit"
              @keyup.enter="onSubmit"
              autocomplete="off"
              class="w-full"
              type="color"
            ></el-input>
            <div v-if="isError('background_color')" class="el-form-item__error">
              {{ getErrorMessage('background_color') }}
            </div>
          </el-form-item>
        </div>
        <div>
          <h3>Ink</h3>
          <div class="border rounded-sm py-2 px-4 my-2">
            <div class="flex">
              <el-form-item
                :label="$t('Black Ink')"
                prop="black_ink"
                class="w-full mr-2"
                :class="{ 'is-error': isError('black_ink') }"
              >
                <el-input
                  v-model="data['black_ink']"
                  :disabled="userCanNotEdit"
                  @keyup.enter="onSubmit"
                  autocomplete="off"
                  class="w-full"
                ></el-input>
                <div v-if="isError('black_ink')" class="el-form-item__error">
                  {{ getErrorMessage('black_ink') }}
                </div>
              </el-form-item>
              <el-form-item
                :label="$t('White Ink')"
                prop="white_ink"
                class="w-full mr-2"
                :class="{ 'is-error': isError('white_ink') }"
              >
                <el-input
                  v-model="data['white_ink']"
                  :disabled="userCanNotEdit"
                  @keyup.enter="onSubmit"
                  autocomplete="off"
                  class="w-full"
                ></el-input>
                <div v-if="isError('white_ink')" class="el-form-item__error">
                  {{ getErrorMessage('white_ink') }}
                </div>
              </el-form-item>
              <el-form-item
                :label="$t('Purple Ink')"
                prop="purple_ink"
                class="w-full mr-2"
                :class="{ 'is-error': isError('purple_ink') }"
              >
                <el-input
                  v-model="data['purple_ink']"
                  :disabled="userCanNotEdit"
                  @keyup.enter="onSubmit"
                  autocomplete="off"
                  class="w-full"
                ></el-input>
                <div v-if="isError('purple_ink')" class="el-form-item__error">
                  {{ getErrorMessage('purple_ink') }}
                </div>
              </el-form-item>
              <el-form-item
                :label="$t('Mix Ink')"
                prop="mix_ink"
                class="w-full"
                :class="{ 'is-error': isError('mix_ink') }"
              >
                <el-input
                  v-model="data['mix_ink']"
                  :disabled="userCanNotEdit"
                  @keyup.enter="onSubmit"
                  autocomplete="off"
                  class="w-full"
                ></el-input>
                <div v-if="isError('mix_ink')" class="el-form-item__error">
                  {{ getErrorMessage('mix_ink') }}
                </div>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item
                :label="$t('Black Ink Xqc')"
                prop="black_ink_xqc"
                class="w-full mr-2"
                :class="{ 'is-error': isError('black_ink_xqc') }"
              >
                <el-input
                  v-model="data['black_ink_xqc']"
                  :disabled="userCanNotEdit"
                  @keyup.enter="onSubmit"
                  autocomplete="off"
                  class="w-full"
                ></el-input>
                <div
                  v-if="isError('black_ink_xqc')"
                  class="el-form-item__error"
                >
                  {{ getErrorMessage('black_ink_xqc') }}
                </div>
              </el-form-item>
              <el-form-item
                :label="$t('White Ink Xqc')"
                prop="white_ink_xqc"
                class="w-full mr-2"
                :class="{ 'is-error': isError('white_ink_xqc') }"
              >
                <el-input
                  v-model="data['white_ink_xqc']"
                  :disabled="userCanNotEdit"
                  @keyup.enter="onSubmit"
                  autocomplete="off"
                  class="w-full"
                ></el-input>
                <div
                  v-if="isError('white_ink_xqc')"
                  class="el-form-item__error"
                >
                  {{ getErrorMessage('white_ink_xqc') }}
                </div>
              </el-form-item>
              <el-form-item
                :label="$t('Purple Ink Xqc')"
                prop="purple_ink_xqc"
                class="w-full mr-2"
                :class="{ 'is-error': isError('purple_ink_xqc') }"
              >
                <el-input
                  v-model="data['purple_ink_xqc']"
                  :disabled="userCanNotEdit"
                  @keyup.enter="onSubmit"
                  autocomplete="off"
                  class="w-full"
                ></el-input>
                <div
                  v-if="isError('purple_ink_xqc')"
                  class="el-form-item__error"
                >
                  {{ getErrorMessage('purple_ink_xqc') }}
                </div>
              </el-form-item>
              <el-form-item
                :label="$t('Mix Ink Xqc')"
                prop="mix_ink_xqc"
                class="w-full"
                :class="{ 'is-error': isError('mix_ink_xqc') }"
              >
                <el-input
                  v-model="data['mix_ink_xqc']"
                  :disabled="userCanNotEdit"
                  @keyup.enter="onSubmit"
                  autocomplete="off"
                  class="w-full"
                ></el-input>
                <div v-if="isError('mix_ink_xqc')" class="el-form-item__error">
                  {{ getErrorMessage('mix_ink_xqc') }}
                </div>
              </el-form-item>
            </div>
          </div>
        </div>
        <div>
          <h3>{{ $t('Print Area') }}</h3>
          <div class="border rounded-sm py-2 px-4 my-2">
            <div
              class="flex items-end"
              v-for="(area, index) in dynamicAreaList"
              :key="area.id"
            >
              <div class="w-44 mb-4" style="">
                {{ $t(`${area.name}`) }}
              </div>
              <div class="flex w-full">
                <el-form-item
                  :prop="`platen_${area.code_name}_size`"
                  class="w-full mr-2"
                  :class="{
                    'is-error': isError(`platen_${area.code_name}_size`),
                  }"
                >
                  <template v-if="!index" #label>
                    <span>{{ $t('Platen Size(inch)') }}</span>
                    <el-tooltip
                      effect="light"
                      raw-content="true"
                      :content="sizeTooltip"
                      placement="top"
                    >
                      <span class="icon pl-2" :style="{ color: '#306CFE' }">
                        <icon :data="iconInformation3" />
                      </span>
                    </el-tooltip>
                  </template>

                  <el-select
                    filterable
                    clearable
                    allow-create
                    :disabled="userCanNotEdit"
                    @change="validatePrintArea"
                    v-model="data[`platen_${area.code_name}_size`]"
                    :placeholder="$t('')"
                  >
                    <el-option
                      v-for="item in platenSizeList"
                      :key="item"
                      :label="item"
                      :value="String(item)"
                    />
                  </el-select>
                  <div
                    v-if="isError(`platen_${area.code_name}_size`)"
                    class="el-form-item__error"
                  >
                    {{ getErrorMessage(`platen_${area.code_name}_size`) }}
                  </div>
                </el-form-item>

                <el-form-item
                  :label="!index ? $t('Print Size(inch)') : null"
                  :prop="`${area.code_name}_size`"
                  class="w-full mr-2"
                  :class="{ 'is-error': isError(`${area.code_name}_size`) }"
                >
                  <template v-if="!index" #label>
                    <span>{{ $t('Print Size(inch)') }}</span>
                    <el-tooltip
                      effect="light"
                      raw-content="true"
                      :content="sizeTooltip"
                      placement="top"
                    >
                      <span class="icon pl-2" :style="{ color: '#306CFE' }">
                        <icon :data="iconInformation3" />
                      </span>
                    </el-tooltip>
                  </template>
                  <el-input
                    v-model="data[`${area.code_name}_size`]"
                    :disabled="userCanNotEdit"
                    @keyup.enter="onSubmit"
                    @blur="validatePrintArea"
                    autocomplete="off"
                    class="w-full"
                  ></el-input>
                  <div
                    v-if="isError(`${area.code_name}_size`)"
                    class="el-form-item__error"
                  >
                    {{ getErrorMessage(`${area.code_name}_size`) }}
                  </div>
                </el-form-item>

                <el-form-item
                  :label="!index ? $t('Position(Top x Left  inch)') : null"
                  :prop="`${area.code_name}_position`"
                  class="w-full"
                  :class="{ 'is-error': isError(`${area.code_name}_position`) }"
                >
                  <el-input
                    v-model="data[`${area.code_name}_position`]"
                    :disabled="userCanNotEdit"
                    autocomplete="off"
                    class="w-full"
                  ></el-input>
                  <div
                    v-if="isError(`${area.code_name}_position`)"
                    class="el-form-item__error"
                  >
                    {{ getErrorMessage(`${area.code_name}_position`) }}
                  </div>
                </el-form-item>
              </div>
            </div>
          </div>
        </div>
        <el-button
          v-if="!userCanNotEdit"
          type="primary"
          @click="onSubmit"
          :disabled="isLoading"
          :loading="isLoading"
          >{{ $t('Update') }}</el-button
        >
      </el-form>
    </el-dialog>
  </div>
</template>
