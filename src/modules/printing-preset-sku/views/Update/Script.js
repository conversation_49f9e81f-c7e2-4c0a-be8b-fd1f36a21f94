import EventBus from '@/utilities/eventBus';
import { update } from '@/api/printingPresetSku';
import { clone, isEmpty, isNil } from 'ramda';
import { mapGetters } from 'vuex';

export default {
  name: 'UpdatePrintingPresetSku',

  props: ['defaultFormData', 'dynamicAreaList'],

  data() {
    return {
      printingPresetSkuId: 0,
      dialogVisible: false,
      data: this.setDefaultData(),
      isLoading: false,
      serverErrors: [],
      platenSizeList: [
        "16x21",
        "16x18",
        "14x16",
        "10x12",
        "7x8",
        "40x60",
        "36x48",
        "36x36",
        "32x48",
        "32x32",
        "30x60",
        "30x40",
        "30x36",
        "30x30",
        "24x48",
        "24x36",
        "24x32",
        "24x30",
        "24x24",
        "20x60",
        "20x40",
        "20x36",
        "20x30",
        "20x24",
        "20x20",
        "18x24",
        "16x48",
        "16x24",
        "16x20",
        "16x16",
        "15x30",
        "14x14",
        "14x11",
        "12x36",
        "12x20",
        "12x18",
        "12x16",
        "12x12",
        "11x14",
        "10x20",
        "10x10",
        "9x12",
        "8x12",
        "8x10",
        "8x8",
        "6x6",
      ],
      initRules: {},
      sizeTooltip:
        'Please input the format: AxB (A & B can be decimal number).<br />The platen size must be larger or equal to the print size <br />for all printing methods, except for Latex printing.<br />Example: 2.22x2.22',
    };
  },

  watch: {
    dynamicAreaList() {
      this.setDynamicValidator();
    },
  },

  created() {
    EventBus.$on('showUpdatePrintingPresetSku', (item) => {
      this.serverErrors = [];
      this.printingPresetSkuId = item.id;
      this.data = Object.assign(this.data, item);
      this.dialogVisible = true;
    });
  },

  mounted() {
    console.log('----------getUserProfile----------');
    console.log(this.getUserProfile);
    console.log(this.isAdmin);
  },

  computed: {
    isAdmin() {
      return this.getUserProfile?.is_admin == 1 ?? false;
    },

    isSpecialTypeProduct() {
      return ['Mugs', 'Ornament', 'Tumbler', 'Sticker', 'Poster'].includes(this.data?.product_type);
    },

    userCanNotEdit() {
      return (
        this.data.admin_edit_only == 1 &&
        !this.isAdmin &&
        this.isSpecialTypeProduct
      );
    },

    isAtLeastOnePrintAreaCreated() {
      let flag = false;
      console.log(this.dynamicAreaList.length);

      if (!this.dynamicAreaList.length) {
        return true;
      }

      this.dynamicAreaList.forEach((area) => {
        const printSizeName = `${area['code_name']}_size`;
        const platenSizeName = `platen_${area['code_name']}_size`;

        if (
          !isNil(this.data[printSizeName]) &&
          !isEmpty(this.data[printSizeName]) &&
          !isNil(this.data[platenSizeName]) &&
          !isEmpty(this.data[platenSizeName])
        )
          flag = true;
      });

      return flag;
    },

    ...mapGetters(['getUserProfile']),
  },
  methods: {
    validatePrintArea() {
      this.$refs.updatePrintingPresetSku.validate();
    },

    genValidator(platenSizeName = '') {
      return (rule, value, callback) => {
        const pattern = /^(\d+(\.\d{1,2})?)x(\d+(\.\d{1,2})?)$/;
        const isPrintSizeEmpty = value === null || value == '';
        const isPlatenSizeEmpty = this.data[platenSizeName] === null || this.data[platenSizeName] == '';

        if (platenSizeName.length && isPrintSizeEmpty && !isPlatenSizeEmpty) {
          callback(new Error(`Please enter the print size.`));
        } else if (value && !pattern.test(value.toLowerCase())) {
          callback(new Error(`Please enter the correct size format.`));
        }

        callback();
      };
    },

    genValidatorPlaten(printSizeName = '') {
      return (rule, value, callback) => {
        const pattern = /^(\d+(\.\d{1,2})?)x(\d+(\.\d{1,2})?)$/;
        const isPlatenSizeEmpty = value === null || value == '';
        const isPrintSizeEmpty =
          this.data[printSizeName] === null || this.data[printSizeName] == '';

        if (printSizeName.length && isPlatenSizeEmpty && !isPrintSizeEmpty) {
          callback(new Error(`Please enter the platen size.`));
        } else if (value && !pattern.test(value.toLowerCase())) {
          callback(new Error(`Please enter the correct size format.`));
        }

        callback();
      };
    },

    genValidatorPosition() {
      return (rule, value, callback) => {
        const pattern = /^(\d+(\.\d{1,2})?)x(\d+(\.\d{1,2})?)$/;

        if (value && !pattern.test(value.toLowerCase())) {
          callback(new Error(`Position wrong format`));
        }

        callback();
      };
    },

    isError(field) {
      return !!this.serverErrors[field];
    },

    getErrorMessage(field) {
      return this.serverErrors[field][0];
    },

    setDefaultData() {
      return clone(this.defaultFormData);
    },

    setDynamicValidator() {
      this.initRules = {};
      this.dynamicAreaList.map((area) => {
        Object.assign(this.initRules, {
          [`${area.code_name}_size`]: [
            {
              validator: this.genValidator(`platen_${area.code_name}_size`),
              trigger: 'blur',
            },
          ],
          [`${area.code_name}_position`]: [
            {
              validator: this.genValidatorPosition(),
              trigger: 'blur',
            },
          ],
          [`platen_${area.code_name}_size`]: [
            {
              validator: this.genValidatorPlaten(`${area.code_name}_size`),
              trigger: 'blur',
            },
          ],
        });
      });
    },

    async onSubmit() {
      if (this.isLoading) {
        return;
      }

      const isValid = await this.$refs.updatePrintingPresetSku.validate();

      if (!isValid) {
        return;
      }

      if (!this.isAtLeastOnePrintAreaCreated) {
        this.notification('At least one print area is required.', 'error');

        return;
      }

      this.isLoading = true;

      try {
        if (this.data.user) {
          delete this.data.user;
        }

        const res = await update(this.printingPresetSkuId, this.data);
        this.dialogVisible = false;
        this.notification(res.data.message);
        this.$emit('refresh');
      } catch (e) {
        this.serverErrors = e.response.data.errors;
        let message = e.response.data.message;
        this.notification(message, 'error');
      } finally {
        this.isLoading = false;
      }
    },
  },
};
