import { getList } from "@/api/printingPreset";
import EventBus from "@/utilities/eventBus";
import CreatePrintingPreset from "@/modules/printing-preset/views/Create/Index.vue";
import UpdatePrintingPreset from "@/modules/printing-preset/views/Update/Index.vue";

export default {
    name: "PrintingPresetList",
    components: {
        CreatePrintingPreset,
        UpdatePrintingPreset
    },
    data() {
        return {
            items: [],
            isLoading: false,
            filter: this.setDefaultFilter(),
        }
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 189);
        }
    },
    mounted() {
        this.filter = this.getRouteParam();
        this.getPrintingPresetList();
    },
    methods: {
        setDefaultFilter() {
            return {
                limit: 25,
                page: 1
            };
        },
        async getPrintingPresetList() {
            this.isLoading = true;
            this.setRouteParam();
            const { data } = await getList(this.filter);
            this.items = data.data;
            this.total = data.total;
            this.isLoading = false;
        },
        changePage(page) {
            this.filter.page = page;
            this.getPrintingPresetList();
        },
        onFilter() {
            this.filter.page = 1;
            this.getPrintingPresetList();
        },
        createPrintingPreset() {
            EventBus.$emit("showCreatePrintingPreset");
        },
        updatePrintingPreset(item) {
            EventBus.$emit("showUpdatePrintingPreset", item);
        }
    }
}