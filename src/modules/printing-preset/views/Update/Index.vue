<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
        v-model="dialogVisible"
        :title="$t('Update Printing Preset')"
        custom-class="el-dialog-custom"
        width="50%"
        :destroy-on-close="true"
    >
      <el-form
          status-icon
          ref="updatePrintingPreset"
          :model="data"
          :rules="dataRules"
          @submit.prevent="onSubmit"
          :label-position="'top'"
          class="w-full"
      >
        <el-form-item
            :label="$t('Name')"
            prop="name"
        >
          <el-input
              v-model="data.name"
              @keyup.enter="onSubmit"
              autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item
            :label="$t('Active time')"
            prop="is_active_time"
        >
          <el-switch
              @change="changeActive"
              v-model="data.is_active_time"
          />
        </el-form-item>
        <el-form-item
            :label="$t('Time')"
            prop="time"
        >
          <el-input-number
              @change="changeTime"
              :disabled="!data.is_active_time" v-model="data.time" :min="0" :max="60" type="number" class="text-center w-100"/>
        </el-form-item>
        <el-form-item
            :label="$t('Data')"
            prop="data"
        >
          <el-input
              v-model="data.data"
              @change="changeData"
              type="textarea"
              autocomplete="off"
              rows="15"
          ></el-input>
        </el-form-item>

        <el-button
            type="primary"
            @click="onSubmit"
            :disabled="isLoading || errorSubmit"
            :loading="isLoading"
        >{{ $t('Update') }}</el-button>
      </el-form>
    </el-dialog>
  </div>
</template>
