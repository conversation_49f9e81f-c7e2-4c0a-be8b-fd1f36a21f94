import EventBus from "@/utilities/eventBus";
import { update } from "@/api/printingPreset";
import {clone} from "ramda";

export default {
    name: "UpdatePrintingPreset",
    data() {
        return {
            printingPresetId: 0,
            dialogVisible: false,
            data: this.setDefaultData(),
            isLoading: false,
            dataRules: {
                name: [this.commonRule()],
                data:[this.commonRule()]
            },
            oldTime: null,
            oldActive: null,
            errorSubmit: false,
        }
    },
    created() {
        EventBus.$on("showUpdatePrintingPreset", (item) => {
            this.printingPresetId = item.id;
            this.data = Object.assign(this.data, item);
            this.dialogVisible = true;
            this.oldTime = clone(item.time);
            this.oldActive = clone(item.is_active_time);
        });
    },
    watch: {
        'data.time': {
            handler(newValue) {
                this.oldTime = newValue;
            },
            deep: true,
        },
        'data.is_active_time': {
            handler(newValue, oldValue) {
                this.oldActive = oldValue;
                if (newValue) {
                    this.dataRules = {
                        name: [this.commonRule()],
                        data:[this.commonRule()],
                        time:[this.commonRule()]
                    }
                } else {
                    this.dataRules = {
                        name: [this.commonRule()],
                        data:[this.commonRule()],
                    }
                }

            },
            deep: true,
        },
    },
    methods: {
        commonRule() {
            return {
                required: true,
                message: this.$t('This field cannot be left blank.'),
                trigger: "change",
            };
        },
        setDefaultData() {
            return {
                name: "",
                data: ""
            };
        },
        changeActive() {
            let oldStatus = this.data.is_active_time ? 'false' : 'true';
            let newStatus = this.data.is_active_time ? 'true' : 'false';
            if (this.data.data.includes('<bPause>true</bPause>') || this.data.data.includes('<bPause>false</bPause>')) {
                this.data.data = this.data.data.replace(`<bPause>${oldStatus}</bPause>`, `<bPause>${newStatus}</bPause>`);
            } else {
                this.data.data = this.data.data.replace(`</GTOPTION>`, `<bPause>${newStatus}</bPause></GTOPTION>`);
            }
        },
        changeTime() {
            if (this.data.data.match(/<byPauseSpan>(\d)*<\/byPauseSpan>/g)) {
                this.data.data = this.data.data.replace(`<byPauseSpan>${this.oldTime}</byPauseSpan>`, `<byPauseSpan>${this.data.time}</byPauseSpan>`);
            } else {
                this.data.data = this.data.data.replace(`</bPause>`, `</bPause><byPauseSpan>${this.data.time}</byPauseSpan>`);
            }
        },
        changeData() {
            if (this.data.data.includes('<bPause>true</bPause>')) {
                this.data.is_active_time = true;
            } else {
                this.data.is_active_time = false;
            }
            const matchResult = this.data.data.match(/<byPauseSpan>(\d+)<\/byPauseSpan>/);
            if (matchResult) {
                if (Number(matchResult[1]) > 60) {
                    this.errorSubmit = true;
                    this.notification('Time delay between white and other colored layers: > 0s and ≤ 60s.', "error");
                } else {
                    this.errorSubmit = false;
                }
                this.data.time = Number(matchResult[1]);
            } else {
                this.data.time = null;
            }
        },
        async onSubmit() {
            if (this.isLoading) return;
            const isValid = await this.$refs.updatePrintingPreset.validate();
            if (!isValid) {
                return;
            }
            this.isLoading = true;
            let activeTime = this.oldActive ? 'true' : 'false';
            let time = this.data.time ?? 20;
            if (this.data.data.includes('<bPause>true</bPause>') || this.data.data.includes('<bPause>false</bPause>')) {
                this.data.data = this.data.data.replace(`<bPause>${activeTime}</bPause>`, `<bPause>${this.data.is_active_time}</bPause>`);
                if (this.data.data.match(/<byPauseSpan>(\d)*<\/byPauseSpan>/g)) {
                    this.data.data = this.data.data.replace(`<byPauseSpan>${this.oldTime}</byPauseSpan>`, `<byPauseSpan>${this.data.time}</byPauseSpan>`);
                } else {
                    this.data.data = this.data.data.replace(`</bPause>`, `</bPause><byPauseSpan>${time}</byPauseSpan>`);
                }
            } else if (this.data.is_active_time) {
                this.data.data = this.data.data.replace(`</GTOPTION>`, `<bPause>${this.data.is_active_time}</bPause><byPauseSpan>${this.data.time}</byPauseSpan></GTOPTION>`);
            } else {
                this.data.data = this.data.data.replace(`</GTOPTION>`, `<bPause>false</bPause><byPauseSpan>${time}</byPauseSpan></GTOPTION>`);
            }
            try {
                const res = await update(this.printingPresetId, this.data);
                this.dialogVisible = false;
                this.notification(res.data.message);
                this.$emit("refresh");
            } catch (e) {
                let message = e.response.data.message;
                this.notification(message, "error");
            } finally {
                this.isLoading = false;
            }
        },
    }
}
