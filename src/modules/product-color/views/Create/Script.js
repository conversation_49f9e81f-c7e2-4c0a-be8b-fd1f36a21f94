import EventBus from "@/utilities/eventBus";
import { create } from "@/api/productColor";

export default {
    name: "CreateProductColor",
    data() {
        return {
            dialogVisible: false,
            data: this.setDefaultData(),
            serverErrors: [],
            isLoading: false,
            neckLabelColor: [
                {
                    label: 'White',
                    value: '#ffffff'
                },
                {
                    label: 'Black',
                    value: '#000000'
                }
            ]
        }
    },
    created() {
        EventBus.$on("showCreateProductColor", () => {
            this.data = this.setDefaultData();
            this.serverErrors = [];
            this.dialogVisible = true;
        });
    },
    methods: {
        isError(field) {
            return !!this.serverErrors[field];
        },
        getErrorMessage(field) {
            return this.serverErrors[field][0];
        },
        setDefaultData() {
            return {
                name: "",
                sku: "",
                color_code: "",
                neck_label_color: "",
            };
        },
        async onSubmit() {
            if (this.isLoading) return;
            const isValid = await this.$refs.createProductColor.validate();
            if (!isValid) {
                return;
            }
            this.data.color_code = this.data.color_code ? this.data.color_code : '#000000';
            this.isLoading = true;
            try {
                const res = await create(this.data);
                this.dialogVisible = false;
                this.notification(res.data.message);
                this.$emit("refresh");
            } catch (e) {
                this.serverErrors = e.response.data.errors;
                let message = e.response.data.message;
                this.notification(message, "error");
            } finally {
                this.isLoading = false;
            }
        },
    }
}
