import { getList } from "@/api/productColor";
import EventBus from "@/utilities/eventBus";
import CreateProductColor from "@/modules/product-color/views/Create/Index.vue";
import UpdateProductColor from "@/modules/product-color/views/Update/Index.vue";

export default {
    name: "ProductColorList",
    components: {
        CreateProductColor,
        UpdateProductColor
    },
    data() {
        return {
            items: [],
            isLoading: false,
            filter: this.setDefaultFilter(),
        }
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 245);
        }
    },
    mounted() {
        this.filter = this.getRouteParam();
        this.getProductColorList();
    },
    methods: {
        setDefaultFilter() {
            return {
                limit: 25,
                page: 1,
                name: '',
                sku: ''
            };
        },
        async getProductColorList() {
            this.isLoading = true;
            this.setRouteParam();
            const { data } = await getList(this.filter);
            this.items = data.data;
            this.total = data.total;
            this.isLoading = false;
        },
        changePage(page) {
            this.filter.page = page;
            this.getProductColorList();
        },
        onFilter() {
            this.filter.page = 1;
            this.getProductColorList();
        },
        resetFilter() {
            this.filter = this.setDefaultFilter();
            this.getProductColorList();
        },
        createProductColor() {
            EventBus.$emit("showCreateProductColor");
        },
        updateProductColor(item) {
            EventBus.$emit("showUpdateProductColor", item);
        },
        setColor ({ row, column, rowIndex, columnIndex }) {
            if (row.color_code !== "" && columnIndex === 3) {
                if (!this.checkIsLightColor(row.color_code)) {
                    return { backgroundColor: row.color_code, color: '#ffffff' };
                }
                return { backgroundColor: row.color_code };
            }
        },
        checkIsLightColor(color) {
            const hex = color.replace('#', '');
            const c_r = parseInt(hex.substr(0, 2), 16);
            const c_g = parseInt(hex.substr(2, 2), 16);
            const c_b = parseInt(hex.substr(4, 2), 16);
            const brightness = ((c_r * 299) + (c_g * 587) + (c_b * 114)) / 1000;
            return brightness > 155;
        }

    }
}