<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
    <div>
        <el-dialog v-model="dialogVisible" destroy-on-close :title="$t('Update Product Color') + ` #${productColorId}`" custom-class="el-dialog-custom" :destroy-on-close="true">
            <el-form status-icon ref="updateProductColor" :model="data" @submit.prevent="onSubmit" :label-position="'top'">
                <el-form-item :label="$t('Name')" :class="{'is-error': isError('name')}" required>
                    <el-input v-model="data.name" @keyup.enter="onSubmit"></el-input>
                    <div v-if="isError('name')" class="el-form-item__error">{{getErrorMessage('name')}}</div>
                </el-form-item>
                <el-form-item :label="$t('Sku')" :class="{'is-error': isError('sku')}" required>
                    <el-input v-model="data.sku" @keyup.enter="onSubmit"></el-input>
                    <div v-if="isError('sku')" class="el-form-item__error">{{getErrorMessage('sku')}}</div>
                </el-form-item>
                <el-form-item :label="$t('Color')" :class="{'is-error': isError('color_code')}" required>
                    <el-input v-model="data.color_code" type="color" @keyup.enter="onSubmit"></el-input>
                    <div v-if="isError('color_code')" class="el-form-item__error">{{getErrorMessage('color_code')}}</div>
                </el-form-item>
              <el-form-item :label="$t('Neck Label Color')" :class="{'is-error': isError('neck_label_color')}" required>
                <el-select v-model="data.neck_label_color" filterable class="w-full" :placeholder="$t('Neck Label Color')">
                  <el-option v-for="item in neckLabelColor" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <div v-if="isError('neck_label_color')" class="el-form-item__error">{{getErrorMessage('neck_label_color')}}</div>
              </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="onSubmit" :disabled="isLoading" :loading="isLoading">{{ $t('Update') }}</el-button>
            </template>
        </el-dialog>
    </div>
</template>