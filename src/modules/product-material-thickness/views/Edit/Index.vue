<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog v-model="openDialogEdit"
      :title="$t(`${data?.id ? 'Edit' : 'Create'} Product Material Thickness/Heat Press`)"
      custom-class="el-dialog-custom" @close="resetData" :close-on-click-modal="false">
      <template #default>
        <div>
          <el-form status-icon ref="formData" :model="data" :rules="dataRules"
            @submit.prevent="createOrUpdateProductMaterialThickness" label-width="130px" :label-position="'top'">
            <el-form-item :label="$t('Product Type')" prop="product_type_id">
              <el-select filterable clearable class="w-full"
                :class="{ 'border border-red-400 rounded': errorValidator && errorValidator.product_type_id }"
                v-model="data.product_type_id" size="large">
                <el-option v-for="item in types" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>

            <el-form-item :label="$t('Style Name')" prop="product_style_id">
              <el-select filterable clearable class="m-0 w-full" v-model="data.product_style_id" size="large">
                <el-option v-for="item in stylesByType" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>

            <el-form-item :label="$t('Print Area')" prop="product_print_side_id">
              <el-select filterable clearable class="m-0 w-full" v-model="data.product_print_side_id" size="large">
                <el-option v-for="item in productPrintSides" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>

            <el-form-item :label="$t('Material Thickness')" prop="material_thickness">
              <el-select filterable clearable class="m-0 w-full" v-model="data.material_thickness" size="large">
                <el-option v-for="item in materialThickness" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>

            <el-form-item prop="heat_press">
              <el-switch style="display: block" v-model="data.heat_press" active-color="#13ce66"
                inactive-color="#ff4949" :active-value="1" :inactive-value="0" :active-text="$t('Heat Press')">
              </el-switch>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="createOrUpdateProductMaterialThickness" :disabled="isLoading"
            :loading="isLoading">
            {{ $t('Submit') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
