import { update, create } from '@/api/productMaterialThickness'
import EventBus from "@/utilities/eventBus.js";

export default {
  name: "FormProductMaterialThickness",
  data() {
    return {
      openDialogEdit: false,
      isLoading: false,
      errorValidator: {},
      data: {
        heat_press: 1,
      },
      dataRules: {
        product_type_id: [this.commonRule()],
        product_style_id: [],
        product_print_side_id: [],
        material_thickness: [this.commonRule()],
        heat_press: [],
      },
      materialThickness: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'],
    }
  },

  props: {
    styles: {
      default: [],
      type: Array
    },
    productPrintSides: {
      default: [],
      type: Array
    },
    types: {
      default: [],
      type: Array
    },
  },

  computed: {
    stylesByType() {
      if (!this.data.product_type_id) {
        this.data.product_style_id = null;
        return [];
      }

      let ptype = JSON.parse(JSON.stringify(this.types)).find(({ id }) => id == this.data.product_type_id);

      if (!ptype) {
        this.data.product_style_id = null;
        return []
      }

      let result = JSON.parse(JSON.stringify(this.styles)).filter(item => item.type == ptype.name);
      let styleCurrent = JSON.parse(JSON.stringify(result)).filter(item => item.id == this.data.product_style_id);

      if (!styleCurrent.length) {
        this.data.product_style_id = null;
      }

      return result;
    },
  },

  watch: {
    data: {
      handler() {
        if (this.data.material_thickness) {
          this.data.material_thickness = this.data.material_thickness.toUpperCase();
        }
      },
      deep: true
    },
  },

  created() {
    EventBus.$on("showFormProductMaterialThickness", (data) => {
      this.openDialogEdit = true;
      this.data.heat_press = 1;

      if (data) {
        this.data = { ...data };
      }
    });
  },

  methods: {
    commonRule() {
      return {
        required: true,
        message: this.$t('This field cannot be left blank.'),
        trigger: "change",
      };
    },

    async resetData() {
      this.errorValidator = {}
      this.data = {}
      this.imageUrl = ''
      await this.$refs['formData'].resetFields();
    },

    async createOrUpdateProductMaterialThickness() {
      const isValid = await this.$refs['formData'].validate();

      if (!isValid) return;
      this.isLoading = true;

      try {
        if (this.data.id) {
          await update(this.data.id, this.data);
          this.notification(this.$t('Update product material thickness/heat press successfully'), 'success')
        } else {
          await create(this.data);
          this.notification(this.$t('Create product material thickness/heat press successfully'), 'success')
        }

        this.resetData();
        this.openDialogEdit = false;
        this.$emit("refresh");
      } catch (e) {
        this.notification(this.$t('An error occurred, the record may already exist. Please check and try again.'), 'error')
      }
      this.isLoading = false;
    }
  }
}