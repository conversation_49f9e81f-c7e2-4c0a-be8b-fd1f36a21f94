<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ $t('Product Material Thickness/Heat Press') }}</h1>
      </div>
      <div class="top-head-right">
        <el-button type="primary" :disabled="isLoading" size="large" @click.prevent="createOrUpdateProductMaterialThickness()">
          <span class="icon-margin-right">
            <icon :data="iconAdd" />
          </span>{{ $t('Create') }}
        </el-button>
      </div>
    </div>

    <div class="table-content mt-4 pb-5">
      <el-form ref="formData" :label-position="'right'" size="large" :model="data" class="el-form-add-product"
        v-on:keyup.enter="fetchListProductMaterialThickness()">

        <el-row :gutter="20">
          <el-col :span="5">
            <el-form-item prop="product_type_id">
              <el-select class="w-full" :placeholder="$t('Product Type')" clearable filterable v-model="filter.product_type_id"
                size="large">
                <el-option v-for="item in types" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item prop="product_style_id">
              <el-select class="w-full" filterable clearable v-model="filter.product_style_id" :placeholder="$t('Style name')"
                size="large">
                <el-option v-for="item in styles" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item prop="product_print_side_id">
              <el-select class="w-full" filterable clearable v-model="filter.product_print_side_id"
                :placeholder="$t('Print Area')" size="large">
                <el-option v-for="item in productPrintSides" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="9" class="">
            <div class="flex justify-end">
              <el-button type="primary" :disabled="isLoading" size="large"
                @click.prevent="fetchListProductMaterialThickness()">
                <span class="">
                  <icon :data="iconFilter" />
                </span>{{ $t('Filter') }}
              </el-button>
              <el-button :disabled="isLoading" size="large" @click="resetData">
                <span class="icon-margin-right"></span>{{ $t('Reset') }}
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>

      <el-table stripe size="small" class="mt-2 table-cus" :data="dataList" style="width: 100%" :max-height="maxHeight"
        v-loading="isLoading" element-loading-text="Loading...">
        <el-table-column prop="product_type" width="100" :label="$t('ID')">
          <template #default="scope">
            {{ scope.row?.id || '' }}
          </template>
        </el-table-column>

        <el-table-column prop="product_type" :label="$t('Product Type')">
          <template #default="scope">
            {{ scope.row?.product_type?.name || '' }}
          </template>
        </el-table-column>

        <el-table-column prop="product_style" :label="$t('Style Name')" min-width="150">
          <template #default="scope">
            {{ scope.row?.product_style?.name || '' }}
          </template>
        </el-table-column>

        <el-table-column prop="print_method" :label="$t('Print Area')" min-width="150">
          <template #default="scope">
            {{ scope.row?.product_print_side?.name || '' }}
          </template>
        </el-table-column>

        <el-table-column width="200" prop="position_platen" :label="$t('Material Thickness')">
          <template #default="scope">
            {{ scope.row?.material_thickness || '' }}
          </template>
        </el-table-column>

        <el-table-column prop="position_platen" :label="$t('Heat Press')">
          <template #default="scope">
            <el-tag v-if="scope.row?.heat_press" type="success">ON</el-tag>
            <el-tag v-else type="danger">OFF</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="action" :label="$t('Action')">
          <template #default="scope">
            <el-link class="el-link-edit" :underline="false" type="primary"
              @click="createOrUpdateProductMaterialThickness(scope.row)">
              <icon :data="iconEdit" />
            </el-link>
            <el-popconfirm :title="`Are you sure to delete ${scope.row?.product_type?.name || ''} ?`"
              @confirm="deleteProductMaterialThickness(scope.row)">
              <template #reference>
                <el-link :underline="false" type="danger">
                  <icon :data="iconDelete" class="ml-2" />
                </el-link>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom">
        <div class="total">
          {{ $t('Total:') }} {{ total }}
        </div>
        <el-pagination :disabled="isLoading" background layout="prev, pager, next" :page-size="filter.limit"
          :total="total" @current-change="changePage" v-model:currentPage="filter.page">
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select v-model="filter.limit" :placeholder="$t('Select')" size="mini" @change="changePage(1)">
            <el-option v-for="item in limits" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
      </div>
    </div>
    <FormProductMaterialThickness :productPrintSides="productPrintSides" :styles="styles" :types="types"
      @refresh="fetchListProductMaterialThickness" />
  </div>
</template>
