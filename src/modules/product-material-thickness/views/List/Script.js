import { getList as getListProductMaterialThickness, destroy } from '@/api/productMaterialThickness'
import { fetchAll } from '@/api/productStyle'
import { fetchAll as fetchAllType } from '@/api/productType'
import EventBus from "@/utilities/eventBus.js";
import FormProductMaterialThickness from "@/modules/product-material-thickness/views/Edit/Index.vue";
import { getList } from '@/api/productPrintSide'

export default {
  name: "ProductPrintAreaList",

  data() {
    return {
      isLoading: false,
      types: [],
      styles: [],
      dataList: [],
      total: 0,
      errorValidator: {},
      filter: {
        page: null,
        limit: 25,
        product_style: null
      },
      data: {},
      style: null,
      canFilterStyle: false,
      attributes: [],
      productPrintSides: []
    }
  },

  components: {
    FormProductMaterialThickness,
  },

  created() {    
    this.fetchAllType();
    this.fetchAllStyle();
    this.fetchProductPrintSide();
    this.fetchListProductMaterialThickness();
  },

  beforeUnmount() {
    EventBus.$off("showFormProductMaterialThickness");
  },

  methods: {
    createOrUpdateProductMaterialThickness(data) {
      EventBus.$emit("showFormProductMaterialThickness", data);
    },

    async fetchAllType() {
      this.isLoading = true;
      const { data } = await fetchAllType();
      this.types = data;
      this.isLoading = false;
    },
    
    async fetchAllStyle() {
      this.isLoading = true;
      const { data } = await fetchAll();
      this.styles = data;
      this.isLoading = false;
    },

    async fetchListProductMaterialThickness() {
      this.isLoading = true;
      const { data } = await getListProductMaterialThickness(this.filter);
      this.dataList = data.data;
      this.total = data?.total;
      this.isLoading = false;
    },

    async fetchProductPrintSide() {
      this.isLoading = true;
      const { data } = await getList();
      this.productPrintSides = data;
      this.isLoading = false;
    },

    resetData() {
      this.filter = {
        page: null,
        limit: 25,
        product_style: null
      };
      this.errorValidator = {}
      this.fetchListProductMaterialThickness();
    },

    showProductStyleName(id) {
      const style = this.styles.find(style => style.id == id)
      return style?.name || ''
    },

    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchListProductMaterialThickness();
      });
    },

    async deleteProductMaterialThickness(item) {
      const { data } = await destroy(item.id);
      
      if (data) {
        this.fetchListProductMaterialThickness();
        this.notification(this.$t('Delete product print area successfully.'), 'success');
      }
    },
  }
}