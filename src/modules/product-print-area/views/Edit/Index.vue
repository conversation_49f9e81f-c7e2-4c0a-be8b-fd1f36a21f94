<style src="./Style.scss" lang="scss" scoped></style>
<style lang="scss">
.el-form-item-rotate {
  label {
    margin-bottom: 0 !important;
  }
}
</style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
        v-model="openDialogEdit"
        :title="$t('Create/Edit Product SKU Print Area')"
        custom-class="el-dialog-custom"
        @close="resetData"
        :close-on-click-modal="false"
    >
      <template #default>
        <div>
          <el-form
              status-icon
              ref="formData"
              :model="data"
              :rules="dataRules"
              @submit.prevent="createOrUpdateProductPrintArea"
              label-width="130px"
              :label-position="'top'"
          >
            <el-form-item v-if="data && data.product_style_id" :label="$t('Product Style')" prop="product_style_id">
              <el-input :placeholder="showProductStyleName(data.product_style_id)" :disabled="data && data.product_style_id"></el-input>
            </el-form-item>
            <el-form-item :label="$t('Product Style')" class="print-area-form" v-else prop="product_style_id">
              <el-select
                  filterable
                  class="w-full"
                  :class="{'border border-red-400 rounded': errorValidator && errorValidator.product_style }"
                  v-model="data.product_style"
                  size="large"
              >
                <el-option
                    v-for="style in styles"
                    :key="style.id"
                    :label="style.name"
                    :value="style.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('Name Of Print Area')" prop="name" >
              <el-select
                  filterable
                  class="m-0 w-full"
                  v-model="data.name"
                  size="large"
              >
                <el-option
                    v-for="style in productPrintSides"
                    :key="style.id"
                    :label="style.name"
                    :value="style.name"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('Rotate mode')" prop="rotate" class="!flex !items-center space-x-3 el-form-item-rotate">
              <el-switch v-model="data.rotate" />
            </el-form-item>
            <div class="text-danger text-[12px]" v-if="errorValidator && errorValidator.name">
              {{ errorValidator.name[0] }}
            </div>
            <el-form-item :label="$t('Print Method')" prop="print_method" >
              <el-select v-model="data.print_method" filterable :placeholder="$t('Select print method')" class="m-0 w-full">
                <el-option v-for="method in printMethods"
                           :key="method.name"
                           :label="method.name"
                           :value="method.name"
                />
              </el-select>
            </el-form-item>
            <div class="text-danger text-[12px]" v-if="errorValidator && errorValidator.print_method">
              {{ errorValidator.print_method[0] }}
            </div>
            <!-- <el-form-item :label="$t('Platen Size (inch)')" prop="platen_size">
              <el-input :placeholder="$t('Platen Size')" v-model="data.platen_size" :class="{'border border-red-400 rounded': errorValidator && errorValidator.platen_size }"></el-input>
            </el-form-item>
            <div class="text-danger text-[12px]" v-if="errorValidator && errorValidator.platen_size">
              {{ errorValidator.platen_size[0] }}
            </div>
            <el-form-item :label="$t('Print Size (mm)')" prop="print_size">
              <el-input :placeholder="$t('Print Size')" v-model="data.print_size" :class="{'border border-red-400 rounded': errorValidator && errorValidator.print_size }"></el-input>
            </el-form-item> -->
            <div class="text-danger text-[12px]" v-if="errorValidator && errorValidator.print_size">
              {{ errorValidator.print_size[0] }}
            </div>
            <el-form-item :label="$t('Image Product')" prop="product_image">
              <div class="w-full custom-input-file">
                <el-input class="opacity-0 w-full" type="file" v-model="productImage" name="product_image" @change="handleFileUpload()" ref="product_image" accept="image/*" ></el-input>
                <span class="icon-margin-right custom-position"><icon :data="iconUploadCloud" /></span>
              </div>
              <img class="preview-img" :src="imageUrl" alt="">
            </el-form-item>
            <div class="text-danger text-[12px]" v-if="errorValidator && errorValidator.product_image">
              {{ errorValidator.product_image[0] }}
            </div>
            <el-form-item :label="$t('Platen Area Size (pixel)')" prop="platen_area_size">
              <el-input :placeholder="$t('Platen Area Size')" v-model="data.platen_area_size" :class="{'border border-red-400 rounded': errorValidator && errorValidator.platen_area_size }"></el-input>
            </el-form-item>
            <div class="text-danger text-[12px]" v-if="errorValidator && errorValidator.platen_area_size">
              {{ errorValidator.platen_area_size[0] }}
            </div>
            <div class="justify-space-between layout-default">
              <div class="w-48">
                <el-form-item :label="$t('Left (pixel)')" prop="left">
                  <el-input v-model="data.left" :class="{'border border-red-400 rounded': errorValidator && errorValidator.left }"></el-input>
                </el-form-item>
                <div class="text-danger text-[12px]" v-if="errorValidator && errorValidator.left">
                  {{ errorValidator.left[0] }}
                </div>
              </div>
              <div class="w-48">
                <el-form-item :label="$t('Top (pixel)')" prop="top">
                  <el-input v-model="data.top" :class="{'border border-red-400 rounded': errorValidator && errorValidator.top }"></el-input>
                </el-form-item>
                <div class="text-danger text-[12px]" v-if="errorValidator && errorValidator.top">
                  {{ errorValidator.top[0] }}
                </div>
              </div>
            </div>

          </el-form>
        </div>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <el-button
              type="primary"
              @click="createOrUpdateProductPrintArea"
              :disabled="isLoading"
              :loading="isLoading"
          >
            {{ $t('Submit') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>


