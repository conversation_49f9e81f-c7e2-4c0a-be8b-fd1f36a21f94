import { update, create } from '@/api/productPrintArea'
import EventBus from "@/utilities/eventBus.js";
import { STORAGE_URL } from "@/utilities/constants";


export default {
  name: "EditProductPrintAreaList",
  data() {
    return {
      openDialogEdit: false,
      isLoading: false,
      errorValidator: {},
      data: {},
      dataRules: {
        product_style: [this.commonRule()],
        name: [this.commonRule()],
        // platen_size: [this.commonRule()],
        print_method: [this.commonRule()],
        // print_size: [this.commonRule()],
        platen_area_size: [this.commonRule()],
        left: [this.commonRule()],
        top: [this.commonRule()],
        product_image: [this.commonRule()],
      },
      urlStorage: STORAGE_URL,
      imageUrl: '',
      productImage: null
    }
  },
  props: {
    styles: {
      default: [],
      type: Array
    },
    productPrintSides: {
      default: [],
      type: Array
    },
    printMethods: {
      default: [],
      type: Array
    },
  },
  watch: {
    style: {
      handler() {
        this.data = {
          ...this.data,
          product_style_id: this.style
        }
        this.fetchProductPrintArea()
      },
      deep: true
    },
    productImage(value) {
      if (value) {
        this.imageUrl = value
        this.data.product_image = value;
      }
    }
  },
  created() {
    EventBus.$on("showUpdateProductPrintArea", (data) => {
      this.openDialogEdit = true;
      this.data = {
        ...data
      };
      if (this.data.product_image) {
        this.imageUrl = this.getImageUrl(this.data);
      }
      this.data.rotate = data.rotate ? true : false
      if (this.data.id) {
        let arrayPosition = this.data.platen_position.split("x");
        this.data.left = arrayPosition[0];
        this.data.top = arrayPosition[1];
      }
    });
  },
  methods: {
    getImageUrl(item){
      return this.urlStorage + '/ProductPrintArea/' + item.product_image
    },
    handleFileUpload(){
      this.data.product_image = this.$refs.product_image.input.files[0];
      this.imageUrl = URL.createObjectURL(this.data.product_image);
    },
    showProductStyleName(id) {
      const style = this.styles.find(style => style.id == id)
      return style?.name || ''
    },
    commonRule() {
      return {
        required: true,
        message: this.$t('This field cannot be left blank.'),
        trigger: "change",
      };
    },
    validateSize (value, attr) {
      if (!(/^\d+x\d+$/).test(value)) {
        this.errorValidator[attr][0] = 'Invalid value. Example: 16x16';
        return false;
      } else if (this.errorValidator.hasOwnProperty(attr)) {
        delete this.errorValidator[attr]
      }
      return true;
    },
    validatePrintSize (value, attr) {
      if (!(/^[\d]+[.\d]{0,10}x[\d]+[.\d]{0,10}$/).test(value)) {
        this.errorValidator[attr][0] = 'Invalid value. Example: 16.5x16.5 or 16x16';
        return false;
      } else if (this.errorValidator.hasOwnProperty(attr)) {
        delete this.errorValidator[attr]
      }
      return true;
    },
    async resetData() {
      this.errorValidator = {}
      this.data = {}
      this.imageUrl = ''
      await this.$refs['formData'].resetFields();
    },
    async createOrUpdateProductPrintArea() {
      const isValid = await this.$refs['formData'].validate();

      if (!isValid) return;
      this.isLoading = true;
      let formData = new FormData()

      formData.append('name', this.data.name);
      // formData.append('platen_size', this.data.platen_size);
      // formData.append('print_size', this.data.print_size);
      formData.append('platen_area_size', this.data.platen_area_size);
      
      formData.append('top', this.data.top);
      formData.append('left', this.data.left);
      formData.append('print_method', this.data.print_method);
      formData.append('rotate', this.data.rotate ? 1 : 0);
      try {
        if (this.data.id) {
          formData.append('_method', 'PUT');
          if (this.productImage) {
            formData.append('image', this.data.product_image);
          }
          formData.append('product_style_id', this.data.product_style_id);
          await update(this.data.id, formData);
          var notification = this.$t('Update product print area successfully', 'success');
        } else {
          formData.append('image', this.data.product_image);
          formData.append('product_style_id', this.data.product_style);
          await create(formData);
          var notification = this.$t('Create product print area successfully', 'success');
        }
        this.data = {}
        this.$refs['formData'].resetFields();
        this.$refs.product_image.value = '';
        this.notification(notification);
        this.openDialogEdit = false;
        this.$emit("refresh");
      } catch (e) {
        this.errorValidator = e.response.data.errors
      }
      this.isLoading = false;
    }
  }
}