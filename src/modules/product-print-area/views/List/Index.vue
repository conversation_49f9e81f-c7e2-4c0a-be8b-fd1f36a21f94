<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ $t('Product Print Area') }}</h1>
      </div>
      <div class="top-head-right">
        <el-button
          type="primary"
          :disabled="isLoading"
          size="large"
          @click.prevent="createOrUpdateProductPrintArea()"
        >
          <span class="icon-margin-right"><icon :data="iconAdd" /></span
          >{{ $t('Create') }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <el-form
        ref="formData"
        :label-position="'right'"
        size="large"
        :model="data"
        :rules="dataRules"
        class="el-form-add-product"
        v-on:keyup.enter="fetchProductPrintArea"
      >
        <div class="flex justify-between mt-4">
          <div class="flex items-start">
            <el-form-item prop="product_style_id">
              <el-select
                filterable
                class="m-0"
                v-model="filter.product_style"
                :placeholder="$t('Select Product Style')"
                size="large"
              >
                <el-option
                  v-for="style in styles"
                  :key="style.id"
                  :label="style.name"
                  :value="style.id"
                />
              </el-select>
            </el-form-item>
            <div class="ml-10">
              <el-form-item prop="name">
                <el-select
                  filterable
                  class="m-0"
                  v-model="filter.name"
                  :placeholder="$t('Name Of Print Area')"
                  size="large"
                >
                  <el-option
                    v-for="style in productPrintSides"
                    :key="style.id"
                    :label="style.name"
                    :value="style.name"
                  />
                </el-select>
              </el-form-item>
              <div
                class="text-danger text-[12px] text-right"
                v-if="errorValidator && errorValidator.name"
              >
                {{ errorValidator.name[0] }}
              </div>
            </div>
            <!-- <div class="ml-10">
              <el-form-item prop="platen_size">
                <el-input
                    v-model="filter.platen_size"
                    :class="{'boreder border-red-400' : errorValidator && errorValidator.platen_size}"
                    :placeholder="$t('Platen Size')" />
              </el-form-item>
              <div class="text-danger text-[12px] text-right" v-if="errorValidator && errorValidator.platen_size">
                {{ errorValidator.platen_size[0] }}
              </div>
            </div>

            <div class="ml-10">
              <el-form-item prop="print_size">
                <el-input
                    v-model="filter.print_size"
                    :class="{'boreder border-red-400' : errorValidator && errorValidator.print_size}"
                    :placeholder="$t('Print Size')" />
              </el-form-item>
              <div class="text-danger text-[12px] text-right" v-if="errorValidator && errorValidator.print_size">
                {{ errorValidator.print_size }}
              </div>
            </div> -->
          </div>
          <div>
            <el-button
              type="primary"
              :disabled="isLoading"
              size="large"
              @click.prevent="fetchProductPrintArea"
            >
              <span class=""><icon :data="iconFilter" /></span
              >{{ $t('Filter') }}
            </el-button>
            <el-button :disabled="isLoading" size="large" @click="resetData">
              <span class="icon-margin-right"></span>{{ $t('Reset') }}
            </el-button>
          </div>
        </div>
      </el-form>

      <el-table
        border
        stripe
        size="small"
        class="mt-2 table-cus"
        :data="printAreas"
        style="width: 100%"
        :max-height="maxHeight"
        v-loading="isLoading"
        element-loading-text="Loading..."
      >
        <el-table-column :label="$t('Image')" width="80">
          <template #default="scope">
            <el-image
              lazy="true"
              style="width: 50px; height: 50px"
              :src="scope.row.thumb_250 != 0 ? getImageUrl(scope.row) : ''"
              :preview-src-list="[getImageUrl(scope.row)]"
            >
            </el-image>
          </template>
        </el-table-column>
        <el-table-column
          sortable
          prop="style"
          width="200"
          :label="$t('Product Style')"
        >
          <template #default="scope">
            {{ showProductStyleName(scope.row.product_style_id) }}
          </template>
        </el-table-column>
        <el-table-column
          sortable
          prop="name"
          :label="$t('Name Of Print Area')"
          min-width="150"
        >
          <template #default="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>
        <el-table-column
          sortable
          prop="print_method"
          :label="$t('Print Method')"
          min-width="150"
        >
          <template #default="scope">
            {{ scope.row.print_method }}
          </template>
        </el-table-column>
        <!-- <el-table-column
            sortable
            prop="platen_size"
            :label="$t('Platen Size')"
        >
          <template #default="scope">
            {{ scope.row.platen_size }}
          </template>
        </el-table-column>
        <el-table-column
            sortable
            prop="print_size"
            :label="$t('Print Size')"
        >
          <template #default="scope">
            {{ scope.row.print_size }}
          </template>
        </el-table-column> -->
        <el-table-column
          sortable
          prop="position_platen"
          :label="$t('Position Platen')"
        >
          <template #default="scope">
            {{ scope.row.platen_position }}
          </template>
        </el-table-column>
        <el-table-column
          sortable
          prop="platen_area_size"
          :label="$t('Platen Area Size')"
        >
          <template #default="scope">
            {{ scope.row.platen_area_size }}
          </template>
        </el-table-column>
        <el-table-column prop="action" :label="$t('Action')">
          <template #default="scope">
            <el-link
              class="el-link-edit"
              :underline="false"
              type="primary"
              @click="createOrUpdateProductPrintArea(scope.row)"
            >
              <icon :data="iconEdit" />
            </el-link>
            <el-popconfirm
              :title="`Are you sure to delete ${scope.row.name} ?`"
              @confirm="deletePrintArea(scope.row)"
            >
              <template #reference>
                <el-link :underline="false" type="danger">
                  <icon :data="iconDelete" class="ml-2" />
                </el-link>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom">
        <div class="total">
          {{ $t('Total:') }} {{ printAreas.length ? total : 0 }}
        </div>
        <el-pagination
          :disabled="isLoading"
          background
          layout="prev, pager, next"
          :page-size="filter.limit"
          :total="total"
          @current-change="changePage"
          v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
            v-model="filter.limit"
            :placeholder="$t('Select')"
            size="mini"
            @change="changePage(1)"
          >
            <el-option
              v-for="item in limits"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
    </div>
    <UpdateProductPrintArea
      :printMethods="printMethods"
      :productPrintSides="productPrintSides"
      :styles="styles"
      @refresh="fetchProductPrintArea"
    />
  </div>
</template>
