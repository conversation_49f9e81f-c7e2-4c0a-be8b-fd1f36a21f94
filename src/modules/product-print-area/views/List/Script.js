import { list, destroy, create , attributes} from '@/api/productPrintArea'
import { fetchAll, getPrintMethod } from '@/api/productStyle'
import EventBus from "@/utilities/eventBus.js";
import UpdateProductPrintArea from "@/modules/product-print-area/views/Edit/Index.vue";
import { getList } from '@/api/productPrintSide'

export default {
  name: "ProductPrintAreaList",
  data() {
    return {
      isLoading: false,
      styles: [],
      printAreas: [],
      printMethods: [],
      total: null,
      errorValidator: {},
      filter: {
        page: null,
        limit: 25,
        product_style: null
      },
      data: {},
      style: null,
      canFilterStyle: false,
      attributes: [],
      productPrintSides: []
    }
  },
  components: {
    UpdateProductPrintArea,
  },
  computed: {
    canClear() {
      let result = false;
      Object.entries(this.data).forEach(([key, val]) => {
        if (!!val) {
          result = true;
        }
      })
      return result;
    }
  },
  watch: {
  },
  created() {
    this.fetchProductPrintArea()
    this.fetchAllStyle();
    this.fetchAllAttributes()
    this.fetchProductPrintSide();
    this.getPrintMethod();
  },
  beforeUnmount() {
    EventBus.$off("showUpdateProductPrintArea");
  },
  methods: {
    getImageUrl(item){
      return item.product_image
    },
    createOrUpdateProductPrintArea(data) {
      EventBus.$emit("showUpdateProductPrintArea", data);
    },
    async fetchAllStyle() {
      this.isLoading = true;
      const { data } = await fetchAll();
      this.styles = data;
      this.isLoading = false;
    },
    async getPrintMethod() {
      this.isLoading = true;
      const { data } = await getPrintMethod();
      this.printMethods = data;
      this.isLoading = false;
    },
    async fetchAllAttributes() {
      this.isLoading = true;
      const { data } = await attributes();
      this.attributes = data;
      this.isLoading = false;
    },
    async fetchProductPrintArea() {
      this.isLoading = true;
      const { data } = await list(this.filter);
      this.printAreas = data.data;
      this.total = data.total;
      this.isLoading = false;
    },
    async fetchProductPrintSide() {
      this.isLoading = true;
      const { data } = await getList();
      this.productPrintSides = data;
      this.isLoading = false;
    },
    resetData() {
      this.filter = {
        page: null,
        limit: 25,
        product_style: null
      };
      this.errorValidator = {}
      this.fetchProductPrintArea();
    },
    showProductStyleName(id) {
      const style = this.styles.find(style => style.id == id)
      return style?.name || ''
    },
    commonRule() {
      return {
        required: true,
        message: this.$t('This field cannot be left blank.'),
        trigger: "change",
      };
    },
    validateSize (value, attr) {
      if (!(/^\d+x\d+$/).test(value)) {
        this.errorValidator[attr] = 'Invalid value. Example: 16x16';
        return false;
      } else if (this.errorValidator.hasOwnProperty(attr)) {
        delete this.errorValidator[attr]
      }
      return true;
    },
    validatePrintSize (value, attr) {
      if (!(/^[\d]+[.\d]{0,10}x[\d]+[.\d]{0,10}$/).test(value)) {
        this.errorValidator[attr] = 'Invalid value. Example: 16.5x16.5 or 16x16';
        return false;
      } else if (this.errorValidator.hasOwnProperty(attr)) {
        delete this.errorValidator[attr]
      }
      return true;
    },
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchProductPrintArea();
      });
    },
    async deletePrintArea(printArea) {
      const { data } = await destroy(printArea.id);
      if (data) {
        this.printAreas = this.printAreas.filter(p => p.id != printArea.id)
        this.notification(this.$t('Delete product print area successfully.'), 'success');
      }
    },
  }
}