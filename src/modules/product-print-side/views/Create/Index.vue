<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <el-dialog
      v-model="dialogVisible"
      destroy-on-close
      :title="$t('Create Product Print Area')"
      custom-class="el-dialog-custom"
      :destroy-on-close="true"
  >
    <el-form
        status-icon
        ref="createProductPrintSide"
        :model="data"
        @submit.prevent="onSubmit"
        :label-position="'top'"
    >
      <el-form-item :label="$t('Name')" :class="{'is-error': isError('name')}" required>
        <el-input v-model="data.name" @keyup.enter="onSubmit"></el-input>
        <div v-if="isError('name')" class="el-form-item__error">{{getErrorMessage('name')}}</div>
      </el-form-item>
      <el-form-item :label="$t('Code')" :class="{'is-error': isError('code')}" required size="1">
        <el-input v-model="data.code" @keyup.enter="onSubmit"></el-input>
        <div v-if="isError('code')" class="el-form-item__error">{{getErrorMessage('code')}}</div>
      </el-form-item>
      <el-form-item :label="$t('Description')" :class="{'is-error': isError('description')}">
        <el-input v-model="data.description" @keyup.enter="onSubmit"></el-input>
        <div v-if="isError('description')" class="el-form-item__error">{{getErrorMessage('description')}}</div>
      </el-form-item>
      <el-form-item :label="$t('Code wip')" :class="{'is-error': isError('code_wip')}" required>
        <el-input v-model="data.code_wip" @keyup.enter="onSubmit"></el-input>
        <div v-if="isError('code_wip')" class="el-form-item__error">{{getErrorMessage('code_wip')}}</div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button
          type="primary"
          @click="onSubmit"
          :disabled="isLoading"
          :loading="isLoading"
      >{{ $t('Create') }}</el-button>
    </template>
  </el-dialog>
</template>
