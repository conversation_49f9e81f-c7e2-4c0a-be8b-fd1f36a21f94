<style src="./Style.scss" lang="scss">

</style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head mb-4">
      <div class="top-head-left">
        <h1>{{ $t('Print areas') }}</h1>
      </div>
      <div class="top-head-right">
        <el-button type="primary" @click="createProductPrintSide">
          <span class="icon-margin-right">
            <icon :data="iconAdd" />
          </span>{{ $t('Create') }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <table class="table-auto w-full border-collapse border border-slate-200">
        <thead>
          <tr class="h-8">
            <th scope="col" class="border border-slate-200 text-left pl-3 text-gray-500">{{$t('ID')}}</th>
            <th scope="col" class="border border-slate-200 text-left pl-3 text-gray-500">{{$t('Name')}}</th>
            <th scope="col" class="border border-slate-200 text-left pl-3 text-gray-500">{{$t('Code')}}</th>
            <th scope="col" class="border border-slate-200 text-left pl-3 text-gray-500">{{$t('Description')}}</th>
            <th scope="col" class="border border-slate-200 text-left pl-3 text-gray-500">{{$t('Code WIP')}}</th>
            <th scope="col" class="border border-slate-200 text-left pl-3 text-gray-500">{{$t('Order')}}</th>
            <th scope="col" class="border border-slate-200 text-left pl-3 text-gray-500">{{$t('Date')}}</th>
            <!-- <th scope="col" class="border border-slate-200 text-left pl-3 text-gray-500">{{$t('Action')}}</th> -->
          </tr>
        </thead>
        <draggable v-model="items" tag="tbody" item-key="id" :list="newItems" :options="{animation: 200}" @change="drag" @end="dragging=false" >
          <template #item="{ element }">
            <tr class="h-8 cursor-move">
              <td scope="row" class="border border-slate-200 pl-3 break-all text-gray-800">{{ element.id }}</td>
              <td class="border border-slate-200 pl-3 break-all text-gray-800">{{ element.name }}</td>
              <td class="border border-slate-200 pl-3 break-all text-gray-800">{{ element.code }}</td>
              <td class="border border-slate-200 pl-3 break-all text-gray-800">{{ element.description }}</td>
              <td class="border border-slate-200 pl-3 break-all text-gray-800">{{ element.code_wip }}</td>
              <td class="border border-slate-200 pl-3 break-all text-gray-800">{{ element.order }}</td>
              <td class="border border-slate-200 pl-3 break-all text-gray-800">{{ listViewDateFormat(element.created_at) }}</td>
              <!-- <td class="border border-slate-200 pl-3 break-all text-gray-800">
                <el-link class="el-link-edit" :underline="false" type="primary"
                  @click="updateProductPrintSide(element)">
                  <icon :data="iconEdit" />
                </el-link>
              </td> -->
            </tr>
          </template>
        </draggable>
      </table>
      <div class="bottom">
        <div class="total">{{$t('Total:')}} {{ items.length ? formatNumber(total) : 0 }}</div>
        <el-pagination :disabled="isLoading" background layout="prev, pager, next" :page-size="filter.limit"
          :total="total" @current-change="changePage" v-model:currentPage="filter.page">
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select v-model="filter.limit" :placeholder="$t('Select')" size="mini" @change="onFilter">
            <el-option v-for="item in limits" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
  </div>
  <CreateProductPrintSide @refresh="getProductPrintSide" />
  <UpdateProductPrintSide @refresh="getProductPrintSide" />
</template>