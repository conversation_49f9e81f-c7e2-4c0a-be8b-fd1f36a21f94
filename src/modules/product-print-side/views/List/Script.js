import { list, drag } from "@/api/productPrintSide";
import EventBus from "@/utilities/eventBus";
import CreateProductPrintSide from "@/modules/product-print-side/views/Create/Index.vue";
import UpdateProductPrintSide from "@/modules/product-print-side/views/Update/Index.vue";
import draggable from "vuedraggable";

export default {
  name: "ProductPrintSide",
  components: {
    CreateProductPrintSide,
    UpdateProductPrintSide,
    draggable
  },
  data() {
    return {
      items: [],
      isLoading: false,
      filter: this.setDefaultFilter(),
      dragging: false
    }
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 189);
    }
  },
  mounted() {
    this.filter = this.getRouteParam();
    this.getProductPrintSide();
  },
  methods: {
    async drag() {
      this.dragging = true;
      this.isLoading = true;
      let options = this.items.map((i, k) => ({
        id: i.id,
        position: k + 1
      }));
      try {
        const res = await drag({positions: options});
        if (res.data.code == 200) {
          this.getProductPrintSide();
        }
      } catch (e) {
        let message = e.response.data.message;
        this.notification(message, "error");
      }
      this.isLoading = false;
    },
    setDefaultFilter() {
      return {
        limit: 25,
        page: 1
      };
    },
    async getProductPrintSide() {
      this.isLoading = true;
      this.setRouteParam();
      const { data } = await list(this.filter);
      this.items = data.data.data;
      this.total = data.data.total;
      this.isLoading = false;
    },
    changePage(page) {
      this.filter.page = page;
      this.getProductPrintSide();
    },
    onFilter() {
      this.filter.page = 1;
      this.getProductPrintSide();
    },
    createProductPrintSide() {
      EventBus.$emit("showCreateProductPrintSide");
    },
    updateProductPrintSide(item) {
      EventBus.$emit("showUpdateProductPrintSide", item);
    }
  }
}