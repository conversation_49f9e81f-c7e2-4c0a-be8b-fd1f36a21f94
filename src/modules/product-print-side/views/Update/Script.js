import EventBus from "@/utilities/eventBus";
import { show, update } from "@/api/productPrintSide";

export default {
  name: "UpdateProductPrintSide",
  data() {
    return {
      productPrintSideId: null,
      dialogVisible: false,
      data: this.setDefaultData(),
      serverErrors: [],
      isLoading: false
    }
  },
  created() {
    EventBus.$on("showUpdateProductPrintSide", (item) => {
      this.productPrintSideId = item.id;
      this.data = Object.assign(this.data, item);
      this.serverErrors = [];
      this.dialogVisible = true;
    });
  },
  methods: {
    isError(field) {
      return !!this.serverErrors[field];
    },
    getErrorMessage(field) {
      return this.serverErrors[field][0];
    },
    setDefaultData() {
      return {
        name: "",
        code: "",
        description: "",
        code_wip: ""
      };
    },
    async onSubmit() {
      if (this.isLoading) return;
      const isValid = await this.$refs.updateProductPrintSide.validate();
      if (!isValid) {
        return;
      }
      this.isLoading = true;
      try {
        const res = await update(this.productPrintSideId, this.data);
        this.dialogVisible = false;
        this.notification(res.data.message);
        this.$emit("refresh");
      } catch (e) {
        this.serverErrors = e.response.data.errors;
        let message = e.response.data.message;
        this.notification(message, "error");
      } finally {
        this.isLoading = false;
      }
    },
  }
}
