<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left"><h1>{{ $t('Manage RBT SKU') }}</h1></div>
    </div>
    <div class="table-content">
      <div class="filter">
        <div class="flex justify-space-between w-full">
          <div class="filter">
            <el-select
                class="w-40 mr-2"
                v-model="filter.style"
                @change="selectStyle"
                filterable
                :placeholder="$t('Choose style')"
            >
              <el-option
                  v-for="item in styles"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
              />
            </el-select>
            <el-select
                class="w-40 mr-2"
                v-model="filter.color"
                @change="selectColors"
                filterable
                :placeholder="$t('Choose color')"
            >
              <el-option
                  v-for="item in colors"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
              />
            </el-select>
            <el-select
                class="w-40 mr-2"
                v-model="filter.size"
                @change="selectSizes"
                filterable
                :placeholder="$t('Choose Size')"
            >
              <el-option
                  v-for="item in sizes"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
              />
            </el-select>
            <span class="mr-2">or</span>
            <el-input class="!w-40 mr-2" v-model="filter.sku" placeholder="Enter SKU" @keyup.enter="getProductBySku" />
            <div class="btn-filter">
              <template v-if="hasFilter">
                <el-link type="danger" @click="onClearFilter" :underline="false"
                >{{ $t('Clear') }}</el-link
                >
              </template>
              <el-button type="primary" @click="onFilter">
            <span class="icon-margin-right"><icon :data="iconFilter" /></span
            >{{ $t('Filter') }}
              </el-button>
            </div>
          </div>
          <el-button class="float-right" type="primary" @click="createProduct">
            <span class="icon-margin-right"><icon :data="iconAdd" /></span>{{ $t('Add SKU') }}
          </el-button>
        </div>

      </div>
      <el-table
        v-if="products"
        stripe size="small"
        :data="products"
        style="width: 100%"
        :max-height="maxHeight"
        v-loading="isLoading"
        element-loading-text="Loading..."
      >
        <el-table-column  prop="is_active" :label="$t('On/Off')" min-width="100">
          <template #default="scope">
            <el-popover
                :visible="
               visible == scope.row.id
              "
                placement="top"
                :width="210"
            >
              <p>{{ $t('Are you sure to update this?') }}</p>
              <div class="flex justify-end">
                <el-button
                    size="small"
                    @click="
                    scope.row.is_active = !scope.row.is_active;
                    visible = null;
                  "
                >Cancel</el-button
                >
                <el-button
                    size="small"
                    type="primary"
                    @click="confirm(scope.row)"
                >Confirm</el-button
                >
              </div>
              <template #reference>
                <el-switch
                    @click=" visible = scope.row.id
                  "
                    v-model="scope.row.is_active"
                />
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column  prop="style" :label="$t('Style')" min-width="100">
          <template #default="scope">
            {{ scope.row?.product?.product_style?.name }}
          </template>
        </el-table-column>
        <el-table-column prop="color" :label="$t('Color')" min-width="100">
          <template #default="scope">
            {{ scope.row.product?.product_color?.name }}
          </template>
        </el-table-column>
        <el-table-column prop="size" :label="$t('Size')" min-width="100">
          <template #default="scope">
            {{ scope.row.product?.product_size?.name }}
          </template>
        </el-table-column>
        <el-table-column prop="sku" :label="$t('SKU')" min-width="100">
          <template #default="scope">
            {{ scope.row?.product?.sku }}
          </template>
        </el-table-column>
        <el-table-column prop="brand" :label="$t('Brand')" min-width="100">
          <template #default="scope">
            {{ scope.row.product?.brand?.name }}
          </template>
        </el-table-column>
        <el-table-column prop="name" :label="$t('Product Name')" min-width="100">
          <template #default="scope">
            {{ scope.row?.product?.name }}
          </template>
        </el-table-column>
      <el-table-column :label="$t('Created at')" min-width="100">
        <template #default="scope">
          {{ listViewDateFormat(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('Created by')" min-width="100">
        <template #default="scope">
          {{ scope.row.user?.username }}
        </template>
      </el-table-column>

      </el-table>
      <div class="bottom">
        <div class="total">
          {{ $t('Total:') }}
          {{ products?.length ? formatNumber(totalProduct) : 0 }}
        </div>
        <el-pagination
            :disabled="isLoading"
            background
            layout="prev, pager, next"
            :page-size="filter.limit"
            :total="totalProduct"
            @current-change="changePage"
            v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
              v-model="filter.limit"
              :placeholder="$t('Select')"
              size="mini"
              @change="onFilter"
          >
            <el-option
                v-for="item in limits"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>

    </div>
    <Create :attributes="attributes" :styles="styles" :allProducts="allProducts" @refresh="fetchProductTiktok" />
  </div>
</template>
