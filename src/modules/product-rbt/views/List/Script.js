import { list, add, update as updateProduct } from "@/api/productRbt.js";
import EventBus from "@/utilities/eventBus.js";
import Create from "@/modules/product-rbt/views/Create/Index.vue";
import {fetchProductByAttribute, getProductAttributes, getProductByParams} from "@/api/product";

export default {
  name: "ProductRbt",
  components: {
    Create,
  },
  data() {
    return {
      product: {
        style: null,
        color: null,
        size: null,
      },
      styles: [],
      colors: [],
      sizes: [],
      errorInputSku: null,
      sku: null,
      attributes: null,
      filter: {
        limit: 25,
        page: 1,
        sku: null,
        style: null,
        color: null,
        size: null,
        product_id: null,
      },
      products: [],
      allProducts: [],
      totalProduct: null,
      productSelected: null,
      visibleStatus: false,
      isLoading: false,
      visible: null,
    };
  },
  props: {
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 140);
    },
  },
  beforeUnmount() {
    EventBus.$off("showCreateProductRbt");
  },
  mounted() {

    this.fetchProductAttributes();
    this.fetchProductRbt();
    this.fetchAllProductRbt();
  },
  methods: {
    setDefaultFilter() {
      return  {
        limit: 25,
        page: 1,
        sku: null,
        style: null,
        color: null,
        size: null,
        product_id: null,
      }
    },
    closeDialog() {
      this.productSelected.is_discontinued = !this.productSelected.is_discontinued;
    },
    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.fetchProductRbt();
    },
    onFilter() {
      this.filter.page = 1;
      this.fetchProductRbt();
    },
    async confirm(product){
      this.productSelected = product;
      this.isLoading = true;
      try {
        const res = await updateProduct({id: this.productSelected.id, product_id: this.productSelected.product_id, is_active: this.productSelected?.is_active});
        this.notification(res.data.message, 'success');
      } catch (error) {
        this.notification('Update error.', 'error');
      }
      this.visible = null;
      await this.fetchProductRbt();
      this.isLoading = false;
    },
    async fetchProductRbt() {
      const { data } = await list(this.filter)
      this.products = data.data;
      this.totalProduct = data.total;
    },
    async fetchAllProductRbt() {
      const { data } = await list({ ...this.filter, is_get_all: true})
      this.allProducts = data;
    },
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchProductRbt();
      });
    },
    async fetchProductAttributes() {
      const res = await getProductAttributes();
      const data = res.data || '';
      let styles = data && Object.keys(data);
      styles =
          styles.length &&
          styles.map((item) => {
            return {
              label: item,
              value: item,
            };
          });
      this.styles = styles;
      this.attributes = data;
    },
    async selectStyle() {
      this.filter.sku = null;
      this.filter.product_id = null;

      this.filter.color = '';
      if (!this.filter.style) {
        this.colors = [];
        this.sizes = [];
        return;
      }
      const currentStyle = this.attributes[this.filter.style];
      let colors = currentStyle.colors;
      colors =
          colors.length &&
          colors.map((item) => {
            return {
              label: item,
              value: item,
            };
          });
      this.colors = colors;

      let sizes = currentStyle.sizes;
      sizes =
          sizes.length &&
          sizes.map((item) => {
            return {
              label: item,
              value: item,
            };
          });
      this.sizes = sizes;
    },
    async selectColors() {
      this.filter.sku = null
      this.filter.product_id = null;
      if (!this.filter.style || !this.filter.color || !this.filter.size) {
        return
      }
      this.filter.sku = data?.sku
      this.filter.product_id = data?.id
    },
    async selectSizes() {
      this.filter.sku = null
      this.filter.product_id = null;
      if (!this.filter.style || !this.filter.color || !this.filter.size) {
        return
      }
      const { data } = await getProductByParams(this.filter)
      this.filter.sku = data?.sku
      this.filter.product_id = data?.id

    },
    generateStatus(data) {
      return ;
      data.forEach((item) => {
        this.selectedList.forEach((i) => {
          if (i.value == item.sku) {
            item.status = i.status;
          }
        });
      });
      return data;
    },
    async getProductBySku() {
      this.filter.sku = this.filter.sku?.trim();
      if (!this.filter.sku) return;
      const response = await fetchProductByAttribute({sku: this.filter.sku });
      if (response?.data && response?.data?.length > 0) {
        this.filter.style = response?.data[0]?.style;
        this.filter.color = response?.data[0]?.color;
        this.filter.size = response?.data[0]?.size;
        this.filter.product_id = response?.data[0]?.id;

      }
    },
    createProduct() {
      EventBus.$emit("showCreateProductRbt");
    },
  },
};
