<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <el-dialog
      v-model="dialogVisible"
      destroy-on-close
      :title="$t('Create Product Size')"
      custom-class="el-dialog-custom"
      :destroy-on-close="true"
  >
    <el-form
        status-icon
        ref="createProductSize"
        :model="data"
        @submit.prevent="onSubmit"
        :label-position="'top'"
    >
      <el-form-item :label="$t('Name')" :class="{'is-error': isError('name')}" required>
        <el-input v-model="data.name" @keyup.enter="onSubmit"></el-input>
        <div v-if="isError('name')" class="el-form-item__error">{{getErrorMessage('name')}}</div>
      </el-form-item>
      <el-form-item :label="$t('Sku')" :class="{'is-error': isError('sku')}" required>
        <el-input v-model="data.sku" @keyup.enter="onSubmit"></el-input>
        <div v-if="isError('sku')" class="el-form-item__error">{{getErrorMessage('sku')}}</div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button
          type="primary"
          @click="onSubmit"
          :disabled="isLoading"
          :loading="isLoading"
      >{{ $t('Create') }}</el-button>
    </template>
  </el-dialog>
</template>
