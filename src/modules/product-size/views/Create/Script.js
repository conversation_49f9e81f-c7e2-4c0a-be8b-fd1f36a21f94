import EventBus from "@/utilities/eventBus";
import { create } from "@/api/productSize";

export default {
    name: "CreateProductSize",
    data() {
        return {
            dialogVisible: false,
            data: this.setDefaultData(),
            serverErrors: [],
            isLoading: false
        }
    },
    created() {
        EventBus.$on("showCreateProductSize", () => {
            this.data = this.setDefaultData();
            this.serverErrors = [];
            this.dialogVisible = true;
        });
    },
    methods: {
        isError(field) {
            return !!this.serverErrors[field];
        },
        getErrorMessage(field) {
            return this.serverErrors[field][0];
        },
        setDefaultData() {
            return {
                name: "",
                sku: ""
            };
        },
        async onSubmit() {
            if (this.isLoading) return;
            const isValid = await this.$refs.createProductSize.validate();
            if (!isValid) {
                return;
            }
            this.isLoading = true;
            try {
                const res = await create(this.data);
                this.dialogVisible = false;
                this.notification(res.data.message);
                this.$emit("refresh");
            } catch (e) {
                this.serverErrors = e.response.data.errors;
                let message = e.response.data.message;
                this.notification(message, "error");
            } finally {
                this.isLoading = false;
            }
        },
    }
}
