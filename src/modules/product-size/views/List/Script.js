import { getList } from "@/api/productSize";
import EventBus from "@/utilities/eventBus";
import CreateProductSize from "@/modules/product-size/views/Create/Index.vue";
import UpdateProductSize from "@/modules/product-size/views/Update/Index.vue";

export default {
    name: "ProductSizeList",
    components: {
        CreateProductSize,
        UpdateProductSize
    },
    data() {
        return {
            items: [],
            isLoading: false,
            filter: this.setDefaultFilter(),
        }
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 189);
        }
    },
    mounted() {
        this.filter = this.getRouteParam();
        this.getProductSizeList();
    },
    methods: {
        setDefaultFilter() {
            return {
                limit: 25,
                page: 1
            };
        },
        async getProductSizeList() {
            this.isLoading = true;
            this.setRouteParam();
            const { data } = await getList(this.filter);
            this.items = data.data;
            this.total = data.total;
            this.isLoading = false;
        },
        changePage(page) {
            this.filter.page = page;
            this.getProductSizeList();
        },
        onFilter() {
            this.filter.page = 1;
            this.getProductSizeList();
        },
        createProductSize() {
            EventBus.$emit("showCreateProductSize");
        },
        updateProductSize(item) {
            EventBus.$emit("showUpdateProductSize", item);
        }
    }
}