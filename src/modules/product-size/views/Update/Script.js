import EventBus from "@/utilities/eventBus";
import { update } from "@/api/productSize";

export default {
    name: "UpdateProductSize",
    data() {
        return {
            productSizeId: '',
            dialogVisible: false,
            data: this.setDefaultData(),
            serverErrors: [],
            isLoading: false
        }
    },
    created() {
        EventBus.$on("showUpdateProductSize", (item) => {
            this.productSizeId = item.id;
            this.data = Object.assign(this.data, item);
            this.serverErrors = [];
            this.dialogVisible = true;
        });
    },
    methods: {
        isError(field) {
            return !!this.serverErrors[field];
        },
        getErrorMessage(field) {
            return this.serverErrors[field][0];
        },
        setDefaultData() {
            return {
                name: "",
                sku: ""
            };
        },
        async onSubmit() {
            if (this.isLoading) return;
            const isValid = await this.$refs.updateProductSize.validate();
            if (!isValid) {
                return;
            }
            this.isLoading = true;
            try {
                const res = await update(this.productSizeId, this.data);
                this.dialogVisible = false;
                this.notification(res.data.message);
                this.$emit("refresh");
            } catch (e) {
                this.serverErrors = e.response.data.errors;
                let message = e.response.data.message;
                this.notification(message, "error");
            } finally {
                this.isLoading = false;
            }
        },
    }
}
