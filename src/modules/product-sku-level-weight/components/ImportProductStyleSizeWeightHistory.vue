<template>
  <div class="">
    <el-dialog
      v-model="openDialogImportReroute"
      custom-class="el-dialog-custom el-dialog-custom2 el-dialog-800"
      title="History"
      :close-on-click-modal="false"
      width="80%"
      @close="closeModal"
    >
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column :label="$t('File name')" width="300">
          <template #default="scope">
            {{ scope.row?.original_file_name }}
          </template>
        </el-table-column>

        <el-table-column :label="$t('User')" width="180">
          <template #default="scope">
            {{ scope.row?.user_name }}
          </template>
        </el-table-column>

        <el-table-column :label="$t('Date')" width="180">
          <template #default="scope">
            {{ utcToLocalTime(scope.row.created_at).format('lll') }}
          </template>
        </el-table-column>

        <el-table-column label="Action">
          <template #default="scope">
            <el-link :href="scope.row?.s3_file_url" type="primary" :underline="false">Download</el-link>
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom flex justify-center mt-3">
        <el-pagination :disabled="isLoading" background layout="prev, pager, next" :page-size="filter.limit" :total="total" @current-change="changePage" v-model:currentPage="filter.page">
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EventBus from '@/utilities/eventBus.js';
import { getImportHistory } from '@/api/productStyleSizeWeight';

export default {
  name: 'ImportProductStyleSizeWeightHistory',
  data() {
    return {
      openDialogImportReroute: false,
      file: [],
      data: {},
      step: 1,
      isLoading: false,
      uploadFileRaw: '',
      activeName: 'import',
      tableData: [],
      filter: this.setDefaultFilter(),
    };
  },
  created() {
    EventBus.$on('showModalImportHistory', async () => {
      await this.getUserImportHistory();
      this.activeName = 'import';
      this.openDialogImportReroute = true;
      this.focusByElClass();
    });
  },
  beforeUnmount() {
    EventBus.$off('showModalImportHistory');
  },
  methods: {
    setDefaultFilter() {
      return {
        limit: 10,
        page: 1,
      };
    },
    changePage(page) {
      this.filter.page = page;
      this.getUserImportHistory();
    },
    async getUserImportHistory() {
      const res = await getImportHistory(this.filter);
      this.tableData = res.data.data;
      this.total = res.data.total;
    },
  },
};
</script>
