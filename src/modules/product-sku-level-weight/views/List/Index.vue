<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
    <div>
        <div class="top-head mb-4">
            <div class="top-head-left">
                <h1>{{ $t('Product Type Weight') }}</h1>
            </div>
            <div class="top-head-right">
                <el-button @click="showModalImportHistory" :style="{ color: '#1a73e8'}">
                    <span class="icon-margin-right">
                        <icon :data="iconHistory" />
                    </span>
                    {{ $t('History') }}
                </el-button>
                <el-button type="primary" @click="showModalImportProductSpec">
                    <span class="icon-margin-right">
                        <icon :data="iconAdd" />
                    </span>
                    {{ $t('Import') }}
                </el-button>
            </div>
        </div>
        <div class="table-content">
            <div class="filter-top">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <p>Style</p>
                        <el-select
                            v-model="filter.style"
                            filterable
                            class="w-full"
                            :placeholder="$t('Product Style')"
                            @change="getProductTypeWeightList"
                            clearable
                            remote
                            :remote-method="debounceFilterStyles"
                        >
                            <el-option
                                v-for="item in stylesFiltered"
                                :key="item.name"
                                :label="item.name"
                                :value="item.name"
                            />
                        </el-select>
                    </el-col>
                    <el-col :span="6">
                        <p>Size</p>
                        <el-select
                            v-model="filter.size" filterable
                            class="w-full"
                            :placeholder="$t('Product Size')"
                            @change="getProductTypeWeightList"
                            clearable
                        >
                            <el-option
                                v-for="item in productSizes"
                                :key="item.id"
                                :label="item.name"
                                :value="item.name"
                            />
                        </el-select>
                    </el-col>
                    <div class="el-col-6 pt-[1.8rem]">
                        <el-link v-if="hasFilter" type="danger" @click="resetFilter" :underline="false">
                            {{$t('Clear')}}
                        </el-link>
                    </div>
                    <div class="el-col-6 flex justify-end pr-[0.6rem] pt-[1.6rem]">
                        <el-radio-group v-model="filter.weight_unit" size="medium" @change="getProductTypeWeightList">
                            <el-radio-button label="oz" value="oz" />
                            <el-radio-button label="lb" value="lb" />
                        </el-radio-group>
                    </div>

                </el-row>
            </div>
            <el-table border stripe size="small" :data="items" :max-height="maxHeight" v-loading="isLoading" element-loading-text="Loading...">
                <el-table-column prop="id" :label="$t('ID')" min-width="100"></el-table-column>
                <el-table-column prop="product_style.product_type.name" :label="$t('Product type')" min-width="200"></el-table-column>
                <el-table-column prop="product_style.name" :label="$t('Style')" min-width="200"></el-table-column>
                <el-table-column prop="product_size.name" :label="$t('Size')" min-width="200"></el-table-column>
                <el-table-column prop="single" :label="$t('Single')" min-width="200"></el-table-column>
                <el-table-column prop="multiple" :label="$t('Multiple')" min-width="200"></el-table-column>
            </el-table>
            <div class="bottom">
                <div class="total">{{$t('Total:')}} {{ items.length ? formatNumber(total) : 0 }}</div>
                <el-pagination :disabled="isLoading" background layout="prev, pager, next" :page-size="filter.limit" :total="total" @current-change="changePage" v-model:currentPage="filter.page">
                </el-pagination>
                <div class="limit" :disabled="isLoading">
                    <el-select v-model="filter.limit" :placeholder="$t('Select')" size="mini" @change="onFilter">
                        <el-option v-for="item in limits" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </div>
            </div>
        </div>
    </div>
    <ImportProductStyleSizeWeight @refresh="getProductTypeWeightList" />
    <ImportProductStyleSizeWeightHistory />
</template>