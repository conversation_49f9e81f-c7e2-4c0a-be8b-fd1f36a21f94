import {
  getList,
  fetchAllStyles,
  fetchAllSizes,
} from '@/api/productStyleSizeWeight';
import EventBus from "@/utilities/eventBus";
import ImportProductStyleSizeWeight from '@/modules/product-sku-level-weight/components/ImportProductStyleSizeWeight.vue';
import ImportProductStyleSizeWeightHistory from '@/modules/product-sku-level-weight/components/ImportProductStyleSizeWeightHistory.vue';
import { equals } from 'ramda';
import { debounce } from '@/utilities/helper.js';

export default {
  name: 'ProductSkuLevelWeightList',
  components: {
    ImportProductStyleSizeWeight,
    ImportProductStyleSizeWeightHistory,
  },
  data() {
    return {
      items: [],
      productStyles: [],
      productSizes: [],
      isLoading: false,
      filter: this.setDefaultFilter(),
      styleFilterKey: '',
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 245);
    },
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },
    stylesFiltered() {
      if (this.styleFilterKey) {
        return this.productStyles.filter((item) => {
          return String(item.name)
            .toLowerCase()
            .includes(this.styleFilterKey.toLowerCase());
        });
      } else {
        return this.productStyles;
      }
    },
  },
  mounted() {
    this.filter = this.getRouteParam();
    this.getProductTypeWeightList();
    this.getProductStyles();
    this.getProductSizes();
  },
  methods: {
    debounceFilterStyles: debounce(async function (query) {
      this.styleFilterKey = query;
    }, 100),
    setDefaultFilter() {
      return {
        limit: 25,
        page: 1,
        size: '',
        style: '',
        weight_unit: 'oz',
      };
    },
    async getProductStyles() {
      const res = await fetchAllStyles();
      console.log(res?.data);
      this.productStyles = res?.data ?? [];
    },
    async getProductSizes() {
      const res = await fetchAllSizes();
      this.productSizes = res?.data ?? [];
    },
    async getProductTypeWeightList() {
      this.isLoading = true;
      this.setRouteParam();
      const { data } = await getList(this.filter);
      this.items = data.data;
      this.total = data.total;
      this.isLoading = false;
      this.styleFilterKey = '';
    },
    changePage(page) {
      this.filter.page = page;
      this.getProductTypeWeightList();
    },
    onFilter() {
      this.filter.page = 1;
      this.getProductTypeWeightList();
    },
    resetFilter() {
      this.filter = this.setDefaultFilter();
      this.getProductTypeWeightList();
    },
    showModalImportProductSpec() {
      console.log('showModalImportSpec');
      EventBus.$emit('showModalImportSpec');
    },
    showModalImportHistory() {
      EventBus.$emit('showModalImportHistory');
    },
  },
};