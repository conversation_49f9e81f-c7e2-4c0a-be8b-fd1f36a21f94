<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ $t('Product SKU Matching') }}</h1>
      </div>
      <div class="top-head-right">
        <el-button type="primary" @click="showModalImportProductSpec">
          {{ $t('Import') }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <div class="filter flex-row flex-nowrap gap-3">
        <el-input
          :placeholder="$t('Attribute String')"
          class="search"
          v-model="filter.zazzle_attribute"
        />
        <el-input
          :placeholder="$t('Swiftpod SKU')"
          class="search el-input-sku"
          v-model="filter.swiftpod_sku"
          @keyup.enter="autoFillProduct"
        />
        <el-select
          :placeholder="$t('Product style')"
          class="brand"
          v-model="product.style"
          filterable
          @change="selectStyle"
        >
          <el-option
            v-for="(style, index) in styleOptions"
            :key="index"
            :label="style.label"
            :value="style.value"
          >
          </el-option>
        </el-select>
        <el-select
          :placeholder="$t('Product Color')"
          class="brand"
          v-model="product.color"
          filterable
          @change="changeProductAttribute"
        >
          <el-option
            v-for="(color, index) in colorOptions"
            :key="index"
            :label="color.label"
            :value="color.value"
          >
          </el-option>
        </el-select>
        <el-select
          :placeholder="$t('Product size')"
          v-model="product.size"
          @change="changeProductAttribute"
        >
          <el-option
            v-for="(size, index) in sizeOptions"
            :key="index"
            :label="size.label"
            :value="size.value"
          >
          </el-option>
        </el-select>
        <div class="btn-filter flex-row gap-3">
          <el-button type="primary" @click="onFilter">
            {{ $t('Search') }}
          </el-button>
          <el-button @click="onClearFilter">
            {{ $t('Reset') }}
          </el-button>
        </div>
      </div>
      <div class="list-page">
        <el-table
          border
          stripe
          size="small"
          :data="products"
          class="table-cus"
          style="width: 100%"
          :max-height="maxHeight"
          v-loading="isLoading"
          element-loading-text="Loading..."
        >
          <el-table-column :label="$t('Attribute String')" min-width="160">
            <template #default="scope">
              {{ scope.row?.zazzle_attribute || scope.row?.attribute }}
            </template>
          </el-table-column>

          <el-table-column :label="$t('store name')" min-width="160">
            <template #default="scope">
              {{ scope.row?.store?.name }}
            </template>
          </el-table-column>
          <el-table-column
            prop="swiftpod_sku"
            :label="$t('Swiftpod SKU')"
            min-width="160"
          >
          </el-table-column>
          <el-table-column :label="$t('Product Name')" min-width="160">
            <template #default="scope">
              {{ scope.row?.product?.name }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('Product Style')" min-width="160">
            <template #default="scope">
              {{ scope.row?.product?.style }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('Product Color')" min-width="160">
            <template #default="scope">
              {{ scope.row?.product?.color }}
            </template>
          </el-table-column>
          <el-table-column
            prop="description"
            :label="$t('Product Size')"
            min-width="160"
          >
            <template #default="scope">
              {{ scope.row?.product?.size }}
            </template>
          </el-table-column>
          <el-table-column
            prop="action"
            fixed="right"
            :label="$t('Action')"
            width="80"
            align="center"
          >
            <template #default="scope">
              <el-popconfirm
                :title="'Are you sure to delete ' + scope.row.name + '?'"
                @confirm="deleteProduct(scope.row)"
              >
                <template #reference>
                  <el-link :underline="false" type="danger"
                    ><icon :data="iconDelete"
                  /></el-link>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>

          <!-- <el-table-column prop="action" :label="$t('Action')" width="80">
            <template #default="scope">
              <el-popconfirm
                :title="'Are you sure to delete ' + scope.row.name + '?'"
                @confirm="deleteProduct(scope.row)"
              >
                <template #reference>
                  <el-link :underline="false" type="danger"
                    ><icon :data="iconDelete"
                  /></el-link>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column> -->
        </el-table>
      </div>
      <div class="bottom">
        <div class="total">
          {{ $t('Total:') }}
          {{ products.length ? formatNumber(totalProduct) : 0 }}
        </div>
        <el-pagination
          :disabled="isLoading"
          background
          layout="prev, pager, next"
          :page-size="filter.limit"
          :total="totalProduct"
          @current-change="changePage"
          v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
            v-model="filter.limit"
            :placeholder="$t('Select')"
            size="mini"
            @change="onFilter"
          >
            <el-option
              v-for="item in limits"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
  </div>
  <ImportProductSKUMatching @refresh="fetchProduct" />
  <!-- <ProductSpec /> -->
</template>
