import { list, destroy } from '@/api/productSkuMatching.js';
import EventBus from '@/utilities/eventBus.js';
import filterMixin from '@/mixins/filter';
import ImportProductSKUMatching from '@/modules/product-sku-matching/components/ImportProductSKUMatching.vue';
import {
  getProductAttributes,
  getProductByParams,
  getProductBySku,
} from '@/api/product';

export default {
  name: 'ProductSKUMatching',
  components: {
    ImportProductSKUMatching,
  },
  mixins: [filterMixin],
  data() {
    return {
      products: [],
      filter: this.setDefaultFilter(),
      isLoading: false,
      attributes: {},
      styleOptions: [],
      colorOptions: [],
      sizeOptions: [],
      product: {
        style: '',
        size: '',
        color: '',
      },
    };
  },
  created() {
    this.fetchProductAttribute();
  },
  watch: {
    product: {
      handler() {
        if (!this.product.style || !this.product.color || !this.product.size)
          return;
        this.getProductSKUByParams();
      },
      deep: true,
    },
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 288);
    },
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    changeProductAttribute() {
      if (!this.product.style || !this.product.color || !this.product.size)
        return;
      this.getProductSKUByParams();
    },
    async getProductSKUByParams() {
      try {
        const res = await getProductByParams(this.product);
        const product = res.data ? res.data : {};
        if (!product || !product.sku) {
          return;
        }
        this.filter.swiftpod_sku = product.sku;
        this.focusByElClass('el-input-sku');
      } catch (e) {}
    },
    async autoFillProduct() {
      try {
        const res = await getProductBySku({
          sku: this.filter.swiftpod_sku,
        });
        const data = res?.data || {};
        if (data.id) {
          this.product.style = data.style;
          this.selectStyle();
          this.product.color = data.color;
          this.product.size = data.size;
        }
        this.focusByElClass('el-input-sku');
      } catch (e) {}
    },
    selectStyle() {
      this.filter.swiftpod_sku = '';
      this.product.size = '';
      this.product.color = '';
      if (!this.product.style) {
        this.colorOptions = [];
        this.sizeOptions = [];
        return;
      }
      const currentStyle = this.attributes[this.product.style];
      let colors = currentStyle.colors;
      colors =
        colors.length &&
        colors.map((item) => {
          return {
            label: item,
            value: item,
          };
        });
      let sizes = currentStyle.sizes;
      sizes =
        sizes.length &&
        sizes.map((item) => {
          return {
            label: item,
            value: item,
          };
        });
      this.colorOptions = colors;
      this.sizeOptions = sizes;
    },
    focusByElClass(elClass = 'el-input-sku') {
      this.$nextTick(() => {
        const el = document.getElementsByClassName(elClass);
        if (!el || !el.length) {
          return;
        }
        const firtsElError = (el && el[0]) || undefined;
        const input = firtsElError.querySelector('input');
        document.body.scrollTop = (input.offsetTop - 120) | 0;
        input.focus();
      });
    },
    async fetchProductAttribute() {
      const res = await getProductAttributes();
      const data = res.data || '';
      let styles = data && Object.keys(data);
      styles =
        styles.length &&
        styles.map((item) => {
          return {
            label: item,
            value: item,
          };
        });
      this.styleOptions = styles;
      this.attributes = data;
    },
    async fetchData() {
      this.filter = this.getRouteParam();
      this.fetchProduct();
    },
    onFilter() {
      this.filter.page = 1;
      this.$nextTick(() => {
        this.fetchProduct();
      });
    },
    onClearFilter() {
      this.resetProductProperties();
      this.filter = this.setDefaultFilter();
      this.$nextTick(() => {
        this.fetchProduct();
      });
    },
    resetProductProperties() {
      this.product = {
        style: '',
        size: '',
        color: '',
      };
      return this.product;
    },
    setDefaultFilter() {
      let params = {
        limit: 25,
        page: 1,
        zazzle_attribute: '',
        swiftpod_sku: '',
      };
      return params;
    },
    setRouteParam() {
      const params = this.filter;
      let routeName = 'product_sku_matching';
      this.$router.replace({ name: routeName, query: params });
    },
    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);
      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }
      filter.page = +filter.page || 1;
      return filter;
    },
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchProduct();
      });
    },
    async fetchProduct() {
      this.isLoading = true;
      this.setRouteParam();
      const res = await list({...this.filter, ...this.product});
      this.isLoading = false;
      const data = res.data || [];
      this.totalProduct = data.total || 1;
      this.products = data.data || [];
    },
    async deleteProduct(product) {
      await destroy(product.id);
      this.notification(this.$t('Delete item successfully.'));
      this.onFilter();
    },
    showModalImportProductSpec() {
      EventBus.$emit('showModalImportSpec');
    },
  },
};
