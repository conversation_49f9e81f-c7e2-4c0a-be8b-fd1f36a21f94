<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
    <el-dialog v-model="dialogVisible" destroy-on-close :title="$t('Create Product Style')"
        custom-class="el-dialog-custom" :destroy-on-close="true">
        <el-form status-icon ref="createProductStyle" :model="data" @submit.prevent="onSubmit" :label-position="'top'">
            <el-form-item :label="$t('Name')" :class="{ 'is-error': isError('name') }" required>
                <el-input v-model="data.name" @keyup.enter="onSubmit"></el-input>
                <div v-if="isError('name')" class="el-form-item__error">
                    {{ getErrorMessage('name') }}
                </div>
            </el-form-item>

            <el-form-item :label="$t('Sku')" :class="{ 'is-error': isError('sku') }" required>
                <el-input v-model="data.sku" @keyup.enter="onSubmit"></el-input>
                <div v-if="isError('sku')" class="el-form-item__error">
                    {{ getErrorMessage('sku') }}
                </div>
            </el-form-item>

            <el-form-item :label="$t('Description')" :class="{ 'is-error': isError('description') }" required>
                <el-input v-model="data.description" @keyup.enter="onSubmit"></el-input>
                <div v-if="isError('description')" class="el-form-item__error">
                    {{ getErrorMessage('description') }}
                </div>
            </el-form-item>

            <el-form-item :label="$t('Type')" :class="{ 'is-error': isError('type') }" required>
                <el-select v-model="data.type" filterable allow-create :placeholder="$t('Select type')" class="w-full">
                    <el-option v-for="type in productTypes" :key="type.id" :label="type.name" :value="type.name" />
                </el-select>
                <div v-if="isError('type')" class="el-form-item__error">
                    {{ getErrorMessage('type') }}
                </div>
            </el-form-item>

            <el-form-item :label="$t('Sleeve length')" :class="{ 'is-error': isError('sleeve_length') }">
                <el-input v-model="data.sleeve_length" @keyup.enter="onSubmit"></el-input>
                <div v-if="isError('sleeve_length')" class="el-form-item__error">
                    {{ getErrorMessage('sleeve_length') }}
                </div>
            </el-form-item>

            <el-form-item :label="$t('Gender')" :class="{ 'is-error': isError('gender') }">
                <el-select v-model="data.gender" filterable allow-create :placeholder="$t('Select gender')"
                    class="w-full">
                    <el-option key="1" label="all" value="all" />
                    <el-option key="2" label="men" value="men" />
                    <el-option key="3" label="women" value="women" />
                    <el-option key="4" label="kid" value="kid" />
                    <el-option key="5" label="baby" value="baby" />
                </el-select>
                <div v-if="isError('gender')" class="el-form-item__error">{{ getErrorMessage('gender') }}</div>
            </el-form-item>
            <el-form-item :label="$t('Print Method')" :class="{ 'is-error': isError('print_method') }" required>
                <el-select v-model="data.print_method" filterable allow-create :placeholder="$t('Select print method')"
                    class="w-full">
                    <el-option v-for="(method, index) in printMethods" :key="method.name" :label="method.name"
                        :value="method.name" />
                </el-select>
                <div v-if="isError('print_method')" class="el-form-item__error">
                    {{ getErrorMessage('print_method') }}
                </div>
            </el-form-item>
            <el-form-item prop="icc_white_convert_status">
                <el-switch style="display: block" v-model="data.icc_white_convert_status" active-color="#13ce66"
                    inactive-color="#ff4949" :active-value="1" :inactive-value="0"
                    :active-text="$t('Convert into Profiled Artwork to ensure matching with White Garments.')">
                </el-switch>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button type="primary" @click="onSubmit" :disabled="isLoading" :loading="isLoading">
                {{ $t('Create') }}
            </el-button>
        </template>
    </el-dialog>
</template>