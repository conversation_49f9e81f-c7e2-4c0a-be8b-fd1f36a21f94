import EventBus from "@/utilities/eventBus";
import {create} from "@/api/productStyle";

export default {
    name: "CreateProductStyle",
    props: {
        productTypes: {
            type: Array,
            default: [],
        },
        printMethods: {
            type: Array,
            default: [],
        },
    },
    data() {
        return {
            dialogVisible: false,
            data: this.setDefaultData(),
            serverErrors: [],
            isLoading: false
        }
    },
    created() {
        EventBus.$on("showCreateProductStyle", () => {
            this.data = this.setDefaultData();
            this.serverErrors = [];
            this.dialogVisible = true;
        });
    },
    methods: {
        isError(field) {
            return !!this.serverErrors[field];
        },
        getErrorMessage(field) {
            return this.serverErrors[field][0];
        },
        setDefaultData() {
            return {
                name: "",
                sku: "",
                type: "",
                sleeve_length: "",
                gender: "",
                print_method: "",
                description: ""
            };
        },
        async onSubmit() {
            if (this.isLoading) return;
            const isValid = await this.$refs.createProductStyle.validate();
            if (!isValid) {
                return;
            }
            this.isLoading = true;
            try {
                const res = await create(this.data);
                this.dialogVisible = false;
                this.notification(res.data.message);
                this.$emit("refresh");
            } catch (e) {
                this.serverErrors = e.response.data.errors;
                let message = e.response.data.message;
                this.notification(message, "error");
            } finally {
                this.isLoading = false;
            }
        },
    }
}
