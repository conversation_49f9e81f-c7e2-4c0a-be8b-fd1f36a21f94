<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
    <div>
        <div class="top-head mb-4">
            <div class="top-head-left">
                <h1>{{ $t('Product Style') }}</h1>
            </div>
            <div class="top-head-right">
                <el-button type="primary" @click="createProductStyle">
                    <span class="icon-margin-right">
                        <icon :data="iconAdd" />
                    </span>{{ $t('Create') }}
                </el-button>
            </div>
        </div>

        <div class="table-content">
            <div class="filter-top">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-input :placeholder="$t('Name')" v-model="filter.name" @keyup.enter="onFilter" />
                    </el-col>
                    <el-col :span="6">
                        <el-input :placeholder="$t('Sku')" v-model="filter.sku" @keyup.enter="onFilter" />
                    </el-col>
                    <el-col :span="6">
                        <el-select v-model="filter.type" filterable class="w-full" :placeholder="$t('Type')">
                            <el-option key="1" label="Empty" value="empty" />
                            <el-option v-for="type in productTypes" :key="type.id" :label="type.name"
                                :value="type.name" />
                        </el-select>
                    </el-col>
                    <el-col :span="6">
                        <div class="btn-filter">
                            <el-button type="primary" @click="onFilter">
                                <span class="icon-margin-right">
                                    <icon :data="iconFilter" />
                                </span>{{ $t("Filter") }}
                            </el-button>
                            <el-button @click="resetFilter">
                                <span class="icon-margin-right">{{ $t("Reset") }}</span>
                            </el-button>
                        </div>
                    </el-col>
                </el-row>
            </div>

            <el-table border stripe size="small" :data="items" :max-height="maxHeight" v-loading="isLoading"
                element-loading-text="Loading...">
                <el-table-column prop="id" :label="$t('ID')" min-width="80"></el-table-column>
                <el-table-column prop="name" :label="$t('Name')" min-width="200"></el-table-column>
                <el-table-column prop="sku" :label="$t('Sku')" min-width="80"></el-table-column>
                <el-table-column prop="description" :label="$t('Description')" min-width="300"></el-table-column>
                <el-table-column prop="type" :label="$t('Type')" min-width="100"></el-table-column>
                <el-table-column prop="sleeve_length" :label="$t('Sleeve Length')" min-width="100"></el-table-column>
                <el-table-column prop="gender" :label="$t('Gender')" min-width="100"></el-table-column>
                <el-table-column prop="print_method" :label="$t('Print Method')" min-width="100"></el-table-column>
                <el-table-column prop="icc_white_convert_status" :label="$t('Convert to Profiled Art')" min-width="150">
                    <template #default="scope">
                        <el-tag v-if="scope.row?.icc_white_convert_status" type="success">ON</el-tag>
                        <el-tag v-else type="danger">OFF</el-tag>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('Date')" min-width="200">
                    <template #default="scope">
                        {{ listViewDateFormat(scope.row.created_at) }}
                    </template>
                </el-table-column>
                <el-table-column prop="action" :label="$t('Action')" fixed="right" width="100">
                    <template #default="scope">
                        <el-link class="el-link-edit" :underline="false" type="primary"
                            @click="updateProductStyle(scope.row)">
                            <icon :data="iconEdit" />
                        </el-link>
                    </template>
                </el-table-column>
            </el-table>
            <div class="bottom">
                <div class="total">{{ $t('Total:') }} {{ items.length ? formatNumber(total) : 0 }}</div>
                <el-pagination :disabled="isLoading" background layout="prev, pager, next" :page-size="filter.limit"
                    :total="total" @current-change="changePage" v-model:currentPage="filter.page">
                </el-pagination>
                <div class="limit" :disabled="isLoading">
                    <el-select v-model="filter.limit" :placeholder="$t('Select')" size="mini" @change="onFilter">
                        <el-option v-for="item in limits" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </div>
            </div>
        </div>
    </div>

    <create-product-style :printMethods="printMethods" :productTypes="productTypes" @refresh="getProductStyleList" />
    <update-product-style :productTypes="productTypes" @refresh="getProductStyleList" />
</template>