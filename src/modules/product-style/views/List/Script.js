import {getList, getPrintMethod, getTypes} from "@/api/productStyle";
import EventBus from "@/utilities/eventBus";
import CreateProductStyle from "@/modules/product-style/views/Create/Index.vue";
import UpdateProductStyle from "@/modules/product-style/views/Update/Index.vue";

export default {
    name: "ProductStyleList",
    components: {
        CreateProductStyle,
        UpdateProductStyle
    },
    data() {
        return {
            items: [],
            productTypes: [],
            printMethods: [],
            isLoading: false,
            filter: this.setDefaultFilter(),
        }
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 245);
        }
    },
    mounted() {
        this.filter = this.getRouteParam();
        this.getProductStyleList();
        this.getTypes();
        this.getPrintMethod();
    },

    methods: {
        async getTypes(){
            const { data } = await getTypes();
            this.productTypes = data || [];
        },
        async getPrintMethod(){
            const { data } = await getPrintMethod();
            this.printMethods = data || [];
        },
        setDefaultFilter() {
            return {
                limit: 25,
                page: 1,
                name: "",
                sku: "",
                type: "",
                gender: "",
                print_method: ""
            };
        },
        async getProductStyleList() {
            this.isLoading = true;
            this.setRouteParam();
            const { data } = await getList(this.filter);
            this.items = data.data;
            this.total = data.total;
            this.isLoading = false;
        },
        changePage(page) {
            this.filter.page = page;
            this.getProductStyleList();
        },
        onFilter() {
            this.filter.page = 1;
            this.getProductStyleList();
        },
        createProductStyle() {
            EventBus.$emit("showCreateProductStyle");
        },
        updateProductStyle(item) {
            EventBus.$emit("showUpdateProductStyle", item);
        },
        resetFilter(){
            this.filter = this.setDefaultFilter()
            this.getProductStyleList()
        }
    }
}