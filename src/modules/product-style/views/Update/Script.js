import EventBus from "@/utilities/eventBus";
import {update} from "@/api/productStyle";
import {clone} from "ramda";

export default {
    name: "UpdateProductStyle",
    props: {
        productTypes: {
            type: Array,
            default: [],
        },
        printMethods: {
            type: Array,
            default: [],
        },
    },
    data() {
        return {
            productStyleId: '',
            dialogVisible: false,
            data: this.setDefaultData(),
            serverErrors: [],
            isLoading: false
        }
    },
    created() {
        EventBus.$on("showUpdateProductStyle", (item) => {
            let productStyle = clone(item);
            productStyle.type = this.getProductTypeName(productStyle.type)
            this.productStyleId = productStyle.id;
            this.data = Object.assign(this.data, productStyle);
            this.serverErrors = [];
            this.dialogVisible = true;
        });
    },
    methods: {
        getProductTypeName(name) {
            if(!name) return;
            return name.toLowerCase().replace(/(^|\s)\S/g, function(l) {
                return l.toUpperCase();
            });
        },
        isError(field) {
            return !!this.serverErrors[field];
        },
        getErrorMessage(field) {
            return this.serverErrors[field][0];
        },
        setDefaultData() {
            return {
                name: "",
                sku: "",
                type: "",
                gender: "",
                print_method: "",
                description: ""
            };
        },
        async onSubmit() {
            if (this.isLoading) return;
            const isValid = await this.$refs.updateProductStyle.validate();
            if (!isValid) {
                return;
            }
            this.isLoading = true;
            try {
                const res = await update(this.productStyleId, this.data);
                this.dialogVisible = false;
                this.notification(res.data.message);
                this.$emit("refresh");
            } catch (e) {
                this.serverErrors = e.response.data.errors;
                let message = e.response.data.message;
                this.notification(message, "error");
            } finally {
                this.isLoading = false;
            }
        },
    }
}
