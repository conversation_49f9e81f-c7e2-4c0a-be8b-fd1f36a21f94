<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
      v-model="openDialogAddProduct"
      :title="$t('Add New SKU')"
      custom-class="el-dialog-custom el-dialog-vendor"
      @close="closeDialog"
      :close-on-click-modal="false"
    >
      <template #default>
        <div class="bg-[#F0F4FB] p-[15px]">
          <el-select
              class="w-40 mr-2"
              v-model="product.style"
              @change="selectStyle"
              filterable
              :placeholder="$t('Choose style')"
          >
            <el-option
                v-for="item in styles"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
          <el-select
              class="w-40 mr-2"
              v-model="product.color"
              @change="selectColors"
              filterable
              :placeholder="$t('Choose color')"
          >
            <el-option
                v-for="item in colors"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
          <el-select
              class="w-40 mr-2"
              v-model="product.size"
              @change="selectSizes"
              filterable
              :placeholder="$t('Choose Size')"
          >
            <el-option
                v-for="item in sizes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
          <span class="mr-2">or</span>
          <el-input class="!w-40 mr-2" v-model="product.sku" placeholder="Enter SKU" @keyup.enter="getProductBySku" />
          <p
              class="text-[12px] text-red-400 self-center break-normal"
              v-if="errorInputSku"
          >
            {{ $t(errorInputSku) }}
          </p>
          <span class="float-right">Select All
          <el-switch class="!ml-2"
                    v-model="selectAll"
                    style="--el-switch-on-color: #13ce66"
          >
          </el-switch>
        </span>
          <el-divider class="!mb-1"></el-divider>

          <div v-if="filterLists.length">
            <div
                v-for="item in filterLists"
                class="item-select item-sku w-full mt-5"
            >
              <div class="flex justify-space-between">
                <div class="w-[200px]">
                  <div class="flex">
                    <span class="store-name flex w-[110px]">{{ item.sku }}</span>
                  </div>
                  <span class="account-name text-xs">{{
                      item.name
                    }}</span>
                </div>
                <el-switch
                    class=""
                    v-model="item.is_active"
                    style="--el-switch-on-color: #13ce66"
                />
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetData">{{ $t('Reset') }}</el-button>
          <el-button
            type="primary"
            @click="onSubmit()"
            :disabled="isLoading"
            :loading="isLoading"
            >{{ $t('Create') }}</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<style scoped lang="scss">
.d-inline-block {
  display: inline-block;
  margin-top: auto;
  margin-bottom: auto;
}
.item-select {
  display: inline-block;
  padding: 5px 10px;
  .account-name {
    display: flex;
    color: #999;
    line-height: 1;
  }
}
.item-select-disable {
  border: solid 1px #ccc;
  border-radius: 5px;
}
.item-sku {
  border-radius: 5px;
  background-color: white;
}
.el-select-dropdown__item {
  height: auto;
  line-height: 1.4;
}
</style>