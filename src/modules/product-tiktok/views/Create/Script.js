import EventBus from "@/utilities/eventBus.js";
import { add } from "@/api/productTiktok.js";
import {fetchProductByAttribute, fetchProducts, getProductByParams} from "@/api/product";

export default {
  name: "CreateProduct",
  components: {},
  data() {
    return {
      product: {
        sku: null,
        style: null,
        color: null,
        size: null,
        product_id: null,
        is_active: false,
        is_not_product_tiktok: true
      },
      selectedList: [],
      filterLists: [],
      errorInputSku: null,
      colors: [],
      sizes: [],
      openDialogAddProduct: false,
      isLoading: false,
      selectAll: false,
    };
  },
  props: {
    attributes: {
      type: Array,
      default: []
    },
    styles: {
      type: Array,
      default: []
    },
    allProducts: {
      type: Array,
      default: []
    }
  },
  watch: {
    selectAll(val) {
      console.log(val);
      this.filterLists.forEach(item => {
        item.is_active = val;
      });
    }
  },
  computed: {},
  mounted() {},
  created() {
    EventBus.$on("showCreateProduct", () => {
      this.openDialogAddProduct = true;
    });
  },
  methods: {

    async getProductBySku() {
      this.selectAll = false;
      this.product.sku = this.product.sku?.trim();
      if (!this.product.sku) return;
      const response = await fetchProductByAttribute({sku: this.product.sku });
      this.filterLists = response?.data ?? [];
      // this.filterLists = this.generateStatus(response?.data || []);
      if (response?.data && response?.data?.length > 0) {
        this.product.style = response?.data[0]?.style;
        this.product.color = response?.data[0]?.color;
        this.product.size = response?.data[0]?.size;
      }
    },
    async selectStyle() {
      this.product.color = '';
      this.product.sku = null
      this.selectAll = false;
      this.product.product_id = null;
      if (!this.product.style) {
        this.colors = [];
        this.sizes = [];
        return;
      }
      const currentStyle = this.attributes[this.product.style];
      let colors = currentStyle.colors;
      colors =
          colors.length &&
          colors.map((item) => {
            return {
              label: item,
              value: item,
            };
          });
      this.colors = colors;
      let sizes = currentStyle.sizes;
      sizes =
          sizes.length &&
          sizes.map((item) => {
            return {
              label: item,
              value: item,
            };
          });
      this.sizes = sizes;
      const { data } = await fetchProducts(this.product);
      // this.filterLists = this.generateStatus(data || []);
      this.filterLists = data;
      if (data.length == 1) {
        this.product.sku = data[0]?.sku
        this.product.product_id = data[0]?.id
      }
    },
    async selectColors() {
      this.product.sku = null;
      this.selectAll = false;
      this.product.product_id = null;
      const { data } = await fetchProducts(this.product);
      this.filterLists = data;
      // this.filterLists = this.generateStatus(data || []);
      if (data.length == 1) {
        this.product.sku = data[0]?.sku
        this.product.product_id = data[0]?.id
      }
    },
    async selectSizes() {
      this.product.sku = null
      this.product.product_id = null;
      this.selectAll = false;
      const { data } = await fetchProducts(this.product);
      this.filterLists = data;
      if (data.length == 1) {
        this.product.sku = data[0]?.sku
        this.product.product_id = data[0]?.id
      }


    },
    generateStatus(data) {
      data.forEach((item) => {
        this.allProducts.forEach((i) => {
          if (i.product_id == item.id && i.is_active) {
            item.is_active = true;
          }
        });
      });
      return data;
    },
    toggleSKU(item) {
      this.allProducts.forEach(i => {
        if (i.product_id == item.id) {
          if (i.is_active != item.is_active) {
            item.isUpdate = true;
          }
          return;
        } else {
          item.isCreate = true;
        }
      });
    },
    resetData() {
      this.product = {
        sku: null,
        style: null,
        color: null,
        size: null,
        product_id: null,
        is_active: false,
        is_not_product_tiktok: true
      };
      this.selectAll = false;
      this.filterLists = [];
    },
    closeDialog() {
      this.resetData();
      this.openDialogAddProduct = false;
    },
    async onSubmit() {
      this.isLoading = true;
      try {
        let params = this.filterLists.filter(item => item.is_active)
        let product_id = params.map(item => item.id);
        await add({ product_ids: product_id });
        this.isLoading = false;
        this.notification(this.$t('Created successfully.'));
        this.openDialogAddProduct = false;
        this.$emit("refresh");
      } catch (e) {
        this.isLoading = false;
        let message = e.message ? e.message : this.$t('Error.');
        this.notification(message, "error");
      }
    },
  },
};
