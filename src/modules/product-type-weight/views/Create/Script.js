import EventBus from "@/utilities/eventBus";
import { getTypes } from "@/api/productStyle";
import { getList as getListSize } from "@/api/productSize";
import { create } from "@/api/productTypeWeight";

export default {
    name: "CreateProductStyle",
    data() {
        return {
            dialogVisible: false,
            data: this.setDefaultData(),
            serverErrors: [],
            isLoading: false,
            productTypes: [],
            productSize: [],
            rules: {
                name: [ { required: true } ],
                size: [ { required: true } ],
                weight: [ { required: true } ],
                weight_unit: [ { required: true } ]
            }
        }
    },
    created() {
        EventBus.$on("showCreateProductStyle", () => {
            this.data = this.setDefaultData();
            this.serverErrors = [];
            this.dialogVisible = true;
        });
    },
    mounted() {
        this.getProductStyle();
        this.getProductSize();
    },
    methods: {
        isError(field) {
            return !!this.serverErrors[field];
        },
        getErrorMessage(field) {
            return this.serverErrors[field][0];
        },
        setDefaultData() {
            return {
                name: "",
                size: "",
                weight: 0,
                weight_unit: ""
            };
        },
        async getProductStyle(){
            const { data } = await getTypes();
            this.productTypes = data || [];
        },
        async getProductSize(){
            const { data } = await getListSize({ limit: 500 });
            this.productSize = data.data || [];
        },
        async onSubmit() {
            if (this.isLoading) return;
            const isValid = await this.$refs.createProductTypeWeight.validate();
            console.log(isValid)
            console.log(this.data)
            if (!isValid) {
                return;
            }
            this.isLoading = true;
            try {
                const res = await create(this.data);
                this.dialogVisible = false;
                this.notification(res.data.message);
                this.$emit("refresh");
            } catch (e) {
                let message = "Create not success";
                this.notification(message, "error");
                this.dialogVisible = false;
                this.isLoading = false;
            } finally {
                this.isLoading = false;
            }
        },
    }
}
