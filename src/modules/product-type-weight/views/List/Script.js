import { getList } from '@/api/productTypeWeight';
import EventBus from '@/utilities/eventBus';
import CreateProductStyle from '@/modules/product-type-weight/views/Create/Index.vue';
import UpdateProductStyle from '@/modules/product-type-weight/views/Update/Index.vue';

export default {
  name: 'ProductTypeWeightList',
  components: {
    CreateProductStyle,
    UpdateProductStyle,
  },
  data() {
    return {
      items: [],
      isLoading: false,
      filter: this.setDefaultFilter(),
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 245);
    },
  },
  mounted() {
    this.filter = this.getRouteParam();
    this.getProductTypeWeightList();
  },
  methods: {
    setDefaultFilter() {
      return {
        limit: 25,
        page: 1,
        name: '',
        size: '',
        unit: '',
      };
    },
    async getProductTypeWeightList() {
      this.isLoading = true;
      this.setRouteParam();
      const { data } = await getList(this.filter);
      this.items = data.data;
      this.total = data.total;
      this.isLoading = false;
    },
    changePage(page) {
      this.filter.page = page;
      this.getProductTypeWeightList();
    },
    onFilter() {
      this.filter.page = 1;
      this.getProductTypeWeightList();
    },
    createProductTypeWeight() {
      EventBus.$emit('showCreateProductStyle');
    },
    updateProductTypeWeight(item) {
      EventBus.$emit('showUpdateProductStyle', item);
    },
    resetFilter() {
      this.filter = this.setDefaultFilter();
      this.getProductTypeWeightList();
    },
  },
};
