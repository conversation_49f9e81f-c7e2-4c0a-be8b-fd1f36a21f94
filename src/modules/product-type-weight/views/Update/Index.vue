<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
    <div>
        <el-dialog
            v-model="dialogVisible"
            destroy-on-close
            :title="$t('Update Product Type Weight') + ` #${productSizeId}`"
            custom-class="el-dialog-custom"
            :destroy-on-close="true"
            >
            <el-form
                status-icon
                ref="updateProductTypeWeight"
                :model="data"
                @submit.prevent="onSubmit"
                :label-position="'top'"
                :rules="rules"
                >
                <el-form-item :label="$t('Name')" prop="name" class="w-full mr-2">
                    <el-select
                        v-model="data.name"
                        filterable
                        class="w-full"
                        :placeholder="$t('Select Name')"
                        >
                        <el-option
                            v-for="item in productTypes"
                            :key="item.name"
                            :label="item.name"
                            :value="item.name"
                            >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item :label="$t('Size')" prop="size" class="w-full mr-2">
                    <el-select
                        v-model="data.size"
                        filterable
                        class="w-full"
                        :placeholder="$t('Select Size')"
                        >
                        <el-option key="any" label="any" value="any"> </el-option>
                        <el-option
                            v-for="item in productSize"
                            :key="item.id"
                            :label="item.name"
                            :value="item.name"
                            >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item :label="$t('Weight')" :class="{'is-error': isError('weight')}">
                    <el-input-number v-model="data.weight" @keyup.enter="onSubmit" required min="0"></el-input-number>
                    <div v-if="isError('weight')" class="el-form-item__error">{{getErrorMessage('weight')}}</div>
                </el-form-item>
                <el-form-item :label="$t('Weight Unit')" prop="weight_unit" class="w-full mr-2">
                    <el-select
                        v-model="data.weight_unit"
                        filterable
                        class="w-full"
                        :placeholder="$t('Select Unit')"
                        >
                        <el-option key="lb" label="lb" value="lb"> lb </el-option>
                        <el-option key="oz" label="oz" value="oz"> oz </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button
                    type="primary"
                    @click="onSubmit"
                    :disabled="isLoading"
                    :loading="isLoading"
                    >{{ $t('Update') }}</el-button>
            </template>
        </el-dialog>
    </div>
</template>