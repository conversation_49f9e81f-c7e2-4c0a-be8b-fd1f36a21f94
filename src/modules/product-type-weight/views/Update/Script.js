import EventBus from '@/utilities/eventBus';
import { getTypes } from '@/api/productStyle';
import { getList as getListSize } from '@/api/productSize';
import { update } from '@/api/productTypeWeight';
export default {
  name: 'UpdateProductSize',
  data() {
    return {
      productSizeId: '',
      dialogVisible: false,
      data: this.setDefaultData(),
      serverErrors: [],
      isLoading: false,
      productSize: [],
      productTypes: [],
      rules: {
        name: [{ required: true }],
        size: [{ required: true }],
        weight: [{ required: true }],
        weight_unit: [{ required: true }],
      },
    };
  },
  created() {
    EventBus.$on('showUpdateProductStyle', (item) => {
      this.productSizeId = item.id;
      this.data = Object.assign(this.data, item);
      this.serverErrors = [];
      this.dialogVisible = true;
    });
  },
  mounted() {
    this.getProductStyle();
    this.getProductSize();
  },
  methods: {
    isError(field) {
      return !!this.serverErrors[field];
    },
    getErrorMessage(field) {
      return this.serverErrors[field][0];
    },
    setDefaultData() {
      return {
        name: '',
        sku: '',
      };
    },
    async getProductStyle() {
      const { data } = await getTypes();
      this.productTypes = data || [];
    },
    async getProductSize() {
      const { data } = await getListSize({ limit: 500 });
      this.productSize = data.data || [];
    },
    async onSubmit() {
      if (this.isLoading) return;
      const isValid = await this.$refs.updateProductTypeWeight.validate();
      if (!isValid) {
        return;
      }
      this.isLoading = true;
      try {
        const res = await update(this.productSizeId, this.data);
        this.dialogVisible = false;
        this.notification(res.data.message);
        this.$emit('refresh');
      } catch (e) {
        let message = 'Edit not success';
        this.notification(message, 'error');
        this.dialogVisible = false;
        this.isLoading = false;
      } finally {
        this.isLoading = false;
      }
    },
  },
};
