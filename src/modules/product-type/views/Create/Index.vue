<style src="./Style.scss" lang="scss"></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
        v-model="openDialogAddProductType"
        :title="$t('Create Product Type')"
        custom-class="el-dialog-custom el-dialog-employee"
        @close="resetData"
        :close-on-click-modal="false"
    >
      <template #default>
        <div class="add-employee">
          <el-form
              status-icon
              ref="addProductTypeForm"
              :model="data"
              :rules="createRules"
              @submit.prevent="onSubmit('addProductTypeForm')"
              label-width="130px"
              :label-position="'left'"
          >
            <el-form-item :label="$t('Name')" prop="name">
              <el-input v-model="data.name"></el-input>
            </el-form-item>
            <el-form-item
                :label="$t('Hard good')"
                prop="is_deleted"
            >
              <el-switch v-model="data.is_hard_good" />
            </el-form-item>
            <el-form-item label="Product icon" class="custom-upload-avatar">
              <div class="self-start" v-if="selectedImage">
                <div class="relative">
                  <img :src="selectedImage" class="max-w-[200px]" />
                  <span class="absolute top-[2px] right-[2px] !leading-[10px]">
                    <el-icon class="text-xl"
                    ><CloseBold
                        class="text-red-600 hover:text-red-500"
                        @click="handleRemove"
                    /></el-icon>
                  </span>
                </div>

                <el-upload
                    name="image"
                    class="upload-demo text-center mt-2"
                    :accept="'image/jpeg,image/png,image/jpg'"
                    :on-change="onChangeImage"
                    :auto-upload="false"
                    :multiple="false"
                    :show-file-list="false"
                >
                  <el-button type="primary">Upload</el-button>
                </el-upload>
              </div>
              <div class="self-start" v-else >
                <el-upload
                    drag
                    :on-change="onChangeImage"
                    class="upload-demo"
                    name="image"
                    :accept="'image/jpeg,image/png,image/jpg'"
                    :auto-upload="false"
                    :multiple="false"
                    :show-file-list="false"
                >
                  <el-icon class="el-icon--upload !text-5xl">
                    <icon :data="iconPlus" />
                  </el-icon>
                  <div class="el-upload__text !text-xs">
                    Drop file here or <em>click to upload</em>
                  </div>
                </el-upload>
              </div>
            </el-form-item>

          </el-form>
        </div>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <el-button
              type="primary"
              @click="onSubmit('addProductTypeForm')"
              :disabled="isLoading"
              :loading="isLoading"
          >{{ $t('Create') }}</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>