import EventBus from "@/utilities/eventBus";
import { create } from "@/api/productType";
import { isEmpty } from 'ramda';
import { CloseBold } from '@element-plus/icons-vue';
export default {
    name: "CreateProductType",
    components: { CloseBold },
    data() {
        return {
            data: this.setDefaultData(),
            isLoading: false,
            openDialogAddProductType: false,
            departments: [],
            uploadedImage: '',
            selectedImage: '',
            previewFile: '',
            image:'',
            createRules: {
                name: [
                    {
                        required: true,
                        message: this.$t('This field cannot be left blank.'),
                        trigger: ['blur', 'change'],
                    },
                ]
            }
        };
    },
    mounted() {

    },
    created() {
        EventBus.$on("showCreateProductType", () => {
            this.data = this.setDefaultData();
            this.selectedImage = '';
            this.uploadedImage = '';
            this.openDialogAddProductType = true;
        });
    },
    computed: {

    },
    methods: {
        isError(field) {
            return !!this.serverErrors[field];
        },
        getErrorMessage(field) {
            return this.serverErrors[field][0];
        },
        setDefaultData() {
            return {
                name: "",
                is_hard_good: true
            };
        },
        onChangeImage(file) {
            if (!file) return false;
            const isPNG = file.raw.type === 'image/png';
            const isLt5M = file.size / 1024 / 1024 < 5;
            if (!isPNG) {
                this.$message.error(
                    'Invalid file format. Please upload an image in PNG format.'
                );
                return false;
            }
            if (!isLt5M) {
                this.$message.error('The file size exceeds the maximum limit of 5MB.');
                return false;
            }
            this.selectedImage = URL.createObjectURL(file.raw); // Hiển thị ảnh xem trước
            this.uploadedImage = file.raw;
            return true;
        },
        resetData() {
            this.$refs['addProductTypeForm'].resetFields();
            this.data = {
                name: '',
            };
            this.selectedImage = '';
            this.uploadedImage = '';
        },
        async onSubmit(formName) {
            const isValid = await this.$refs[formName].validate();
            if (!isValid) {
                return;
            }
            if (this.uploadedImage === '') {
                this.$message.error('Please upload an image.');
                return;
            }
            this.isLoading = true;
            try {
                const formData = new FormData();
                for (let key in this.data) {
                    if (typeof this.data[key] == 'boolean') {
                        formData.append(key, this.data[key] ? 1 : 0);
                    } else {
                        formData.append(key, this.data[key]??'');
                    }
                }
                formData.append('icon', this.uploadedImage);
                await create(formData);
                this.isLoading = false;
                this.notification(this.$t('Product type add successfully.'));
                this.openDialogAddProductType = false;
                this.$emit('refresh');
            } catch (e) {
                let data = e.response?.data;
                let message = data?.message || this.$t('Create product type error.');
                if (!isEmpty(data) && data?.errors) {
                    data = data.errors;
                    const keyFirstData = Object.keys(data)[0];
                    const firstData = data[keyFirstData];
                    message = firstData[0];
                }
                this.notification(message, 'error');
            } finally {
                this.isLoading = false;
            }
        },
        handleRemove(uploadFile, uploadFiles) {
            this.selectedImage = '';
            this.uploadedImage = '';
        },
    },
}
