<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head mb-4">
      <div class="top-head-left"><h1>{{ $t('Product Type') }}</h1></div>
      <div class="top-head-right">
        <el-button type="primary" @click="createProductType">
          <span class="icon-margin-right"><icon :data="iconAdd" /></span>{{ $t('Create') }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <el-table
          border
          stripe size="small" :data="items"
          :max-height="maxHeight"
          v-loading="isLoading"
          element-loading-text="Loading..."
      >
        <el-table-column prop="id" :label="$t('ID')" min-width="100"></el-table-column>
        <el-table-column prop="name" :label="$t('Name')" min-width="200"></el-table-column>
        <el-table-column prop="is_hard_good" :label="$t('Hard good')" min-width="200">
          <template #default="scope">
            {{ scope.row.is_hard_goods ? $t('Yes') : $t('No') }}
          </template>
        </el-table-column>
        <el-table-column prop="action" :label="$t('Action')" fixed="right" width="100">
          <template #default="scope">
            <el-link
                class="el-link-edit"
                :underline="false"
                type="primary"
                @click="updateProductType(scope.row)"
            ><icon :data="iconEdit"
            /></el-link>
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom">
        <div class="total">{{$t('Total:')}} {{ items.length ? formatNumber(total) : 0 }}</div>
        <el-pagination
            :disabled="isLoading"
            background
            layout="prev, pager, next"
            :page-size="filter.limit"
            :total="total"
            @current-change="changePage"
            v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
              v-model="filter.limit"
              :placeholder="$t('Select')"
              size="mini"
              @change="onFilter"
          >
            <el-option
                v-for="item in limits"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
  </div>
  <create-product-type @refresh="getProductTypeList" />
  <update-product-type @refresh="getProductTypeList" />
</template>