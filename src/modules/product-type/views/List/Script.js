import { getList } from "@/api/productType";
import EventBus from "@/utilities/eventBus";
import CreateProductType from "@/modules/product-type/views/Create/Index.vue";
import UpdateProductType from "@/modules/product-type/views/Update/Index.vue";

export default {
    name: "ProductTypeList",
    components: {
        CreateProductType,
        UpdateProductType
    },
    data() {
        return {
            items: [],
            isLoading: false,
            filter: this.setDefaultFilter(),
        }
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 189);
        }
    },
    mounted() {
        this.filter = this.getRouteParam();
        this.getProductTypeList();
    },
    methods: {
        setDefaultFilter() {
            return {
                limit: 25,
                page: 1
            };
        },
        async getProductTypeList() {
            this.isLoading = true;
            this.setRouteParam();
            const { data } = await getList(this.filter);
            this.items = data.data;
            this.total = data.total;
            this.isLoading = false;
        },
        changePage(page) {
            this.filter.page = page;
            this.getProductTypeList();
        },
        onFilter() {
            this.filter.page = 1;
            this.getProductTypeList();
        },
        createProductType() {
            EventBus.$emit("showCreateProductType");
        },
        updateProductType(item) {
            EventBus.$emit("showUpdateProductType", item);
        }
    }
}