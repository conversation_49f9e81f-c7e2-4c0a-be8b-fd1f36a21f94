<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
        v-model="openDialogUpdateProductType"
        destroy-on-close
        :title="$t('Update Product Type') + ` #${productTypeId}`"
        custom-class="el-dialog-custom"
        :destroy-on-close="true"
    >
      <template #default>
        <div class="add-employee">
          <el-form
              status-icon
              ref="updateProductTypeForm"
              :model="data"
              :rules="updateProductTypeRules"
              @submit.prevent="onSubmit('updateProductTypeForm')"
              label-width="130px"
              :label-position="'left'"
          >
            <el-form-item :label="$t('Name')" prop="name">
              <el-input v-model="data.name"></el-input>
            </el-form-item>
            <el-form-item
                :label="$t('Hard good')"
                prop="is_deleted"
            >
              <el-switch v-model="data.is_hard_good" />
            </el-form-item>

            <el-form-item label="Product icon" class="custom-upload-avatar">
              <div class="self-start" v-if="selectedImage">
                <div class="relative">
                  <img :src="selectedImage" class="max-w-[200px]" />
                  <span class="absolute top-[2px] right-[2px] !leading-[10px]">
                    <el-icon class="text-xl"
                    ><CloseBold
                        class="text-red-600 hover:text-red-500"
                        @click="handleRemove"
                    /></el-icon>
                  </span>
                </div>

                <el-upload
                    name="image"
                    class="upload-demo text-center mt-2"
                    :accept="'image/jpeg,image/png,image/jpg'"
                    :on-change="onChangeImage"
                    :auto-upload="false"
                    :multiple="false"
                    :show-file-list="false"
                >
                  <el-button type="primary">Upload</el-button>
                </el-upload>
              </div>
              <div class="self-start" v-else >
                <el-upload
                    drag
                    :on-change="onChangeImage"
                    class="upload-demo"
                    name="image"
                    :accept="'image/jpeg,image/png,image/jpg'"
                    :auto-upload="false"
                    :multiple="false"
                    :show-file-list="false"
                >
                  <el-icon class="el-icon--upload !text-5xl">
                    <icon :data="iconPlus" />
                  </el-icon>
                  <div class="el-upload__text !text-xs">
                    Drop file here or <em>click to upload</em>
                  </div>
                </el-upload>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #footer>
        <el-button
            type="primary"
            @click="onSubmit('updateProductTypeForm')"
            :disabled="isLoading"
            :loading="isLoading"
        >{{ $t('Update') }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>
