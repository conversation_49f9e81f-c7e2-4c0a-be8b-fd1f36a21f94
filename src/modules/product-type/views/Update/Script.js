import EventBus from "@/utilities/eventBus";
import { update } from "@/api/productType";
import { CloseBold } from '@element-plus/icons-vue';
import {isEmpty} from "ramda";

export default {
    name: "UpdateProductType",
    components: { CloseBold },
    data() {
        return {
            productTypeId: '',
            openDialogUpdateProductType: false,
            data: this.setDefaultData(),
            serverErrors: [],
            isLoading: false,
            updateProductTypeRules: {
                name: [
                    {
                        required: true,
                        message: this.$t('This field cannot be left blank.'),
                        trigger: ['blur', 'change'],
                    },
                ]
            },
            selectedImage: '',
            uploadedImage: '',
        }
    },
    created() {
        EventBus.$on("showUpdateProductType", (item) => {
            console.log(item, "cau chuyen tinh yeu");
            this.productTypeId = item.id;
            this.data.name = item.name;
            this.data.is_hard_good = item.is_hard_goods ? true : false;
            this.openDialogUpdateProductType = true;
            this.selectedImage = item.icon_url;
            this.uploadedImage = item.icon_url;
        });
    },
    methods: {
        isError(field) {
            return !!this.serverErrors[field];
        },
        getErrorMessage(field) {
            return this.serverErrors[field][0];
        },
        setDefaultData() {
            return {
                name: "",
                is_hard_good: false
            };
        },
        async onSubmit(formName) {
            if (this.isLoading) return;
            const isValid = await this.$refs[formName].validate();
            if (!isValid) {
                return;
            }
            this.isLoading = true;
            try {
                const formData = new FormData();
                for (let key in this.data) {
                    if (typeof this.data[key] == 'boolean') {
                        formData.append(key, this.data[key] ? 1 : 0);
                    } else {
                        formData.append(key, this.data[key]??'');
                    }
                }
                formData.append('icon', this.uploadedImage);
                const res = await update(this.productTypeId, formData);
                this.openDialogUpdateProductType = false;
                this.notification(res.data.message);
                this.$emit("refresh");
            } catch (e) {
                let data = e.response?.data;
                let message = data?.message || this.$t('Update product type error.');
                if (!isEmpty(data) && data?.errors) {
                    data = data.errors;
                    const keyFirstData = Object.keys(data)[0];
                    const firstData = data[keyFirstData];
                    message = firstData[0];
                }
                this.notification(message, 'error');
            } finally {
                this.isLoading = false;
            }
        },
        onChangeImage(file) {
            if (!file) return false;
            const isPNG = file.raw.type === 'image/png';
            const isLt2M = file.size / 1024 / 1024 < 5;
            if (!isPNG) {
                this.$message.error(
                    'Invalid file format. Please upload an image in PNG format.'
                );
                return false;
            }
            if (!isLt2M) {
                this.$message.error('The file size exceeds the maximum limit of 5MB.');
                return false;
            }

            this.selectedImage = URL.createObjectURL(file.raw); // Hiển thị ảnh xem trước
            this.uploadedImage = file.raw;

            return true;
        },
        handleRemove(uploadFile, uploadFiles) {
            this.selectedImage = '';
            this.uploadedImage = '';
        },
    }
}
