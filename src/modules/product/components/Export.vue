<template>
  <div>
    <el-dialog
      v-model="modalProductExport"
      :title="$t('Notification')"
      custom-class="el-dialog-custom "
      :close-on-click-modal="false"
    >
      <template #default>
        <div class="modal-notification-body">
          <div class="text-system">
            The system is processing please wait a moment.
          </div>
          <el-progress
            :text-inside="true"
            :stroke-width="20"
            :percentage="progress"
          />
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { downloadCsv } from '@/utilities/helper.js';
import EventBus from '@/utilities/eventBus.js';
import { splitEvery } from 'ramda';
import { list } from '@/api/product.js';
import { PRODUCT_SPEC } from '@/utilities/ProductSpec.js';

export default {
  name: 'ProductExport',
  props: {
    filter: {
      type: Object,
    },
    totalProduct: {
      type: Number,
    },
    currentTab: {
      type: String
    },
  },
  data() {
    return {
      maxRowExport: 1000,
      isLoadingExport: false,
      progress: 0,
      modalProductExport: false,
    };
  },
  created() {
    EventBus.$on('productExport', () => {
      console.log(PRODUCT_SPEC);
      this.exportData();
    });
  },
  methods: {
    async exportData() {
      this.modalProductExport = true;
      this.progress = 0;

      const totalPage = Math.ceil(this.totalProduct / this.maxRowExport);
      const totalPageArr = Array.from({ length: totalPage }, (_, i) => i);

      const chunk = splitEvery(5, totalPageArr);
      let dataItems = [];

      let params = Object.assign({}, this.filter);
      params.limit = this.maxRowExport;

      for (let chunkItem of chunk) {
        let progress = parseInt(100 / chunk.length);
        this.progress += progress;
        if (this.progress >= 100) {
          this.progress = 95;
        }
        this.$forceUpdate();

        const promiseAll = [];
        for await (let i of chunkItem) {
          params.page = i + 1;
          promiseAll.push(list(params));
        }

        await Promise.all(promiseAll).then((res) => {
          for (let data of res) {
            let items = (data.data && data.data.data) || [];
            dataItems = [...dataItems, ...items];
          }
        });
      }

      const csvData = this.formatCsvData(
        dataItems,
        params.spec == 'yes' ? true : false
      );
      downloadCsv('products.csv', csvData);
      this.modalProductExport = false;
    },
    formatCsvData(data, spec) {
      let header = `STYLE,COLOR,SIZE,SKU,${this.currentTab=='product' ? 'SKU Rank,':''}GTIN,STOCK`;
      if (spec) {
        const column = Object.values(PRODUCT_SPEC);
        header = `${header},${column.join(',')}`;
      }
      let csvData = `${header}\n`;
      for (let item of data) {
        let row = `"${item.style || ''}","${item.color || ''}","${
          item.size || ''
        }","${item.sku || ''}",${this.currentTab=='product' ? `"${item.product_rank}",`:''}"${item.gtin || ''}","${
          item.quantity && item.quantity > 0 ? 'In stock' : 'Out of stock'
        }"`;
        if (spec) {
          for (let col of Object.keys(PRODUCT_SPEC)) {
            if (col !== 'fabric_content') {
              row = `${row},${item.product_spec[col] === 1 ? 'yes' : ''}`;
            } else {
              row = `${row},${item.product_spec[col] !== null ? item.product_spec[col] : ''}`;
            }
          }
        }
        csvData += `${row}\n`;
      }
      return csvData;
    },
  },
};
</script>

<style lang="scss">
.modal-notification-body {
  text-align: center;
  .text-system {
    margin-bottom: 15px;
  }
}
</style>
