import EventBus from "@/utilities/eventBus";
import { create, checkExistSkus } from "@/api/product";
import notificationMixin from "@/mixins/notification";
import { ElTooltip } from 'element-plus';
import { debounce } from "@/utilities/helper.js";
import { isEmpty, isNil } from 'ramda';

export default {
    name: "CreateProduct",
    mixins: [notificationMixin],
    props: ['brandOptions', 'styleOptions', 'colorOptions', 'sizeOptions'],
    components: {
        ElTooltip,
    },
    data() {
        return {
            dialogVisible: false,
            isLoading: false,
            isGeneratingData: false,
            data: this.setDefaultData(),
            serverErrors: [],
            listData: [],
            listDataError: [],
            listColors: [],
            filteredColors: [],
            filteredSizes: [],
            rules: {
                brand_id: [
                    { required: true, message: 'Please select a brand', trigger: 'blur' }
                ]
            },
            styleFilterKey: ''
        }
    },
    created() {
        EventBus.$on("showCreateProductModal", () => {
            this.serverErrors = [];
            this.data = this.setDefaultData();
            this.dialogVisible = true;
        });
    },
    computed: {
        showAddButton() {
            return this.data.style && this.data.color.length > 0 && this.data.size.length > 0 > 0;
        },
        hasErrors() {
            return (row) => {
                return row.error !== undefined && row.error !== null && row.error !== '';
            };
        },
        stylesFiltered() {
            if (this.styleFilterKey) {
                return this.styleOptions.filter((item) => {
                    return String(item.name).toLowerCase().includes(this.styleFilterKey.toLowerCase())
                })
            } else {
                return this.styleOptions
            }
        }
    },

    methods: {
    isWeightError(row, type) {
        if (type === 'single') {
            return row.error && (row.weight_single <= 0 || !row?.weight_single);
        }
        return row.error && (row.weight_multiple <= 0 || !row?.weight_multiple);
    },
        filterOptionColors(colorOptions, listcolors) {
            if (!Array.isArray(listcolors.colors) || listcolors.colors.length === 0) {
                console.error("listcolors.colors is empty or not an array.");
                return [];
            }
            const listColorNames = listcolors.colors;
            const filteredColors = colorOptions.filter(option => !listColorNames.includes(option.name));
            return filteredColors;
        },
        filterOptionSizes(sizeOptions, listsizes) {
            if (!Array.isArray(listsizes.sizes) || listsizes.sizes.length === 0) {
                console.error("listsizes.sizes is empty or not an array.");
                return [];
            }
            const listColorSizes = listsizes.sizes;
            const filteredSizes = sizeOptions.filter(option => !listColorSizes.includes(option.name));
            return filteredSizes;
        },

        isError(field) {
            return !!this.serverErrors[field];
        },
        getErrorMessage(field) {
            return this.serverErrors[field][0];
        },
        setDefaultData() {
            return {
                style: '',
                color: [],
                size: [],
                gtin: '',
                gtin_case: 1,
                brand_id: '',
                description: '',
            };
        },
        addNewData() {
            this.generateData(this.getStyleSkus(), this.getColorSkus(), this.getSizeSkus());
            this.data = this.setDefaultData();
            this.filteredColors = [],
                this.filteredSizes = []

        },
        getStyleSkus() {
            const selectedStyle = this.data.style;
            const filteredOptions = this.styleOptions.filter(option => option.name === selectedStyle);
            return filteredOptions.map(option => ({ name: option.name, sku: option.sku }));
        },
        getColorSkus() {
            return this.data.color.map(selectedColor => {
                const matchingOption = this.colorOptions.find(option => option.name === selectedColor);
                return matchingOption ? { name: matchingOption.name, sku: matchingOption.sku } : null;
            }).filter(colorObj => colorObj !== null);
        },
        getSizeSkus() {
            return this.data.size.map(selectedStyle => {
                const matchingOption = this.sizeOptions.find(option => option.name === selectedStyle);
                return matchingOption ? { name: matchingOption.name, sku: matchingOption.sku } : null;
            }).filter(colorObj => colorObj !== null);
        },
        generateData(styles, colors, sizes) {
            this.isGeneratingData = true; // Start loading state
            const skus = [];
            const dataMap = {};

            colors.forEach(color => {
                sizes.forEach(size => {
                    const newData = {
                        sku: styles[0].sku + color.sku + size.sku,
                        style: styles[0].name,
                        color: color.name,
                        size: size.name,
                        gtin: '',
                        gtin_case: 0,
                    };
                    skus.push(newData.sku);
                    dataMap[newData.sku] = newData;
                });
            });

            return checkExistSkus({ skus })
                .then(response => {
                    console.log(skus);
                    const existingSkus = response.data;
                    skus.forEach(sku => {
                        const data = dataMap[sku];
                        if (existingSkus.includes(sku) && !this.isInList(this.listDataError, data)) {
                            this.listDataError.push(data);
                        } else if (!existingSkus.includes(sku) && !this.isInList(this.listData, data)) {
                            this.listData.push(data);
                        }
                    });
                })
                .catch(error => {
                    console.error("Error:", error);
                })
                .finally(() => {
                    this.isGeneratingData = false; // End loading state
                });
        },
        async onSubmit(formName) {
            const valid = await new Promise((resolve) => {
                this.$refs[formName].validate((valid) => {
                    resolve(valid);
                });
            });

            if (!valid) {
                return false;
            }

            const duplicateGTINs = this.findDuplicateGTINs(this.listData);
            console.log(this.findDuplicateGTINs);
            if (duplicateGTINs.length > 0) {
                this.notification(`Duplicate GTIN values found: ${duplicateGTINs.join(', ')}`, "error");
                return false;
            }

            this.isGeneratingData = true;
            this.isLoading = true;
            try {
                // Prepare the data for submission
                this.listData = this.listData.map(item => ({
                    ...item,
                    brand_id: this.data.brand_id,
                    description: this.data.description,
                }));

                const data = this.listData;
                const res = await create(data);

                // Reset listData and listDataError
                this.listData = [];
                this.listDataError = [];

                if (res.data.created_products.length > 0) {
                    this.notification(res.data.message);
                } else {
                    this.notification("Failed to create products", "error");
                }

                this.$emit("refresh");
            } catch (e) {
                if (e.response && e.response.status === 422) {
                    if (e.response?.data?.failed_products?.length > 0) {
                        e.response.data.failed_products.forEach(failedProduct => {
                            const { item, error } = failedProduct;
                            const index = this.listData.findIndex(product => product.sku === item.sku);
                            if (index !== -1) {
                                this.listData[index].error = error;
                            }
                        });
                    }
                    if (e.response?.data?.created_products?.length > 0) {
                        e.response.data.created_products.forEach(createdProducts => {
                            const index = this.listData.findIndex(product => product.sku === createdProducts.sku);
                            if (index !== -1) {
                                this.listData.splice(index, 1);
                            }
                        });
                    }
                    this.serverErrors = e.response.data.errors || [];
                    this.notification(e.response.data.message || 'Validation error occurred', "error");
                } else {
                    this.serverErrors = e.response?.data?.errors || [];
                    this.notification(e.response?.data?.message || 'An error occurred', "error");
                }
            } finally {
                this.isLoading = false;
                this.isGeneratingData = false;
            }
        },
        async fetchData() {
            this.isGeneratingData = true;
            this.isGeneratingData = false;
            this.styleFilterKey = '';
        },
        isInList(list, data) {
            return list.some(item => item.sku === data.sku);
        },

        debounceFilterStyles: debounce(async function(query) {
            this.styleFilterKey = query;
        }, 100),
        findDuplicateGTINs(data) {
            const gtinCounts = data.reduce((acc, item) => {
                if (item.gtin) {  // Only count non-empty GTIN values
                    acc[item.gtin] = (acc[item.gtin] || 0) + 1;
                }
                return acc;
            }, {});

            const duplicateGTINs = Object.keys(gtinCounts).filter(gtin => gtinCounts[gtin] > 1);

            data.forEach(item => {
                if (duplicateGTINs.includes(item.gtin)) {
                    item.error = 'Duplicate GTIN Please Edit';
                } else {
                    delete item.error;  // Clear any existing errors if the GTIN is not a duplicate
                }
            });

            return Object.keys(gtinCounts).filter(gtin => gtinCounts[gtin] > 1);
        },
    }
}