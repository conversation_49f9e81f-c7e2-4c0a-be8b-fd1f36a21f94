<script src="./Script.js"></script>
<template>
  <div class="">
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ $t('Production Report') }}</h1>
      </div>
    </div>
    <div class="mt-3">
      <div class="mb-5 flex items-center">
        <div class="mr-3 flex flex-col">
          <label for="" class="mb-1">Date Report</label>
          <el-date-picker
            :disabled-date="disabledDate"
            format="YYYY-MM-DD"
            v-model="filter.date"
            type="date"
            @change="onFilter"
            :clearable="false"
            value-format="YYYY-MM-DD"
            size="large"
          >
          </el-date-picker>
        </div>
        <div class="mr-3 flex flex-col">
          <label for="" class="mb-1">Warehouse</label>
          <el-select
            size="large"
            filterable
            v-model="filter.warehouse"
            :placeholder="$t('Select warehouse')"
            @change="onFilter"
          >
            <!-- <el-option label="All" value=""></el-option> -->
            <el-option
              v-for="item in warehouses"
              :key="item.id"
              :label="item.name"
              :value="String(item.id)"
            >
            </el-option>
          </el-select>
        </div>
        <div class="flex flex-col">
          <label for="" class="mb-1">Product</label>
          <el-select
            size="large"
            filterable
            v-model="filter.product_type"
            @change="onChangeProductType"
          >
            <el-option value="" label="All"></el-option>
            <el-option
              v-for="item in productTypes"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <div v-if="hasFilter" class="ml-2 flex flex-col">
          <label for="" class="mb-1 opacity-0">Action</label>
          <el-link type="danger" @click="onClearFilter" :underline="false">
            {{ $t('Clear') }}
          </el-link>
        </div>
      </div>
    </div>
    <div class="py-5">
      <LineChart
        v-loading="isLoading"
        :chartData="data"
        :options="options"
        :height="maxHeight"
        :plugins="plugins"
        cssClasses=""
        ref="lineChart"
      />
      <div id="legend" class="mt-3"></div>
    </div>
  </div>
</template>
