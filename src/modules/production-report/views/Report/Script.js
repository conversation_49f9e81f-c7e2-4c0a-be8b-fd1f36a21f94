import { mapGetters } from 'vuex';
import { equals } from 'ramda';
import formatNumberMixin from '@/mixins/formatNumber.js';
import warehouseMixin from '@/mixins/warehouse';
import { list } from '@/api/warehouse';
import { report } from '@/api/productionReport';

import { Chart, registerables } from 'chart.js';
import { LineChart } from 'vue-chart-3';
Chart.register(...registerables);

export default {
  name: 'ProductionReport',
  components: { LineChart },
  mixins: [formatNumberMixin, warehouseMixin],
  data() {
    return {
      date: '',
      filter: this.setDefaultFilter(),
      isLoading: false,
      warehouses: [],
      productTypes: [
        {
          name: 'Apparel',
          value: 'apparel',
        },
        {
          name: 'Mug',
          value: 'mug',
        },
        {
          name: 'Accessories',
          value: 'accessories',
        },
      ],
      options: {
        interaction: {
          mode: 'index',
        },
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          x: {
            ticks: {
              font: {
                size: 14,
              },
            },
          },
          y: {
            suggestedMin: 0,
            suggestedMax: 10,
            ticks: {
              font: {
                size: 14,
              },
            },
          },
        },
        plugins: {
          legend: {
            position: 'bottom',
            display: false,
            labels: {
              usePointStyle: true,
              padding: 20,
              font: {
                size: 14,
              },
              boxHeight: 10,
            },
            onClick: (e) => e.stopPropagation(),
          },
          tooltip: {
            usePointStyle: true,
            backgroundColor: 'white',
            borderColor: '#DBDBDB',
            borderWidth: 1,
            titleColor: '#0A0B0D',
            bodyColor: '#0A0B0D',
            cornerRadius: 8,
            titleFont: {
              size: 20,
            },
            bodyFont: {
              size: 16,
            },
            padding: {
              top: 20,
              left: 20,
              bottom: 10,
              right: 20,
            },
            boxPadding: 8,
            boxHeight: 30,
            callbacks: {
              title: (context) => {
                return (
                  context?.[0]?.label +
                    ' (' +
                    this.formatDate(this.filter.date, false) +
                    ')' || ''
                );
              },
              label: (context) => {
                let label = context.dataset.label || '';
                if (label) {
                  label += ': ';
                }
                if (context.parsed.y !== null) {
                  label += this.formatNumber(context.parsed.y) + ' items';
                }

                return label;
              },
              labelPointStyle: () => {
                return {
                  pointStyle: 'circle',
                  rotation: 0,
                };
              },
              labelColor: (context) => {
                return {
                  borderColor: context.dataset?.borderColor,
                  backgroundColor: context.dataset?.backgroundColor,
                };
              },
            },
            position: 'nearest',
          },
        },
      },
      plugins: [
        {
          // Custom HTML Legend
          afterUpdate(chart) {
            const getOrCreateLegendList = () => {
              const legendContainer = document.getElementById('legend');
              let listContainer = legendContainer.querySelector('ul');

              if (!listContainer) {
                listContainer = document.createElement('ul');
                listContainer.style.display = 'flex';
                listContainer.style.flexDirection = 'row';
                listContainer.style.justifyContent = 'center';

                legendContainer.appendChild(listContainer);
              }

              return listContainer;
            };

            const ul = getOrCreateLegendList();

            // Remove old legend items
            while (ul.firstChild) {
              ul.firstChild.remove();
            }

            // Reuse the built-in legendItems generator
            const items =
              chart.options.plugins.legend.labels.generateLabels(chart);

            items.forEach((item, index) => {
              const li = document.createElement('li');
              li.style.alignItems = 'center';
              li.style.display = 'flex';
              li.style.flexDirection = 'row';
              li.style.position = 'relative';
              if (item.hidden) {
                li.style.opacity = '15%';
              }

              if (index !== 0) {
                li.style.marginLeft = '30px';
              }

              // Color box
              const boxSpan = document.createElement('span');
              boxSpan.style.background = item.strokeStyle;
              boxSpan.style.borderColor = item.strokeStyle;
              boxSpan.style.borderWidth = item.lineWidth + 'px';
              boxSpan.style.borderRadius = '100%';
              boxSpan.style.display = 'inline-block';
              boxSpan.style.height = '20px';
              boxSpan.style.marginRight = '10px';
              boxSpan.style.width = '20px';
              boxSpan.style.position = 'relative';
              boxSpan.style.zIndex = '2';

              // Text
              const textContainer = document.createElement('p');
              textContainer.style.color = item.fontColor;
              textContainer.style.fontSize = '16px';
              textContainer.style.lineHeight = '24px';
              const text = document.createTextNode(item.text);
              textContainer.appendChild(text);

              li.appendChild(boxSpan);
              li.appendChild(textContainer);

              ul.appendChild(li);
            });
          },
        },
      ],
      data: {},
    };
  },
  computed: {
    ...mapGetters([]),
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },
    maxHeight() {
      return parseInt(window.innerHeight - 285);
    },
  },
  mounted() {
    this.fetchData();
    this.fetchWarehouse();
  },
  methods: {
    disabledDate(time) {
      return time.getTime() > Date.now();
    },
    onFilter() {
      this.fetchDataProductionReport();
    },
    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.fetchDataProductionReport();
    },
    setDefaultFilter() {
      let params = {
        date: this.formatDate(new Date(), false),
        product_type: '',
        warehouse: String(this.userWarehouseId),
      };
      return params;
    },
    setRouteParam() {
      if (this.$route.name !== 'production_report') return;
      const params = this.filter;
      this.$router.replace({ name: 'production_report', query: params });
    },
    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      return filter;
    },
    async fetchData() {
      this.filter = this.getRouteParam();
      await this.fetchDataProductionReport();
    },
    async fetchWarehouse() {
      this.warehouses = this.getUserProfile.warehouses ?? [];
    },
    async fetchDataProductionReport() {
      if (!this.filter.date) {
        this.notification('Date is required.', 'error');
        return;
      }
      if (!this.filter.warehouse) {
        this.notification('Warehouse is required.', 'error');
        return;
      }
      this.isLoading = true;
      this.setRouteParam();
      const res = await report(this.filter);
      this.buildData(res.data || []);
      this.isLoading = false;
    },
    buildData(dataRes) {
      let dataObj = {};
      for (let i = 0; i < dataRes.length; i++) {
        const item = dataRes[i];
        if (!item.product_type) continue;
        dataObj[item.product_type.toLowerCase()] = [
          item.quantity_pulled || 0,
          item.quantity_printed || 0,
          item.quantity_qc || 0,
          item.quantity_shipped || 0,
          item.quantity_folded || 0,
        ];
      }

      const dataSetPointStyle = {
        pointRadius: 6,
        pointHoverRadius: 6,
        pointBackgroundColor: '#fff',
        pointBorderWidth: 3,
        pointHoverBorderWidth: 3,
      };

      let datasets = [
        {
          key: 'apparel',
          ...dataSetPointStyle,
          label: 'Apparel',
          data: [0, 0, 0, 0, 0],
          borderColor: '#923EF3',
          backgroundColor: '#923EF3',
        },
        {
          key: 'mug',
          ...dataSetPointStyle,
          label: 'Mug',
          data: [0, 0, 0, 0, 0],
          borderColor: '#D45190',
          backgroundColor: '#D45190',
        },
        {
          key: 'accessories',
          ...dataSetPointStyle,
          label: 'Accessories',
          data: [0, 0, 0, 0, 0],
          borderColor: '#54D451',
          backgroundColor: '#54D451',
        },
      ];
      datasets = datasets.map((item) => {
        return {
          ...item,
          data: dataObj[item.key] || [0, 0, 0, 0, 0],
        };
      });
      const data = {
        labels: ['Pulled', 'Printed', `QC'ed`, 'Shipped', 'Folded'],
        datasets,
      };
      this.data = data;
      this.onChangeProductType();
    },
    onChangeProductType() {
      this.setRouteParam();
      let data = this.data;
      const datasets = data.datasets.map((item) => {
        return {
          ...item,
          hidden: this.filter.product_type
            ? item.key === this.filter.product_type
              ? false
              : true
            : false,
        };
      });
      data = {
        ...data,
        datasets: datasets,
      };
      this.data = data;
    },
  },
};
