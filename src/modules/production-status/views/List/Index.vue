<style src="./Style.scss" lang="scss"></style>
<script src="./Script.js"></script>
<template>
  <div class="production-status">
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ $t("Items") }}</h1>
      </div>
      <div class="top-head-right">
        <!-- <el-button type="primary" @click="exportExcel">
          {{ $t("Reprint") }}
        </el-button> -->
        <el-button type="primary" @click="openDialogExportShipment()">
          <span class="icon-margin-right"><icon :data="iconExport" /></span
          >{{ $t("Export") }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <el-tabs
        class="el-tab-filter"
        v-model="filterName"
        @tab-click="onChangeFilter()"
        type="card"
      >
        <el-tab-pane
          :label="item.name"
          :name="item.key"
          v-for="(item, index) in filters"
          :key="index"
          :disabled="filterName == item.key"
        >
          <template #label>
            <span class="custom-tabs-label">
              <span>{{ item.name }}</span>

              <el-popconfirm
                v-if="item.key !== 'all'"
                @confirm="removeFilter(index)"
                title="Are you sure to delete this?"
              >
                <template #reference>
                  <icon class="ml-1" :data="iconClose" />
                </template>
              </el-popconfirm>
            </span>
          </template>
        </el-tab-pane>
      </el-tabs>
      <div class="filter">
        <div class="label">{{ $t("Filter by:") }}</div>
        <div class="filter-item" v-if="hasAllWarehouse">
          <el-dropdown
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('warehouse') }"
            ref="warehouse"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('warehouse')">
                <el-tooltip
                  effect="dark"
                  :content="$t('Warehouse')"
                  placement="top-start"
                >
                  <span>{{ getWarehouseById(filter.warehouse) }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t("Warehouse") }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-select
                  filterable
                  v-model="filter.warehouse"
                  :placeholder="$t('Select warehouse')"
                  @change="onFilter('warehouse')"
                  clearable
                  @clear="onFilter('warehouse')"
                >
                  <el-option :label="'All'" :value="''" >
                  </el-option>
                  <el-option
                    v-for="item in getWarehouses"
                    :key="item.id"
                    :label="item.name"
                    :value="String(item.id)"
                  >
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': orderDate && orderDate.length }"
          >
            <span class="el-dropdown-link">
              <template v-if="orderDate && orderDate.length">
                <el-tooltip
                  effect="dark"
                  :content="$t('Order Date')"
                  placement="top-start"
                >
                  <span>
                    {{ templateDateRange(orderDate[0], orderDate[1]) }}</span
                  >
                </el-tooltip>
              </template>
              <template v-else> {{ $t("Order Date") }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-date-picker
                  format="YYYY-MM-DD"
                  v-model="orderDate"
                  type="daterange"
                  range-separator="To"
                  :start-placeholder="$t('Start date')"
                  :end-placeholder="$t('End date')"
                  @change="onChangeDate"
                >
                </el-date-picker>
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown
            ref="keyword"
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('keyword') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('keyword')">
                <el-tooltip
                  effect="dark"
                  :content="$t('Label / Ref / Tracking')"
                  placement="top-start"
                >
                  <span>{{ filter.keyword }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{
                $t(" Label / Ref / Tracking")
              }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input
                  :placeholder="$t('Label / Ref / Tracking')"
                  class="search"
                  v-model="filter.keyword"
                  @keydown.enter="onFilter('keyword')"
                  clearable
                  @clear="clearFilterItem('keyword')"
                />
              </div>
            </template>
          </el-dropdown>
        </div>
        <!-- <div class="filter-item">
          <el-dropdown
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{
              'is-active': hasChangeFilterByItem('barcode_printed_id'),
            }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('barcode_printed_id')">
                <el-tooltip
                  effect="dark"
                  :content="$t('Print Label')"
                  placement="top-start"
                >
                  <span>{{ getEmployee(filter.barcode_printed_id) }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(" Print Label ") }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-select
                  filterable
                  v-model="filter.barcode_printed_id"
                  :placeholder="$t('Print Label ')"
                  @change="onFilter"
                >
                  <el-option
                    v-for="item in getEmployees"
                    :key="item.id"
                    :label="item.name"
                    :value="String(item.id)"
                  >
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-dropdown>
        </div> -->
        <div class="filter-item">
          <el-dropdown
            trigger="click"
            class="el-dropdown-filter-item"
            ref="productionStatus"
          >
            <span class="el-dropdown-link">
              {{ $t(" Status ") }}
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-form label-width="120px">
                  <el-form-item
                    v-for="item in productionStatus"
                    :key="item.value"
                    :label="item.label"
                  >
                    <el-select
                      filterable
                      v-model="filter[item.value]"
                      :placeholder="$t('Select')"
                      class="w-[120px]"
                      :teleported="false"
                    >
                      <el-option
                        v-for="item in yesOrNo"
                        :key="item.value"
                        :label="item.label"
                        :value="String(item.value)"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    key="shipment_status"
                    :label="$t('Shipment')"
                  >
                    <el-select
                      filterable
                      v-model="filter['shipment_status']"
                      :placeholder="$t('Select')"
                      class="w-[120px]"
                      :teleported="false"
                    >
                      <el-option
                        v-for="item in shipmentStatus"
                        :key="item.value"
                        :label="item.label"
                        :value="String(item.value)"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-form>
                <div class="flex justify-end">
                  <el-button @click="clearOrderType('productionStatus')">{{
                    $t("Clear")
                  }}</el-button>
                  <el-button
                    type="primary"
                    @click="onFilter('productionStatus')"
                    >{{ $t("Apply") }}</el-button
                  >
                </div>
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('store_id') }"
            ref="store_id"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('store_id')">
                <el-tooltip
                  effect="dark"
                  :content="$t('Store')"
                  placement="top-start"
                >
                  <span>{{ getStoreById(filter.store_id) }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t("Store") }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-select
                  filterable
                  v-model="filter.store_id"
                  :placeholder="$t('Select store')"
                  @change="onFilter('store_id')"
                >
                  <el-option
                    v-for="item in getStores"
                    :key="item.id"
                    :label="item.code || storeNA"
                    :value="String(item.id)"
                  >
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('employee_id') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('employee_id')">
                <el-tooltip
                  effect="dark"
                  :content="$t('Employee')"
                  placement="top-start"
                >
                  <span>{{ getEmployee(filter.employee_id) }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(" Employee ") }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-select
                  filterable
                  v-model="filter.employee_id"
                  :placeholder="$t('Employee')"
                  @change="onFilter"
                >
                  <el-option
                    v-for="item in getEmployees"
                    :key="item.id"
                    :label="item.name"
                    :value="String(item.id)"
                  >
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item" v-if="hasFilter">
          <el-link type="danger" @click="onClearFilter" :underline="false">
            {{ $t("Clear") }}
          </el-link>
          <el-button
            size="small"
            class="ml-2"
            type="primary"
            @click="openSaveFilter"
            plain
          >
            {{ $t("Save Filter") }}</el-button
          >
        </div>
      </div>

      <el-table
        border
        stripe
        size="small"
        :data="items"
        style="width: 100%"
        :max-height="maxHeight"
        v-loading="isLoading"
        element-loading-text="Loading..."
        @sort-change="sortTable"
        @selection-change="handleSelectionChange"
        :cell-class-name="tableCellClassName"
        :header-cell-class-name="headerCellClassName"
        class="table-production-status"
        :row-class-name="tableRowClassName"
      >
        <el-table-column type="selection" width="32" fixed="" />
        <el-table-column v-if="hasAllWarehouse" prop="warehouse" width="120" :label="$t('Warehouse')">
         <template #default="scope">
           {{ getWarehouseById(scope.row?.warehouse_id) }}
         </template>
        </el-table-column>
        <el-table-column prop="store" width="150" :label="$t('Store')">
          <template #default="scope">
            <el-tooltip
                effect="dark"
                :content="scope.row.store_code || storeNA"
                placement="top-start"
            >
              <div class="truncate">
                {{ scope.row.store_code || storeNA }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="age" :label="$t('Age')" min-width="68">
          <template #default="scope">
            {{ timeFromNow(scope.row.created_at, scope.row.shipped_at) }}
          </template>
        </el-table-column>
        <el-table-column
            prop="order_number"
            :label="$t('Order Number')"
            min-width="150"
        >
          <template #default="scope">
            <el-link
                :underline="false"
                type="primary"
                @click="showDetails(scope.row?.sale_order)"
            >{{ scope.row?.sale_order?.order_number }}</el-link
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="order_date"
          :label="$t('Order Date')"
          width="100"
        >
          <template #default="scope">
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="formatTime(scope.row.sale_order.created_at, 'YYYY-MM-DD')"
              placement="top-start"
            >
              {{ formatTime(scope.row.sale_order.created_at, 'YYYY-MM-DD') }}
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="label_id" :label="$t('Label ID')" width="180">
          <template #default="scope">
            {{ scope.row.label_id }}
          </template>
        </el-table-column>
        <el-table-column
          prop="ref_number"
          :label="$t('Ref Number')"
          width="200"
        >
          <template #default="scope">
            {{ scope.row.sale_order.external_number }}
          </template>
        </el-table-column>
        <el-table-column
          prop="tracking_status"
          :label="$t('Shipment Status')"
          width="200"
        >
          <template #default="scope">
            {{ displayShipmentStatus(scope.row.sale_order?.shipment_default?.tracking_status) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="Tracking Number"
          :label="$t('Tracking Number')"
          width="200"
        >
          <template #default="scope">
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="
                getOrderShippingStatusByValue(scope.row.sale_order?.shipment_default?.tracking_status)
              "
              placement="top-start"
              v-if="scope.row.sale_order?.shipment_default?.tracking_status"
            >
              <icon
                class="mr-1 relative cursor-pointer"
                style="top: -1px; outline: none"
                :data="scope.row.sale_order?.shipment_default?.provider == 'shipstation' ? iconShipStation : iconShipEasypost"
                :style="scope.row.sale_order?.shipment_default?.provider == 'shipstation' ? 'color: #6cbd45' : 'color: #0375d7'"
              />
            </el-tooltip>
            {{ scope.row.sale_order?.shipment_default?.tracking_number }}
          </template>
        </el-table-column>
        <el-table-column prop="pulled" :label="$t('Pulled')" width="140">
          <template #default="scope">
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="scope.row.pulled_by"
              placement="top-start"
            >
              {{ formatTime(scope.row.pulled_at, 'YYYY-MM-DD HH:mm:ss') }}
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          prop="pretreated"
          :label="$t('Pretreated')"
          width="140"
        >
          <template #default="scope">
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="scope.row.pretreated_by"
              placement="top-start"
            >
              {{ formatTime(scope.row.pretreated_at, 'YYYY-MM-DD HH:mm:ss') }}
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="printed" :label="$t('Printed')" width="140">
          <template #default="scope">
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="scope.row.printed_by"
              placement="top-start"
            >
              {{ formatTime(scope.row.printed_at, 'YYYY-MM-DD HH:mm:ss') }}
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="qc" :label="$t('QC\'ed')" width="140">
          <template #default="scope">
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="scope.row.qc_by"
              placement="top-start"
            >
              {{ formatTime(scope.row.qc_at, 'YYYY-MM-DD HH:mm:ss') }}
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="press" :label="$t('Pressed')" width="140">
          <template #default="scope">
            <el-tooltip
                class="box-item"
                effect="dark"
                :content="scope.row.pressed_by"
                placement="top-start"
            >
              {{ formatTime(scope.row.pressed_at, 'YYYY-MM-DD HH:mm:ss') }}
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="staged" :label="$t('Staged')" width="140">
          <template #default="scope">
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="scope.row.staged_by"
              placement="top-start"
            >
              {{ formatTime(scope.row.staged_at, 'YYYY-MM-DD HH:mm:ss') }}
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="folded" :label="$t('Folded')" width="140">
          <template #default="scope">
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="scope.row.folded_by"
              placement="top-start"
            >
              {{ formatTime(scope.row.folded_at, 'YYYY-MM-DD HH:mm:ss') }}
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="labelled" :label="$t('Labelled')" width="140">
          <template #default="scope">
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="scope.row.shipped_by"
              placement="top-start"
            >
              {{ formatTime(scope.row.shipped_at, 'YYYY-MM-DD HH:mm:ss') }}
            </el-tooltip>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="reprinted" :label="$t('Reprinted')" width="140">
          <template #default="scope">
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="scope.row.reprinted_by"
              placement="top-start"
            >
              {{ convertUtcToLocalTime(scope.row.reprinted_at, 'YYYY-MM-DD HH:mm:ss') }}
            </el-tooltip>
          </template>
        </el-table-column> -->

      </el-table>
      <div class="bottom">
        <div class="total">
          {{ $t("Total:") }} {{ items.length ? formatNumber(total) : 0 }}
        </div>
        <el-pagination
          :disabled="isLoading"
          background
          layout="prev, pager, next"
          :page-size="filter.limit"
          :total="total"
          @current-change="changePage"
          v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
            v-model="filter.limit"
            :placeholder="$t('Select')"
            size="mini"
            @change="onFilter"
          >
            <el-option
              v-for="item in limits"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <el-dialog
        v-model="openDialogAddFilter"
        :title="$t('Save Filter')"
        custom-class="el-dialog-custom el-dialog-custom2 el-dialog-medium"
        :close-on-click-modal="false"
        @close="addFilterName = ''"
      >
        <template #default>
          <div class="add-location">
            <el-form
              status-icon
              @submit.prevent="saveFilter()"
              label-width="130px"
              :label-position="'left'"
            >
              <el-form-item :label="$t('Filter Name')" prop="barcode">
                <el-input
                  v-model="addFilterName"
                  type="text"
                  autocomplete="off"
                ></el-input>
              </el-form-item>
            </el-form>
          </div>
        </template>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="saveFilter()">{{
              $t("Save Filter")
            }}</el-button>
          </div>
        </template>
      </el-dialog>
      <DetailsSaleOrder @refresh="fetchData" />
    </div>
  </div>

  <el-dialog
      v-model="openDialogExport"
      :title="$t('Export Items')"
      custom-class="el-dialog-custom el-dialog-box"
      width="20%"
      @close="onClose"
  >
    <template #default>
      <div class="pt-3 pb-3">
        <el-form-item v-show="!isExportActive" :label="$t('Password')" required>
          <el-input v-model="passwordExport" @keyup.enter="checkPasswordExport" type="password"></el-input>
        </el-form-item>
      </div>
      <div class="text-center w-full text-red-400  w-full" v-if="showNotificationExport" >
        <span class="break-normal">
         {{ messageConfirmExport }}
        </span>
      </div>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="openDialogExport = false">Cancel</el-button>
        <el-button type="primary" :disabled="!isExportActive" @click="exportExcel()"
        >Export</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
