import { list } from "@/api/productionStatus.js";
import { mapGetters } from "vuex";
import EventBus from "@/utilities/eventBus.js";
import { equals, clone, isEmpty, isNil } from "ramda";
import filterMixin from "@/mixins/filter";
import dateMixin from "@/mixins/date.js";
import saleOrderMixin from "@/mixins/saleOrder.js";
import formatNumberMixin from "@/mixins/formatNumber.js";
import warehouseMixin from "@/mixins/warehouse";
import FilterAdd from "@/components/filter/Add.vue";
import DetailsSaleOrder from "@/modules/sale-order/components/Details/Index.vue";
import {API_URL, NOT_APPLICABLE} from "../../../../utilities/constants";
import userRoleWarehouseMixin from "@/mixins/userRoleWarehouse.js";
import {
  SHIPMENT_STATUS
} from '@/utilities/constants';

export default {
  name: "ProductionStatus",
  components: { FilterAdd, DetailsSaleOrder },
  mixins: [
    filterMixin,
    dateMixin,
    formatNumberMixin,
    saleOrderMixin,
    warehouseMixin,
    userRoleWarehouseMixin
  ],
  data() {
    return {
      items: [],
      filter: this.setDefaultFilter(),
      isLoading: false,
      orderDate: '',
      type: 'all',
      types: [
        {
          label: 'All',
          value: 'all',
        },
        {
          label: 'Late 3 Days',
          value: '3days',
        },
        {
          label: 'Late Over 7 Days',
          value: '7days',
        },
      ],
      defaultFilters: [
        {
          name: 'All',
          key: 'all',
          value: clone(this.setDefaultFilter()),
          is_default: 1,
        },
      ],
      localStorageFilterKey: 'production_filter',
      openDialogExport: false,
      isExportActive: false,
      passwordExport: null,
      showNotificationExport: false,
      messageConfirmExport:
        'If you do not select a time period, the default period will be 30 days from today.',
      storeNA: NOT_APPLICABLE,
      shipmentStatus: [
        {
          label: 'All',
          value: '',
        },
        {
          label: 'Unknown',
          value: 'unknown',
        },
        {
          label: 'Pre Transit',
          value: 'pre_transit',
        },
        {
          label: 'In Transit',
          value: 'in_transit',
        },
        {
          label: 'Out For Delivery',
          value: 'out_for_delivery',
        },
        {
          label: 'Delivered',
          value: 'delivered',
        },
        {
          label: 'Available For Pickup',
          value: 'available_for_pickup',
        },
        {
          label: 'Return To Sender',
          value: 'return_to_sender',
        },
        {
          label: 'Failure',
          value: 'failure',
        },
        {
          label: 'Cancelled',
          value: 'cancelled',
        },
        {
          label: 'Error',
          value: 'error',
        },
      ],
    };
  },
  beforeUnmount() {
    EventBus.$off("showDetailsSaleOrder");
  },
  watch: {
    type: {
      deep: true,
      handler() {
        let newDate = "";
        if (this.type === "3days") {
          newDate = this.subtractDays(3);
        }
        if (this.type === "7days") {
          newDate = this.subtractDays(7);
        }
        if (!newDate) {
          this.orderDate = "";
          this.onClearFilter();
          return;
        }
        this.orderDate = [this.subtractDays(0), newDate];
        this.onChangeDate();
      },
    },
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 278);
    },
    ...mapGetters(["getAccounts", "getStores", "getEmployees", "getUserProfile"]),
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },
    buildLinkDownload() {
      let link = `${API_URL}/sale-order-item-shipment/export`;
      let authUser = {
        is_all_warehouse: this.getUserProfile.is_all_warehouse,
        store_ids: this.getUserProfile.store_ids
      }
      let params = {
        ...this.filter,
        ...authUser
      };
      params.warehouse_id = this.userWarehouseId
      delete params.limit
      delete params.page

      params = new URLSearchParams(params);
      return `${link}?${params.toString()}`;
    },
  },
  mounted() {
    this.fetchData();
    this.fetchStore();
    this.fetchAccount();
    this.fetchEmployee();
    this.setFilters(this.defaultFilters, this.localStorageFilterKey);
    this.filter['shipment_status'] = '';
    this.fetchWarehouse();
  },
  methods: {
    displayShipmentStatus(status = '') {
      if (isEmpty(status) || isNil(status)) {
        return '';
      }
      switch (status) {
        case 'unknown':
          return SHIPMENT_STATUS['unknown'];
        case 'pre_transit':
          return SHIPMENT_STATUS['pre_transit'];
        case 'in_transit':
          return SHIPMENT_STATUS['in_transit'];
        case 'out_for_delivery':
          return SHIPMENT_STATUS['out_for_delivery'];
        case 'delivered':
          return SHIPMENT_STATUS['delivered'];
        case 'available_for_pickup':
          return SHIPMENT_STATUS['available_for_pickup'];
        case 'return_to_sender':
          return SHIPMENT_STATUS['return_to_sender'];
        case 'failure':
          return SHIPMENT_STATUS['failure'];
        case 'cancelled':
          return SHIPMENT_STATUS['cancelled'];
        case 'error':
          return SHIPMENT_STATUS['error'];
        default:
          return '';
      }
    },
    showDetails(data){
      EventBus.$emit("showDetailsSaleOrder", data);
    },
   async exportExcel() {
      window.location.href = await this.buildLinkDownload;
      this.onClose()
    },
    setType(type) {
      this.type = type;
    },
    async fetchEmployee() {
      await this.$store.dispatch("getEmployees");
    },
    onClear(item) {
      this.filter[item] = "";
      this.onFilter();
    },
    handleSelectionChange(data) {
      console.log(data);
    },
    async fetchStore() {
      await this.$store.dispatch("getStores", { without_pagination: 1 });
    },
    async fetchAccount() {
      await this.$store.dispatch("getAccounts", { without_pagination: 1 });
    },
    async fetchData() {
      this.filter = this.getRouteParam();
      this.fetchProductionStatus();
    },
    clearOrderType(item = "") {
      this.filter.pulled = "";
      this.filter.pretreated = "";
      this.filter.printed = "";
      this.filter.qc = "";
      this.filter.staged = "";
      this.filter.folded = "";
      this.filter.shipped = "";
      this.filter.reprinted = "";
      this.filter.shipment_status = "";
      if (item && this.$refs[item]) {
        this.$refs[item].handleClose();
      }
    },
    onFilter(item = "") {
      this.filter.page = 1;
      this.$nextTick(() => {
        this.fetchProductionStatus();
        if (item && this.$refs[item]) {
          this.$refs[item].handleClose();
        }
      });
    },
    onClearFilter() {
      this.filterName = "all";
      this.filter = this.setDefaultFilter();
      this.orderDate = "";
      this.$nextTick(() => {
        this.fetchProductionStatus();
      });
    },
    onChangeDate() {
      if (this.orderDate && this.orderDate.length) {
        this.filter.order_date_start = this.formatDate(
          this.orderDate[0],
          false
        );
        this.filter.order_date_end = this.formatDate(this.orderDate[1], false);
      } else {
        this.filter.order_date_start = "";
        this.filter.order_date_end = "";
      }

      this.onFilter();
    },
    setDefaultFilter() {
      let params = {
        limit: 25,
        page: 1,
        keyword: "",
        order_date_start: "",
        order_date_end: "",
        store_id: "",
        employee_id: "",
        pulled: "",
        pretreated: "",
        pressed: "",
        printed: "",
        qc: "",
        staged: "",
        folded: "",
        shipped: "",
        reprinted: "",
        barcode_printed_id: "",
        filter_name: "all",
        warehouse: "",
        shipment_status: "",
      };
      return params;
    },
    setRouteParam() {
      const params = this.filter;
      this.$router.replace({ name: "production_status", query: params });
    },
    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);
      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }
      if (filter.order_date_start && filter.order_date_end) {
        this.orderDate = [filter.order_date_start, filter.order_date_end];
      }
      filter.page = +filter.page || 1;
      return filter;
    },
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchProductionStatus();
      });
    },
    async fetchProductionStatus() {
      this.isLoading = true;
      this.setRouteParam();
      list(this.filter).then((res) => {
        this.isLoading = false;
        const data = res.data || [];
        this.items = data.data || [];
      });

      list({ ...this.filter, get_total: true }).then((resTotal) => {
        this.total = resTotal.data.total || 1;
      });
    },
    hasChangeFilterByItem(name) {
      const query = this.$route.query;
      if (query[name]) {
        return true;
      }
      return false;
    },
    sortTable(data) {
      let sortColumn = "";
      let sortBy = "";
      if (data.prop && data.order) {
        sortColumn = data.prop;
        if (data.order === "ascending") {
          sortBy = "ASC";
        } else if (data.order === "descending") {
          sortBy = "DESC";
        }
      }
      this.filter.sort_column = sortColumn;
      this.filter.sort_by = sortBy;
      this.$nextTick(() => {
        this.fetchProductionStatus();
      });
    },
    clearFilterItem(item) {
      this.filter[item] = "";
      if (this.$refs[item]) {
        this.$refs[item].handleClose();
      }
      this.onFilter();
    },
    getEmployee(id) {
      const selectItem = this.getEmployees.find((item) => +item.id === +id);
      return (selectItem && selectItem.name) || "";
    },
    getAccountById(id) {
      const selectItem = this.getAccounts.find((item) => +item.id === +id);
      return (selectItem && selectItem.name) || "";
    },
    getStoreById(id) {
      const selectItem = this.getStores.find((item) => +item.id === +id);
      return (selectItem && selectItem.code) || this.storeNA;
    },
    tableCellClassName(data) {
      if (data?.column?.property == "age") {
        if (data?.row.created_at) {
          const days = this.getDays(data?.row.created_at);
          if (days === 0) {
            return "text-emerald-500";
          } else if (days >= 3) {
            return "text-red-400";
          } else {
            return "text-warning";
          }
        }
      }
      if (["production_status"].includes(data?.column?.property)) {
        return "text-center";
      }
    },
    tableRowClassName(data) {
      return data.row.is_deleted ? "is-delete" : "";
    },
    headerCellClassName(data) {
      if (["production_status"].includes(data?.column?.property)) {
        return "text-center";
      }
    },

    getOrderStatusByValue(status) {
      const selectItem = this.productionStatus.find(
        (item) => item.value === status
      );
      return (selectItem && selectItem.label) || "";
    },
    checkPasswordExport() {
      let d = new Date();
      let date = d.getDate();
      let month = d.getMonth()+1;
      date = (month < 10 ? '0' + month : month.toString()) + (date < 10 ? '0' + date : date.toString());
      if (this.passwordExport.toUpperCase() == 'SM' + date) {
        this.isExportActive = true;
      } else {
        this.notification(this.$t('Password is incorrect!'), "error");
      }
    },
    onClose() {
      this.openDialogExport = false;
      this.isExportActive = false;
      this.showNotificationExport = false;
      this.passwordExport = null;
      this.fileName = null;
    },
    openDialogExportShipment(){
      if (this.filter.order_date_start == "" && this.filter.order_date_end == "") {
        this.showNotificationExport = true;
      } else {
        this.showNotificationExport = false;
      }
      this.openDialogExport = true;
    }
  },
};
