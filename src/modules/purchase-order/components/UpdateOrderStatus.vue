<template>
  <el-dialog
    v-model="openDialogUpdateStatus"
    :title="'Update Order Status ' + '(' + purchaseOrder.po_number + ')'"
    custom-class="el-dialog-custom el-dialog-update-status-purchase-order"
    :close-on-click-modal="false"
  >
    <template #default>
      <el-form :label-position="'top'" @submit.prevent="updateStatus">
        <el-form-item :label="$t('Order Status')" class="mb-0">
          <el-select
            v-model="currentStatus"
            :placeholder="$t('Select order status')"
            class="el-select-full-width"
          >
            <el-option
              v-for="item in orderStatus"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </template>
    <template #footer>
      <div class="dialog-footer">
        <el-button
          type="primary"
          @click="updateStatus"
          :disabled="isLoading"
          :loading="isLoading"
          >{{ $t('Update') }}</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script>
import purchaseOrderMixin from "@/mixins/purchaseOrder.js";
import { updateOrderStatusPurchaseOrder } from "@/api/purchaseOrder.js";
import EventBus from "@/utilities/eventBus.js";
import { isEmpty } from "ramda";
export default {
  name: "PurchaseOrderUpdateStatus",
  mixins: [purchaseOrderMixin],
  data() {
    return {
      showMessage: false,
      isLoading: false,
      currentStatus: "",
      openDialogUpdateStatus: false,
      purchaseOrder: {},
    };
  },
  watch: {},
  created() {
    EventBus.$on("showUpdateStatus", (data) => {
      data = Object.assign({}, data);
      this.currentStatus = data.order_status;
      this.purchaseOrder = data;
      this.openDialogUpdateStatus = true;
    });
  },
  computed: {
    orderStatus() {
      return this.purchaseOrderStatus.filter(
        (status) => status.value !== "incompleted"
      );
    }
  },
  mounted() {},
  methods: {
    async updateStatus() {
      const params = {
        po_id: this.purchaseOrder.id,
        order_status: this.currentStatus,
      };
      this.isLoading = true;
      try {
        await updateOrderStatusPurchaseOrder(params);
        this.notification(this.$t('Update order status purchase order successfully.'));
        this.openDialogUpdateStatus = false;
        this.isLoading = false;
        this.$emit("onRefresh");
        EventBus.$emit("updateStatus", this.currentStatus);
      } catch (e) {
        this.isLoading = false;
        let message = this.$t('Update order status error.');
        const data = e.response.data || {};
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          console.log(keyFirstData, "keyFirstData");
          const firstData = data[keyFirstData];
          message = firstData;
        }
        this.notification(message, "error");
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
