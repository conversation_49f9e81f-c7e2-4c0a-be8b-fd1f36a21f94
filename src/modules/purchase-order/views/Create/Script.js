import EventBus from "@/utilities/eventBus.js";
import { mapGetters } from "vuex";
import { add } from "@/api/purchaseOrder.js";
import { isEmpty } from "ramda";
import dateMixin from "@/mixins/date.js";

export default {
  name: "PurchaseOrderAdd",
  components: {},
  mixins: [dateMixin],
  data() {
    return {
      data: this.setDefautData(),
      rules: {
        // order_number: [
        //   {
        //     required: true,
        //     message: this.$t('This field cannot be left blank.'),
        //     trigger: "blur",
        //   },
        // ],
        order_date: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: "blur",
          },
        ],
        discount: [
          {
            required: true,
            message: "This field cannot be left blank",
            trigger: ['blur', 'change'],
          },
          {
            validator: (rule, value, callback) => {
              const totalBeforeDiscount = parseFloat(this.totalPrice) + parseFloat(this.data.fee);
              if (value > totalBeforeDiscount) {
                callback(new Error("This field must be less than or equal to Total."));
              } else {
                callback();
              }
            },
            trigger: ['blur', 'change'],
          },
        ],
      },
      isLoading: false,
      products: [],
      totalPrice: 0,
      serverError: {},
      openDialogAddPurchaseOrder: false,
    };
  },
  computed: {
    ...mapGetters(["getVendors"]),
    ...mapGetters(["getPaymentTerms"]),
    ...mapGetters(["getCarriers", "getTags"]),
    lastPrice() {
      const result = parseFloat(parseFloat(this.totalPrice) + parseFloat(this.data.fee) - parseFloat(this.data.discount)).toFixed(2);

      return result >= 0 ? result : parseFloat(0).toFixed(2);
    }
  },
  beforeUnmount() {
    EventBus.$off("productSelect");
  },
  created() {
    EventBus.$on("showCreatePurchaseOrder", () => {
      this.openDialogAddPurchaseOrder = true;
    });
    EventBus.$on("selectProducts", (products) => {
      this.selectProducts(products);
    });
  },
  mounted() {},
  methods: {
    resetData() {
      this.$refs['addPurchaseOrderForm'].resetFields();
      this.data = this.setDefautData();
      this.items = [];
      this.products = [];
      this.totalPrice = 0;
      this.serverError = {};
    },
    setDefautData() {
      return {
        vendor_id: "",
        po_number: "",
        order_number: "",
        invoice_number: "",
        order_date: "",
        delivery_date: "",
        payment_terms: "",
        tracking_carrier: "",
        tracking_number: "",
        note: "",
        tag: "",
        fee: 0,
        discount: 0,
      };
    },
    selectProduct() {
      EventBus.$emit("productSelect");
    },
    showPaymentTermManagement() {
      EventBus.$emit("showPaymentTermManagement");
    },
    showCarrierManagement() {
      EventBus.$emit("showListCarrier");
    },
    showTagManagement() {
      EventBus.$emit("showListTag");
    },
    selectProducts(products) {
      products = products.map((item) => {
        return {
          total: this.getTotalItem(item),
          name: item.name,
          sku: item.sku,
          quantity: 1,
          price: 0,
          product_id: item.id,
        };
      });
      if (this.products.length) {
        this.products.forEach((item) => {
          products.forEach((newItem) => {
            if (item.product_id === newItem.product_id) {
              item.quantity += 1;
              item.total = this.getTotalItem(item);
              newItem.remove = 1;
            }
          });
        });
        products = products.filter((item) => item.remove !== 1);
        this.products = [...this.products, ...products];
      } else {
        this.products = products;
      }
      this.getTotalPrice();
    },
    changeProductItem(item) {
      this.products.forEach((product) => {
        if (product.id === item.id) {
          product.total = this.getTotalItem(product);
        }
      });
      this.getTotalPrice();
    },
    removeProductItem(index) {
      this.products.splice(index, 1);
      this.getTotalPrice();
    },
    getTotalPrice() {
      this.$nextTick(() => {
        let totalPrice = 0;
        if (this.products && this.products.length) {
          this.products.forEach((item) => {
            totalPrice = parseFloat(totalPrice) + parseFloat(item.total);
          });
        }
        totalPrice = parseFloat(totalPrice).toFixed(2);
        this.totalPrice = totalPrice;
      });
    },
    getTotalItem(item) {
      if (!item.price || !item.quantity) {
        return 0;
      }
      return parseFloat(item.price * item.quantity).toFixed(2);
    },
    async onSubmit(formName) {
      const isValid = await this.$refs[formName].validate();
      if (!isValid) {
        return;
      }
      let params = this.data;
      const products = this.products.map((item) => {
        return {
          product_id: item.product_id,
          quantity: item.quantity,
          price: item.price,
          total: item.total,
        };
      });
      params.items = products;
      params.order_date = this.formatDate(params.order_date);
      params.delivery_date = this.formatDate(params.delivery_date);
      params.sub_total = this.totalPrice;
      params.total = this.lastPrice;
      params = {
        ...params,
        fee: parseFloat(params.fee).toFixed(2),
        discount: parseFloat(params.discount).toFixed(2)
      }
      this.isLoading = true;
      try {
        await add(params);
        this.isLoading = false;
        this.$emit("onRefresh");
        this.openDialogAddPurchaseOrder = false;
        this.notification(this.$t("Add purchase order successfully."));
      } catch (e) {
        this.isLoading = false;
        let message = this.$t("Purchase order add error.");
        const data = e.response.data || {};
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
          let serverError = {};
          Object.keys(data).forEach((key) => {
            serverError[key] = {};
            if (data[key]) {
              data[key].forEach((item) => {
                serverError[key][item] = item;
              });
            }
          });
          this.serverError = serverError;
          this.focusFirtError();
        }
        this.notification(message, "error");
      }
    },
    focusFirtError() {
      this.$nextTick(() => {
        const elErrors = document.getElementsByClassName("is-error");
        if (!elErrors || !elErrors.length) {
          return;
        }
        const firtsElError = (elErrors && elErrors[0]) || undefined;
        const input = firtsElError.querySelector("input");
        document.body.scrollTop = (input.offsetTop - 120) | 0;
        input.focus();
      });
    },
    changeTag() {
      if (this.data.tag && Array.isArray(this.data.tag)) {
        this.data.tag = this.data.tag.filter((tag) => tag);
      }
    },
  },
};
