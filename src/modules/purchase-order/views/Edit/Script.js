import EventBus from "@/utilities/eventBus.js";
import { mapGetters } from "vuex";
import { update, details, searchOrderParent } from "@/api/purchaseOrder.js";
import PurchaseOrderHistory from "@/modules/purchase-order/components/History.vue";
import { isEmpty } from "ramda";
import purchaseOrderMixin from "@/mixins/purchaseOrder.js";
import dateMixin from "@/mixins/date.js";

export default {
  name: "PurchaseOrderEdit",
  components: {
    PurchaseOrderHistory,
  },
  mixins: [purchaseOrderMixin, dateMixin],
  data() {
    return {
      data: this.setDefautData(),
      rules: {
        // order_number: [
        //   {
        //     required: true,
        //     message: this.$t('This field cannot be left blank.'),
        //     trigger: "blur",
        //   },
        // ],
        order_date: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: "blur",
          },
        ],
        discount: [
          {
            required: true,
            message: "This field cannot be left blank",
            trigger: ['blur', 'change'],
          },
          {
            validator: (rule, value, callback) => {
              const totalBeforeDiscount = parseFloat(this.totalPrice) + parseFloat(this.data.fee);
              if (value > totalBeforeDiscount) {
                callback(new Error("This field must be less than or equal to Total."));
              } else {
                callback();
              }
            },
            trigger: ['blur', 'change'],
          },
        ],
      },
      isLoading: false,
      isLoadingSearch: false,
      items: [],
      products: [],
      totalPrice: 0,
      shortAmount: 0,
      serverError: {},
      purchaseOrderId: this.$route.params.id,
      isLoadingFetchData: false,
      openDialogEditPurchaseOrder: false,
    };
  },
  computed: {
    ...mapGetters(["getVendors"]),
    ...mapGetters(["getPaymentTerms"]),
    ...mapGetters(["getCarriers", "getTags"]),
    disabledEdit() {
      return ['completed', 'closed'].includes(this.data.order_status);
    },
    isCompletedOrder() {
      return ['completed', 'cancelled', 'closed'].includes(this.data.order_status);
    },
    isClosedOrder() {
      return ['closed'].includes(this.data.order_status);
    },
    lastPrice() {
      const result = this.isClosedOrder
        ? parseFloat(parseFloat(this.totalPrice) + parseFloat(this.data.fee) - parseFloat(this.shortAmount) - parseFloat(this.data.discount)).toFixed(2)
        : parseFloat(parseFloat(this.totalPrice) + parseFloat(this.data.fee) - parseFloat(this.data.discount)).toFixed(2);

      return this.isClosedOrder ? result : (result >= 0 ? result : parseFloat(0).toFixed(2));
    }
  },
  beforeUnmount() {
    EventBus.$off("productSelect");
  },
  created() {
    EventBus.$on("showEditPurchaseOrder", (data) => {
      this.purchaseOrderId = data.id;
      this.openDialogEditPurchaseOrder = true;
      this.fetchDetails();
    });
    EventBus.$on("selectProducts", (products) => {
      this.selectProducts(products);
    });
    EventBus.$on("updateStatus", (status) => {
      this.data.order_status = status;
      this.fetchDetails();
    });
  },
  mounted() {},
  methods: {
    showUpdateStatus() {
      EventBus.$emit("showUpdateStatus", this.data);
    },
    resetData() {
      this.$refs['editPurchaseOrderForm'].resetFields();
      this.data = this.setDefautData();
      this.items = [];
      this.products = [];
      this.totalPrice = 0;
      this.shortAmount = 0;
      this.serverError = {};
    },
    setDefautData() {
      return {
        vendor_id: "",
        order_number: "",
        reference_number: "",
        order_date: "",
        delivery_date: "",
        payment_terms: "",
        tracking_carrier: "",
        tracking_number: "",
        note: "",
        tag: "",
        fee: 0,
        discount: 0,
      };
    },
    async fetchDetails() {
      this.isLoadingFetchData = true;
      const res = await details(this.purchaseOrderId);
      this.isLoadingFetchData = false;
      const data = res.data;
      if (!data.tag) {
        data.tag = "";
      } else {
        data.tag = data.tag.split(",");
      }
      this.data = {
        ...data,
        payment_terms: data.payment_terms || "",
        tracking_carrier: data.tracking_carrier || "",
        fee: data.fee || 0,
        sub_total: data.sub_total || 0,
        discount: data.discount || 0,
        total: data.total || 0,
      };
      const items =
        (data.items &&
          data.items.map((item) => {
            return {
              ...item,
              name: (item.product && item.product.name) || "",
              sku: (item.product && item.product.sku) || "",
              total: item.total || 0,
            };
          })) ||
        [];
      this.products = items;
      this.getTotalPrice();
      this.getShortAmount();
    },
    selectProduct() {
      EventBus.$emit("productSelect");
    },
    showPaymentTermManagement() {
      EventBus.$emit("showPaymentTermManagement");
    },
    showCarrierManagement() {
      EventBus.$emit("showListCarrier");
    },
    showTagManagement() {
      EventBus.$emit("showListTag");
    },
    async searchPurchaseOrder(query) {
      if (query) {
        this.isLoadingSearch = true;
        const res = await searchOrderParent({ keyword: query });
        this.isLoadingSearch = false;
        const data = res.data;
        this.items = data;
      } else {
        this.items = [];
      }
    },

    selectProducts(products) {
      products = products.map((item) => {
        return {
          name: item.name,
          sku: item.sku,
          quantity: 1,
          quantity_onhand :0,
          price: 0,
          product_id: item.id,
          total: this.getTotalItem(item),
        };
      });
      if (this.products.length) {
        this.products.forEach((item) => {
          products.forEach((newItem) => {
            if (item.product_id === newItem.product_id) {
              item.quantity += 1;
              item.total = this.getTotalItem(item);
              newItem.remove = 1;
            }
          });
        });
        products = products.filter((item) => item.remove !== 1);
        this.products = [...this.products, ...products];
      } else {
        this.products = products;
      }
      this.getTotalPrice();
      this.getShortAmount();
    },
    changeProductItem(item) {
      this.products.forEach((product) => {
        if (product.id === item.id) {
          product.total = this.getTotalItem(product);
        }
      });
      this.getTotalPrice();
      this.getShortAmount();
    },
    removeProductItem(index) {
      this.products.splice(index, 1);
      this.getTotalPrice();
      this.getShortAmount();
    },
    getTotalPrice() {
      this.$nextTick(() => {
        let totalPrice = 0;
        if (this.products && this.products.length) {
          this.products.forEach((item) => {
            const totalItem = this.isClosedOrder ? this.getTotalItem(item) : parseFloat(item.total)
            totalPrice = parseFloat(totalPrice) + parseFloat(totalItem);
          });
        }
        totalPrice = parseFloat(totalPrice).toFixed(2);
        this.totalPrice = totalPrice;
      });
    },
    getTotalItem(item) {
      if (!item.price || !item.quantity) {
        return 0;
      }
      return parseFloat(item.price * item.quantity).toFixed(2);
    },
    getShortAmount() {
      this.$nextTick(() => {
        let totalShortAmount = 0;
        if (this.products && this.products.length) {
          this.products.forEach((item) => {
            totalShortAmount = parseFloat(totalShortAmount) + parseFloat(this.getShortAmountItem(item));
          });
        }
        totalShortAmount = parseFloat(totalShortAmount).toFixed(2);
        this.shortAmount = totalShortAmount;
      });
    },
    getShortAmountItem(item) {
      if (!item.price || !item.quantity) {
        return 0;
      }
      const quantityOnhand = item.quantity_onhand ?? 0;

      return parseFloat(item.price * (item.quantity - quantityOnhand)).toFixed(2);
    },
    async onSubmit(formName) {
      const isValid = await this.$refs[formName].validate();
      if (!isValid) {
        return;
      }
      let params = this.data;
      const products = this.products.map((item) => {
        return {
          id: item.id || undefined,
          product_id: item.product_id,
          quantity: item.quantity,
          price: item.price,
          total: item.total,
        };
      });
      params.items = products;

      this.isLoading = true;
      params.order_date = this.formatDate(params.order_date);
      params.delivery_date = this.formatDate(params.delivery_date);
      params.sub_total = this.totalPrice;
      params.total = this.lastPrice;
      params = {
        ...params,
        fee: parseFloat(params.fee).toFixed(2)
      }
      try {
        await update(params);
        this.isLoading = false;
        this.$emit("onRefresh");
        this.openDialogEditPurchaseOrder = false;
        this.notification(this.$t("Update purchase order successfully."));
      } catch (e) {
        this.isLoading = false;
        let message = this.$t("Purchase order update error.");
        const data = e.response.data || {};
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
          let serverError = {};
          Object.keys(data).forEach((key) => {
            serverError[key] = {};
            if (data[key]) {
              data[key].forEach((item) => {
                serverError[key][item] = item;
              });
            }
          });
          this.serverError = serverError;
          this.focusFirtError();
        }
        this.notification(message, "error");
      }
    },
    focusFirtError() {
      this.$nextTick(() => {
        const elErrors = document.getElementsByClassName("is-error");
        if (!elErrors || !elErrors.length) {
          return;
        }
        const firtsElError = (elErrors && elErrors[0]) || undefined;
        const input = firtsElError.querySelector("input");
        document.body.scrollTop = (input.offsetTop - 120) | 0;
        input.focus();
      });
    },
    changeTag() {
      if (this.data.tag && Array.isArray(this.data.tag)) {
        this.data.tag = this.data.tag.filter((tag) => tag);
      }
    },
  },
};
