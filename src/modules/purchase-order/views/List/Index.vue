<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ $t('Purchase Orders') }}</h1>
      </div>
      <div class="top-head-right">
        <el-button @click="openModalExport" plain type="primary">
          <span class="icon-margin-right"><icon :data="iconExport" /></span
          >{{ $t('Export') }}
        </el-button>
        <el-button type="primary" @click="createPurchaseOrder">
          <span class="icon-margin-right"><icon :data="iconAdd" /></span
          >{{ $t('Create') }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <div class="">
        <el-tabs
          class="el-tab-filter"
          type="card"
          v-model="defaultTab"
          @tab-click="handleTabClick"
        >
          <el-tab-pane
            :label="item.name"
            :name="item.key"
            v-for="item in tabs"
            :key="item.key"
          >
            <template #label>
              <span class="custom-tabs-label">
                <span>
                  {{ item.name }}
                  <span
                    v-if="item.total > 0"
                    class="text-white bg-orange-400 rounded-full px-2 py-1 text-[11px]"
                    >{{ item.total }}</span
                  >
                </span>
              </span>
            </template>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="stat-overview grid grid-cols-4 gap-4 mb-6">
        <div class="stat-box flex items-center justify-center flex-col bg-white border shadow rounded-xl py-3">
          <span class="text-sm font-semibold text-gray-500">Total Unit</span>
          <span class="text-2xl font-bold text-gray-800">{{ formatNumber(summary.total_unit) }}</span>
        </div>
        <div class="stat-box flex items-center justify-center flex-col bg-white border shadow rounded-xl py-3">
          <span class="text-sm font-semibold text-gray-500">Addition Unit</span>
          <span class="text-2xl font-bold text-gray-800">{{ formatNumber(summary.addition_unit) }}</span>
        </div>
        <div class="stat-box flex items-center justify-center flex-col bg-white border shadow rounded-xl py-3">
          <span class="text-sm font-semibold text-gray-500">Incoming Unit</span>
          <span class="text-2xl font-bold text-gray-800">{{ formatNumber(summary.incoming_unit) }}</span>
        </div>
        <div class="stat-box flex items-center justify-center flex-col bg-white border shadow rounded-xl py-3">
          <span class="text-sm font-semibold text-gray-500">Total Purchase Amount</span>
          <span class="text-2xl font-bold text-gray-800">${{ (summary.total_purchase_amount) }}</span>
        </div>
      </div>

      <div class="filter">
        <div class="label">{{ $t('Filter by:') }}</div>
        <div class="filter-item">
          <el-dropdown
            ref="po_number"
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('po_number') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('po_number')">
                <el-tooltip effect="dark" content="PO#" placement="top-start">
                  <span>{{ filter.po_number }}</span>
                </el-tooltip>
              </template>
              <template v-else> PO# </template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input
                  :placeholder="$t('Enter search keyword')"
                  class="search"
                  v-model="filter.po_number"
                  @keydown.enter="onFilter('po_number')"
                  clearable
                  @clear="clearFilterItem('po_number')"
                />
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item">
          <el-dropdown
            ref="order_number"
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('order_number') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('order_number')">
                <el-tooltip
                  effect="dark"
                  :content="$t('Order Number')"
                  placement="top-start"
                >
                  <span>{{ filter.order_number }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(' Order Number ') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input
                  :placeholder="$t('Enter search keyword')"
                  class="search"
                  v-model="filter.order_number"
                  @keydown.enter="onFilter('order_number')"
                  clearable
                  @clear="clearFilterItem('order_number')"
                />
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item">
          <el-dropdown
            ref="invoice_number"
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('invoice_number') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('invoice_number')">
                <el-tooltip
                  effect="dark"
                  :content="$t('Invoice Number')"
                  placement="top-start"
                >
                  <span>{{ filter.invoice_number }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(' Invoice Number ') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input
                  :placeholder="$t('Enter search keyword')"
                  class="search"
                  v-model="filter.invoice_number"
                  @keydown.enter="onFilter('invoice_number')"
                  clearable
                  @clear="clearFilterItem('invoice_number')"
                />
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item">
          <el-dropdown
            ref="tracking_number"
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('tracking_number') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('tracking_number')">
                <el-tooltip
                  effect="dark"
                  :content="$t('Tracking Number')"
                  placement="top-start"
                >
                  <span>{{ filter.tracking_number }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(' Tracking Number ') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input
                  :placeholder="$t('Enter search keyword')"
                  class="search"
                  v-model="filter.tracking_number"
                  @keydown.enter="onFilter('tracking_number')"
                  clearable
                  @clear="clearFilterItem('tracking_number')"
                />
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item">
          <el-dropdown
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('status') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('status')">
                <el-tooltip
                  effect="dark"
                  :content="$t('Order Status')"
                  placement="top-start"
                >
                  <span>{{ getOrderStatusByValue(filter.status) }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(' Order Status ') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-select
                  filterable
                  v-model="filter.status"
                  :placeholder="$t('Select status')"
                  @change="onFilter"
                >
                  <el-option
                    v-for="item in purchaseOrderStatus"
                    :key="item.value"
                    :label="item.label"
                    :value="String(item.value)"
                  >
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item">
          <el-dropdown
            ref="sku"
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('sku') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('sku')">
                <el-tooltip
                  effect="dark"
                  :content="$t('SKU')"
                  placement="top-start"
                >
                  <span>{{ filter.sku }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(' SKU ') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input
                  :placeholder="$t('Enter search keyword')"
                  class="search"
                  v-model="filter.sku"
                  @keydown.enter="onFilter('sku')"
                  clearable
                  @clear="clearFilterItem('sku')"
                />
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item">
          <el-dropdown
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('vendor_id') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('vendor_id')">
                <el-tooltip
                  effect="dark"
                  :content="$t('Vendor')"
                  placement="top-start"
                >
                  <span>{{ getVendorById(filter.vendor_id) }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(' Vendor ') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-select
                  filterable
                  v-model="filter.vendor_id"
                  :placeholder="$t('Select vendor')"
                  @change="onFilter"
                >
                  <el-option
                    v-for="item in getVendors"
                    :key="item.id"
                    :label="item.name"
                    :value="String(item.id)"
                  >
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item">
          <el-dropdown
            ref="tag"
            trigger="click"
            class="el-dropdown-filter-item"
            @visible-change="handleOpenTag"
          >
            <span class="el-dropdown-link">
              {{ $t(' Tag ') }}
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-select
                  filterable
                  multiple
                  v-model="filter.tag"
                  :placeholder="$t('Select tag')"
                  @change="onFilter('tag')"
                  @remove-tag="$refs.tag.handleClose()"
                  :popper-append-to-body="openDropdownTag"
                >
                  <el-option
                    v-for="item in getTags"
                    :key="item.id"
                    :label="item.name"
                    :value="String(item.id)"
                  >
                    <span>{{ item.name }}</span>
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item">
          <el-dropdown
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{
              'is-active': filter.order_date && filter.order_date.length,
            }"
          >
            <span class="el-dropdown-link">
              <template v-if="filter.order_date && filter.order_date.length">
                <el-tooltip
                  effect="dark"
                  :content="$t('Order date')"
                  placement="top-start"
                >
                  <span>
                    {{
                      templateDateRange(
                        filter.order_date[0],
                        filter.order_date[1]
                      )
                    }}</span
                  >
                </el-tooltip>
              </template>
              <template v-else> {{ $t('Order date') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-date-picker
                  format="YYYY-MM-DD"
                  v-model="filter.order_date"
                  type="daterange"
                  range-separator="To"
                  :start-placeholder="$t('Start date')"
                  :end-placeholder="$t('End date')"
                  @change="onChangeDate"
                >
                </el-date-picker>
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item" v-if="hasFilter">
          <el-link type="danger" @click="onClearFilter" :underline="false">
            {{ $t('Clear') }}
          </el-link>
        </div>
      </div>
      <el-table
        border
        stripe
        size="small"
        :data="items"
        style="width: 100%"
        :max-height="maxHeight"
        v-loading="isLoading"
        element-loading-text="Loading..."
        @sort-change="sortTable"
        :cell-class-name="cellClassName"
        :header-cell-class-name="headerCellClassName"
      >
        <el-table-column
          fixed
          prop="po_number"
          :label="$t('PO Number')"
          min-width="150"
        >
          <template #default="scope">
            <el-link
              :underline="false"
              type="primary"
              @click="editPurchaseOrder(scope.row)"
              >{{ scope.row.po_number }}</el-link
            >
          </template>
        </el-table-column>
        <el-table-column
          sortable
          prop="created_at"
          :label="$t('Age')"
          min-width="80"
        >
          <template #default="scope">
            {{ timeFromNow(scope.row.created_at_utc, today) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="order_number"
          :label="$t('Order number')"
          width="150"
        >
          <template #default="scope">
            {{ scope.row.order_number }}
          </template>
        </el-table-column>
        <el-table-column
          prop="invoice_number"
          :label="$t('Invoice Number')"
          width="150"
        >
          <template #default="scope">
            <span class="break-normal">
              {{ scope.row.invoice_number }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="order_date"
          :label="$t('Order Date')"
          width="130"
          sortable="custom"
        >
          <template #default="scope">
            {{ scope.row.order_date }}
          </template>
        </el-table-column>

        <el-table-column prop="vendor_id" :label="$t('Vendor')" min-width="140">
          <template #default="scope">
            {{ scope.row.vendor && scope.row.vendor.name }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Received Box')" min-width="150">
          <template #default="scope">
            {{ formatNumber(scope.row.total_received_box) }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Total Qty')" min-width="100">
          <template #default="scope">
            {{ formatNumber(scope.row.total_quantity) }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Addition Qty')" min-width="100">
          <template #default="scope">
            {{ formatNumber(scope.row.total_quantity_onhand) }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Incoming Qty')" min-width="100">
          <template #default="scope">
            {{ formatNumber(scope.row.total_incoming) }}
          </template>
        </el-table-column>
        <el-table-column prop="amount" :label="$t('Amount')" min-width="100">
          <template #default="scope">
            ${{ formatNumber(scope.row.total_price) || 0 }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Status')" min-width="150">
          <template #default="scope">
            <div v-html="getOrderStatus(scope.row)"></div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('Payment')" min-width="150" prop="payment_status">          
        </el-table-column>
        <el-table-column label="Tracking#" min-width="220">
          <template #default="scope">
            {{ scope.row.tracking_number }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('ETA')" min-width="120">
          <template #default="scope">
            {{ scope.row.delivery_date }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Tag')" min-width="160">
          <template #default="scope">
            <span v-for="(tag, index) in renderTag(scope.row.tag)" :key="index">
              <el-tag
                size="small"
                class="mr-2 mb-1"
                type="info"
                :color="tag.color"
                v-if="tag.color"
                style="color: #fff"
                >{{ tag.name }}</el-tag
              >
              <el-tag size="small" class="mr-2 mb-1" type="info" v-else>{{
                tag.name
              }}</el-tag>
            </span>
          </template>
        </el-table-column>
        <!-- <el-table-column :label="$t('Total box')" width="100">
          <template #default="scope">
            {{ scope.row.total_box || 0 }}
          </template>
        </el-table-column> -->
        <el-table-column :label="$t('User')" min-width="100">
          <template #default="scope">
            {{ scope.row.user && scope.row.user.username }}
          </template>
        </el-table-column>
        <el-table-column
          prop="box"
          :label="$t('Box')"
          width="60"
          fixed="right"
          class="text-center"
        >
          <template #default="scope">
            <!-- <el-popconfirm
              v-if="
                scope.row &&
                scope.row.order_status &&
                scope.row.order_status !== 'cancelled'
              "
              :title="
                'Are you sure to cancelled ' +
                scope.row.po_number +
                '?'
              "
              @confirm="cancelPurchaseOrder(scope.row)"
            >
              <template #reference>
                <el-link :underline="false" type="primary">{{ $t('Cancelled') }}</el-link>
              </template>
            </el-popconfirm> -->
            <div @click="viewBox(scope.row)">
              <el-link :underline="false" type="primary">{{
                scope.row.total_box || 0
              }}</el-link>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column
          prop="action"
          :label="$t('Actions')"
          width="100"
          fixed="right"
          class="text-center"
        >
          <template #default="scope">
            <el-link
              :underline="false"
              type="primary"
              @click="showUpdateStatus(scope.row)"
              >{{ $t('Update status') }}</el-link
            >
          </template>
        </el-table-column> -->
      </el-table>
      <div class="bottom">
        <div class="total">
          {{ $t('Total PO:') }} {{ items.length ? formatNumber(total) : 0 }}
        </div>
        <el-pagination
          :disabled="isLoading"
          background
          layout="prev, pager, next"
          :page-size="filter.limit"
          :total="total"
          @current-change="changePage"
          v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
            v-model="filter.limit"
            :placeholder="$t('Select')"
            size="mini"
            @change="onFilter"
          >
            <el-option
              v-for="item in limits"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
    <el-dialog
        v-model="dialogExport"
        :title="$t('Export Purchase Orders')"
        custom-class="el-dialog-custom custom-dialog rounded-xl"
        @close="resetData"
        :close-on-click-modal="false"
    >
      <template #default>
        <div
            class="my-4 mx-auto items-center flex flex-col justify-center"
        >
          <el-date-picker
              format="YYYY-MM-DD"
              v-model="date"
              :clearable="false"
              type="daterange"
              range-separator="To"
              :start-placeholder="$t('Start Date')"
              :end-placeholder="$t('End Date')"
          >
          </el-date-picker>
        </div>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetData">Cancel</el-button>
          <el-button @click="exportExcel" type="primary">Export</el-button>
        </div>
      </template>
    </el-dialog>
    <PurchaseOrderExport :filter="filter" :total="total" />
    <PaymentTermList />
    <CarrierList />
    <SelectProduct />
    <TagList :sourceTag="sourceTag" />
    <CreatePurchaseOrder @onRefresh="onClearFilter" />
    <EditPurchaseOrder @onRefresh="fetchPurchaseOrder" />
    <ViewBoxPurchaseOrder />
    <UpdateStatusPurchaseOrder @onRefresh="fetchPurchaseOrder" />
  </div>
</template>
