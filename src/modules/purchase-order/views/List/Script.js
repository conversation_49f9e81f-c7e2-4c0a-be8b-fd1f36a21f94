import { list, cancelPurchaseOrder, getSummary } from "@/api/purchaseOrder.js";
import { mapGetters } from "vuex";
import PurchaseOrderExport from "@/modules/purchase-order/components/Export.vue";
import EventBus from "@/utilities/eventBus.js";
import {clone, equals} from "ramda";
import filterMixin from "@/mixins/filter";
import PaymentTermList from "@/modules/payment-term/views/List/Index.vue";
import CarrierList from "@/modules/carrier/views/List/Index.vue";
import TagList from "@/modules/tag/views/List/Index.vue";
import CreatePurchaseOrder from "@/modules/purchase-order/views/Create/Index.vue";
import EditPurchaseOrder from "@/modules/purchase-order/views/Edit/Index.vue";
import ViewBoxPurchaseOrder from "@/modules/purchase-order/components/ViewBox.vue";
import UpdateStatusPurchaseOrder from "@/modules/purchase-order/components/UpdateOrderStatus.vue";

import SelectProduct from "@/modules/product/components/Select.vue";
import purchaseOrderMixin from "@/mixins/purchaseOrder.js";
import dateMixin from "@/mixins/date.js";
import formatNumberMixin from "@/mixins/formatNumber.js";
import warehouseMixin from "@/mixins/warehouse";
import { API_URL } from "@/utilities/constants";
import moment from "moment";
export default {
  name: "PurchaseOrder",
  components: {
    PurchaseOrderExport,
    PaymentTermList,
    CarrierList,
    CreatePurchaseOrder,
    EditPurchaseOrder,
    SelectProduct,
    ViewBoxPurchaseOrder,
    UpdateStatusPurchaseOrder,
    TagList,
  },
  mixins: [filterMixin, purchaseOrderMixin, dateMixin, formatNumberMixin, warehouseMixin],
  data() {
    return {
      items: [],
      rendered: false,
      filter: this.setDefaultFilter(),
      summary: this.setDefaultSummary(),
      isLoading: false,
      sourceTag: "purchase_order",
      openDropdownTag: false,
      date: null,
      dialogExport: false,
      tabs: [
        {
          name : "All",
          key : "all",
        },
        {
          name : "Incoming Orders",
          key : "incoming_order",
          total : 0,
        }
      ],
      defaultTab : "all",
    };
  },
  beforeUnmount() {
    EventBus.$off("purchaseOrderExport");
    EventBus.$off("showPaymentTermManagement");
    EventBus.$off("showListCarrier");
    EventBus.$off("showCreatePurchaseOrder");
    EventBus.$off("showViewBox");
    EventBus.$off("showUpdateStatus");
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 370);
    },
    ...mapGetters(["getBrands", "getVendors", "getTags"]),
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },
    buildLinkDownload() {
      let link = `${API_URL}/purchase-order-export`;

      let params = { ...this.filter };
      delete params.page;
      delete params.limit;
      params.date = [this.formatDate(this.date[0], false), this.formatDate(this.date[1], false)].join(',');
      params.warehouse_id = this.userWarehouseId;

      if (this.filter.date && this.filter.date.length) {
        params.start_date = this.formatDate(this.filter.date[0], false);
        params.end_date = this.formatDate(this.filter.date[1], false);
      }

      params = new URLSearchParams(params);

      return `${link}?${params.toString()}`;
    },
  },
  mounted() {
    this.fetchData();
    this.fetchVendor();
    this.fetchTag();
    this.setDefaultDate();
  },
  methods: {
    exportExcel() {
      return (window.location.href = this.buildLinkDownload);
    },
    openModalExport() {
      this.dialogExport = true;
    },
    resetData() {
      this.dialogExport = false;
      this.setDefaultDate();
    },
    setDefaultDate() {
      let time = new Date();
      this.date = [
        this.formatDate(moment(time).subtract(7, 'days'), false),
        this.formatDate(new Date(), false),
      ];
    },
    async fetchTag() {
      await this.$store.dispatch("getTags", { source: this.sourceTag });
    },
    showUpdateStatus(item) {
      EventBus.$emit("showUpdateStatus", item);
    },
    getOrderStatusByValue(status) {
      const selectItem = this.purchaseOrderStatus.find(
        (item) => item.value === status
      );
      return (selectItem && selectItem.label) || "";
    },
    getVendorById(id) {
      const selectItem = this.getVendors.find((item) => +item.id === +id);
      return (selectItem && selectItem.name) || "";
    },
    viewBox(item) {
      EventBus.$emit("showViewBox", item);
    },
    async fetchVendor() {
      await this.$store.dispatch("getVendors");
    },
    createPurchaseOrder() {
      EventBus.$emit("showCreatePurchaseOrder");
    },
    async fetchData() {
      this.filter = this.getRouteParam();
      this.fetchPurchaseOrder();
      this.fetchBrands();
    },
    onFilter(item = "") {
      this.filter.page = 1;
      this.$nextTick(() => {
        this.fetchPurchaseOrder();
        if (item && this.$refs[item]) {
          this.$refs[item].handleClose();
        }
      });
    },
    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.$nextTick(() => {
        this.fetchPurchaseOrder();
      });
    },
    onChangeDate() {
      if (this.filter.order_date && this.filter.order_date.length) {
        this.filter.order_date[0] = this.formatDate(
          this.filter.order_date[0],
          false
        );
        this.filter.order_date[1] = this.formatDate(
          this.filter.order_date[1],
          false
        );
        this.onFilter();
      }
    },
    setDefaultFilter() {
      let params = {
        limit: 25,
        page: 1,
        vendor_id: "",
        sku: "",
        order_number: "",
        po_number: "",
        invoice_number: "",
        tracking_number: "",
        status: "",
        order_date: "",
        sort_column: "",
        sort_by: "",
        incoming: 'false',
        tag: [],
      };

      return params;
    },

    setDefaultSummary() {
      return {
        total_unit: 0,
        addition_unit: 0,
        incoming_unit: 0,
        total_purchase_amount: 0,
      }
    },

    setRouteParam() {
      const params = this.filter;
      this.$router.replace({ name: "purchase_order", query: params });
    },
    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      if (!filter.order_date || !Array.isArray(filter.order_date) || filter.order_date.length === 0) {
        const nowPST = moment.tz('America/Los_Angeles');
        filter.order_date = [
          this.formatDate(nowPST.clone().subtract(60, 'days'), false),
          this.formatDate(nowPST, false),
        ];
      }
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);
      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }
      filter.page = +filter.page || 1;
      if (!filter.tag) {
        filter.tag = [];
      }
      if (filter.tag && !Array.isArray(filter.tag)) {
        filter.tag = [filter.tag];
      }
      if (filter.incoming == 'true') {
        this.defaultTab = 'incoming_order';
      }
      return filter;
    },
    fetchBrands() {
      this.$store.dispatch("getBrands");
    },
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchPurchaseOrder();
      });
    },
    async fetchPurchaseOrder() {
      this.isLoading = true;
      this.setRouteParam();
      this.fetchTotalIncoming();
      const res = await list(this.filter);
      const summary = await getSummary(this.filter);
      this.summary = summary.data || this.setDefaultSummary();
      this.isLoading = false;
      const data = res.data || [];
      this.total = data.total || 0;
      this.items = data.data || [];
    },
    async fetchTotalIncoming() {
      const resIncoming = await list({incoming: true, get_total: true});
      const totalIncoming = resIncoming?.data?.total ||  0;
      for(let i = 0; i < this.tabs.length; i++) {
        const tab = this.tabs[i];
        if(tab.key !== 'incoming_order') continue;
        tab.total = totalIncoming
      }
    },
    getBrandNameById(id) {
      const brand = this.getBrands.find((item) => item.id === id);
      return (brand && brand.name) || "";
    },
    editPurchaseOrder(purchaseOrder) {
      EventBus.$emit("showEditPurchaseOrder", purchaseOrder);
    },
    async cancelPurchaseOrder(purchaseOrder) {
      await cancelPurchaseOrder(purchaseOrder.id);
      this.notification(this.$t("Cancelled purchase order successfully."));
      this.onFilter();
    },
    hasChangeFilterByItem(name) {
      const query = this.$route.query;
      if (query[name]) {
        return true;
      }
      return false;
    },
    sortTable(data) {
      let sortColumn = "";
      let sortBy = "";
      if (data.prop && data.order) {
        sortColumn = data.prop;
        if (data.order === "ascending") {
          sortBy = "ASC";
        } else if (data.order === "descending") {
          sortBy = "DESC";
        }
      }
      this.filter.sort_column = sortColumn;
      this.filter.sort_by = sortBy;
      this.$nextTick(() => {
        this.fetchPurchaseOrder();
      });
    },
    clearFilterItem(item) {
      this.filter[item] = "";
      if (this.$refs[item]) {
        this.$refs[item].handleClose();
      }
      this.onFilter();
    },
    handleOpenTag (isShow) {
      this.openDropdownTag = isShow;
    },
    renderTag(tag) {
      if (!tag) {
        return [];
      }
      const tags = tag.split(",");
      const data = [];
      const length = tags.length;
      for (let i = 0; i < length; i++) {
        const itemTag = this.getTags.find((item) => item.id == tags[i]);
        if (!itemTag) {
          continue;
        }
        data.push(itemTag);
      }
      return data;
    },
    cellClassName(data) {
      if(data?.column?.property=== "amount") {
        return "text-right";
      }
    },
    headerCellClassName(data) {
      if(data?.column?.property=== "amount") {
        return "text-right";
      }
    },
    handleTabClick(){
      this.filter.incoming = false;
      if(this.defaultTab === 'incoming_order'){
        this.filter.incoming = true;
      }
      this.onFilter()
    }
  },
};
