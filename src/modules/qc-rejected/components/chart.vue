<template>
  <div v-loading="isLoading" class="table-content p-4">
    <div class="top-head-left">
      <h1>{{ $t('Fulfillment SLA') }}</h1>
    </div>
    <el-row :gutter="20">
      <!-- Filter Section -->
      <el-col :span="16" class="h-fit my-auto">
        <div class="filter-top mb-4">
          <el-row>
            <el-col :span="8" class="pr-2">
              <el-date-picker style="width: 100%" type="daterange" unlink-panels v-model="filter.date"
                range-separator="To" :start-placeholder="$t('Start date')" :end-placeholder="$t('End date')"
                class="width-option" value-format="YYYY/MM/DD">
              </el-date-picker>
            </el-col>
            <el-col :span="8">
              <div class="btn-filter flex space-x-2">
                <el-button type="primary" @click="onFilter">
                  <span class="icon-margin-right">
                    <icon :data="iconSearch" />
                  </span>{{ $t('Search') }}
                </el-button>
                <el-button type="danger" @click="resetFilter">
                  <span class="icon-margin-right">{{ $t('Clear') }}</span>
                </el-button>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-col>

      <!-- Total Additional Cost Section -->
      <el-col :span="8">
        <div class="border rounded-lg shadow-lg w-[75%] p-4 flex mx-auto">

          <div class="w-full text-right">
            <p class="text-2xl font-semibold text-center mb-4">Total Additional Cost</p>
            <div class="flex justify-between items-center addition_cost w-full">
              <p class="text-2xl font-bold">${{ current_cost.toLocaleString() }}</p>
              <div class="flex items-center text-2xl ml-2">
                <span v-if="current_cost > 0 && previous_cost > 0">
                  <Bottom v-if="current_cost < previous_cost" style="width: 24px; height: 16px;" />
                  <Top v-if="current_cost >= previous_cost" style="width: 24px; height: 16px;" />
                </span>
                <span v-if="current_cost <= 0 || previous_cost <= 0">---</span>
                <span v-else class="text-lg font-semibold">
                  {{ calculateDifference(current_cost, previous_cost) }} %
                </span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>

  <div v-loading="isLoading" class="p-4">
    <div class="grid grid-cols-1 md:grid-cols-12 gap-4">
      <!-- Main Chart Column -->
      <div class="col-span-8">
        <apexchart type="line" height="625" :options="chartOptions" :series="series"></apexchart>
      </div>

      <!-- Sidebar Column for Donut Charts -->
      <div class="col-span-4 flex flex-col space-y-4">
        <div>
          <h1 class="items-center text-center text-red-500	text-[18px]">Current Failed Items: {{ current_failed_items }}
          </h1>

          <h2 class="flex items-center">

            1st Failed
            <span class="tooltip-icon ml-2" @mouseover="showTooltip('tooltip1')"
              @mouseleave="hideTooltip('tooltip1')">?</span>
          </h2>
          <div class="tooltip-content" id="tooltip1">
            All items that are QC Rejected for the first time.
          </div>
          <apexchart height="175" type="donut" :options="pieChartOptions1" :series="pieSeries1"></apexchart>
        </div>

        <div>
          <h2 class="flex items-center">
            2nd Failed
            <span class="tooltip-icon ml-2" @mouseover="showTooltip('tooltip2')"
              @mouseleave="hideTooltip('tooltip2')">?</span>
          </h2>
          <div class="tooltip-content" id="tooltip2">
            All items that are QC Rejected for the second time.
          </div>
          <apexchart height="175" type="donut" :options="pieChartOptions2" :series="pieSeries2"></apexchart>
        </div>

        <div>
          <h2 class="flex items-center">
            3rd Failed
            <span class="tooltip-icon ml-2" @mouseover="showTooltip('tooltip3')"
              @mouseleave="hideTooltip('tooltip3')">?</span>
          </h2>
          <div class="tooltip-content" id="tooltip3">
            All items that are QC Rejected for the 3rd time or more.
          </div>
          <apexchart height="175" type="donut" :options="pieChartOptions3" :series="pieSeries3"></apexchart>
        </div>
      </div>
    </div>
  </div>

  <div v-loading="isLoadingCost" class="p-4">
    <div class="top-head-left pt-4 border-t solid grey">
      <h1>{{ $t('Failed QC incurs to additional cost') }}</h1>
    </div>
    <div class="w-[70%]">
      <div class="flex justify-between space-x-4 py-4">
        <!-- First Filter Group -->
        <div class="flex space-x-4">
          <div class="p-2 rounded">

            <el-select v-model="filterAdditionCost.warehouse_id" placeholder="Select" filterable style="width: 150px">
              <el-option :label="`All Warehouse`" value="all" />
              <el-option v-for="item in warehouses" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </div>
          <div class="bg-blue-100 p-2 rounded">
            <el-date-picker style="width: 270px" class="bg-green-100" type="daterange" unlink-panels
              v-model="filterAdditionCost.current_date" range-separator="To" :start-placeholder="$t('Start date')"
              :end-placeholder="$t('End date')" value-format="YYYY/MM/DD" />
          </div>
        </div>

        <!-- Second Filter Group -->
        <div class="flex space-x-4">
          <div class="p-2 mt-auto mb-auto rounded">

            <span class="mt-auto mb-auto">
              <h3>
                Compare to
              </h3>
            </span>
          </div>
          <div class="bg-green-100 p-2 rounded">

            <el-date-picker style="width: 270px" type="daterange" unlink-panels
              v-model="filterAdditionCost.previous_date" range-separator="To" :start-placeholder="$t('Start date')"
              :end-placeholder="$t('End date')" value-format="YYYY/MM/DD" />
          </div>
        </div>
        <!-- Filter Buttons -->
        <div class="flex space-x-2 p-2">
          <el-button type="primary" @click="onFilterCost">
            <span class="icon-margin-right">
              <icon :data="iconSearch" />
            </span>
            {{ $t('Search') }}
          </el-button>
          <el-button type="danger" @click="resetFilterCost">
            <span class="icon-margin-right">{{ $t('Clear') }}</span>
          </el-button>
        </div>
      </div>
    </div>

    <div class="flex">
      <div class="w-[75%]">
        <el-table :data="dataCostCurrent" style="width: 100%" row-key="id" v-loading="isLoading"
          element-loading-text="Loading..." :summary-method="getSummaries" show-summary border
          :cell-class-name="setDepartmentCellStyle">
          <!-- Department column with green background -->
          <el-table-column prop="department" :label="$t('Department')" width="160">
            <template #default="scope">
              {{ scope.row.department }}
            </template>
          </el-table-column>
          <el-table-column prop="count" :label="$t('Total QC failed')">
            <template #default="scope">
              {{ scope.row.count }}
            </template>
          </el-table-column>
          <el-table-column prop="percentage" :label="$t('Proportion')">
            <template #default="scope">
              {{ scope.row.percentage }} %
            </template>
          </el-table-column>
          <el-table-column prop="rate" :label="$t('Defect rate')">
            <template #default="scope">
              {{ scope.row.rate }} %
            </template>
          </el-table-column>
          <el-table-column prop="cost" :label="$t('Additional cost')">
            <template #default="scope">
              {{ scope.row.cost }}
            </template>
          </el-table-column>
          <el-table-column prop="previous_count" :label="$t('Total QC failed')">
            <template #default="scope">
              {{ scope.row.previous_count }}
            </template>
          </el-table-column>
          <el-table-column prop="previous_percentage" :label="$t('Proportion')">
            <template #default="scope">
              {{ scope.row.previous_percentage }} %
            </template>
          </el-table-column>
          <el-table-column prop="previous_rate" :label="$t('Defect rate')">
            <template #default="scope">
              {{ scope.row.previous_rate }} %
            </template>
          </el-table-column>
          <el-table-column prop="previous_cost" :label="$t('Additional cost')">
            <template #default="scope">
              {{ scope.row.previous_cost }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="w-[25%] mt-auto mb-auto ml-[20px]">
        <el-row :gutter="20" justify="space-between">
          <!-- Card 1 -->
          <el-col :span="12" class="mb-[20px]">
            <el-card class="border border-t-0 rounded-b-lg shadow-lg h-[175px] flex items-center p-4">
              <div class="w-full">
                <p class="font-bold">Total QC
                </p>
                <p class="text-2xl font-bold">{{ current_total }}</p>
                <p class="text-xs flex">
                  <span class="mt-auto mb-auto" v-if="current_total > 0 && previous_total > 0">
                    <Top v-if="current_total > previous_total" style="width: 24px; height: 16px; color: black" />
                    <Bottom v-if="current_total <= previous_total" style="width: 24px; height: 16px; color: black" />
                  </span>
                  <span class="font-bold text-lg" v-if="current_total <= 0 || previous_total <= 0">---</span>
                  <span v-else class="font-bold text-lg">
                    {{ calculateDifference(current_total, previous_total) }} %
                  </span>
                </p>
              </div>
            </el-card>
          </el-col>

          <!-- Card 2 -->
          <el-col :span="12" class="mb-[20px]">
            <el-card class="border border-t-0 rounded-b-lg shadow-lg h-[175px] flex items-center p-4">
              <div class="w-full">
                <p class="font-bold">Total QC Failed</p>
                <p class="text-2xl font-bold">{{ current_item }}</p>
                <p class="text-xs flex">
                  <span class="mt-auto mb-auto" v-if="current_item > 0 && previous_item > 0">
                    <Bottom v-if="current_item < previous_item" style="width: 24px; height: 16px; color: black" />
                    <Top v-if="current_item >= previous_item" style="width: 24px; height: 16px; color: black" />
                  </span>
                  <span class="font-bold text-lg" v-if="current_item <= 0 || previous_item <= 0">---</span>
                  <span v-else class="font-bold text-lg">
                    {{ calculateDifference(current_item, previous_item) }} %
                  </span>
                </p>
              </div>
            </el-card>
          </el-col>

          <!-- Card 3 -->
          <el-col :span="12">
            <el-card class="border border-t-0 rounded-b-lg shadow-lg h-[175px] flex items-center p-4">
              <div class="w-full">
                <p class="font-bold">Defect rate</p>
                <p class="text-2xl font-bold">{{ current_rate }}%</p>
                <p class="text-xs flex">
                  <span class="mt-auto mb-auto" v-if="current_rate > 0 && previous_rate > 0">
                    <Bottom v-if="current_rate < previous_rate" style="width: 24px; height: 16px; color: black" />
                    <Top v-if="current_rate >= previous_rate" style="width: 24px; height: 16px; color: black" />
                  </span>
                  <span class="font-bold text-lg" v-if="current_rate <= 0 || previous_rate <= 0">---</span>
                  <span v-else class="font-bold text-lg">
                    {{ calculateDifference(current_rate, previous_rate) }} %
                  </span>
                </p>
              </div>
            </el-card>
          </el-col>

          <!-- Card 4 -->
          <!-- <el-col :span="12">
            <el-card class="border border-t-0 rounded-b-lg shadow-lg h-[175px] flex items-center p-4">
              <div class="w-full">
                <p class="font-bold">Additional cost</p>
                <p class="text-2xl font-bold">{{ current_cost }}</p>
                <p class="text-xs flex">
                  <span class="mt-auto mb-auto" v-if="current_cost > 0 && previous_cost > 0">
                    <Bottom v-if="current_cost < previous_cost" style="width: 24px; height: 16px; color: black" />
                    <Top v-if="current_cost >= previous_cost" style="width: 24px; height: 16px; color: black" />
                  </span>
                  <span class="font-bold text-lg" v-if="current_cost <= 0 || previous_cost <= 0">---</span>
                  <span v-else class="font-bold text-lg">
                    {{ calculateDifference(current_cost, previous_cost) }} %
                  </span>
                </p>
              </div>
            </el-card>
          </el-col> -->
        </el-row>
      </div>
    </div>
  </div>

  <div v-loading="isLoadingWeekly" class="p-4">
    <div class="top-head-left py-4 border-t solid grey">
      <h1>{{ $t('Weekly Cost') }}</h1>
    </div>
    <div class="w-[70%]">
      <div class="flex justify-between space-x-4">
        <div class="flex space-x-4">
          <el-select v-model="filterWeekly.warehouse_id" placeholder="Select" filterable style="width: 150px">
            <el-option :label="`All Warehouse`" value="all" />
            <el-option v-for="item in warehouses" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>

          <el-date-picker style="width: 270px" type="daterange" unlink-panels v-model="filterWeekly.date"
            range-separator="To" :start-placeholder="$t('Start date')" :end-placeholder="$t('End date')"
            value-format="YYYY/MM/DD" />
        </div>

        <!-- Filter Buttons -->
        <div class="flex space-x-2">
          <el-button type="primary" @click="onFilterWeekly">
            <span class="icon-margin-right">
              <icon :data="iconSearch" />
            </span>
            {{ $t('Search') }}
          </el-button>
          <el-button type="danger" @click="resetFilterWeekly">
            <span class="icon-margin-right">{{ $t('Clear') }}</span>
          </el-button>
        </div>
      </div>
    </div>

    <apexchart type="line" height="350" :options="WeeklychartOptions" :series="chartSeries" />
  </div>
</template>

<script>
import { list, listTableCost, listChartReport } from '@/api/qc_rejected';
import { mapGetters } from 'vuex';
import { Top, Bottom } from '@element-plus/icons-vue';

import { downloadCsv, makeid } from '@/utilities/helper.js';
import EventBus from '@/utilities/eventBus';
import ViewDesignItem from '@/modules/sale-order/components/ViewDesignItem.vue';
import { S3_URL } from '@/utilities/constants';
import { socketClient } from '@/utilities/socket';
import VueApexCharts from 'vue3-apexcharts';
import warehouseMixin from '@/mixins/warehouse';

const socket = socketClient();

export default {
  name: 'QCReport',
  components: {
    apexchart: VueApexCharts,
    ViewDesignItem: ViewDesignItem,
    Top,
    Bottom,
  },
  mixins: [warehouseMixin],
  data() {
    return {
      warehouse_id: null,
      isLoading: false,
      listItem: [],
      listItemTotal: '',
      listItemTotalScreen: '',
      page: 1,
      item_of_page: 50,
      current_failed_items: 0,
      filter: this.setDefaultFilter(),
      warehouses: [],
      listStatus: [
        {
          value: '',
          label: 'All Status',
        },
        {
          value: 'pass',
          label: 'Passed',
        },
        {
          value: 'rejected',
          label: 'Rejected',
        },
      ],
      dataReport: [],
      dataCostCurrent: [],
      dataCostWeek: [],
      chartOptions: {
        chart: {
          id: 'report-chart',
        },
        title: {
          text: 'Average Time To Pass QC', // Your chart title
          align: 'center', // Can be 'left', 'center', or 'right'
          style: {
            fontSize: '18px', // You can adjust the font size
            color: '#333', // You can set the color
          }
        },

        xaxis: {
          categories: [], // This will hold the dates
          rotate: -45, // Rotate labels if they are too crowded
          format: 'yyyy-MM-dd', // Format dates directly if necessary
        },
        // title: {
        //   text: '',
        //   align: 'left',
        // },
        yaxis: {
          title: {
            text: 'Total Failed',
          },
          labels: {
            formatter: function (value) {
              const dayValue = Math.floor(value);  // Round down to the nearest whole number
              return dayValue > 1 ? dayValue + ' days' : dayValue + ' day';  // Handle singular and plural
            },
          },
        },
        tooltip: {
          y: {
            formatter: function (value) {
              return value.toFixed(1) + ' days';  // Show decimal values in the tooltip
            },
          },
        },
        colors: ['#ead621', '#ee8810', '#f3281b'],
      },
      series: [], // This will hold the series data
      randomKey: makeid(8),
      filterReport: {},
      filterCost: this.setDefaultFilterAdditionCost(),
      filterWeekly: this.setDefaultFilterWeekly(),
      filterWeeklyReport: {},
      isShowProof: false,
      imageSelected: '',
      zoomLevel: 1,
      rotate: 0,
      pieChartOptions1: {
        labels: ['Total Rejected', 'Total Passed'],
        plotOptions: {
          pie: {
            donut: {
              labels: {
                show: true,
                total: {
                  fontSize: '14px',
                  fontWeight: 'bold', // Make label text bold
                  show: true,
                  label: 'Total Items',
                  formatter: function (val) {
                    let total = val.config.series.reduce((a, b) => {
                      return a + b;
                    }, 0);
                    return total
                      .toString()
                      .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                  },
                },
              },
            },
          },
        },
        colors: ['#ead621', '#55bb3b'],
      },
      pieSeries1: [], // 1st_failed data
      pieChartOptions2: {
        labels: ['Total Rejected', 'Total Passed'],
        plotOptions: {
          pie: {
            donut: {
              labels: {
                show: true,
                total: {
                  fontSize: '14px',
                  fontWeight: 'bold', // Make label text bold
                  show: true,
                  label: 'Total Items',
                  formatter: function (val) {
                    let total = val.config.series.reduce((a, b) => {
                      return a + b;
                    }, 0);
                    return total
                      .toString()
                      .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                  },
                },
              },
            },
          },
        },
        colors: ['#ee8810', '#55bb3b'],
      },
      pieSeries2: [], // 2nd_failed data
      pieChartOptions3: {
        labels: ['Total Rejected', 'Total Passed'],
        plotOptions: {
          pie: {
            donut: {
              labels: {
                show: true,
                total: {
                  fontSize: '14px',
                  fontWeight: 'bold', // Make label text bold
                  show: true,
                  label: 'Total Items',
                  formatter: function (val) {
                    let total = val.config.series.reduce((a, b) => {
                      return a + b;
                    }, 0);
                    return total
                      .toString()
                      .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                  },
                },
              },
            },
          },
        },
        colors: ['#f3281b', '#55bb3b'],
      },
      pieSeries3: [], // 3rd_failed data
      filterAdditionCost: this.setDefaultFilterAdditionCost(),
      current_item: 0,
      current_rate: 0,
      current_percentage: 0,
      current_cost: 0,
      previous_item: 0,
      previous_rate: 0,
      previous_percentage: 0,
      previous_cost: 0,
      current_total: 0,
      previous_total: 0,
      dataWeekly: [],
      WeeklychartOptions: {
        chart: {
          id: 'weekly-chart',
          type: 'line',
          height: 350,
        },
        xaxis: {
          categories: [], // This will hold the dates
          rotate: -45, // Rotate labels if they are too crowded
          format: 'yyyy-MM-dd', // Format dates directly if necessary
        },
        yaxis: {
          title: {
            text: 'Cost',
          },
        },
        dataLabels: {
          enabled: true, // Enable data labels
          offsetY: -10, // Adjust vertical position of labels
          style: {
            colors: ['#000'], // Color of the data labels
            fontSize: '12px',
            fontWeight: 'bold',
          },
          formatter: (value) => {
            // Format the value to include a dollar sign
            return value ? `$${value.toFixed(2)}` : ''; // Format to two decimal places
          },
        },
        stroke: {
          curve: 'smooth', // Use smooth curves for lines
        },
      },
      chartSeries: [
        {
          name: 'Cost',
          data: [], // Will be populated with cost values
        },
      ],
      isLoadingCost: false,
      isLoadingWeekly: false,
    };
  },
  beforeUnmount() { },
  computed: {
    ...mapGetters(['getStores', 'getUserProfile']),

    maxHeight() {
      return parseInt(window.innerHeight - 280);
    },
    maxHeightDay() {
      return parseInt(window.innerHeight - 370);
    },
  },
  mounted() {
    this.warehouse_id = this.userWarehouseId;
    this.filterAdditionCost.warehouse_id = 'all';
    this.filterWeekly.warehouse_id = 'all';
    this.filter.limit = 50;
    this.fetchWarehouse();
    this.fetchReport();
    this.fetchReportCost();
    this.fetchReportChart();
  },
  methods: {
    async fetchWarehouse() {
      this.warehouses = this.getUserProfile.warehouses ?? [];
    },
    formatDate() {
      let today = new Date();
      let yyyy = today.getFullYear();
      let mm = today.getMonth() + 1; // Months start at 0!
      let dd = today.getDate();

      if (dd < 10) dd = '0' + dd;
      if (mm < 10) mm = '0' + mm;

      return (today = mm + dd + yyyy);
    },
    async onFilter() {
      this.page = 1;
      this.item_of_page = this.filter.limit;
      this.fetchReport();
    },
    async onFilterCost() {
      this.fetchReportCost();
    },
    async onFilterWeekly() {
      this.fetchReportChart();
    },
    resetFilter() {
      this.filter = this.setDefaultFilter();
      this.filter.limit = 50;
      this.page = 1;
      // this.fetchProduct();
      this.fetchReport();
    },
    resetFilterCost() {
      this.filterAdditionCost = this.setDefaultFilterAdditionCost();
      this.fetchReportCost();
    },
    resetFilterWeekly() {
      this.filterWeekly = this.setDefaultFilterWeekly();
      this.fetchReportChart();
    },

    async fetchReport() {
      this.isLoading = true;

      // Creating a new object 'param' to hold the parameters
      let paramLine = {
        start_date:
          this.filter.date.length > 0
            ? this.filter.date[0].replace(/\//g, '-')
            : null,
        end_date:
          this.filter.date.length > 0
            ? this.filter.date[1].replace(/\//g, '-')
            : null,
        // Adding custom parameter
        type: 'line',
      };

      let paramPie = {
        start_date:
          this.filter.date.length > 0
            ? this.filter.date[0].replace(/\//g, '-')
            : null,
        end_date:
          this.filter.date.length > 0
            ? this.filter.date[1].replace(/\//g, '-')
            : null,
        // Adding custom parameter
        type: 'pie',
      };

      try {
        // Making the API call with custom parameters
        const resLine = await list(paramLine);
        const resPie = await list(paramPie);

        // Handling the response
        this.dataReportLine = resLine.data;
        this.dataReportPie = resPie.data;

        this.updateChartData(this.dataReportLine.data);
        this.updatePieData(this.dataReportPie.data);
      } catch (error) {
        console.error('Error fetching report:', error);
      } finally {
        this.isLoading = false;
      }
    },
    async fetchReportCost() {
      this.isLoadingCost = true;
      this.filterCost.current_date_start =
        this.filterAdditionCost.current_date.length > 0
          ? this.filterAdditionCost.current_date[0].replace(/\//g, '-')
          : null;
      this.filterCost.current_date_end =
        this.filterAdditionCost.current_date.length > 0
          ? this.filterAdditionCost.current_date[1].replace(/\//g, '-')
          : null;
      this.filterCost.previous_date_start =
        this.filterAdditionCost.previous_date.length > 0
          ? this.filterAdditionCost.previous_date[0].replace(/\//g, '-')
          : null;
      this.filterCost.previous_date_end =
        this.filterAdditionCost.previous_date.length > 0
          ? this.filterAdditionCost.previous_date[1].replace(/\//g, '-')
          : null;
      console.log(this.filterAdditionCost);
      this.filterCost.warehouse_id = this.filterAdditionCost.warehouse_id;
      const res = await listTableCost(this.filterCost);
      this.dataCostCurrent = res.data?.data;
      (this.current_total = res.data?.total?.current_total),
        (this.previous_total = res.data?.total?.previous_total),
        this.updateDataWithSums(res.data.data);
      this.isLoadingCost = false;
    },
    async fetchReportChart() {
      this.isLoadingWeekly = true;
      this.filterWeeklyReport.start_date =
        this.filterWeekly.date.length > 0
          ? this.filterWeekly.date[0].replace(/\//g, '-')
          : null;
      this.filterWeeklyReport.end_date =
        this.filterWeekly.date.length > 0
          ? this.filterWeekly.date[1].replace(/\//g, '-')
          : null;
      this.filterWeeklyReport.warehouse_id = this.filterWeekly.warehouse_id;
      const res = await listChartReport(this.filterWeeklyReport);
      this.dataWeekly = res.data;
      this.populateChart();
      this.isLoadingWeekly = false;

    },
    populateChart() {
      const dates = this.dataWeekly.map((item) => item.formatted_date);
      console.log(dates);
      const costs = this.dataWeekly.map((item) =>
        item.cost ? parseFloat(item.cost) : 0
      );

      this.chartSeries[0].data = costs;

      this.WeeklychartOptions = {
        ...this.WeeklychartOptions,
        ...{
          xaxis: {
            categories: dates,
          },
        },
      };
    },

    updateChartData(data) {
      console.log(data);
      // const dates = Object.keys(data).map((date) => date); // Use the date keys directly
      const dates = Object.keys(data).sort((a, b) => new Date(a) - new Date(b));
      const firstFailed = dates.map((date) => data[date]['1st_failed']);
      const secondFailed = dates.map((date) => data[date]['2nd_failed']);
      const thirdFailed = dates.map((date) => data[date]['3rd_failed']);
      this.chartOptions = {
        ...this.chartOptions,
        ...{
          xaxis: {
            categories: dates,
          },
        },
      };

      // Assuming you need the sorted data in a specific format
      const sortedData = dates.reduce((acc, date) => {
        acc[date] = data[date];
        return acc;
      }, {});

      // Update chart options and series data
      // this.chartOptions.xaxis.categories = dates;
      this.series = [
        {
          name: '1st Failed',
          data: firstFailed,
        },
        {
          name: '2nd Failed',
          data: secondFailed,
        },
        {
          name: '3rd Failed',
          data: thirdFailed,
        },
      ];
    },
    updatePieData(pieData) {
      if (!pieData) {
        console.error('Pie data is undefined or null');
        return;
      }
      console.log(pieData);
      this.pieSeries1 = [
        pieData['1st_failed'].total_rejected,
        pieData['1st_failed'].total_passed,
      ];
      this.pieSeries2 = [
        pieData['2nd_failed'].total_rejected,
        pieData['2nd_failed'].total_passed,
      ];
      this.pieSeries3 = [
        pieData['3rd_failed'].total_rejected,
        pieData['3rd_failed'].total_passed,
      ];
      this.current_failed_items = pieData['1st_failed'].total_rejected + pieData['2nd_failed'].total_rejected + pieData['3rd_failed'].total_rejected;
    },

    setDefaultFilter() {
      return {
        item_of_page: 10,
        page: 1,
        status: 'rejected',
        sku: '',
        date: this.generatePastWeekStartAndEndDates(),
        exportData: false,
      };
    },
    setDefaultFilterAdditionCost() {
      return {
        current_date: this.generatePastWeekStartAndEndDates(),
        previous_date: this.generatePastWeekStartAndEndDatesPast(),
        warehouse_id: 'all',
      };
    },
    setDefaultFilterWeekly() {
      return {
        warehouse_id: 'all',
        date: this.generatePastWeekStartAndEndDates(),
      };
    },

    showTooltip(id) {
      document.getElementById(id).style.display = 'block';
    },
    hideTooltip(id) {
      document.getElementById(id).style.display = 'none';
    },
    generatePastWeekStartAndEndDates() {
      const today = new Date();
      const startDate = new Date(today);
      startDate.setDate(today.getDate() - 6);
      const endDate = today;
      const formattedStartDate = `${startDate.getFullYear()}/${(
        startDate.getMonth() + 1
      )
        .toString()
        .padStart(2, '0')}/${startDate.getDate().toString().padStart(2, '0')}`;
      const formattedEndDate = `${endDate.getFullYear()}/${(
        endDate.getMonth() + 1
      )
        .toString()
        .padStart(2, '0')}/${endDate.getDate().toString().padStart(2, '0')}`;
      return [formattedStartDate, formattedEndDate];
    },
    generatePastWeekStartAndEndDatesPast() {
      const today = new Date();
      const startDate = new Date(today);
      // Trừ đi 7 ngày từ ngày hiện tại
      startDate.setDate(today.getDate() - 14);

      // Tạo endDate là 7 ngày sau startDate
      const endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + 7); // Lấy 7 ngày tiếp theo sau startDate

      // Định dạng lại ngày tháng thành yyyy/mm/dd
      const formattedStartDate = `${startDate.getFullYear()}/${(
        startDate.getMonth() + 1
      )
        .toString()
        .padStart(2, '0')}/${startDate.getDate().toString().padStart(2, '0')}`;
      const formattedEndDate = `${endDate.getFullYear()}/${(
        endDate.getMonth() + 1
      )
        .toString()
        .padStart(2, '0')}/${endDate.getDate().toString().padStart(2, '0')}`;

      return [formattedStartDate, formattedEndDate];

    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];

      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = 'Total';
          return;
        }

        // Get all values for the current column
        const values = data.map((item) => {
          // For columns with money values, strip out '$' and ',' if present
          const value = item[column.property];
          return typeof value === 'string' ? Number(value.replace(/[$,]/g, '')) : Number(value);
        });

        // Check if there are any non-numeric values
        if (!values.every((value) => isNaN(value))) {
          let sum = values.reduce((prev, curr) => {
            return !isNaN(curr) ? prev + curr : prev;
          }, 0);

          // Add % for index 2 and 3, $ for index 4
          if (index === 2 || index === 3 || index === 6 || index === 7) {
            sums[index] = sum.toFixed(1) + ' %';
          } else if (index === 4 || index === 8) {
            sums[index] = '$ ' + sum.toFixed(2);
          } else {
            sums[index] = sum;
          }
        } else {
          sums[index] = 'N/A';
        }
      });

      return sums;
    },
    setDepartmentCellStyle({ column, rowIndex }) {
      const current = [1, 2, 3, 4];
      const previous = [5, 6, 7, 8];
      const columnIndex = column.no;
      if (current.includes(columnIndex)) {
        return 'bg-blue-100';
      } else if (previous.includes(columnIndex)) {
        return 'bg-green-100';
      }
      return '';
    },
    updateDataWithSums(dataArray) {
      // Calculate sums from the provided data array
      const sumValues = dataArray.reduce(
        (acc, item) => {
          acc.count += item.count;
          acc.percentage += parseFloat(item.percentage); // Convert string to number
          acc.rate += parseFloat(item.rate); // Convert string to number
          acc.cost += isNaN(parseFloat(item.cost.replace(/,/g, ''))) ? 0 : parseFloat(item.cost.replace(/,/g, ''));
          acc.previous_count += item.previous_count;
          acc.previous_percentage += parseFloat(item.previous_percentage); // Convert string to number
          acc.previous_rate += parseFloat(item.previous_rate); // Convert string to number
          acc.previous_cost += isNaN(parseFloat(item.previous_cost.replace(/,/g, ''))) ? 0 : parseFloat(item.previous_cost.replace(/,/g, ''));
          return acc;
        },
        {
          count: 0,
          percentage: 0,
          rate: 0,
          cost: 0,
          previous_count: 0,
          previous_percentage: 0,
          previous_rate: 0,
          previous_cost: 0,
        }
      );

      // Update the dataObject with the summed values
      this.current_item = sumValues.count;
      this.current_rate = sumValues.rate.toFixed(1);
      this.current_percentage = Math.round(sumValues.percentage);
      this.current_cost = sumValues.cost.toFixed(2);
      this.previous_item = sumValues.previous_count;
      this.previous_rate = sumValues.previous_rate.toFixed(1);
      this.previous_percentage = Math.round(sumValues.previous_percentage);
      this.previous_cost = sumValues.previous_cost;
    },
    calculateDifference(currentValue, previousValue) {
      if (previousValue === 0) {
        return currentValue;
      }
      const difference = (currentValue - previousValue) / previousValue;
      let result = parseFloat(difference.toFixed(4)) * 100
      result = Math.abs(Math.round(result * 100) / 100);
      return result;
    },
    generatePastWeekStartAndEndDatesMonday() {
      const today = new Date();

      const dayOfWeek = today.getDay();

      const diffToMonday = (dayOfWeek === 0 ? 6 : dayOfWeek - 1);

      const startDate = new Date(today);
      startDate.setDate(today.getDate() - diffToMonday);

      const endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + 6);

      const formattedStartDate = `${startDate.getFullYear()}/${(
        startDate.getMonth() + 1
      )
        .toString()
        .padStart(2, '0')}/${startDate.getDate().toString().padStart(2, '0')}`;

      const formattedEndDate = `${endDate.getFullYear()}/${(
        endDate.getMonth() + 1
      )
        .toString()
        .padStart(2, '0')}/${endDate.getDate().toString().padStart(2, '0')}`;

      return [formattedStartDate, formattedEndDate];
    },

  },
};
</script>

<style scoped>
/* Add styling for tooltip and icon */
.tooltip-icon {
  cursor: pointer;
  border: 1px solid #ccc;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  text-align: center;
  line-height: 20px;
  font-size: 14px;
  background-color: #f1f1f1;
}

.tooltip-content {
  display: none;
  position: absolute;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 10px;
  font-size: 14px;
  color: #333;
  z-index: 1000;
  white-space: nowrap;
}

.el-table__cell {
  background: red !important;
}

.custom-height .el-input__inner {
  height: 32px !important;
  line-height: 32px !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.addition_cost .el-card__body {
  width: 100% !important;
}
</style>
