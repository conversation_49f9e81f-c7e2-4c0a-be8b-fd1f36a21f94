<template>
  <div class="table-content p-4">
    <el-row class="flex flex-wrap items-center justify-between">
      <div class="filter-item flex items-center">
        <el-dropdown trigger="click" class="el-dropdown-filter-item" :class="{ 'is-active': filter.store_id }" clearable>
          <span class="el-dropdown-link flex items-center">
            <template v-if="filter.store_id">
              <el-tooltip effect="dark" :content="$t('Store')" placement="top-start">
                <span>{{ getStoreById(filter.store_id) }}</span>
              </el-tooltip>
            </template>
            <template v-else>{{ $t('Store') }}</template>
            <span class="icon ml-2">
              <icon :data="iconChevronDown" />
            </span>
          </span>
          <template #dropdown>
            <div class="el-dropdown-menu-filter-item">
              <el-select filterable v-model="filter.store_id" :placeholder="$t('Select store')" @change="onFilter" clearable>
                <el-option v-for="item in getStores" :key="item.id" :label="item.code || storeNA"
                  :value="String(item.id)">
                </el-option>
              </el-select>
            </div>
          </template>
        </el-dropdown>
      </div>

      <div class="filter-item flex items-center">
        <el-dropdown trigger="click" class="el-dropdown-filter-item" :class="{ 'is-active': filter.order_number }" clearable>
          <span class="el-dropdown-link flex items-center">
            <template v-if="filter.order_number">
              <el-tooltip effect="dark" :content="$t('Order Number')" placement="top-start">
                <span>{{ filter.order_number }}</span>
              </el-tooltip>
            </template>
            <template v-else>{{ $t('Order Number') }}</template>
            <span class="icon ml-2">
              <icon :data="iconChevronDown" />
            </span>
          </span>
          <template #dropdown>
            <div class="el-dropdown-menu-filter-item">
              <el-input v-model="filter.order_number" :placeholder="$t('Order Number')" class="w-full"
              clearable  
              @change="onFilter">
              </el-input>
            </div>
          </template>
        </el-dropdown>
      </div>

      <div class="filter-item flex items-center">
        <el-dropdown trigger="click" class="el-dropdown-filter-item"
          :class="{ 'is-active': this.filter.date && this.filter.date.length }">
          <span class="el-dropdown-link flex items-center">
            <template v-if="this.filter.date && this.filter.date.length">
              <el-tooltip effect="dark" :content="$t('QC’ed Date')" placement="top-start">
                <span>{{
                  templateDatesRange(this.filter.date[0], this.filter.date[1])
                  }}</span>
              </el-tooltip>
            </template>
            <template v-else>{{ $t('QC’ed Date') }}</template>
            <span class="icon ml-2">
              <icon :data="iconChevronDown" />
            </span>
          </span>
          <template #dropdown>
            <div class="el-dropdown-menu-filter-item">
              <el-date-picker     
                :clearable="false"
                style="width: 100%" type="daterange" unlink-panels v-model="filter.date"
                range-separator="To" :start-placeholder="$t('Start date')" :end-placeholder="$t('End date')"
                value-format="YYYY/MM/DD" @change="onFilter">
              </el-date-picker>
            </div>
          </template>
        </el-dropdown>
      </div>

      <div class="filter-item flex items-center">
        <el-dropdown trigger="click" class="el-dropdown-filter-item" :class="{ 'is-active': filter.order_status }" clearable>
          <span class="el-dropdown-link flex items-center">
            <template v-if="filter.order_status">
              <el-tooltip effect="dark" :content="$t('Order Status')" placement="top-start">
                <span>{{ getOrderStatusByValue(filter.order_status) }}</span>
              </el-tooltip>
            </template>
            <template v-else>{{ $t('Order Status') }}</template>
            <span class="icon ml-2">
              <icon :data="iconChevronDown" />
            </span>
          </span>
          <template #dropdown>
            <div class="el-dropdown-menu-filter-item">
              <el-select filterable v-model="filter.order_status" :placeholder="$t('Select status')" @change="onFilter" clearable
                class="w-full">
                <el-option v-for="item in saleOrderStatus" :key="item.value" :label="item.label"
                  :value="String(item.value)">
                </el-option>
              </el-select>
            </div>
          </template>
        </el-dropdown>
      </div>

      <div class="filter-item flex items-center">
        <el-dropdown trigger="click" class="el-dropdown-filter-item" :class="{ 'is-active': filter.keyword }">
          <span class="el-dropdown-link flex items-center">
            <template v-if="filter.keyword">
              <el-tooltip effect="dark" :content="$t('Sku')" placement="top-start">
                <span>{{ filter.keyword }}</span>
              </el-tooltip>
            </template>
            <template v-else>{{ $t('Sku') }}</template>
            <span class="icon ml-2">
              <icon :data="iconChevronDown" />
            </span>
          </span>
          <template #dropdown>
            <div class="el-dropdown-menu-filter-item">
              <el-input v-model="filter.keyword" :placeholder="$t('Sku')" class="w-full" @change="onFilter" clearable>
              </el-input>
            </div>
          </template>
        </el-dropdown>
      </div>

      <div class="filter-item flex items-center">
        <el-dropdown trigger="click" class="el-dropdown-filter-item" :class="{ 'is-active': filter.label_id }" clearable>
          <span class="el-dropdown-link flex items-center">
            <template v-if="filter.label_id">
              <el-tooltip effect="dark" :content="$t('Label Id')" placement="top-start">
                <span>{{ filter.label_id }}</span>
              </el-tooltip>
            </template>
            <template v-else>{{ $t('Label Id') }}</template>
            <span class="icon ml-2">
              <icon :data="iconChevronDown" />
            </span>
          </span>
          <template #dropdown>
            <div class="el-dropdown-menu-filter-item">
              <el-input v-model="filter.label_id" :placeholder="$t('Label Id')" class="w-full" @change="onFilter" clearable>
              </el-input>
            </div>
          </template>
        </el-dropdown>
      </div>

      <div class="filter-item flex items-center">
        <el-dropdown trigger="click" class="el-dropdown-filter-item" :class="{ 'is-active': filter.prod_status }" clearable>
          <span class="el-dropdown-link flex items-center">
            <template v-if="filter.prod_status">
              <el-tooltip effect="dark" :content="$t('Prod Status')" placement="top-start">
                <span>{{ getProductionStatusByValue(filter.prod_status) }}</span>
              </el-tooltip>
            </template>
            <template v-else>{{ $t('Prod Status') }}</template>
            <span class="icon ml-2">
              <icon :data="iconChevronDown" />
            </span>
          </span>
          <template #dropdown>
            <div class="el-dropdown-menu-filter-item">
              <el-select v-model="filter.prod_status" :placeholder="$t('Select status')" @change="onFilter" clearable
                class="w-full">
                <el-option v-for="item in productionFilterStatus" :key="item.value" :label="item.label"
                  :value="String(item.value)">
                </el-option>
              </el-select>
            </div>
          </template>
        </el-dropdown>
      </div>

      <div class="filter-item flex items-center">
        <el-dropdown trigger="click" class="el-dropdown-filter-item" :class="{ 'is-active': filter.qc_status }">
          <span class="el-dropdown-link flex items-center">
            <template v-if="filter.qc_status">
              <el-tooltip effect="dark" :content="$t('QC Status')" placement="top-start">
                <span>{{ getQcStatusByValue(filter.qc_status) }}</span>
              </el-tooltip>
            </template>
            <template v-else>{{ $t('QC Status') }}</template>
            <span class="icon ml-2">
              <icon :data="iconChevronDown" />
            </span>
          </span>
          <template #dropdown>
            <div class="el-dropdown-menu-filter-item">
              <el-select v-model="filter.qc_status" :placeholder="$t('Select status')" @change="onFilter"
                class="w-full">
                <el-option v-for="item in qc_status" :key="item.value" :label="item.label" :value="String(item.value)">
                </el-option>
              </el-select>
            </div>
          </template>
        </el-dropdown>
      </div>


      <div class="flex items-center space-x-2">
        <el-button type="primary" @click="onFilter">
          <span class="icon-margin-right">
            <icon :data="iconSearch" />
          </span>
          {{ $t('Search') }}
        </el-button>
        <el-button type="danger" @click="resetFilter">
          <span class="icon-margin-right">{{ $t('Clear') }}</span>
        </el-button>
      </div>
    </el-row>
  </div>
  <el-table :data="listItem" size="small" style="width: 100%" row-key="id" v-loading="isLoading"
    element-loading-text="Loading..." :max-height="maxHeight" @sort-change="sortTable"
    :cell-class-name="tableCellClassName">
    >
    <el-table-column prop="store_code" :label="$t('Store')" width="60">
      <template #default="scope">
        {{ scope.row.code }}
      </template>
    </el-table-column>
    <el-table-column prop="order_number" :label="$t('Order Number')">
      <template #default="scope">
        <span class="!text-xs">

          {{ scope.row.order_number }}
        </span>
      </template>
    </el-table-column>
    <el-table-column prop="qc_created_at" :label="$t('Order Date')">
      <template #default="scope">
        <span class="!text-xs">
          {{ listViewDateFormatPST(scope.row.created_at_utc) }}

        </span>
      </template>
    </el-table-column>
    <el-table-column prop="created_at_utc" :label="$t('Order Age')" sortable width="80">
      <template #default="scope">
        {{ timeFromNow(scope.row.created_at_utc, today) }}
      </template>
    </el-table-column>

    <el-table-column prop="order_status" :label="$t('Order Status')" :width="160">
      <template #default="scope">
        <div>
          <el-tag type="info" :class="getClassOrderStatus(scope.row)" class="rounded-xl" effect="dark" round
            size="small">
            {{ getOrderStatusByValue(scope.row.order_status) }}
          </el-tag>
        </div>
      </template>
    </el-table-column>
    <el-table-column prop="order_status" :label="$t('Prod Status')" width="90">
      <template #default="scope">
        {{ getProductionStatusByValue(scope.row.production_status) }}
      </template>
    </el-table-column>

    <el-table-column prop="sku" :label="$t('Item SKU')">
      <template #default="scope">
        {{ scope.row.sku }}
      </template>
    </el-table-column>
    <el-table-column prop="label_id" :label="$t('Label Root Id')">
      <template #default="scope">
        {{ scope.row.label_id }}
      </template>
    </el-table-column>
    <el-table-column prop="label_id" :label="$t('Qc 1st Failed At')">
      <template #default="scope">
        <el-tooltip class="item" effect="dark" placement="top">
          <template #content>
            <div>
              <span>{{ convertTimeZone(scope.row.first_qc) }}</span>
            </div>
          </template>
          <span>
            {{ formatTimeDifferenceQc1(scope.row) }}
          </span>
        </el-tooltip>

      </template>
    </el-table-column>
    <el-table-column prop="label_id" :label="$t('Qc 2nd Failed At')">
      <template #default="scope">
        <el-tooltip class="item" effect="dark" placement="top">
          <template #content>
            <div>
              <span>{{ convertTimeZone(scope.row.second_qc) }}</span>
            </div>
          </template>
          <span v-if="scope.row.second_qc">
            {{ formatTimeDifferenceQc2(scope.row, false) }}
          </span>
        </el-tooltip>

      </template>
    </el-table-column>
    <el-table-column prop="label_id" :label="$t('Qc ≥ 3rd Failed At')">
      <template #default="scope">
        <el-tooltip class="item" effect="dark" placement="top">
          <template #content>
            <div>
              <span>{{ scope.row.total_reject }} times failed</span><br>
              <span>{{ convertTimeZone(scope.row.third_qc) }}</span>
            </div>
          </template>
          <span v-if="scope.row.third_qc">
            {{ formatTimeDifferenceQc3(scope.row) }}
          </span>
        </el-tooltip>
      </template>
    </el-table-column>
    <el-table-column prop="total_stock" :label="$t('Stock')" sortable width="75">
      <template #default="scope">
        <div class="flex justify-center items-center h-full">
          <span v-if="scope.row.stock > 0">
            {{ scope.row.stock }}
          </span>
          <span v-else class="text-rose-600"> OOS </span>
        </div>
      </template>
    </el-table-column>
    <el-table-column prop="max_status" :label="$t('Qc Status')" width="75">
      <template #header>
        <div class="text-center">
          {{ $t('Qc Status') }}
        </div>
      </template>
      <template #default="scope">
        <div class="flex justify-center items-center h-full">
          <el-tag type="info" :class="getClassQcStatus(scope.row.status)" class="rounded-xl" effect="dark" round
            size="small">
            {{ getNameQcStatus(scope.row.status) }}
          </el-tag>
        </div>
      </template>
    </el-table-column>
    <el-table-column prop="last_qc_at" sortable :label="$t('Resolved Within')">
      <template #default="scope">
        <span v-if="scope.row.status === 'pass'">
          {{ formatAgeQc(scope.row.first_qc, scope.row.last_qc_at) }}

        </span>
      </template>
    </el-table-column>
    <el-table-column prop="addition_cost" sortable :label="$t('Addition Cost')">
      <template #default="scope">
        {{ scope.row.addition_cost }}
      </template>
    </el-table-column>

  </el-table>
  <div class="bottom flex items-center justify-between mt-4">
    <div class="total flex">
      {{ $t('Total:') }} {{ formatNumber(listItemTotalScreen) }} 
      <span class="ml-5">
        {{  $t('Total Addition Cost:') }} ${{  totalQcCost }} 

      </span>
      <!-- <span class="ml-2 flex" v-if="previousTotalQcCost != 0">
        <Top v-if="totalQcCost > previousTotalQcCost" style="width: 24px; height: 16px; color: red" />
        <Bottom v-if="totalQcCost <= previousTotalQcCost" style="width: 24px; height: 16px; color: green" />
        {{ calculateDifference(totalQcCost, previousTotalQcCost) }}% 
      </span> -->
    </div>

    <div class="pagination-container flex-grow flex justify-center">
      <el-pagination :disabled="isLoading" background layout="prev, pager, next" :page-size="item_of_page"
        :total="listItemTotal" @current-change="fetchProduct" v-model:currentPage="page">
      </el-pagination>
    </div>

    <div class="limit">
      <el-select v-model="filter.limit" :placeholder="$t('Select')" size="mini" @change="onFilter">
        <el-option v-for="item in limits" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
    </div>
  </div>
</template>

<script>
import { listTable } from '@/api/qc_rejected';
import moment from 'moment';
import { WAREHOUSE_MEXICO, NOT_APPLICABLE } from '@/utilities/constants';

import { downloadCsv, makeid } from '@/utilities/helper.js';
import EventBus from '@/utilities/eventBus';
import ViewDesignItem from '@/modules/sale-order/components/ViewDesignItem.vue';
import { S3_URL } from '@/utilities/constants';
import { socketClient } from '@/utilities/socket';
import VueApexCharts from 'vue3-apexcharts';
import saleOrderMixin from '@/mixins/saleOrder.js';
import { mapGetters } from 'vuex';
import { Top, Bottom } from '@element-plus/icons-vue';

const socket = socketClient();

export default {
  name: 'FirstFailed',
  props: {
    typeReport: {
      type: Number,
      default: 1,
    },
  },
  components: { apexchart: VueApexCharts, ViewDesignItem: ViewDesignItem, Top, Bottom },
  mixins: [saleOrderMixin],
  data() {
    return {
      storeNA: NOT_APPLICABLE,
      listItem: [],
      listItemTotal: '',
      listItemTotalScreen: '',
      page: 1,
      item_of_page: 25,
      filter: this.setDefaultFilter(),
      type: '',
      randomKey: makeid(8),
      filterReport: {},
      isShowProof: false,
      imageSelected: '',
      zoomLevel: 1,
      rotate: 0,
      today: Date.now(),
      havePrevious: false,
      totalQcCost: 0,
      previousTotalQcCost: 0,
      qc_status: [
        { value: 'pass', label: 'Pass' },
        { value: 'rejected', label: 'Rejected' }
      ]
    };
  },
  computed: {
    ...mapGetters(['getStores']),
    maxHeight() {
      return parseInt(window.innerHeight - 300);
    },
    maxHeightDay() {
      return parseInt(window.innerHeight - 370);
    },
  },
  watch: {
    typeReport(newValue, oldValue) {
    },
  },
  async mounted() {
    this.filter.limit = 25;
    this.fetchStore();
    this.fetchProduct();
  },
  methods: {
    formatDate() {
      let today = new Date();
      let yyyy = today.getFullYear();
      let mm = today.getMonth() + 1; // Months start at 0!
      let dd = today.getDate();

      if (dd < 10) dd = '0' + dd;
      if (mm < 10) mm = '0' + mm;

      return (today = mm + dd + yyyy);
    },
    async onFilter() {
      this.page = 1;
      this.item_of_page = this.filter.limit;
      this.havePrevious = true;
      this.previousTotalQcCost = this.totalQcCost;
      this.fetchProduct();
    },
    resetFilter() {
      this.filter = this.setDefaultFilter();
      this.filter.limit = 25;
      this.page = 1;
      this.havePrevious = false;
      this.previousTotalQcCost = 0;
      this.fetchProduct();
    },
    setDefaultFilter() {
      return {
        item_of_page: 25,
        page: 1,
        sku: '',
        total: false,
        qc_status: '',
        qc_status: 'rejected',
        date: this.generatePastWeekStartAndEndDates(),
      };
    },
    async fetchProduct() {
      this.isLoading = true;
      this.listItem = [];
      this.filter.page = this.page;
      this.filter.item_of_page = this.item_of_page;
      this.filter.start_date =
        this.filter.date.length > 0
          ? this.filter.date[0].replace(/\//g, '-')
          : null;
      this.filter.end_date =
        this.filter.date.length > 0
          ? this.filter.date[1].replace(/\//g, '-')
          : null;
      this.filter.type = this.typeReport ?? 1;
      this.filter.total = 0;


      this.filter.total = 1;
      let totalResponse = listTable({ ...this.filter }).then(res => {
        this.listItemTotalScreen = new Intl.NumberFormat().format(res?.data?.data.total_count);
        this.listItemTotal = res?.data?.data.total_count;
        this.totalQcCost = res?.data?.data.total_addition_cost;
      });


      this.filter.total = 0;

      let lists = await listTable(this.filter);

      this.listItem = lists?.data?.data.data;

      this.listItem = lists?.data?.data.data.map((item) => ({
        ...item,
        total_stock: item.incoming_stock + item.quantity,
      }));

      this.listItem.sort((a, b) => new Date(b.qc_created_at) - new Date(a.qc_created_at));
      this.isLoading = false;
    },

    generatePastWeekStartAndEndDates() {
      const today = new Date();
      const startDate = new Date(today);
      startDate.setDate(today.getDate() - 6);
      const endDate = today;
      const formattedStartDate = `${startDate.getFullYear()}/${(
        startDate.getMonth() + 1
      )
        .toString()
        .padStart(2, '0')}/${startDate.getDate().toString().padStart(2, '0')}`;
      const formattedEndDate = `${endDate.getFullYear()}/${(
        endDate.getMonth() + 1
      )
        .toString()
        .padStart(2, '0')}/${endDate.getDate().toString().padStart(2, '0')}`;
      return [formattedStartDate, formattedEndDate];
    },
    getOrderStatusByValue(status) {
      const selectItem = this.saleOrderStatus.find(
        (item) => item.value === status
      );
      return (selectItem && selectItem.label) || '';
    },
    getClassQcStatus(status) {
      let classStatus = '';
      switch (status) {
        case 'pass':
          classStatus = `bg-emerald-500`;
          break;
        default:
          classStatus = `bg-red-400`;
      }
      return classStatus;
    },
    getNameQcStatus(status) {
      let classStatus = '';
      switch (status) {
        case 'pass':
          classStatus = `Pass`;
          break;
        default:
          classStatus = `Rejected`;
      }
      return classStatus;
    },
    // getCellClass(date) {
    //   const now = new Date();
    //   const createdAt = new Date(date);
    //   const timeDiff = (now - createdAt) / (1000 * 60 * 60); // Difference in hours
    //   return timeDiff > 24 ? 'bg-red-200' : '';
    // },
    tableCellClassName({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 8) {
        let timeQc = this.formatTimeDifferenceQc1(row, true);
        return timeQc >= 24 ? '!bg-red-200' : '';
      }
      if (columnIndex === 9 && row.max_status !== 'pass') {
        let timeQc = this.formatTimeDifferenceQc2(row, true);
        return timeQc >= 3 ? '!bg-red-200' : '';
      }
      if (columnIndex === 10 && row.max_status !== 'pass') {
        let timeQc = this.formatTimeDifferenceQc3(row, true);
        return timeQc >= 1 ? '!bg-red-200' : '';
      }

    },

    sortTable(data) {
      let sortColumn = '';
      let sortBy = '';
      if (data.prop && data.order) {
        sortColumn = data.prop;
        if (data.order === 'ascending') {
          sortBy = 'ASC';
        } else if (data.order === 'descending') {
          sortBy = 'DESC';
        }
      }
    },
    getStoreById(id) {
      const selectItem = this.getStores.find((item) => +item.id === +id);
      return (selectItem && selectItem.code) || this.storeNA;
    },
    templateDatesRange(startDate, endDate) {
      if (!startDate || !endDate) return '';

      // Convert date strings to Moment.js objects
      const start = moment(startDate, 'YYYY/MM/DD');
      const end = moment(endDate, 'YYYY/MM/DD');

      // Format the dates as MM/DD/YYYY
      const formattedStart = start.format('MM/DD/YYYY');
      const formattedEnd = end.format('MM/DD/YYYY');

      return `${formattedStart} - ${formattedEnd}`;
    },
    async fetchStore() {
      await this.$store.dispatch('getStores', { without_pagination: 1 });
    },

    calculateTime(createdAt) {
      const createdAtMoment = moment(createdAt).format('YYYY-MM-DD hh:mm:ss');

      // Get the current time in PST
      const nowPST = moment
        .utc()
        .tz('America/Los_Angeles')
        .format('YYYY-MM-DD hh:mm:ss');

      // Calculate the difference in seconds
      const seconds = moment(nowPST).diff(moment(createdAtMoment), 'seconds');

      // Convert seconds to hours and days
      const days = Math.floor(seconds / (3600 * 24));
      const hours = Math.floor((seconds % (3600 * 24)) / 3600);
      const minutes = Math.floor((seconds % (3600 * 24)) / 60);
      const minuteLabel = minutes === 1 ? 'minute' : 'minutes';

      // Determine labels
      const dayLabel = days === 1 ? 'day' : 'days';
      const hourLabel = hours === 1 ? 'hour' : 'hours';

      if (days > 0) {
        return `${days} ${dayLabel} ${hours} ${hourLabel}`;
      } else if (hours > 0) {
        return `${hours} ${hourLabel}`;
      } else {
        return `${minutes} ${minuteLabel}`;
      }
    },
    getQcStatusByValue(value) {
      if (value === 'pass') {
        return `Pass`;
      } else if (value === 'rejected') {
        return `Rejected`;

      }
    },
    formatTimeDifferenceQc1(row, getTableClass = false) {
      let start;
      let end;

      start = moment(row.first_qc);
      if (row.status === 'pass' && row.total_reject === 1) {
        end = moment(row.last_qc_at);
      } else if (row.total_reject != 1) {
        end = moment(row.second_qc);
      } else {
        end = moment();
      }

      const duration = moment.duration(end.diff(start));
      if (getTableClass) {
        return Math.floor(duration.asHours());
      }

      const days = Math.floor(duration.asDays());
      const hours = Math.floor(duration.asHours()) % 24;
      const minutes = Math.floor(duration.asMinutes()) % 60;

      let result = '';

      if (days > 0) {
        result += `${days} day${days > 1 ? 's' : ''}`;
        if (hours > 0) result += ` ${hours} hr`;
      } else if (hours > 0) {
        result += `${hours} hr`;
        if (minutes > 0) result += ` ${minutes} min`;
      } else if (minutes > 0) {
        result += `${minutes} min`;
      } else {
        result = 'Less than a minute'; // Optional: handle cases where the difference is less than a minute
      }

      return result;
    },
    formatTimeDifferenceQc2(row, getTableClass = false) {
      let start;
      let end;
      if (row.total_reject < 2) {
        return;
      }
      start = moment(row.second_qc);
      if (row.status === 'pass' && row.total_reject === 2) {
        end = moment(row.last_qc_at);
      } else if (row.total_reject >= 3) {
        end = moment(row.third_qc);
      } else {
        end = moment();
      }


      const duration = moment.duration(end.diff(start));
      if (getTableClass) {
        return Math.floor(duration.asHours());
      }

      const days = Math.floor(duration.asDays());
      const hours = Math.floor(duration.asHours()) % 24;
      const minutes = Math.floor(duration.asMinutes()) % 60;
      let result = '';

      if (days > 0) {
        result += `${days} day${days > 1 ? 's' : ''}`;
        if (hours > 0) result += ` ${hours} hr`;
      } else if (hours > 0) {
        result += `${hours} hr`;
        if (minutes > 0) result += ` ${minutes} min`;
      } else if (minutes > 0) {
        result += `${minutes} min`;
      } else {
        result = 'Less than a minute'; // Optional: handle cases where the difference is less than a minute
      }

      return result;
    },
    formatTimeDifferenceQc3(row, getTableClass = false) {
      let start;
      let end;
      if (row.total_reject < 3) {
        return;
      }
      start = moment(row.third_qc);
      if (row.status === 'pass' && row.total_reject >= 3) {
        end = moment(row.last_qc_at);
      } else {
        end = moment();
      }
      const duration = moment.duration(end.diff(start));
      if (getTableClass) {
        return Math.floor(duration.asHours());
      }

      const days = Math.floor(duration.asDays());
      const hours = Math.floor(duration.asHours()) % 24;
      const minutes = Math.floor(duration.asMinutes()) % 60;

      let result = '';

      if (days > 0) {
        result += `${days} day${days > 1 ? 's' : ''}`;
        if (hours > 0) result += ` ${hours} hr`;
      } else if (hours > 0) {
        result += `${hours} hr`;
        if (minutes > 0) result += ` ${minutes} min`;
      } else if (minutes > 0) {
        result += `${minutes} min`;
      } else {
        result = 'Less than a minute'; // Optional: handle cases where the difference is less than a minute
      }

      return result;
    },

    formatAgeQc(startDate, endDate) {
      const start = moment(startDate);
      const end = moment(endDate);
      const duration = moment.duration(end.diff(start));
      const days = Math.floor(duration.asDays());
      const hours = Math.floor(duration.asHours()) % 24;
      const minutes = Math.floor(duration.asMinutes()) % 60;

      let result = '';

      if (days > 0) {
        result += `${days} day${days > 1 ? 's' : ''}`;
        if (hours > 0) result += ` ${hours} hr`;
      } else if (hours > 0) {
        result += `${hours} hr`;
        if (minutes > 0) result += ` ${minutes} min`;
      } else if (minutes > 0) {
        result += `${minutes} min`;
      } else {
        result = 'Less than a minute'; // Optional: handle cases where the difference is less than a minute
      }

      return result;
    },
    getProductionStatusByValue(status) {
      const selectItem = this.productionFilterStatus.find(
        (item) => item.value === status
      );
      return (selectItem && selectItem.label) || '';
    },
    convertTimeZone(timestamp) {
      return moment(timestamp).tz('America/Los_Angeles').format('YYYY-MM-DD HH:mm:ss');
    },
    calculateDifference(currentValue, previousValue) {
      console.log(currentValue);
      console.log(previousValue);
      if (previousValue === 0) {
        return currentValue;
      }
      const difference = (currentValue - previousValue) / previousValue;
      let result = parseFloat(difference.toFixed(4)) * 100
      result = (Math.round(result * 100) / 100);
      return result;
    },

  },
};
</script>

<style scoped>
/* Add styling for tooltip and icon */
.el-table__row:hover {
  background-color: inherit;
}

.el-table__row:hover {
  background-color: inherit !important;
}
</style>
