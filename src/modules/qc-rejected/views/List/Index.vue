<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="flex justify-between items-center p-4 bg-gray-100">
      <div class="top-head-left">
        <h1 class="text-xl font-semibold">{{ $t('QC - Fulfillment SLA') }}</h1>
      </div>
    </div>
    <el-tabs v-model="activeTab" class="ml-3" @tab-click="handleTabChange">
      <el-tab-pane :label="$t('Dashboard')" name="all">
        <ChartData/>
    </el-tab-pane>
    <el-tab-pane :label="$t('All Failed')" name="1st">
      <FirstFailed v-if="isFirstFailedTab('1st')" :typeReport="type" />
    </el-tab-pane>
    </el-tabs>
  </div>
</template>

