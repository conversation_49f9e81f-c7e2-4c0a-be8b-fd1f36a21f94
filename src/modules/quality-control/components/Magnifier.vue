<template>
  <div class="image-magnifier-container" @mousemove="debouncedMoveLens" @mouseleave="hideLens">
    <el-image ref="imageContainer" :src="imageSrc" fit="fill" class="image-magnifier-image cursor-pointer" style="width: 135px; height: auto; background-image: linear-gradient(
        45deg,
        #b0b0b0 25%,
        transparent 25%
      ),
      linear-gradient(-45deg, #b0b0b0 25%, transparent 25%),
      linear-gradient(45deg, transparent 75%, #b0b0b0 75%),
      linear-gradient(-45deg, transparent 75%, #b0b0b0 75%);
      background-size: 20px 20px;
      background-position: 0 0, 0 10px, 10px -10px, -10px 0;
      "></el-image>
    <div ref="lens" class="image-magnifier-lens" :style="{ width: lensSize + 'px', height: lensSize + 'px' }"></div>
    <div ref="zoomLens" class="zoom-lens relative w-full h-full"
      :style="{ display: zoomLensVisible ? 'block' : 'none' }">
      <el-image :src="imageSrc" fit="fill" class="image-magnifier-image w-full h-full"
        :style="{ backgroundColor: 'aqua' }"></el-image>
    </div>
    <div class="border mt-3 min-h-[22px]" :style="{ backgroundColor: colorCode }">
    </div>
  </div>
</template>



<script>
export default {
  props: {
    imageSrc: {
      type: String,
      required: true,
    },
    colorCode: {
      type: String,
      default: null,
    },
    inkColor: {
      type: String,
      default: null,
    },
    lensSize: {
      type: Number,
      default: 90,
    },
    zoomLensSize: {
      type: Number,
      default: 278,
    },
    totalArtWork: {
      type: Number,
      default: 1,
    },

  },
  data() {
    return {
      debouncedMoveLens: this.debounce(this.moveLens, 10),
      zoomLensVisible: false,
    };
  },
  methods: {
    moveLens(event) {
      const lens = this.$refs.lens;
      const zoomLens = this.$refs.zoomLens;
      const imageContainer = this.$refs.imageContainer;
      const image = imageContainer.$el.querySelector('img');

      if (!lens || !zoomLens || !image) return;

      const { left, top, width, height } = image.getBoundingClientRect();
      const x = event.clientX - left;
      const y = event.clientY - top;

      // Calculate lens position
      const lensX = Math.max(this.lensSize / 2, Math.min(x, width - this.lensSize / 2));
      const lensY = Math.max(this.lensSize / 2, Math.min(y, height - this.lensSize / 2));
      const screenWidth = window.innerWidth;
      const screenHeight = window.innerHeight;

      const zoomLensWidth = screenWidth * 0.3; // 70% of screen width
      const zoomLensHeight = screenHeight * 0.2; // 70% of screen height


      // lens.style.display = 'block';
      zoomLens.style.display = 'block';
      this.zoomLensVisible = true;

      // Use requestAnimationFrame for smoothness
      requestAnimationFrame(() => {
        const originalSize = zoomLens.getBoundingClientRect();
        const originalWidth = originalSize.width;
        const originalHeight = originalSize.height;


        if (this.totalArtWork < 3) {
          zoomLens.style.left = `-${screenWidth * 0.3}px`;
          zoomLens.style.top = `-${screenHeight * 0.3}px`;
          zoomLens.style.width = `${screenWidth * 0.3}px`;
          if (originalHeight > 800) {
            zoomLens.style.height = `${screenWidth * 0.1}px`;

          } else {
            zoomLens.style.height = `${screenWidth * 0.35}px`;
          }


        } else if (this.totalArtWork >= 3) {
          zoomLens.style.left = `-${screenWidth * 0.2}px`;
          zoomLens.style.top = `-${screenHeight * 0.1}px`;
          zoomLens.style.width = `${screenWidth * 0.2}px`;
          if (originalHeight > 800) {
            zoomLens.style.height = `${screenWidth * 0.1}px`;

          } else {
            zoomLens.style.height = `${screenWidth * 0.25}px`;
          }

        }
        if (this.inkColor) {
          lens.style.backgroundColor = this.inkColor;
        }
      });
    },
    hideLens() {
      const lens = this.$refs.lens;
      const zoomLens = this.$refs.zoomLens;
      if (lens) lens.style.display = 'none';
      if (zoomLens) zoomLens.style.display = 'none';
      this.zoomLensVisible = false;
    },
    debounce(func, wait) {
      let timeout;
      return function (...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
      };
    },
  },
};
</script>

<style scoped>
.image-magnifier-container {
  position: relative;
  display: inline-block;
}

.image-magnifier-image {
  display: block;
}

.image-magnifier-lens {
  position: absolute;
  border: 2px solid #000;
  background-repeat: no-repeat;
  pointer-events: none;
  cursor: none;
}

.zoom-lens {
  position: absolute;
  border: 2px solid #f00;
  /* Different border color for zoom lens */
  background-repeat: no-repeat;
  pointer-events: none;
  cursor: none;
  display: none;
  /* Default to hidden */
}
</style>
