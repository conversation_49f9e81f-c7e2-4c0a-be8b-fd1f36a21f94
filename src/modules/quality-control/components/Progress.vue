<template>
  <div
    class="w-full rounded-lg p-1 relative"
    :class="current < totalItem ? 'bg-red-300' : 'bg-green-300'"
  >
    <div
      class="rounded-md h-full min-h-[30px] flex justify-center align-middle"
      :class="current < totalItem ? 'bg-red-500' : 'bg-green-500'"
      :style="getPercent"
    ></div>
    <span
      class="font-bold text-white w-full h-full absolute z-10 top-0 text-center text-2xl"
    >
      {{ current + ' of ' + totalItem }}
    </span>
  </div>
</template>

<script>
export default {
  props: {
    current: {
      default: 0,
      type: Number,
    },
    totalItem: {
      default: 1,
      type: Number,
    },
  },
  computed: {
    getPercent() {
      return {
        width: `${(this.current / this.totalItem) * 100}%`,
      };
    },
  },
};
</script>

<style lang="scss" scoped></style>
