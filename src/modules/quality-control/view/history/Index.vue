<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left"><h1>{{ $t('Quality control History') }}</h1></div>
    </div>
    <div class="table-content">
      <div class="filter">
        <el-input
            :placeholder="$t('SKU')"
            class="search mr-2"
            v-model="filter.sku"
            @keyup.enter="onFilter"
        />
        <el-select
            ref="productStyle"
            v-model="filter.status"
            filterable clearable class="mr-2" :placeholder="$t('Select status')"
        >
          <el-option
              v-for="item in types"
              :key="item.value"
              :label="item.label"
              :value="item.value"
          />
        </el-select>
        <div class="btn-filter">
          <template v-if="hasFilter">
            <el-link type="danger" @click="onClearFilter" :underline="false"
            >{{ $t('Clear') }}</el-link
            >
          </template>
          <el-button type="primary" @click="fetchData()">
            <span class="icon-margin-right"><icon :data="iconFilter" /></span
            >{{ $t('Filter') }}
          </el-button>
        </div>
      </div>

      <el-table
          border
          stripe size="small" :data="items"
          style="width: 100%"
          :max-height="maxHeight"
          v-loading="isLoading"
          element-loading-text="Loading..."
      >
        <el-table-column prop="id" :label="$t('ID')" min-width="100">
          <template #default="scope">
            {{ scope.row.id}}
          </template>
        </el-table-column>
        <el-table-column prop="sku" :label="$t('SKU')" min-width="250">
          <template #default="scope">
            {{ scope.row.sku }}
          </template>
        </el-table-column>
        <el-table-column prop="name" :label="$t('Product Name')" min-width="300">
          <template #default="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>
        <el-table-column prop="status" :label="$t('Status')" width="150">
          <template #default="scope">
            {{statusQualityControl(scope.row.status) }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" :label="$t('Created At')" min-width="200">
          <template #default="scope">
            {{ utcToLocalTime(scope.row.created_at).format('lll') }}
          </template>
        </el-table-column>
        <el-table-column prop="updated_at" :label="$t('Updated At')" min-width="200">
          <template #default="scope">
            {{ utcToLocalTime(scope.row.updated_at).format('lll') }}
          </template>
        </el-table-column>
        <el-table-column prop="username" :label="$t('user name')" width="150">
          <template #default="scope">
            {{ scope.row.username }}
          </template>
        </el-table-column>
      </el-table>

      <div class="bottom">
        <div class="total">{{$t('Total:')}} {{ items.length ? formatNumber(total) : 0 }}</div>
        <el-pagination
            :disabled="isLoading"
            background
            layout="prev, pager, next"
            :page-size="filter.limit"
            :total="total"
            @current-change="changePage"
            v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
              v-model="filter.limit"
              :placeholder="$t('Select')"
              size="mini"
              @change="onFilter"
          >
            <el-option
                v-for="item in limits"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>

  </div>
</template>
