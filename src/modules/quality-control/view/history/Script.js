import {equals} from "ramda";
import { list } from "@/api/qualityControl.js";


export default {
    name: "QualityControlHistory",
    components: {

    },
    data() {
        return {
            filter: this.setDefaultFilter(),
            isLoading: false,
            types: [
                {
                    label: this.$t('Pass'),
                    value: "pass",
                },
                {
                    label: this.$t('Wrong Style'),
                    value: "style",
                },
                {
                    label: this.$t('Wrong Color'),
                    value: "color",
                },
                {
                    label: this.$t('Wrong Size'),
                    value: "size",
                },
                {
                    label: this.$t('Wrong Artwork'),
                    value: "artwork",
                },
                {
                    label: this.$t('Print Error'),
                    value: "print",
                },
                {
                    label: this.$t('Wrong Side'),
                    value: "side",
                },
            ],
            items: [],
        }
    },
    mounted() {
        this.fetchData();
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 230);
        },
        hasFilter() {
            const defaultFilter = this.setDefaultFilter();
            console.log(this.filter);
            return !equals(defaultFilter, this.filter);
        },
    },
    methods: {
        setDefaultFilter(){
            return{
                sku: "",
                limit: 25,
                page: 1,
                status: ''
            }
        },
        changePage(page) {
            this.filter.page = page;
            this.$nextTick(() => {
                this.fetchData();
            });
        },
        onClearFilter() {
            this.filter = this.setDefaultFilter()
            this.$nextTick(() => {
                this.fetchData();
            });
        },
        onFilter() {
            this.fetchData()
        },
        async fetchData(){
            console.log(this.filter);
            this.isLoading = true;
            // this.setRouteParam();
            const res = await list(this.filter);
            console.log(res, "log data");
            this.isLoading = false;
            const data = res.data || [];
            this.total = data.total;
            this.items = data.data;

        },
        statusQualityControl(status){
            console.log(status);
            let qualityStatus = "";
            switch (status) {
                case "pass":
                    qualityStatus = "Pass";
                    break;
                case "style":
                    qualityStatus = "Wrong Style";
                    break;
                case "color":
                    qualityStatus = "Wrong Color";
                    break;
                case "size":
                    qualityStatus = "Wrong Size";
                    break;
                case "artwork":
                    qualityStatus = "Wrong Artwork";
                    break;
                case "print":
                    qualityStatus = "Print Error";
                    break;
                case "side":
                    qualityStatus = "Wrong Side";
                    break;
            }
            return qualityStatus;
        },
    }
}