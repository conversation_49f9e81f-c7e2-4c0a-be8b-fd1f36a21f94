<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div class="top-head relative">
    <h2 class="!font-bold absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 !text-red-600 !text-[20px] ">{{ $t('Backlog: ') }} {{ formatNum(totalBacklog) }}</h2>
    <div class="top-head-left">
      <h1>{{ $t('Quality Control') }}</h1>
    </div>
<!--    <div class="!mt-[-22px] item">-->
<!--    </div>-->
  </div>
  <div class="top-head table-content">
    <div class="top-head-left flex">
      <div class="demo-ruleForm mt-3">
        <div class="scan-employee">
          <div class="flex">
            <div v-if="employee.length > 0" class="flex" style="min-width: 400px">
            <div
              class="bg-gray-50 border rounded mr-3"
              style="max-width: 712px; min-width: 712px"
              :class="performanceColor, performanceColorBorder"
            >
              <div class="">
                <div
                  class="flex justify-between p-3 border-b border-b-grey-50"
                  :class="performanceColorBorderBottom"
                >
                  <b style="font-size: 18px" class="mr-2">
                    {{ $t('Hi') }} {{ totalNameEmployee }}
                    {{ $t(' Have a nice day!') }}
                  </b>
                  <el-link
                    type="danger"
                    class="ml-3"
                    @click="resetEmployee"
                    :underline="false"
                  >
                    {{ $t('Logout') }}
                  </el-link>
                </div>
                <div class="flex p-3">
                  <div
                    class="w-full"
                    :class="performanceColorBorderRight"
                  >
                    <div class="flex justify-between items-center w-full">
                      <span class="my-auto font-bold">{{ $t('Output:') }}</span>
                      <span class="my-auto">{{ convertOutPut(total_item) }} </span>
                    </div>
                    <div class="flex justify-between items-center w-full">
                      <span class="font-bold">{{ $t('Time:') }}</span>
                      <span>
                        <IncrementTime />
                      </span>
                    </div>
                    <div class="flex justify-between items-center w-full">
                      <span class="border-black font-bold">{{
                        $t('Performance:')
                      }}</span>
                      <span class="border-black"
                        >{{ convertItem(avg_item_by_hour) }} </span
                      >
                    </div>
                  </div>

                  <div
                    v-if="isCollapsed && is15Minutes"
                    class="ml-8 rounded-b w-full"
                  >
                    <div class="w-full">
                      <div
                        class="flex justify-between items-center w-full my-auto"
                      >
                        <div
                          class="flex justify-between items-center w-full h-full"
                        >
                          <span
                            class="flex items-center border-black font-bold"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              fill="currentColor"
                              class="bi bi-cake2 mr-1"
                            >
                              <path
                                d="m3.494.013-.595.79A.747.747 0 0 0 3 1.814v2.683q-.224.051-.432.107c-.702.187-1.305.418-1.745.696C.408 5.56 0 5.954 0 6.5v7c0 .546.408.94.823 1.201.44.278 1.043.51 1.745.696C3.978 15.773 5.898 16 8 16s4.022-.227 5.432-.603c.701-.187 1.305-.418 1.745-.696.415-.261.823-.655.823-1.201v-7c0-.546-.408-.94-.823-1.201-.44-.278-1.043-.51-1.745-.696A12 12 0 0 0 13 4.496v-2.69a.747.747 0 0 0 .092-1.004l-.598-.79-.595.792A.747.747 0 0 0 12 1.813V4.3a22 22 0 0 0-2-.23V1.806a.747.747 0 0 0 .092-1.004l-.598-.79-.595.792A.747.747 0 0 0 9 1.813v2.204a29 29 0 0 0-2 0V1.806A.747.747 0 0 0 7.092.802l-.598-.79-.595.792A.747.747 0 0 0 6 1.813V4.07c-.71.05-1.383.129-2 .23V1.806A.747.747 0 0 0 4.092.802zm-.668 5.556L3 5.524v.967q.468.111 1 .201V5.315a21 21 0 0 1 2-.242v1.855q.488.036 1 .054V5.018a28 28 0 0 1 2 0v1.964q.512-.018 1-.054V5.073c.72.054 1.393.137 2 .242v1.377q.532-.09 1-.201v-.967l.175.045c.655.175 1.15.374 1.469.575.344.217.356.35.356.356s-.012.139-.356.356c-.319.2-.814.4-1.47.575C11.87 7.78 10.041 8 8 8c-2.04 0-3.87-.221-5.174-.569-.656-.175-1.151-.374-1.47-.575C1.012 6.639 1 6.506 1 6.5s.012-.139.356-.356c.319-.2.814-.4 1.47-.575M15 7.806v1.027l-.68.907a.94.94 0 0 1-1.17.276 1.94 1.94 0 0 0-2.236.363l-.348.348a1 1 0 0 1-1.307.092l-.06-.044a2 2 0 0 0-2.399 0l-.06.044a1 1 0 0 1-1.306-.092l-.35-.35a1.935 1.935 0 0 0-2.233-.362.935.935 0 0 1-1.168-.277L1 8.82V7.806c.42.232.956.428 1.568.591C3.978 8.773 5.898 9 8 9s4.022-.227 5.432-.603c.612-.163 1.149-.36 1.568-.591m0 2.679V13.5c0 .006-.012.139-.356.355-.319.202-.814.401-1.47.576C11.87 14.78 10.041 15 8 15c-2.04 0-3.87-.221-5.174-.569-.656-.175-1.151-.374-1.47-.575-.344-.217-.356-.35-.356-.356v-3.02a1.935 1.935 0 0 0 2.298.43.935.935 0 0 1 1.08.175l.348.349a2 2 0 0 0 2.615.185l.059-.044a1 1 0 0 1 1.2 0l.06.044a2 2 0 0 0 2.613-.185l.348-.348a.94.94 0 0 1 1.082-.175c.781.39 1.718.208 2.297-.426"
                              />
                            </svg>
                            Age
                          </span>
                          <span class="border-black">{{ age }}</span>
                        </div>
                      </div>
                      <div class="flex justify-between items-center w-full">
                        <span class="border-black font-bold flex">
                          <Medal class="w-4 h-4 my-auto mr-1" />{{
                            performanceHistory.last_year + $t(' avg. perf.')
                          }}
                        </span>
                        <span class="border-black"
                          >{{ convertItem(avg_item_last_year) }} </span
                        >
                      </div>
                      <div class="flex justify-between items-center w-full">
                        <span class="border-black font-bold flex">
                          <Medal class="w-4 h-4 my-auto mr-1" />{{
                            performanceHistory.current_year + $t(' expected. perf.')
                          }}
                        </span>
                        <span class="border-black"
                          >{{ convertItem(ave_item_current_year) }} </span
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- <div class="m-3 flex justify-end">
                <el-button
                  @click="addEmployee()"
                  class="bg-[#1bae9f] p-2"
                  type="primary"
                >
                  <span class="text-3xl font-bold text-white">+</span>
                </el-button>
              </div> -->
            </div>
          </div>
          <div class="flex justify-end" v-if="employee.length > 0">
                <el-button
                  @click="addEmployee()"
                  class="bg-[#1bae9f] p-2"
                  type="primary"
                >
                  <span class="text-3xl font-bold text-white">+</span>
                </el-button>
              </div>

          </div>

          <div style="min-width: 400px" class="flex flex-col mt-3">
            <template v-for="(input, k) in itemsArray" :key="k">
              <div class="flex pb-3.5">
                <el-input
                  :placeholder="$t('Scan code employee')"
                  class="el-form-item-scan-employee border-stone-500 mr-2"
                  size="large"
                  ref="employeeCode"
                  v-model="input.codeEmployeeValue"
                  @keyup.enter="getScanCodeEmployee(k)"
                />
                <div class="m-auto" v-if="employee.length > 0">
                  <el-button
                    @click="removeEmployeeInList(k)"
                    class="bg-[#1bae9f] w-11"
                    type="danger"
                    ><span class="text-3xl font-bold text-white"
                      >-</span
                    ></el-button
                  >
                </div>
                <template v-else>
                  <div class="m-auto" v-if="k > 0">
                    <el-button
                      @click="removeEmployeeInList(k)"
                      class="bg-[#1bae9f] w-11"
                      type="danger"
                      ><span class="text-3xl font-bold text-white"
                        >-</span
                      ></el-button
                    >
                  </div>
                  <div class="m-auto" v-else>
                    <el-button
                      @click="addEmployee()"
                      class="bg-[#1bae9f] w-11"
                      type="primary"
                    >
                      <span class="text-3xl font-bold text-white"
                        >+</span
                      ></el-button
                    >
                  </div>
                </template>
              </div>
            </template>
          </div>
        </div>
        <div class="flex">
          <div class="scan-sku my-auto">
            <el-input
                :placeholder="$t('Scan QR code')"
                class="mt-2 el-form-item-tracking-number border-stone-500"
                style="width: 400px;"
                size="large"
                ref="skuQualityControl"
                v-model="filter.label"
                :disabled="!isAllowScan"
                @keyup.enter="getScanBarcodeQualityControl()"
            />
          </div>
          <div class=" ml-4 pt-2 my-auto flex">
            <div
                v-for="(item, index) in orderTypes"

                class="py-1 h-5 rounded-full my-auto flex items-center justify-center text-white select-none hover:shadow-sm min-w-fit px-2 !text-[13px] bg-[#0DA5EA] mr-4">
              {{ item }}
            </div>
            <div class="mr-4">
              <span v-for="(item, index) in renderTag(tags)" :key="index">
              <el-tooltip class="box-item" effect="dark" :content="item?.name"
                          placement="top-start">
                <el-tag size="small" class="mr-2 mb-1 !px-[4px] h-5 !rounded-full" type="info" :color="item?.color"
                        v-if="item?.color" style="color: #fff"></el-tag>
                <el-tag size="small" class="mr-2 mb-1 !px-[4px] h-5 !rounded-full" type="info" v-else></el-tag>
              </el-tooltip>
            </span>
            </div>
            <span v-if="internalNote" class="bg-[#fef0f0] text-[#F67474] my-auto !py-1 pl-2 pr-4 rounded"> {{ internalNote }} </span>
          </div>
        </div>

      </div>
      <div class="ml-3">
        <span
          v-if="isFBA"
          class="w-[90px] justify-center flex uppercase font-bold"
        >
          <img src="/src/assets/images/fba.svg" width="60" height="60" />
        </span>
        <el-image
          v-for="(item, index) in iconProduct"
          lazy="true"
          :class="['mr-2', isFBA ? 'mt-[20px]' : 'mt-[62px]']"
          style="width: 90px; height: 90px"
          :src="getImageUrl(item)"
          :fit="fit"
        ></el-image>
      </div>
    </div>
    <div class="top-head-right">
      <div v-if="label" class="mb-2 font-bold text-6xl text-center uppercase">
        {{ storeCode || storeNA }}
      </div>
      <div class="flex justify-end">
        <el-button
          color="#1AAF9F"
          type="primary"
          v-if="label"
          :disabled="isProcessed"
          @click="debouncehandlePassedQC"
          size="large"
        >
          <span class="icon-margin-right"></span>{{ $t('Passed') }}
        </el-button>
        <el-button
          type="danger"
          v-if="label"
          :disabled="isProcessed"
          @click="modalReject(false)"
          size="large"
        >
          <span class="icon-margin-right"></span>{{ $t('Reject') }}
        </el-button>
        <el-button
          type="danger"
          v-if="label"
          :disabled="isProcessed"
          @click="modalReject(true)"
          size="large"
        >
          <span class="icon-margin-right"></span>{{ $t('Reject & Reprint') }}
        </el-button>
      </div>
      <div class="flex flex-end w-full mt-4" v-if="label">
        <Progress :current="totalItemsScan" :totalItem="totalItems"/>
      </div>
    </div>
  </div>
  <div v-if="label">
    <div class="my-3 grid gap-6 grid-cols-2">
      <div class="grow bg-slate-50 p-4 border rounded flex justify-between">
        <div>
          <h1>
            {{ label }}
          </h1>
        </div>
        <div v-if="slaOrder && ['new_order', 'in_production'].includes(slaOrder.order_status)">
          <el-tooltip :content="formattedDate(slaOrder?.sla_expired_at_utc)" placement="top">
            <h1 :class="getRemainingTime(slaOrder?.sla_expired_at_utc)?.className">
              {{ getRemainingTime(slaOrder?.sla_expired_at_utc).text }}
            </h1>
          </el-tooltip>
        </div>
       <div v-if="orderInsert && orderInsert.length > 0" class="mt-auto mb-auto">
         <span class="flex gap-2" >
          <SignTag
              v-for="item in orderInsert"
              :key="item.id"
              :type="item.type"
              :data="item"
          />
        </span>
       </div>

      </div>
      <div class="grow bg-slate-50 p-4 border rounded">
        <h1>
          {{ product }}
        </h1>
      </div>
    </div>
    <div
      :class="[
        'grid gap-6 mt-2',
        options.length > 2
          ? 'grid-cols-3'
          : options.length > 1
          ? 'grid-cols-2'
          : 'grid-cols-1',
      ]"
    >
      <div v-for="(option, key) in options">
        <div class="font-semibold uppercase">
          {{ getNamePrintSide(option.name) }}
          <span
            v-if="option.is_purple == 1"
            class="border text-center rounded-full font-bold bg-purple-400 p-2 ml-2"
          >
            PURPLE INK
          </span>
        </div>
        <!-- <el-link
          type="primary"
          @click="getLinkManualMockup(option.print_side)"
          :underline="false"
          >{{ $t('Mockup manual') }}</el-link
        > -->
        <div
          :class="[
            'border rounded relative',
            options.length == 1 ? 'w-1/2 m-auto' : '',
          ]"
          style="height: calc(100vh - 420px)"
        >
          <img
            v-if="option.PreviewFiles"
            class="preview"
            :src="option.PreviewFiles"
            alt=""
          />
          <el-empty v-else :description="$t('no data')" />
          <div class="absolute top-[16px] right-4 z-10">
            <Magnifier v-show="option.thumb_750" 
            :imageSrc="option.thumb_750" 
            :colorCode="colorCode"
            :inkColor="inkColor"
            :totalArtWork="options.length"

            alt="Description of image" />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-else class="border rounded mt-3">
    <el-empty :description="$t('Please scan QR code')">
      <template #image>
        <img src="@/assets/images/scan-label-id.png" />
      </template>
    </el-empty>
  </div>
  <el-dialog
    v-model="modalRejectOrder"
    :title="$t('Reject')"
    custom-class="el-dialog-custom !max-w-[1300px]"
    :close-on-click-modal="false"
    width="90%"
  >
    <template #default>
      <div v-if="warehouseMexico.includes($author.warehouseId())" class="mb-8" style="font-family: Tahoma!important;">
        <p>Country of origin<span style="color: red"> *</span></p>
        <el-select
          :disabled="disableSelectCountry"
          :placeholder="$t('Select country')"
          class="el-select-country w-full el-form-item-country-distributor"
          v-model="countrySelect"
          filterable
        >
          <el-option
            v-for="item in countries"
            :key="item.iso2"
            :label="item.name"
            :value="item.iso2"
          >
          </el-option>
        </el-select>
      </div>
      <el-row :gutter="20" style="font-family: Tahoma!important;">
        <el-col :span="3">
          <div class="text-center text-xl mb-4 text-[#e6a23c] font-black min-h-[110px] content-center word-break">
            {{ $t('Pull / Kéo') }}
          </div>
          <div class="text-center mb-4">
            <el-button
              size="large"
              class="w-full"
              type="warning"
              :disabled="isLoading"
              @click="submitReject('defective item')"
              >
              <div>
                <p class="!leading-[1.2]">{{ $t('Defective Item') }}</p>
                <p class="!leading-[1.2]">{{ $t('Sản phẩm lỗi') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
              size="large"
              class="w-full"
              type="warning"
              :disabled="isLoading"
              @click="submitReject('wrong style')"
              >
              <div>
                <p class="!leading-[1.2]">{{ $t('Wrong Style') }}</p>
                <p class="!leading-[1.2]">{{ $t('Sai kiểu dáng') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
              size="large"
              class="w-full"
              type="warning"
              :disabled="isLoading"
              @click="submitReject('wrong size')"
              >
              <div>
                <p class="!leading-[1.2]">{{ $t('Wrong Size') }}</p>
                <p class="!leading-[1.2]">{{ $t('Sai kích thước') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
              size="large"
              class="w-full"
              type="warning"
              :disabled="isLoading"
              @click="submitReject('wrong color')"
              >
              <div>
                <p class="!leading-[1.2]">{{ $t('Wrong Color') }}</p>
                <p class="!leading-[1.2]">{{ $t('Sai màu') }}</p>
              </div>
            </el-button
            >
          </div>
        </el-col>
        <el-col :span="3">
          <div class="text-center text-xl mb-4 word-break text-[#f56c6c] font-black min-h-[110px] content-center word-break">
            {{ $t('Pretreat / Phủ keo') }}
          </div>
          <div class="text-center mb-4">
            <el-button
              size="large"
              class="w-full"
              type="danger"
              :disabled="isLoading"
              @click="submitReject('missing pretreat')"
              >
              <div>
                <p class="!leading-[1.2]">{{ $t('Missing Pretreat') }}</p>
                <p class="!leading-[1.2]">{{ $t('Chưa dán keo') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
              size="large"
              class="w-full"
              type="danger"
              :disabled="isLoading"
              @click="submitReject('heat press stain')"
              >
              <div>
                <p class="!leading-[1.2]">{{ $t('Heat Press Stain') }}</p>
                <p class="!leading-[1.2]">{{ $t('Vết ô bàn là') }}</p>
              </div>
            </el-button
            >
          </div>
        </el-col>
        <el-col :span="4">
          <div class="text-center text-xl mb-4 text-[#67c23a] font-black min-h-[110px] content-center word-break">
            {{ $t('Print / In') }}
          </div>
          <div class="text-center mb-4">
            <el-button
              size="large"
              class="w-full !whitespace-normal !h-auto !py-2 !px-1"
              type="success"
              :disabled="isLoading"
              @click="submitReject('wrong print')"
              >

              <div>
                <p class="!leading-[1.2]">{{ $t('Wrong Print') }}</p>
                <p class="!leading-[1.2]">{{ $t('In sai') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
              size="large"
              class="w-full !text-white !whitespace-normal !h-auto !py-2 !px-1"
              type="success"
              :disabled="isLoading"
              @click="submitReject('wrong location print')"
              >
              <div>
                <p class="!leading-[1.2]">{{ $t('Wrong Location Print') }}</p>
                <p class="!leading-[1.2]">{{ $t('In sai vị trí') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
              size="large"
              class="w-full !whitespace-normal !h-auto !py-2 !px-1"
              type="success"
              :disabled="isLoading"
              @click="submitReject('faded print')"
              >
              <div>
                <p class="!leading-[1.2]">{{ $t('Faded Print') }}</p>
                <p class="!leading-[1.2]">{{ $t('In bị phai màu') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !whitespace-normal !h-auto !py-2 !px-1"
                type="success"
                :disabled="isLoading"
                @click="submitReject('crooked print')"
            >
              <div>
                <p class="!leading-[1.2]">{{ $t('Crooked print') }}</p>
                <p class="!leading-[1.2]">{{ $t('In lệch') }}</p>
              </div>
            </el-button
            >
          </div>
        </el-col>
        <el-col :span="3">
          <div class="text-center text-xl word-break mb-4 text-[#1a73e8] font-black min-h-[110px] content-center word-break">
            {{ $t('Lost In Production / Mất khi sản xuất') }}
          </div>
          <div class="text-center mb-4">
            <el-button
              size="large"
              class="w-full"
              type="primary"
              :disabled="isLoading"
              @click="submitReject('lost in pulling')"
              >
              <div>
                <p class="!leading-[1.2]">{{ $t('Lost In Pulling') }}</p>
                <p class="!leading-[1.2]">{{ $t('Mất khi kéo hàng') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
              size="large"
              class="w-full !whitespace-normal !h-auto !py-2 !px-1"
              type="primary"
              :disabled="isLoading"
              @click="submitReject('lost in pretreat')"
            >
              <div>
                <p class="!leading-[1.2]">{{ $t('Lost In Pretreat') }}</p>
                <p class="!leading-[1.2]">{{ $t('Mất khi dán keo') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
              size="large"
              class="w-full !whitespace-normal !h-auto !py-2 !px-1"
              type="primary"
              :disabled="isLoading"
              @click="submitReject('lost in printing')"
            >
              <div>
                <p class="!leading-[1.2]">{{ $t('Lost In Printing') }}</p>
                <p class="!leading-[1.2]">{{ $t('Mất trong khâu in') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
              size="large"
              class="w-full !whitespace-normal !h-auto !py-2 !px-1"
              type="primary"
              :disabled="isLoading"
              @click="submitReject('lost in qc')"
              >
              <div>
              <p class="!leading-[1.2]">{{ $t('Lost In QC') }}</p>
              <p class="!leading-[1.2]">{{ $t('Mất khi kiểm tra chất lượng') }}</p>
            </div>
            </el-button
            >
          </div>
        </el-col>

        <el-col :span="3">
          <div class="text-center text-xl mb-4 text-[#DD639D] font-black min-h-[110px] content-center word-break">
            {{ $t('Maintenance / Bảo trì') }}
          </div>
          <div class="text-center mb-4">
            <el-button
              size="large"
              class="w-full !whitespace-normal !h-auto !py-2 !px-1 !bg-[#DD639D] !text-white"
              :disabled="isLoading"
              @click="submitReject('printing machine')"
              >
              <div>
                <p class="!leading-[1.2]">{{ $t('Printing Machine') }}</p>
                <p class="!leading-[1.2]">{{ $t('Lỗi máy in') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
              size="large"
              class="w-full !whitespace-normal !h-auto !py-2 !px-1 !bg-[#DD639D] !text-white"
              :disabled="isLoading"
              @click="submitReject('pretreat machine')"
              >
              <div>
                <p class="!leading-[1.2]">{{ $t('Pretreat Machine') }}</p>
                <p class="!leading-[1.2]">{{ $t('Lỗi máy pretreat') }}</p>
              </div>
            </el-button
            >
          </div>
        </el-col>
        <el-col :span="4">
          <div class="text-center text-xl mb-4 text-[#B275FF] font-black min-h-[110px] content-center word-break">
            {{ $t('Vendor Defect / Lỗi nhà cung cấp') }}
          </div>
          <div class="text-center mb-4">
            <el-button
              size="large"
              class="w-full !whitespace-normal !h-auto !py-2 !px-1 !bg-[#B275FF] !text-white"
              :disabled="isLoading"
              @click="submitReject('wrong garment size')"
              >
              <div>
                <p class="!leading-[1.2]">{{ $t('Wrong Garment Size') }}</p>
                <p class="!leading-[1.2]">{{ $t('Sai kích thước trang phục') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
              size="large"
              class="w-full !bg-[#B275FF] !text-white  !whitespace-normal !h-auto !py-2 !px-1"
              :disabled="isLoading"
              @click="submitReject('out of tolerance')"
              >
<!--              <span class="!block !leading-[1.2]">{{-->
<!--                $t('Size is Not Within -/+ 1 inch Tolerance')-->

<!--              }}</span>-->
              <div>
                <p class="!leading-[1.2]">{{ $t('Size is Not Within -/+ 1 inch Tolerance') }}</p>
                <p class="!leading-[1.2]">{{ $t('Kích thước không nằm trong dung sai +/- 1 inch') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
              size="large"
              class="w-full !bg-[#B275FF] !text-white !whitespace-normal !h-auto !py-2 !px-1"
              :disabled="isLoading"
              @click="submitReject('threads issue')"
              >
              <div>
                <p class="!leading-[1.2]">{{ $t('Threads Issue') }}</p>
                <p class="!leading-[1.2]">{{ $t('Lỗi chỉ') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
              size="large"
              class="w-full !bg-[#B275FF] !text-white !whitespace-normal !h-auto !py-2 !px-1"
              :disabled="isLoading"
              @click="submitReject('torn garment')"
              >
              <div>
                <p class="!leading-[1.2]">{{ $t('Torn Garment') }}</p>
                <p class="!leading-[1.2]">{{ $t('Trang phục bị rách') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
              size="large"
              class="w-full !bg-[#B275FF] !text-white !whitespace-normal !h-auto !py-2 !px-1"
              :disabled="isLoading"
              @click="submitReject('stained garment')"
              >
              <div>
                <p class="!leading-[1.2]">{{ $t('Stained Garment') }}</p>
                <p class="!leading-[1.2]">{{ $t('Trang phục bị ố') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#B275FF] !text-white !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('misaligned print')"
            >
<!--              <span class="!block !leading-[1.2]">{{ $t('Skewed/ Off Centered Print') }}</span>-->
              <div>
                <p class="!leading-[1.2]">{{ $t('Skewed/ Off Centered Print') }}</p>
                <p class="!leading-[1.2]">{{ $t('In lệch/ không đúng trung tâm') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#B275FF] !text-white !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('damaged/broken')"
            >
              <div>
                <p class="!leading-[1.2]">{{ $t('Damaged/broken') }}</p>
                <p class="!leading-[1.2]">{{ $t('Hư hỏng/ Bị vỡ') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#B275FF] !text-white !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('with stain')"
            >
              <div>
                <p class="!leading-[1.2]">{{ $t('With Stain') }}</p>
                <p class="!leading-[1.2]">{{ $t('Có vết bẩn') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#B275FF] !text-white !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('vendor with hole')"
            >
              <div>
                <p class="!leading-[1.2]">{{ $t('With Hole') }}</p>
                <p class="!leading-[1.2]">{{ $t('Có lỗ') }}</p>
              </div>
              </el-button
            >
          </div>

        </el-col>
        <el-col :span="4">
          <div class="text-center text-xl mb-4 text-[#0ABFAA] font-black min-h-[110px] content-center  word-break">
            {{ $t('Customer Claims (Khách hàng khiếu nại)') }}
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#0ABFAA] !text-white  !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('design placement issue')"
            >
<!--              <span class="!block !leading-[1.2]">{{ $t('Design Placement Issue') }}</span>-->
              <div>
                <p class="!leading-[1.2]">{{ $t('Design Placement Issue') }}</p>
                <p class="!leading-[1.2]">{{ $t('Lỗi vị trí thiết kế') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#0ABFAA] !text-white !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('peeling print')"
            >
<!--              <span class="!block !leading-[1.2]">{{ $t('Peeling Print') }}</span>-->
              <div>
                <p class="!leading-[1.2]">{{ $t('Peeling Print') }}</p>
                <p class="!leading-[1.2]">{{ $t('In bị bong tróc') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#0ABFAA] !text-white !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('fibrillation')"
            >
<!--              <span class="!block !leading-[1.2]">{{ $t('Fibrillation') }}</span>-->
              <div>
                <p class="!leading-[1.2]">{{ $t('Fibrillation') }}</p>
                <p class="!leading-[1.2]">{{ $t('Lỗi sợi vải bị bung xù') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#0ABFAA] !text-white !flex !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('wrong item')"
            >
<!--              <span class="!block !leading-[1.2]">{{ $t('Wrong Item') }}</span>-->
              <div>
                <p class="!leading-[1.2]">{{ $t('Wrong Item') }}</p>
                <p class="!leading-[1.2]">{{ $t('Sai sản phẩm') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#0ABFAA] !text-white !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('defective item - faded print')"
            >
              <div>
                <p class="!leading-[1.2]">{{ $t('Faded Print') }}</p>
                <p class="!leading-[1.2]">{{ $t('In bị phai màu') }}</p>
              </div>
<!--              <span class="!block !leading-[1.2]">{{ $t('Faded Print') }}</span>-->
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#0ABFAA] !text-white !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('pilling')"
            >
<!--              <span class="!block !leading-[1.2]">{{ $t('Pilling') }}</span>-->
              <div>
                <p class="!leading-[1.2]">{{ $t('Pilling') }}</p>
                <p class="!leading-[1.2]">{{ $t('Lỗi xù lông') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#0ABFAA] !text-white !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('defective item - wrong size')"
            >
              <div>
                <p class="!leading-[1.2]">{{ $t('Wrong Size') }}</p>
                <p class="!leading-[1.2]">{{ $t('Sai kích thước') }}</p>
              </div>
<!--              <span class="!block !leading-[1.2]">{{ $t('Wrong Size') }}</span>-->
            </el-button
            >
          </div><div class="text-center mb-4">
          <el-button
              size="large"
              class="w-full !bg-[#0ABFAA] !text-white !whitespace-normal !h-auto !py-2 !px-1"
              :disabled="isLoading"
              @click="submitReject('broken')"
          >
            <div>
              <p class="!leading-[1.2]">{{ $t('Defective Item - Broken') }}</p>
              <p class="!leading-[1.2]">{{ $t('Sản phẩm lỗi - Bị hỏng') }}</p>
            </div>
<!--            <span class="!block !leading-[1.2]">{{ $t('Defective Item - Broken') }}</span>-->
          </el-button
          >
        </div><div class="text-center mb-4">
          <el-button
              size="large"
              class="w-full !bg-[#0ABFAA] !text-white !whitespace-normal !h-auto !py-2 !px-1"
              :disabled="isLoading"
              @click="submitReject('stains')"
          >
            <div>
              <p class="!leading-[1.2]">{{ $t('Stains') }}</p>
              <p class="!leading-[1.2]">{{ $t('Vết bẩn') }}</p>
            </div>
<!--            <span class="!block !leading-[1.2]">{{ $t('Stains') }}</span>-->
          </el-button
          >
        </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#0ABFAA] !text-white !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('holes')"
            >
              <div>
                <p class="!leading-[1.2]">{{ $t('Defective Item - Holes') }}</p>
                <p class="!leading-[1.2]">{{ $t('Sản phẩm lỗi - Có lỗ') }}</p>
              </div>
<!--              <span class="!block !leading-[1.2]">{{ $t('Defective Item - Holes') }}</span>-->
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#0ABFAA] !text-white !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('stitching')"
            >
              <div>
                <p class="!leading-[1.2]">{{ $t('Defective Item - Stitching') }}</p>
                <p class="!leading-[1.2]">{{ $t('Sản phẩm lỗi - Đường may') }}</p>
              </div>
<!--              <span class="!block !leading-[1.2]">{{ $t('Defective Item - Stitching') }}</span>-->
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#0ABFAA] !text-white !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('coloring issue')"
            >
              <div>
                <p class="!leading-[1.2]">{{ $t('Coloring Issue') }}</p>
                <p class="!leading-[1.2]">{{ $t('Lỗi màu sắc') }}</p>
              </div>
<!--              <span class="!block !leading-[1.2]">{{ $t('Coloring Issue') }}</span>-->
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#0ABFAA] !text-white !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('sizing issue')"
            >
<!--              <span class="!block !leading-[1.2]">{{ $t('Sizing Issue') }}</span>-->
              <div>
                <p class="!leading-[1.2]">{{ $t('Sizing Issue') }}</p>
                <p class="!leading-[1.2]">{{ $t('Lỗi kích thước') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#0ABFAA] !text-white !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('smell')"
            >
              <div>
                <p class="!leading-[1.2]">{{ $t('Smell') }}</p>
                <p class="!leading-[1.2]">{{ $t('Sản phẩm có mùi') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#0ABFAA] !text-white !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('defective item - wrong print')"
            >
<!--              <span class="!block !leading-[1.2]">{{ $t('Wrong Print') }}</span>-->
              <div>
                <p class="!leading-[1.2]">{{ $t('Wrong Print') }}</p>
                <p class="!leading-[1.2]">{{ $t('In sai') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#0ABFAA] !text-white !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('missing item')"
            >
<!--              <span class="!block !leading-[1.2]">{{ $t('Missing Item') }}</span>-->
              <div>
                <p class="!leading-[1.2]">{{ $t('Missing Item') }}</p>
                <p class="!leading-[1.2]">{{ $t('Thiếu sản phẩm') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#0ABFAA] !text-white !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('heat press')"
            >
<!--              <span class="!block !leading-[1.2]">{{ $t('Heat Press') }}</span>-->
              <div>
                <p class="!leading-[1.2]">{{ $t('Heat Press') }}</p>
                <p class="!leading-[1.2]">{{ $t('Lỗi ép nhiệt') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#0ABFAA] !text-white !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('missing print')"
            >
<!--              <span class="!block !leading-[1.2]">{{ $t('Missing Print') }}</span>-->
              <div>
                <p class="!leading-[1.2]">{{ $t('Missing Print') }}</p>
                <p class="!leading-[1.2]">{{ $t('In bị thiếu') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#0ABFAA] !text-white !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('underbase issue')"
            >
<!--              <span class="!block !leading-[1.2]">{{ $t('Underbase Issue') }}</span>-->
              <div>
                <p class="!leading-[1.2]">{{ $t('Underbase Issue') }}</p>
                <p class="!leading-[1.2]">{{ $t('Lỗi lớp nền') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#0ABFAA] !text-white !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('blurry print')"
            >
<!--              <span class="!block !leading-[1.2]">{{ $t('Blurry Print') }}</span>-->
              <div>
                <p class="!leading-[1.2]">{{ $t('Blurry Print') }}</p>
                <p class="!leading-[1.2]">{{ $t('In bị mờ') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#0ABFAA] !text-white !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('lines')"
            >
<!--              <span class="!block !leading-[1.2]">{{ $t('Lines') }}</span>-->
              <div>
                <p class="!leading-[1.2]">{{ $t('Lines') }}</p>
                <p class="!leading-[1.2]">{{ $t('Vết đường kẻ') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#0ABFAA] !text-white !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('torn')"
            >
<!--              <span class="!block !leading-[1.2]">{{ $t('Defective Item - Torn') }}</span>-->
              <div>
                <p class="!leading-[1.2]">{{ $t('Defective Item - Torn') }}</p>
                <p class="!leading-[1.2]">{{ $t('Sản phẩm lỗi - Bị rách') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#0ABFAA] !text-white !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('mechanical')"
            >
<!--              <span class="!block !leading-[1.2]">{{ $t('Defective Item - Mechanical') }}</span>-->
              <div>
                <p class="!leading-[1.2]">{{ $t('Defective Item - Mechanical') }}</p>
                <p class="!leading-[1.2]">{{ $t('Sản phẩm lỗi - Lỗi cơ khí') }}</p>
              </div>
            </el-button
            >
          </div>
          <div class="text-center mb-4">
            <el-button
                size="large"
                class="w-full !bg-[#0ABFAA] !text-white !whitespace-normal !h-auto !py-2 !px-1"
                :disabled="isLoading"
                @click="submitReject('packaging issue')"
            >
<!--              <span class="!block !leading-[1.2]">{{ $t('Defective Item - Packaging Issue') }}</span>-->
              <div>
                <p class="!leading-[1.2]">{{ $t('Defective Item - Packaging Issue') }}</p>
                <p class="!leading-[1.2]">{{ $t('Sản phẩm lỗi - Vấn đề đóng gói') }}</p>
              </div>
            </el-button
            >
          </div>
        </el-col>
      </el-row>

      <!--      <div class="grid gap-2 grid-cols-2 mb-3 mt-5">-->
      <!--        <el-button size="large" :disabled="isLoading" @click="submitReject('style')">{{ $t('Wrong Style') }}</el-button>-->
      <!--        <el-button size="large" :disabled="isLoading" @click="submitReject('color')">{{ $t('Wrong Color') }}</el-button>-->
      <!--      </div>-->
      <!--      <div class="grid gap-2 grid-cols-2 mb-3 ">-->
      <!--        <el-button size="large" :disabled="isLoading" @click="submitReject('size')">{{ $t('Wrong Size') }}</el-button>-->
      <!--        <el-button size="large" :disabled="isLoading" @click="submitReject('artwork')">{{ $t('Wrong Artwork') }}</el-button>-->
      <!--      </div>-->
      <!--      <div class="grid gap-2 grid-cols-2 mb-3 ">-->
      <!--        <el-button size="large" :disabled="isLoading" @click="submitReject('print')">{{ $t('Print Error') }}</el-button>-->
      <!--        <el-button size="large" :disabled="isLoading" @click="submitReject('side')">{{ $t('Wrong Side') }}</el-button>-->
      <!--      </div>-->
    </template>
  </el-dialog>
</template>
