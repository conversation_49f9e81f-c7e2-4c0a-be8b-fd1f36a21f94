import { equals, isEmpty, isNil } from 'ramda';
import { get, update, pass, reject } from '@/api/qualityControl.js';
import { getInfo as getPasQC } from '@/api/multipleStaging.js';
import {
  employeeLogoutTimeCheckingMultipleStaging,
  employeeTimeChecking,
} from '@/api/employee.js';
import IncrementTime from '@/components/IncrementTime.vue';
import { getLinkManualMockup } from '@/api/saleOrderItemImage';
import { STORAGE_URL } from '@/utilities/constants';
import { countries, fetchCountryPartNumber } from '@/api/default.js';
import { WAREHOUSE_MEXICO, NOT_APPLICABLE } from '@/utilities/constants';
import Progress from '@/modules/quality-control/components/Progress.vue';
import SignTag from '@/components/SignTag.vue';
import EventBus from "@/utilities/eventBus";
import { socketClient } from '@/utilities/socket';
import warehouseMixin from '@/mixins/warehouse';
import numberMixin from '@/mixins/formatNumber';
import { livePerformance } from '@/api/performance.js';
import { ArrowDownBold, ArrowUpBold, Medal, Trophy } from '@element-plus/icons-vue';
import Magnifier from '../../components/Magnifier.vue';
import { S3_URL } from '@/utilities/constants';
import { debounce } from "@/utilities/helper.js";
import {mapGetters} from "vuex";
import {getBacklogWithJobType} from "@/api/backlog";
import moment from "moment-timezone";
import helperMixin from '@/mixins/helpers.js';

const socket = socketClient();

export default {
  name: 'QualityControl',
  components: {
    IncrementTime,
    Progress,
    SignTag,
    ArrowDownBold,
    ArrowUpBold,
    Medal,
    Trophy,
    Magnifier
  },
  mixins: [warehouseMixin, numberMixin, helperMixin],
  data() {
    return {
      isLoading: false,
      filter: this.setDefaultFilter(),
      options: [],
      qualityControlId: '',
      label: '',
      product: '',
      sku: '',
      modalRejectOrder: false,
      codeEmployee: '',
      employee: [],
      employeeError: '',
      skuError: '',
      job_type_qc: 'quality_control',
      job_type_stag: 'multiple_staging',
      hasReprint: false,
      storeName: '',
      iconProduct: '',
      urlStorage: STORAGE_URL,
      isFBA: false,
      orderTypes: [],
      tag: '',
      tags: [],
      countries: [],
      warehouseMexico: WAREHOUSE_MEXICO,
      countrySelect: null,
      itemsArray: [
        {
          codeEmployeeValue: '',
        },
      ],
      idParentTimeChecking: '',
      dataTimeCheckingIdQC: [],
      dataTimeCheckingIdStagging: [],
      employeeIdParentTimeChecking: '',
      arrCodeEmployee: [],
      totalNameEmployee: '',
      totalItems: 0,
      totalItemsScan: 0,
      isProcessed: false,
      storeNA: NOT_APPLICABLE,
      storeCode: '',
      orderInsert: [],
      partNumberCountry: '',
      disableSelectCountry: false,
      total_item: 0,
      avg_item_by_hour: 0,
      avg_item_last_year: 0,
      performanceHistory: {},
      ave_item_current_year: 0,
      warehouse_id: '',
      department: 22,
      age: '',
      isCollapsed: true, // Collapsible state
      is15Minutes: true,
      countPerformanceReport: 0,
      colorCode: null,
      inkColor: null,
      isCount: true,
      keydownTimeout: null, // Variable to store the timeout reference
      delay: 500, // Delay in milliseconds
      isAllowScan: false,
      internalNote: '',
      totalBacklog: 0,
      intervalId: null,
      slaOrder: null,
    };
  },
  async created() {
    // window.addEventListener("keydown", this.escapeListener);
    await socket.socket.connect()
    socket.socket.on('quality_control', (data) => {
      if (this.employee.length > 0) {
        let employeeIds = this.employee.map((item) => item.data.id);
        if (employeeIds.includes(data.employee_id)) {
          this.onClear();
          this.focusByElClass();
          this.modalRejectOrder = false;
        }
      }
    });
    this.focusByElClassScanEmployee();
    // this.focusByElClass()
  },
  mounted() {
    this.$store.dispatch('getTags');
    this.fetchBacklog();
    this.intervalId = setInterval( () => {
      this.fetchBacklog();
    }, 60000 )

    window.addEventListener('keydown', this.handleKeyDown);
    this.warehouse_id = this.getWarehouseId();
  },
  beforeUnmount() {
    clearTimeout(this.keydownTimeout);
    window.removeEventListener('keydown', this.handleKeyDown);
    this.clearPerformanceInterval();
  },
  unmounted() {
    this.stopInterval();
    clearTimeout(this.keydownTimeout);
    window.removeEventListener('keydown', this.handleKeyDown);
    this.clearPerformanceInterval();
    socket.socket.disconnect();
  },
  beforeDestroy() {
    clearTimeout(this.keydownTimeout);
    window.removeEventListener('keydown', this.handleKeyDown);
    this.clearPerformanceInterval();
  },
  computed: {
    ...mapGetters(['getTags']),
    displayEmployeeList() { },
    performanceColor() {
      if(this.countPerformanceReport < 2) {
        return '';
      }

      let percentageDifference = (this.avg_item_by_hour / this.ave_item_current_year) * 100 - 100;

      if (percentageDifference < -30) {
        return 'bg-red-200'; // Change background to red if below 30%
      } else if (percentageDifference < -20) {
        return 'bg-yellow-200'; // Change background to yellow if below 20%
      } else {
        return ''; // Default background color
      }
    },
    performanceColorBorder() {
      if(this.countPerformanceReport < 2) {
        return '';
      }

      let percentageDifference = (this.avg_item_by_hour / this.ave_item_current_year) * 100 - 100;

      if (percentageDifference < -30) {
        return 'border border-red-500'; // Change background to red if below 30%
      } else if (percentageDifference < -20) {
        return 'border border-yellow-200'; // Change background to yellow if below 20%
      } else {
        return ''; // Default background color
      }
    },

    performanceColorBorderBottom() {
      if(this.countPerformanceReport < 2) {
        return '';
      }

      let percentageDifference = (this.avg_item_by_hour / this.ave_item_current_year) * 100 - 100;

      if (percentageDifference < -30) {
        return 'border-b border-b-red-500'; // Change background to red if below 30%
      } else if (percentageDifference < -20) {
        return 'border-b border-b-yellow-500'; // Change background to yellow if below 20%
      } else {
        return ''; // Default background color
      }
    },
    performanceColorBorderRight() {
      if(this.countPerformanceReport < 2) {
        return '';
      }

      let percentageDifference = (this.avg_item_by_hour / this.ave_item_current_year) * 100 - 100;

      if (percentageDifference < -30 && this.is15Minutes) {
        return 'pr-8 border-r border-r-red-500'; // Change background to red if below 30%
      } else if (percentageDifference < -20) {
        return 'pr-8 border-r border-r-yellow-500'; // Change background to yellow if below 20%
      } else if(this.avg_item_by_hour > 0 && this.ave_item_current_year > 0 ){
        return 'pr-8 border-r border-r-grey-50'; // Default background color
      }
    },
  collapseIcon() {
    return this.isCollapsed ? ArrowUpBold : ArrowDownBold;
  },

  },
  methods: {
    stopInterval() {
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
      }
    },
    async fetchBacklog() {
      let params = {
        'job_type': this.job_type_qc,
      }
      const res = await getBacklogWithJobType(params);
      this.totalBacklog = res.data?.total;
    },
    renderTag(tag) {
      if (!tag) {
        return [];
      }

      const data = [];

      Object.values(tag).forEach((item) => {
        const itemTag = this.getTags.find((i) => {
          return i.id == item;
        });
        data.push(itemTag);
      });

      return data;
    },
    getTag(orderTag) {
      this.tag = '';
      this.tags = [];

      if (orderTag?.tag) {
        this.tag = orderTag.tag.trim().split(',');
        this.tags = this.tag.filter((item) => item != '');
        this.tag = this.tag.filter((item) => item != '' && item != 203);
      }
    },
    getImageUrl(item) {
      return this.urlStorage + '/IconProduct/' + item;
    },
    getLinkManualMockup(printSide) {
      const link = getLinkManualMockup(this.sku, printSide);
      return window.open(link);
    },
    onClear() {
      this.isFBA = false;
      this.options = [];
      this.qualityControlId = '';
      this.label = '';
      this.product = '';
      this.sku = '';
      this.storeName = '';
      this.storeCode = '';
      this.iconProduct = '';
      this.totalItems = 0;
      this.totalItemsScan = 0;
      this.countrySelect = '';
      this.disableSelectCountry = false,
      this.slaOrder = null
    },
    setDefaultFilter() {
      return {
        label: '',
      };
    },
    // scanBarcode: debounce(async function () {
    //     await this.getScanBarcodeQualityControl();
    // }, 500),
    async getScanBarcodeQualityControl() {
      if (!this.employeeIdParentTimeChecking) {
        this.codeEmployee = '';
        this.employeeError = 'Employee code field cannot be left blank.';
        this.filter.label = '';
        this.focusByElClassScanEmployee();
        return;
      }

      if (!this.employee) {
        this.codeEmployee = '';
        this.employeeError = 'Scan Employee code again';
        this.focusByElClassScanEmployee();
        return;
      }
      this.employeeError = '';

      if (this.filter.label === '') {
        this.focusByElClass();
        return;
      }
      this.onClear();
      this.isLoading = true;
      try {
        const res = await get({
          label: this.filter.label,
          employee_id: this.employeeIdParentTimeChecking,
          id_time_checking: this.dataTimeCheckingIdQC,
        });
        this.isFBA = res.data.data.is_fba_order;
        this.orderTypes = res.data?.data?.order_types ?? [];
        this.internalNote = res.data?.data?.internal_note ?? '';
        this.getTag(res.data?.data);
        this.options = res.data.data.map_options;
        this.options = this.options.map(option => ({
          ...option,
          thumb_750: `${S3_URL}/thumb/750/${res.data.data?.order_date}/${res.data.data?.sku}-${option.print_side}.png`

        }));

        this.qualityControlId = res.data.qualityControlId;
        this.label = res.data.data.label_id
          ? res.data.data.label_id
          : res.data.data.sku;
        this.storeName = res.data.data.store_name;
        this.storeCode = res.data.data.store_code;
        this.product = res.data.data.name;
        this.sku = res.data.data.sku;
        this.filter.label = '';
        this.iconProduct = res.data.iconProduct;
        this.totalItems = res.data.totalItem;
        this.totalItemsScan = res.data.totalItemScan;
        const response = await fetchCountryPartNumber({ product_id: [res.data.data.product_id] });
        this.countries = response.data || [];
        this.orderInsert = res.data.data?.order_insert;
        this.focusByElClass();
        this.isProcessed = false;
        this.isLoading = false;
        this.countrySelect = res.data.data?.country_part_number
        this.disableSelectCountry = res.data.data?.country_part_number ? true : false
        this.colorCode = res.data.data?.color_code;
        this.inkColor = res.data.data?.ink_color;
        this.slaOrder = res.data?.slaOrder
        if(res.data.data?.staged_at) {
          this.isCount = true;
        }
      } catch (e) {
        const data = e.response.data;

        let message = this.$t('Not found');
        if (!isEmpty(data)) {
          if (data.errors) {
            const keyFirstData = Object.keys(data.errors)[0];
            const firstData = data.errors[keyFirstData];
            message = firstData[0];
          } else if (data.message) {
            message = data.message[0] ?? data.message ?? '';
          }
        }
        this.filter.label = '';
        this.focusByElClass();
        this.isLoading = false;
        this.notification(message, 'error');
      }
    },
    modalReject(reprint) {
      this.modalRejectOrder = true;
      this.hasReprint = reprint;
    },
    async submitReject(name) {
      if (
        this.warehouseMexico.includes(this.$author.warehouseId()) &&
        isNil(this.countrySelect)
      ) {
        this.notification('Please select country!', 'error');
        return;
      }
      this.isLoading = true;
      try {
        const res = await reject({
          label: this.label,
          status: name,
          hasReprint: this.hasReprint,
          employeeId: this.employeeIdParentTimeChecking,
          id_time_checking: this.dataTimeCheckingIdQC,
          country: this.countrySelect,
        });
        this.isProcessed = true;
        this.onClear();
        this.focusByElClass();
        this.modalRejectOrder = false;
        this.notification(this.$t('Reject success'), 'success');
        this.isLoading = false;
        if(res.data?.isCount) {
          this.total_item++;
        }
      } catch (e) {
        const data = e.response.data;
        let message = this.$t('Reject error');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.onClear();
        this.filter.label = '';
        this.focusByElClass();
        this.modalRejectOrder = false;
        this.isLoading = false;
        this.notification(message, 'error');
      }
    },
    focusByElClass(elClass = 'el-form-item-tracking-number') {
      this.$nextTick(() => {
        const el = document.getElementsByClassName(elClass);
        if (!el || !el.length) {
          return;
        }
        const firtsElError = (el && el[0]) || undefined;
        const input = firtsElError.querySelector('input');
        document.body.scrollTop = (input.offsetTop - 120) | 0;
        input.focus();
      });
    },
    focusByElClassScanEmployee(elClass = 'el-form-item-scan-employee') {
      this.$nextTick(() => {
        const el = document.getElementsByClassName(elClass);
        if (!el || !el.length) {
          return;
        }
        const firtsElError = (el && el[0]) || undefined;
        const input = firtsElError.querySelector('input');
        document.body.scrollTop = (input.offsetTop - 120) | 0;
        input.focus();
      });
    },
    // async getScanCodeEmloyee() {
    //   try {
    //     this.isLoading = true;
    //     const res = await employeeTimeChecking({
    //       code: Number(this.codeEmployee),
    //       job_type: this.job_type_qc,
    //     });
    //     if (!res.data.data) {
    //       this.codeEmployee = '';
    //       this.employeeError = 'Scan employee code error, please scan again.';
    //       this.notification(
    //         'Scan employee code error, please scan again.',
    //         'error'
    //       );
    //       this.focusByElClassScanEmployee();
    //       return;
    //     }
    //     this.employee = res.data.data;
    //     this.time_checking_id = res.data.id_time_checking;
    //     this.focusByElClass();
    //     this.isLoading = false;
    //   } catch (e) {
    //     const data = e.response.data;
    //     let message = this.$t('Not found');
    //     if (!isEmpty(data)) {
    //       const keyFirstData = Object.keys(data)[0];
    //       const firstData = data[keyFirstData];
    //       message = firstData[0];
    //     }
    //     this.isLoading = false;
    //     this.notification(message, 'error');
    //   }
    // },
    async resetEmployee() {
      socket.socket.disconnect();
      const res = await employeeLogoutTimeCheckingMultipleStaging({
        id: [...this.dataTimeCheckingIdQC, ...this.dataTimeCheckingIdStagging],
      });
      this.employee = [];
      this.employeeError = '';
      this.codeEmployee = '';
      this.totalNameEmployee = '';
      this.dataTimeCheckingIdQC = [];
      this.dataTimeCheckingIdStagging = [];
      this.itemsArray = [
        {
          codeEmployeeValue: '',
        },
      ];
      this.onClear();
      this.time = 0;
      this.focusByElClassScanEmployee();
      location.reload();
    },
    getNamePrintSide(side) {
      return side.replaceAll('_', ' ');
    },
    addEmployee() {
      this.itemsArray.push({
        value: '',
      });
    },
    removeEmployeeInList(index) {
      this.itemsArray.splice(index, 1);
    },
    async getScanCodeEmployee(index) {
      try {
        this.isLoading = true;
        const code = this.itemsArray[index].codeEmployeeValue;
        const checkCode = this.arrCodeEmployee.find(
          (element) => element === code
        );
        if (checkCode) {
          this.itemsArray[index].codeEmployeeValue = '';
          let message = this.$t('Employee scanned');
          this.notification(message, 'error');
          return;
        }

        if (this.idParentTimeChecking === '') {
          var dataQC = {
            code: Number(code),
            job_type: this.job_type_qc,
          };
          var dataStag = {
            code: Number(code),
            job_type: this.job_type_stag,
          };
        } else {
          var dataQC = {
            code: Number(code),
            job_type: this.job_type_qc,
            id_parent: this.idParentTimeChecking,
          };
          var dataStag = {
            code: Number(code),
            job_type: this.job_type_stag,
            id_parent: this.idParentTimeChecking,
          };
        }
        const resQC = await employeeTimeChecking(dataQC);
        const resStag = await employeeTimeChecking(dataStag);
        if (!resQC.data.data || !resStag.data.data) {
          this.codeEmployee = '';
          this.employeeError = 'Scan employee code error, please scan again.';
          this.focusByElClassScanEmployee();
          return;
        }
        this.itemsArray.splice(this.itemsArray[index], 1);
        if (this.idParentTimeChecking === '') {
          this.idParentTimeChecking = resQC.data.id_time_checking;
          this.employeeIdParentTimeChecking = resQC.data.data.id;
        }
        const dataEmployee = {
          data: resQC.data.data,
          time_checking_id_QC: resQC.data.id_time_checking,
          time_checking_id_Stag: resStag.data.id_time_checking,
        };
        this.employee.push(dataEmployee);
        this.arrCodeEmployee.push(code);
        await this.fetchLivePerformance();
        this.startPerformanceInterval();

        await socket.socket.connect();
        this.makeDataForMulStaging(this.employee);
        this.itemsArray.map((item) => {
          item.codeEmployeeValue = '';
        });
        this.focusByElClass();
        this.isAllowScan = true;
        this.isLoading = false;
        this.$refs.skuQualityControl.focus();
      } catch (e) {
        const data = e.response.data;
        let message = this.$t('Not found');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.itemsArray.map((item) => {
          item.codeEmployeeValue = '';
        });
        this.isLoading = false;
        this.notification(message, 'error');
      }
    },
    makeDataForMulStaging(allEmployee) {
      this.totalNameEmployee = '';
      this.dataTimeCheckingIdQC = [];
      this.dataTimeCheckingIdStagging = [];
      allEmployee.forEach((item) => {
        this.totalNameEmployee = this.totalNameEmployee + item.data.name + ', ';
        this.dataTimeCheckingIdQC.push(item.time_checking_id_QC);
        this.dataTimeCheckingIdStagging.push(item.time_checking_id_Stag);
      });
    },

    debouncehandlePassedQC: debounce(async function () {
      this.handlePassedQC();
  }, 500),

    async handlePassedQC() {
      if (!this.employeeIdParentTimeChecking) {
        const mess = 'Employee code field cannot be left blank.';
        this.notification(mess, 'error');
        this.focusByElClassScanEmployee();
        return;
      }
      this.employeeError = '';
      if (this.label === '') {
        this.focusByElClass();
        return;
      }
      try {
        // let message = `Scan QR code ${this.label} success`;
        const res = await pass({
          qc_id: this.qualityControlId,
          label: this.label,
          employee_id: this.employeeIdParentTimeChecking,
          // id_time_checking: this.dataTimeCheckingIdQC,
          id_time_checking: [...this.dataTimeCheckingIdQC, ...this.dataTimeCheckingIdStagging],
        });
        if (res.data) {
          this.totalItems = res.data.totalItem;
          this.totalItemsScan = res.data.totalItemScan;
          if(res.data.isCount === true) {
            this.total_item++;
          }
          this.notification("Passed!", 'success');
        }
        // this.notification(message, 'success');
        this.focusByElClass();
        // this.isLoading = false;
      } catch (e) {
        this.onClear();
        const data = e.response.data;
        let message = this.$t('Not found QR code');
        if (!isEmpty(data)) {
          if (data.errors) {
            const keyFirstData = Object.keys(data.errors)[0];
            const firstData = data.errors[keyFirstData];
            message = firstData[0];
          } else if (data.message) {
            message = data.message[0] ?? data.message ?? '';
          }
        }
        this.focusByElClass();
        this.isLoading = false;
        this.notification(message, 'error');
      }
    },
    async fetchLivePerformance() {
      try {
        const resPerformance = await livePerformance({
          employee_id: this.employeeIdParentTimeChecking,
          job_type: this.job_type_qc,
          department_id: this.department,
          time_tracking_id: this.idParentTimeChecking,
          warehouse_id: this.warehouse_id,
        });
        if (resPerformance.data) {
          const performance = resPerformance.data;
          this.avg_item_by_hour = performance.average_user;
          this.ave_item_current_year = performance.average_current_year;
          this.avg_item_last_year = performance.average_last_year;
          this.performanceHistory = performance;
          if (performance.age) {
            this.age = performance.age
          }
          this.is15Minutes = true;
          this.countPerformanceReport++;
        }
      } catch (e) {
        console.error('Failed to fetch live performance:', e);
      }
    },
    startPerformanceInterval() {
      this.clearPerformanceInterval();
      this.performanceInterval = setInterval(async () => {
        if (this.employee) {
          await this.fetchLivePerformance();
        }
      }, 15 * 60 * 1000); // 15 minutes
    },
    clearPerformanceInterval() {
      if (this.performanceInterval) {
        clearInterval(this.performanceInterval);
        this.performanceInterval = null;
      }
    },
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed;
    },
    convertOutPut(data) {
      if (data > 1) {
          return data + ' items'
      } else {
          return data + ' item'
      }
    },
    convertItem(data) {
      if(data > 1) {
        return data +' items/hour'
      } else {
        return data +' item/hour'
      }
    },  
    handleKeyDown(event) {
      if (event.code === 'Space' && this.label) {
        // Clear any existing timeout
        if (this.keydownTimeout) {
          clearTimeout(this.keydownTimeout);
        }
        // Set a new timeout
        this.keydownTimeout = setTimeout(() => {
          this.handlePassedQC();
        }, this.delay);
      }
    },
    formattedDate(date) {
      return moment.utc(date).tz('America/Los_Angeles').format('YYYY-MM-DD HH:mm:ss');
    }
  },
};
