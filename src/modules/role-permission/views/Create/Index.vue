<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
      v-model="openDialogAddPermission"
      :title="$t('Create permission')"
      custom-class="el-dialog-custom el-dialog-user-role"
      @close="closeModal"
      :close-on-click-modal="false"
    >
      <template #default>
        <div class="add-userRole mb-3">
          <el-form
            status-icon
            ref="addPermissionForm"
            :model="permission"
            :rules="permissionRules"
            @submit.prevent="onSubmit('addPermissionForm')"
            label-width="130px"
            :label-position="'top'"
          >
            <el-form-item :label="$t('Name')" prop="name">
              <el-input v-model="permission.name"></el-input>
            </el-form-item>
            <div class="top-head">
              <div class="top-head-left"></div>
              <div class="top-head-right">
                  <el-button
                      type="primary"
                      @click="onSubmit('addPermissionForm')"
                      :disabled="isLoading"
                      :loading="isLoading"
                  >{{ $t('Create permission') }}</el-button
                  >
              </div>
            </div>

          </el-form>
        </div>
          <div class="list-item">
            <div class="mb-1">
              <span class="bold text-lg">{{ $t('List Permission') }}</span>
            </div>
            <el-table
                border
                :data="items"
                style="width: 100%"
                size="small"
            >
              <el-table-column :label="$t('Id')" width="150">
                <template #default="scope">
                  {{ scope.row.id }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('Name')" min-width="200">
                <template #default="scope">
                  {{ scope.row.name }}
                </template>
              </el-table-column>
              <el-table-column
                  prop="action"
                  :label="$t('Action')"
                  width="80"
                  fixed="right"
              >
                <template #default="scope">
                  <el-popconfirm
                      :title="$t('Are you sure to delete ?')"
                      @confirm="destroyPermission(scope.row)"
                  >
                    <template #reference>
                      <el-link :underline="false" type="danger"
                      ><icon :data="iconDelete"
                      /></el-link>
                    </template>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
          </div>
      </template>
      <template #footer>

      </template>
    </el-dialog>
  </div>
</template>
