import EventBus from "@/utilities/eventBus.js";
import { isEmpty } from "ramda";
import { getPermission, creatPermission, destroyPermission } from "@/api/rolePermission.js";

export default {
  name: "Permission",
  components: {},
  data() {
    return {
      isLoading: false,
      openDialogAddPermission: false,
      items: [],
      permission: {},
      permissionRules: {
        name: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: "blur",
          },
        ],
      },
    };
  },
  mounted() {
    this.fetchRolePermission()
  },
  created() {
    EventBus.$on("showCreatePermission", () => {
      this.$nextTick(() => {
        this.openDialogAddPermission = true;
      });
    });
  },
  methods: {
    async fetchRolePermission(){
      this.loading  =  true;
      const res = await getPermission();
      this.items = res.data;
      this.loading  =  false;
    },
    async onSubmit(formName) {
      const isValid = await this.$refs[formName].validate();
      if (!isValid) {
        return;
      }
      this.isLoading = true;
      try {
        await creatPermission(this.permission);
        this.isLoading = false;
        this.notification(this.$t("Permission add successfully."));
        await this.fetchRolePermission()
        this.permission =  {}
        this.$emit("refresh");
      } catch (e) {
        const data = e.response.data;
        this.isLoading = false;
        let message = this.$t("Permission add error.");
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.notification(message, "error");
      }
    },
    async destroyPermission(item){
      await destroyPermission(item.id);
      await this.fetchRolePermission()
      this.notification(this.$t("Delete brand successfully."));
    },
    closeModal(){
      this.openDialogAddPermission = false;
      this.$refs["addPermissionForm"].resetFields();
    }
  },
};
