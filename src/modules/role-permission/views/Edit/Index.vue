<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>

<template>
  <div>
    <el-dialog
        v-model="openDialogEditRolePermission"
        :title="$t('Edit role permission')"
        custom-class="el-dialog-custom el-dialog-user-role"
        :close-on-click-modal="false"
    >
      <template #default>
        <div class="add-userRole">
          <el-form
              status-icon
              ref="editUserRoleForm"
              label-width="130px"
              :label-position="'top'"
          >
            <el-form-item :label="$t('Name')" prop="name">
              <el-input v-model="nameRole" disabled></el-input>
            </el-form-item>
            <h3 class="text-md mb-2">{{ $t('Pick Permission') }}</h3>
<!--            <div class="mb-1">-->
<!--              <div class="mb-1">-->
<!--                <el-alert-->
<!--                    title="Set is default when login, select warehouse redirect to menu."-->
<!--                    type="info"-->
<!--                    :closable="false"-->
<!--                />-->
<!--              </div>-->
<!--              <el-alert-->
<!--                  title="Set is default module when click menu top bar (parent) redirect to menu."-->
<!--                  type="info"-->
<!--                  :closable="false"-->
<!--              />-->
<!--            </div>-->
            <div
                class="list-item"
                v-for="(item, index) in allPermission"
                :key="index"
            >
              <div class="item">
                <div class="item-check">
                  <el-checkbox
                      true-label="1"
                      false-label="0"
                      v-model="item.is_checked"
                      :label="item.name"
                  />
                </div>
              </div>
            </div>

          </el-form>
        </div>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <el-button
              type="primary"
              @click="onSubmit('editRolePermission')"
              :disabled="isLoading"
              :loading="isLoading"
          >{{ $t('Update') }}</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>

</template>
