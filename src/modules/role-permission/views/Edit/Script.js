import EventBus from "@/utilities/eventBus.js";
import { getPermission, postRolePermission } from "@/api/rolePermission.js";
import {isEmpty} from "ramda";


export default {
    name: "RolePermissionEdit",
    components: {},
    data() {
        return {
            isLoading: false,
            openDialogEditRolePermission: false,
            allPermission: [],
            rolePermission: [],
            role: '',
            nameRole: ''
        }
    },
    mounted() {
        this.getAllPermission()
    },
    created() {
        EventBus.$on("showEditRolePermission", async (data) => {
            this.rolePermission = data.permissions;
            this.role = data.id;
            this.nameRole = data.name,
            await this.getAllPermission()
            this.renderData();
            this.$nextTick(() => {
                this.openDialogEditRolePermission = true;
            });
        });
    },
    methods: {
       async onSubmit(){
            this.isLoading = true;
            const data = [];
            this.allPermission.forEach((item, index) => {
                if(item.is_checked == "1") {
                    data.push(item.id)
                }
            })

           try {
               await postRolePermission({
                   "roles_id" : this.role,
                   "permission_id": data
               });
               this.isLoading = false;
               this.notification(this.$t("Role permission update successfully."));
               this.openDialogEditRolePermission = false;
               this.$emit("refresh");
           } catch (e) {
               const data = e.response.data;
               this.isLoading = false;
               let message = this.$t("Role permission update error.");
               if (!isEmpty(data)) {
                   const keyFirstData = Object.keys(data)[0];
                   const firstData = data[keyFirstData];
                   message = firstData[0];
               }
               this.notification(message, "error");
           }
        },
        async getAllPermission(){
            const res = await getPermission();
            this.allPermission = res.data;
        },
        renderData(){
            this.allPermission.forEach((item, index) => {
                item.is_checked = "0";
                const isElementPresent = this.rolePermission.some((o) => o.id === item.id);
                if (isElementPresent) {                  // As some return Boolean value
                    item.is_checked = "1";
                }
            })
        },
        checkItem(item){
        },
    }
}