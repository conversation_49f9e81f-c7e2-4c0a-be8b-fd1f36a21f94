<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>

<template>
  <div>
    <div class="top-head">
      <div class="top-head-left"><h1>{{ $t('Role Permission') }}</h1></div>
      <div class="top-head-right">
        <el-button type="primary" @click="createPermission">
          <span class="icon-margin-right"><icon :data="iconAdd" /></span>{{ $t('Create') }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <el-table
          border
          stripe
          size="small"
          class="mt-3"
          :data="items"
          style="width: 100%"
          :max-height="maxHeight"
          v-loading="isLoading"
          element-loading-text="Loading..."
      >
        <el-table-column type="expand">
          <template #default="props">
            <el-table
                :data="props.row.permissions"
                stripe
                border
                style="width: 50%;margin-left: 50px"
                size="small"
            >
              <el-table-column :label="$t('Permission')" >
              <template #default="scope">
                {{ scope.row.name }}
              </template>
            </el-table-column>
            </el-table>
          </template>
        </el-table-column>
        <el-table-column prop="name" :label="$t('Name')" min-width="200">
          <template #default="scope">
            <el-link
                :underline="false"
                type="primary"
                @click="editRolePermission(scope.row)"
            >{{ scope.row.name }}</el-link
            >
          </template>
        </el-table-column>
        <el-table-column prop="name" :label="$t('Created at')" min-width="150">
          <template #default="scope">
            {{ utcToLocalTime(scope.row.created_at).format('lll') }}
          </template>
        </el-table-column>
        <el-table-column prop="name" :label="$t('Updated at')" min-width="150">
          <template #default="scope">
            {{ utcToLocalTime(scope.row.updated_at).format('lll') }}
          </template>
        </el-table-column>
        <el-table-column prop="action" :label="$t('Action')" width="100">
          <template #default="scope">
            <el-link
                class="el-link-edit"
                :underline="false"
                type="primary"
                @click="editRolePermission(scope.row)"
            ><icon :data="iconEdit"
            /></el-link>
            <el-popconfirm
                :title="'Are you sure to delete ' + scope.row.name + '?'"
                @confirm="deleteUserRole(scope.row)"
            >
              <template #reference>
                <el-link :underline="false" type="danger"
                ><icon :data="iconDelete"
                /></el-link>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <EditRolePermission @refresh="fetchRolePermission"/>
    <CreatPermission @refresh="fetchRolePermission"/>




  </div>
</template>
