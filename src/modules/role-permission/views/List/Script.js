import { getRolePermission } from "@/api/rolePermission.js";
import EventBus from "@/utilities/eventBus.js";
import EditRolePermission from "@/modules/role-permission/views/Edit/Index.vue"
import { destroyRole } from "@/api/user.js";
import CreatPermission from "@/modules/role-permission/views/Create/Index.vue"
export default {
    name: "RolePermission",
    components: {
        EditRolePermission,
        CreatPermission
    },
    data() {
        return {
            isLoading: false,
            items: [],
        };
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 140);
        },
    },
    mounted() {
        this.fetchRolePermission()
    },
    beforeUnmount() {
        EventBus.$off("showEditRolePermission");
        EventBus.$off("showCreatePermission");
    },
    methods: {
        async fetchRolePermission(){
            this.isLoading  =  true;
            const res = await getRolePermission();
            this.items = res.data;
            this.isLoading  =  false;
        },
        editRolePermission(item){
            EventBus.$emit("showEditRolePermission", item);
        },
        async deleteUserRole(item) {
            await destroyRole(item.id);
            this.notification(this.$t("Delete user role successfully."));
            this.fetchRolePermission();
        },
        createPermission(){
            EventBus.$emit("showCreatePermission");
        }
    }
}