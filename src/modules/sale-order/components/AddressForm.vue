<template>
  <div>
    <el-form
      status-icon
      label-width="80px"
      :label-position="'left'"
      ref="addressForm"
      :model="address"
      :rules="addressRules"
      :show-message="false"
    >
      <el-form-item :class="{ 'mb-0': !isValidateAddress }">
        <div class="text-right w-full">
          <el-link
            class="el-link-edit"
            :underline="false"
            type="primary"
            @click="showValidateAddress"
            >{{ $t('Paste US Address') }}</el-link
          >
        </div>
        <el-input
          v-if="isValidateAddress"
          v-model="validateAddress"
          :placeholder="$t('Paste or type address here')"
          type="textarea"
          @keyup.enter="parseAddress"
          ref="validateAddress"
        ></el-input>
      </el-form-item>
      <el-form-item :label="$t('Name')" prop="name">
        <el-input v-model="address.name" :placeholder="$t('Name')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('Company')" prop="company">
        <el-input
          v-model="address.company"
          :placeholder="$t('Company')"
        ></el-input>
      </el-form-item>

      <el-form-item :label="$t('Country')" prop="country_id">
        <el-select
          :placeholder="$t('Select country')"
          class="el-select-country w-full"
          v-model="address.country_id"
          filterable
          @change="changeCountry"
        >
          <el-option
            v-for="item in countries"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('Address')" prop="street1">
        <el-input
          v-model="address.street1"
          :placeholder="$t('Address1')"
        ></el-input>
        <el-input
          v-model="address.street2"
          :placeholder="$t('Address2')"
          class="mt-3"
        ></el-input>
      </el-form-item>

      <el-form-item :label="$t('City')" prop="city">
        <el-input v-model="address.city" :placeholder="$t('City')"></el-input>
      </el-form-item>

      <div class="flex justify-between">
        <el-form-item
          :label="$t('State')"
          prop="state_id"
          v-if="address.country_id == '233'"
        >
          <el-select
            :placeholder="$t('Select state')"
            class="el-select-state mr-1"
            v-model="address.state_id"
            @change="changeState"
            filterable
          >
            <el-option
              v-for="item in states"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('State')" v-else prop="state">
          <el-input
            v-model="address.state"
            :placeholder="$t('State')"
          ></el-input>
        </el-form-item>
        <el-form-item
          :label="$t('Zip code')"
          prop="zip"
          placeholder="Zip code"
          class="ml-1"
        >
          <el-input
            v-model="address.zip"
            :placeholder="$t('Zip code')"
          ></el-input>
        </el-form-item>
      </div>
      <el-form-item :label="$t('Phone')" prop="phone">
        <el-input v-model="address.phone" :placeholder="$t('Phone')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('Email')" prop="email">
        <el-input v-model="address.email" :placeholder="$t('Email')"></el-input>
      </el-form-item>
      <div class="flex justify-end">
        <div>
          <el-button v-if="showCancel" @click="onCancel">{{
            $t('Cancel')
          }}</el-button>
          <el-button
            type="primary"
            @click="onSubmit('addressForm')"
            :disabled="isLoading || !hasChangeAddress"
            :loading="isLoading"
            >{{ $t('Save') }}</el-button
          >
        </div>
      </div>
    </el-form>
  </div>
</template>
<script>
import { countries, fetchStateByCountry } from '@/api/default.js';
import { equals, isEmpty, mapObjIndexed, pick, pickBy, trim } from 'ramda';
import { updateAddressByAddressId } from '@/api/saleOrder';
import EventBus from '@/utilities/eventBus.js';
import * as parser from 'parse-address';
import { mapGetters } from 'vuex';

export default {
  name: 'AddressForm',
  components: {},
  mixins: [],
  props: {
    address: {
      type: Object,
    },
    addressCached: {
      type: Object,
    },
    pCountries: {
      type: Array,
      default: [],
    },
    showCancel: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      isLoading: false,
      isLoadingVefifyAddress: false,
      validateAddress: '',
      isValidateAddress: false,
      countries: this.pCountries,
      states: [],
      currentState: '',
      currentCountry: '',
      parseAddressString: '',
      addressRules: {
        name: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: 'change',
          },
        ],
        country_id: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: 'change',
          },
        ],
        street1: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: 'change',
          },
        ],
        city: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: 'change',
          },
        ],
        // state: [
        //   {
        //     required: true,
        //     message: this.$t('This field cannot be left blank.'),
        //     trigger: 'change',
        //   },
        // ],
        // state_id: [
        //   {
        //     required: true,
        //     message: this.$t('This field cannot be left blank.'),
        //     trigger: 'change',
        //   },
        // ],
        zip: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: 'change',
          },
        ],
        email: [
          {
            type: 'email',
            message: this.$t('Invalid email.'),
            trigger: 'change',
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters(["getUsStates"]),
    hasChangeAddress() {
      const arrKey = [
        'address_id',
        'name',
        'company',
        'country',
        'street1',
        'street2',
        'residential',
        'phone',
        'email',
        'state',
        'city',
        'zip',
      ];
      const filterEmptyField = (val) => !isEmpty(val) && val !== null;
      const trimString = (item) => {
        if (typeof item === 'string') {
          return trim(item);
        }
        return item;
      };
      const newAddress = pickBy(
        filterEmptyField,
        mapObjIndexed(trimString, pick(arrKey, this.address))
      );
      const newAddressCached = pickBy(
        filterEmptyField,
        mapObjIndexed(trimString, pick(arrKey, this.addressCached))
      );
      return !equals(newAddress, newAddressCached);
    },
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      if (!this.countries || !this.countries.length) {
        await this.fetchCountries();
      }
      this.mapDataInfoAddress();
    },
    async parseAddress() {
      const defaultCounty = 'US';
      const country = this.countries.find(
        (item) => item.iso2 === defaultCounty
      );
      const parsed = parser.parseLocation(this.validateAddress);
      const {
        number = "",
        prefix = "",
        street: _street = "",
        type = "",
        suffix = "",
        city: _city = "",
        state: _state = "",
        zip: _zip = "",
        sec_unit_type = "",
        sec_unit_num = ""
      } = parsed;
      const preStreet = `${number} ${prefix} ${_street} ${type} ${suffix}`;
      const secUnit = `${sec_unit_type} ${sec_unit_num}`;
      const city = _city;
      const state = _state;
      const street = (preStreet.trim() && secUnit.trim()) ? `${preStreet.trim()}, ${secUnit.trim()}` : (preStreet.trim() ? preStreet.trim() : secUnit.trim());
      const zip = _zip;
      if (country && country.id && state) {
        this.address.country_id = country && country.id;
        await this.fetchStateByCountry(this.address.country_id);
        const stateItem = this.states.find((item) => item.iso2 === state);
        this.address.state_id = stateItem ? stateItem.id : this.address.state;
      }
      this.address.street1 = street;
      this.address.zip = zip;
      this.address.city = city;

      this.isValidateAddress = false;
      this.validateAddress = '';
    },
    onCancel() {
      this.$emit('onCancel', false);
      EventBus.$emit('updateOrder');
    },
    async onSubmit(formName) {
      const isValid = await this.$refs[formName].validate();
      if (!isValid) {
        return;
      }
      this.isLoading = true;
      try {
        const country = this.countries.find(
          (item) => +item.id === +this.address.country_id
        );
        const state = this.states.find(
          (item) => +item.id === +this.address.state_id
        );
        const address = {
          ...this.address,
          country: country && country.iso2,
          state: state ? state.iso2 : this.address.state,
        };
        if (country.iso2 != 'US') {
          address.state = this.address.state;
          const state = this.states.find(
            (item) =>
              item.name == this.address.state || item.iso2 == this.address.state
          );
          address.state_id = state ? state.id : null;
        }
        const res = await updateAddressByAddressId(address);
        this.notification(this.$t('Update address successfully.'));
        this.isLoading = false;
        this.$emit('onUpdate');
        this.onCancel();
      } catch (e) {
        this.isLoading = false;
        const data = e.response.data?.errors;
        let message = this.$t('Update address error.');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.notification(message, 'error');
      }
    },
    async mapDataInfoAddress() {
      const country = this.countries.find(
        (item) => item.iso2 === this.address.country
      );
      if (country && country.id) {
        this.address.country_id = country && country.id;
        await this.fetchStateByCountry(this.address.country_id);
        const state = this.states.find(
          (item) => item.iso2 === this.address.state
        );
        this.address.state_id = state && state.id;
      }
    },
    showValidateAddress() {
      this.isValidateAddress = !this.isValidateAddress;
      this.validateAddress = '';
      this.$nextTick(() => {
        if (this?.$refs?.validateAddress) {
          this.$refs.validateAddress.focus();
        }
      });
    },
    async fetchCountries() {
      const res = await countries();
      this.countries = res.data || [];
    },
    async fetchStateByCountry(id) {
      if(this.getUsStates?.length) {
        this.states = this.getUsStates;
        return;
      }
      const res = await fetchStateByCountry(id);
      this.states = res.data || [];
    },
    async changeCountry() {
      this.address.state_id = '';
      this.address.state = '';
      await this.fetchStateByCountry(this.address.country_id);
    },
    changeState() {
      this.address.state = this.states.find(
        (item) => item.id == this.address.state_id
      ).iso2;
    },
  },
};
</script>
