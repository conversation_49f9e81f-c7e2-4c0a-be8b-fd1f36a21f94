import EventBus from '@/utilities/eventBus.js';
import saleOrderMixin from '@/mixins/saleOrder.js';
import {clone, isEmpty} from 'ramda';
import ViewDesignItem from '@/modules/sale-order/components/ViewDesignItem.vue';
import OrderNote from '@/modules/sale-order/components/Note.vue';
import OrderShipment from '@/modules/sale-order/components/Shipment.vue';
import OrderShippingMethod from '@/modules/sale-order/components/ShippingMethod.vue';
import OrderStatus from '@/modules/sale-order/components/Status.vue';
import OrderItems from '@/modules/sale-order/components/OrderItems.vue';
import OrderAddress from '@/modules/sale-order/components/Address.vue';
import OrderReturnAddress from '@/modules/sale-order/components/ReturnAddress.vue';
import ProductionStatus from '@/modules/sale-order/components/ProductionStatus.vue';
import Timeline from '@/modules/sale-order/components/Timeline.vue';
import Reprint from '@/modules/sale-order/components/Reprint.vue';
import {details, submitEmail} from '@/api/saleOrder.js';
import ManualTracking from '@/modules/sale-order/components/ManualTracking.vue';
import IOSSNumber from '@/modules/sale-order/components/IOSSNumber.vue';
import TaxIndentifier from '@/modules/sale-order/components/TaxIndentifier.vue';

import LabelChip from '@/components/LabelChip.vue';
import { NOT_APPLICABLE } from '@/utilities/constants';
import SignTag from '@/components/SignTag.vue';

export default {
  name: 'SaleOrderDetails',
  components: {
    ViewDesignItem,
    OrderNote,
    OrderShipment,
    OrderShippingMethod,
    OrderStatus,
    OrderItems,
    OrderAddress,
    ProductionStatus,
    Timeline,
    Reprint,
    OrderReturnAddress,
    ManualTracking,
    IOSSNumber,
    LabelChip,
    SignTag,
    TaxIndentifier
  },

  mixins: [saleOrderMixin],

  data() {
    return {
      isLoading: false,
      openDialogDetailsSaleOrder: false,
      saleOrder: {},
      option: null,
      saleOrderId: '',
      editing: null,
      internalNote: '',
      openManualTracking: false,
      openAddTaxIdentifier: false,
      shipmentSelected: {},
      isUpdateShipment: false,
      storeNA: NOT_APPLICABLE,
      isReloadListPage: false,
      isOpenModal: false,
      formData: {
        email: null
      },
      errorValidator: {
      }
    };
  },

  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 150);
    },
  },

  beforeUnmount() {
    EventBus.$off('showDetailsSaleOrder');
    EventBus.$off('showDetailsSaleOrderEditing');
    EventBus.$off('updateOrder');
    EventBus.$off('refresh');
  },

  created() {
    EventBus.$on('showDetailsSaleOrder', (data) => {
      this.isReloadListPage = false;
      this.saleOrderId = data.id;

      if (data.editing) {
        this.editing = true;
      }

      this.fetchDetails();
      this.openDialogDetailsSaleOrder = true;
    });

    EventBus.$on('showDetailsSaleOrderEditing', (data) => {
      this.saleOrderId = data.id;

      if (data.editing) {
        this.editing = true;
      }

      this.fetchDetails();
      this.openDialogDetailsSaleOrder = true;
    });

    EventBus.$on('updateOrder', (needReloadListPage) => {
      this.isReloadListPage = !!needReloadListPage;
      this.fetchDetails(false);
    });
  },

  methods: {
    inputEmail() {
      this.isOpenModal = true;
    },
    async submitEmail() {
      try {
        if (!this.formData?.email) {
          this.notification('Email is required', "error");
          return;
        }
        let params = {
          order_id: this.saleOrder.id,
          emails: this.formData.email.replaceAll(' ', '').split(","),
        }
        this.isLoading = true;
        const response = await submitEmail(params);
        this.fetchDetails();
        this.notification(response.data.message, "success");
        this.closeModal();
      } catch (e) {
        this.isLoading = false;
        this.notification(e?.response?.data?.message ?? 'Error', "error");
      }
    },
    closeModal() {
      this.isLoading = false;
      this.isOpenModal = false;
      this.formData.email = this.saleOrder.customer_email.map(item => item.email).join(",");
      this.errorValidator = {};
    },
    updateShipment(shipment) {
      this.openManualTracking = !this.openManualTracking;
      this.shipmentSelected = shipment;
      this.isUpdateShipment = true;
    },

    dialogManualTracking() {
      this.openManualTracking = !this.openManualTracking;
      this.isUpdateShipment = false;
    },

    dialogAddTaxIdentifier() {
      this.openAddTaxIdentifier = !this.openAddTaxIdentifier;
    },

    onClose() {
      EventBus.$emit('refresh');
      this.formData.email = null;

      if (this.isReloadListPage) {
        EventBus.$emit('refreshOrders');
      }
    },

    async fetchDetails(isLoading = true) {
      this.isLoading = isLoading;
      const res = await details(this.saleOrderId);
      const data = clone(res.data);
      if (data?.customer_email?.length > 0) {
        this.formData.email = data?.customer_email.map(item => item.email).join(",")
      }
      if (data?.items?.length) {
        data.items = data.items.map((item) => {
          return {
            ...item,
            options: item.options && JSON.parse(item.options),
          };
        });
      }

      this.saleOrder = data;
      this.internalNote = data.internal_note;
      this.isLoading = false;
    },

    viewMore(data) {
      this.option = data;
      EventBus.$emit('showViewDesignItem', data);
    },
  },
};
