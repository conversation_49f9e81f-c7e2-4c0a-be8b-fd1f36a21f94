<template>
  <div>
    <el-dialog v-model="openDialogAddress" :title="modalTitle"
      custom-class="el-dialog-custom el-dialog-medium el-dialog-custom-new" @close="onCancel"
      :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form status-icon label-width="80px" :label-position="'top'" ref="addressForm" :model="address"
        :rules="addressRules">
        <div class="pb-6 border-b border-[#DBDBDB] mb-6">
          <h4 class="text-[#0A0B0D] text-base font-medium">Quick edit</h4>
          <div class="text-[#5B616E] my-2 text-sm">
            Paste or type US Address, the content will be automatically filled
            in {{ type == 'billing_address_data' ? 'Billing' : 'Ship' }} Address below after enter 
          </div>
          <el-form-item class="mb-0">
            <el-input v-model="validateAddress" :placeholder="$t('US Address here')" type="textarea"
              @keyup.enter="parseAddress" ref="validateAddress" rows="3"></el-input>
          </el-form-item>
        </div>
        <div class="mb-4">
          <h4 class="text-base font-semibold text-[#0A0B0D]">
            {{ modalTitle.replace('Edit', '') }}
          </h4>
        </div>
        <el-form-item :label="$t('Name')" prop="name">
          <el-input size="large" v-model="address.name" :placeholder="$t('Name')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('Company')" prop="company">
          <el-input size="large" v-model="address.company" :placeholder="$t('Company')"></el-input>
        </el-form-item>

        <el-form-item :label="$t('Street 1')" prop="street1">
          <el-input size="large" v-model="address.street1" :placeholder="$t('Street 1')"></el-input>
        </el-form-item>

        <el-form-item :label="$t('Street 2')" prop="street2">
          <el-input size="large" v-model="address.street2" :placeholder="$t('Street 2')"></el-input>
        </el-form-item>

        <div class="grid grid-cols-2 gap-6">
          <el-form-item :label="$t('Country')" prop="country_id">
            <el-select ref="selectCountry" size="large" :placeholder="$t('Select country')"
              class="el-select-country w-full" v-model="address.country_id" filterable @change="changeCountry">
              <el-option v-for="item in countries" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('State')" prop="state_id" v-if="address.country_id == '233'">
            <el-select ref="selectState" size="large" :placeholder="$t('Select state')" class="el-select-state w-full"
              v-model="address.state_id" @change="changeState" filterable>
              <el-option v-for="item in states" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('State')" v-else prop="state">
            <el-input size="large" v-model="address.state" :placeholder="$t('State')"></el-input>
          </el-form-item>
        </div>
        <div class="grid grid-cols-2 gap-6">
          <el-form-item :label="$t('City')" prop="city">
            <el-input size="large" v-model="address.city" :placeholder="$t('City')"></el-input>
          </el-form-item>
          <el-form-item :label="$t('Zip code')" prop="zip" placeholder="Zip code" class="ml-1">
            <el-input size="large" v-model="address.zip" :placeholder="$t('Zip code')"></el-input>
          </el-form-item>
        </div>
        <div class="grid grid-cols-2 gap-6">
          <el-form-item :label="$t('Phone Number')" prop="phone">
            <el-input size="large" v-model="address.phone" :placeholder="$t('Phone Number')"></el-input>
          </el-form-item>
          <el-form-item :label="$t('Email')" prop="email">
            <el-input size="large" v-model="address.email" :placeholder="$t('Email')"></el-input>
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <div>
          <el-button @click="onCancel" class="w-[120px]" size="large">{{
      $t('Cancel')
    }}</el-button>
          <el-button size="large" class="w-[120px]" type="primary" @click="onSubmit('addressForm')"
            :disabled="!hasChangeAddress || isDisabledForm">{{ $t('Save') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { countries, fetchStateByCountry } from '@/api/default.js';
import {
  equals,
  isEmpty,
  mapObjIndexed,
  pick,
  pickBy,
  trim,
  clone,
} from 'ramda';
import EventBus from '@/utilities/eventBus.js';
import * as parser from 'parse-address';
import { mapGetters } from 'vuex';

export default {
  name: 'AddressFormModal',
  data() {
    return {
      modalTitle: 'Ship Address',
      type: 'address',
      address: {},
      addressCached: {},
      openDialogAddress: false,
      validateAddress: '',
      countries: [],
      states: [],
      currentState: '',
      currentCountry: '',
      parseAddressString: '',
      addressRules: {
        name: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: 'change',
          },
        ],
        country_id: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: 'change',
          },
        ],
        street1: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: 'change',
          },
        ],
        city: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: 'change',
          },
        ],
        zip: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: 'change',
          },
        ],
        email: [
          {
            type: 'email',
            message: this.$t('Invalid email.'),
            trigger: 'change',
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters(['getUsStates']),
    isDisabledForm() {
      if (
        !this.address.name ||
        !this.address.street1 ||
        !this.address.city ||
        !this.address.zip ||
        !this.address.country_id
      ) {
        return true;
      }
      return false;
    },
    hasChangeAddress() {
      const arrKey = [
        'address_id',
        'name',
        'company',
        'country',
        'street1',
        'street2',
        'residential',
        'phone',
        'email',
        'state',
        'city',
        'zip',
      ];
      const filterEmptyField = (val) => !isEmpty(val) && val !== null;
      const trimString = (item) => {
        if (typeof item === 'string') {
          return trim(item);
        }
        return item;
      };
      const newAddress = pickBy(
        filterEmptyField,
        mapObjIndexed(trimString, pick(arrKey, this.address))
      );
      const newAddressCached = pickBy(
        filterEmptyField,
        mapObjIndexed(trimString, pick(arrKey, this.addressCached))
      );
      return !equals(newAddress, newAddressCached);
    },
  },
  beforeUnmount() {
    EventBus.$off('showAddressFormModal');
  },
  created() {
    EventBus.$on(
      'showAddressFormModal',
      async ({ address, type, modalTitle, countries }) => {
        if (!address?.country_id && !address?.country) {
          address = {
            ...address,
            country: 'US',
          };
        }
        this.modalTitle = modalTitle || 'Ship Address';
        this.type = type;
        this.countries = countries || [];
        this.address = clone(address);
        this.addressCached = clone(address);
        await this.fetchData();
        this.openDialogAddress = true;
      }
    );
  },
  methods: {
    async fetchData() {
      if (!this.countries || !this.countries.length) {
        await this.fetchCountries();
      }
      this.mapDataInfoAddress();
    },
    async parseAddress() {
      const defaultCounty = 'US';
      const country = this.countries.find(
        (item) => item.iso2 === defaultCounty
      );
      const parsed = parser.parseLocation(this.validateAddress);
      const {
        number = "",
        prefix = "",
        street: _street = "",
        type = "",
        suffix = "",
        city: _city = "",
        state: _state = "",
        zip: _zip = "",
        sec_unit_type = "",
        sec_unit_num = ""
      } = parsed;
      const preStreet = `${number} ${prefix} ${_street} ${type} ${suffix}`;
      const secUnit = `${sec_unit_type} ${sec_unit_num}`;
      const city = _city;
      const state = _state;
      const street = (preStreet.trim() && secUnit.trim()) ? `${preStreet.trim()}, ${secUnit.trim()}` : (preStreet.trim() ? preStreet.trim() : secUnit.trim());
      const zip = _zip;
      if (country && country.id && state) {
        this.address.country_id = country && country.id;
        await this.fetchStateByCountry(this.address.country_id);
        const stateItem = this.states.find((item) => item.iso2 === state);
        this.address = {
          ...this.address,
          state_id: stateItem ? stateItem.id : this.address.state,
        };
      }
      this.address = {
        ...this.address,
        street1: street,
        zip,
        city,
      };

      this.validateAddress = '';
    },
    onCancel() {
      this.openDialogAddress = false;
      this.validateAddress = '';
      this.$refs['addressForm'].resetFields();
    },
    async onSubmit(formName) {
      const isValid = await this.$refs[formName].validate();
      if (!isValid) {
        return;
      }
      const country = this.countries.find(
        (item) => +item.id === +this.address.country_id
      );
      const state = this.states.find(
        (item) => +item.id === +this.address.state_id
      );
      const address = {
        ...this.address,
        country: country.iso2,
        state: state ? state.iso2 : this.address.state,
      };
      this.$emit('onSubmit', {
        address,
        type: this.type,
      });
      this.openDialogAddress = false;
    },
    async mapDataInfoAddress() {
      const country = this.countries.find(
        (item) => item.iso2 === this.address.country
      );
      if (country && country.id) {
        this.address.country_id = country && country.id;
        await this.fetchStateByCountry(this.address.country_id);
        const state = this.states.find(
          (item) => item.iso2 === this.address.state
        );
        this.address.state_id = state && state.id;
      }
    },
    async fetchCountries() {
      const res = await countries();
      this.countries = res.data || [];
    },
    async fetchStateByCountry(id) {
      if (this.getUsStates?.length) {
        this.states = this.getUsStates;
        return;
      }
      const res = await fetchStateByCountry(id);
      this.states = res.data || [];
    },
    async changeCountry(e) {
      setTimeout(() => {
        this.$refs.selectCountry?.blur();
      }, 0);
      this.address.state_id = '';
      this.address.state = '';
      await this.fetchStateByCountry(this.address.country_id);
    },
    changeState() {
      this.address.state = this.states.find(
        (item) => item.id == this.address.state_id
      ).iso2;
      setTimeout(() => {
        this.$refs.selectState?.blur();
      }, 0);
    },
  },
};
</script>
<style scoped>
.el-form-item .el-form-item {
  margin-bottom: 10px !important;
}
</style>