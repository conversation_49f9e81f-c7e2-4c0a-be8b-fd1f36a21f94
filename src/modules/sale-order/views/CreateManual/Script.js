import { mapGetters } from 'vuex';
import EventBus from '@/utilities/eventBus.js';
import { storeDetail } from '@/api/store.js';
import { countries } from '@/api/default.js';
import { addManual, details } from '@/api/saleOrder';
import { clone, isEmpty } from 'ramda';
import Card from '@/components/Card.vue';
import AddressInfo from '@/modules/sale-order/components/create-manual/AddressInfo.vue';
import AddressFormModal from '@/modules/sale-order/components/create-manual/AddressFormModal.vue';
import LineItemModal from '@/modules/sale-order/components/create-manual/LineItemModal.vue';
import ListLineItem from '@/modules/sale-order/components/create-manual/ListLineItem.vue';
import { NOT_APPLICABLE } from '@/utilities/constants';
import { getByCode } from "@/api/employee";
import { getList } from "@/api/productPrintSide";
import moment from 'moment';
import { STORE_CODE_SAMPLE_ORDER } from '@/utilities/constants';

export default {
  name: 'CreateManualSaleOrder',
  components: {
    Card,
    AddressInfo,
    AddressFormModal,
    LineItemModal,
    ListLineItem,
  },
  mixins: [],
  data() {
    return {
      productPrintSides: [],
      employeeCode: '',
      employee: {},
      employeeError: '',
      isReprint: false,
      title: 'Create Order Manual',
      order: {},
      openDialogCreateManual: false,
      isLoading: false,
      saleOrder: this.setDefaultData(),
      shippingMethods: [
        {
          name: 'Standard (default)',
          value: 'standard',
        },
        {
          name: 'Express',
          value: 'express',
        },
        {
          name: 'Priority',
          value: 'priority',
        },
        {
          name: 'Economy',
          value: 'economy',
        },
        {
          name: 'Letter First Class',
          value: 'letter_first_class',
        }
      ],
      currentReturnAddress: 'current_address_in_setting',
      storeReturnAddress: {},
      countries: [],
      orderTypeList: [
        {
          value: 1,
          label: 'XQC',
        },
        {
          value: 2,
          label: 'Pretreated Shirt Order',
        },
        {
          value: 3,
          label: 'Blank Shirt Order',
        },
        {
          value: 6,
          label: 'Tiktok Order',
        },
        {
          value: 5,
          label: 'Label Order',
        },

      ],
      reprintReason: [
        {
          value: 'Faded/coloring issue',
          label: 'Faded/coloring issue',
        },
        {
          value: 'Lost in transit',
          label: 'Lost in transit',
        },
        {
          value: 'Missing print',
          label: 'Missing print',
        },
        {
          value: 'Missing order for multi item order',
          label: 'Missing order for multi item order',
        },
        {
          value: 'Return to sender',
          label: 'Return to sender',
        },
        {
          value: 'Wrong print',
          label: 'Wrong print',
        },
        {
          value: 'Wrong garment',
          label: 'Wrong garment',
        },
        {
          value: 'Damage items',
          label: 'Damage items',
        },
      ],
      storeNA: NOT_APPLICABLE,
      isStoreCreateOrderSample: false,
      hologram_sticker : false,
      sticker_and_bag : false,
      mug_packaging : false,
    };
  },
  computed: {
    ...mapGetters(['getStores']),
    maxHeight() {
      return parseInt(window.innerHeight - 240);
    },
    isDisabledForm() {
      if (!this.saleOrder.store_id || !this.saleOrder.external_number) {
        return true;
      }
      return false;
    },
    isPlasticBagChecked() {
      return this.saleOrder.plastic_bag !== false;
    },
    isHologramStickerChecked() {
      return this.hologram_sticker !== false;
    } ,
    isStickerAndBagChecked() {
      return this.sticker_and_bag !== false;
    },
    isMugPackaging() {
      return this.mug_packaging !== false;
    },
  },
  beforeUnmount() {
    EventBus.$off('createManual');
    EventBus.$off('createReprintOrder');
  },
  created() {
    EventBus.$on('createManual', () => {
      this.isReprint = false;
      this.title = 'Create Order Manual';
      this.openDialogCreateManual = true;
      this.isStoreCreateOrderSample = false;
    });

    EventBus.$on('createReprintOrder', (id) => {
      this.isReprint = true;
      this.title = 'Create Reprint Order';
      this.fetchDetails(id);
      this.fetchProductPrintSide();
      this.openDialogCreateManual = true;
      this.currentReturnAddress = 'current_address'
    });
  },
  mounted() {
    this.fetchStore();
    this.fetchCountries();
  },
  methods: {
    disableReprint() {
      return Object.keys(this.employee).length === 0 || !this.saleOrder.reason;
    },
    async fetchDetails(id) {
      this.isLoading = true;
      const res = await details(id);
      const data = clone(res.data);
      if (data?.items?.length) {
        data.items = data.items.map((item) => {
          return {
            ...item,
            options: item.options && JSON.parse(item.options),
          };
        });
      }
      let countReprint = res.data.order_reprints.length
      let external_number = res.data.external_number;
      if (countReprint == 0) {
        external_number = 'reprint-' + external_number;
      } else {
        external_number = 'reprint' + (countReprint + 1) + '-' + external_number;
      }
      this.saleOrder = {
        store_id: res.data.store_id,
        parent_id: id,
        external_number: external_number,
        order_id: res.data.external_number,
        sample_order: res.data.is_xqc,
        shipping_method: res.data.shipping_method,
        return_address: res.data.address_sale_order.filter((item) => item.type_address === 'return_address')[0],
        address: res.data.address_sale_order.filter((item) => item.type_address === 'to_address')[0],
        tax_id: res.data.ioss_number,
        tax_id_type: res.data.tax_id_type,
        orderTypeFormat: this.generateOrderType(data),
        line_items: this.generateLineItems(data.items),
        plastic_bag: !!res.data.plastic_bag,
      }
      if(data.additional_services?.length){
        data.additional_services.forEach(service => {
          this[service] = true;
        });
      }
      this.isLoading = false;
    },
    generateLineItems(items) {
      let lineItems = [];
      if (!items) return lineItems;
      items.forEach((item) => {
        const lineItem = {
          order_item_id: item.external_id + '-1',
          color: item.product.color,
          name: item.name,
          quantity: item.quantity,
          size: item.product.size,
          sku: item.product_sku,
          style: item.product.style,
        };
        let file = this.generatePrintFiles(item.options)
        lineItems.push({ ...lineItem, ...file });
      });
      return lineItems;
    },
    generateOrderItemId(external_id) {
      let orderItemId = external_id.toString();
      if (orderItemId.indexOf('reprint') == 0) {
        let splitOrderItemId = orderItemId.split('-');
        if (splitOrderItemId.length > 0) {
          let index = (splitOrderItemId[0]).substring(7, (splitOrderItemId[0]).length);
          if (index == '') index = 0;
          splitOrderItemId[0] = 'reprint' + (parseInt(index) + 1);
          orderItemId = splitOrderItemId.join('-');
        } else {

          orderItemId = 'reprint-' + orderItemId;
        }
      } else {
        orderItemId = 'reprint-' + orderItemId;
      }
      return orderItemId;
    },
    generatePrintFiles(options) {
        if (!options) return [];
            const printFiles = [];
            const previewFiles = [];
            if (options?.length) {
              let printSides = this.productPrintSides.map(item => item.replaceAll(' ', '').toLowerCase()); // Convert to lowercase
              options.forEach((file) => {
                let name = file.name.split('.');
                let index = printSides.indexOf(name[1].toLowerCase()); // Convert to lowercase
                if (name.length == 2 && index != -1) {
                  let key = this.productPrintSides[index].toLowerCase().replaceAll(' ', '_');
                  if (name[0] == 'PrintFiles') {
                    printFiles.push({
                      key: key,
                      name: file.name,
                      print_side: name[0],
                      url: file.value,
                    });
                  } else {
                    previewFiles.push({
                      key: key,
                      name: file.name,
                      print_side: name[0],
                      url: file.value,
                    });
                  }

                }
              });
            }
            return {
                print_files: printFiles,
                preview_files: previewFiles,
            };
    },
    generateOrderType(order) {
      if (!order) return [];
      let orderTypes = [];
      if (order.is_xqc) {
        orderTypes.push(1);
      }
      if (order.order_type == 2) {
        orderTypes.push(2);
      } else if (order.order_type == 3) {
        orderTypes.push(3);
      } else if (order.order_type == 6) {
        orderTypes.push(6);
      }else if (order.order_type == 5) {
        orderTypes.push(5);
      }
      return orderTypes;
    },
    async resetEmployee() {
      this.employee = {}
      this.employeeError = ""
      this.employeeCode = ""
      this.saleOrder.employee_id = null
    },
    async scanCodeEmployee() {
      try {
        this.isLoading = true;
        const res = await getByCode(this.employeeCode);
        if (!res.data.data) {
          this.employeeCode = '';
          this.notification(
            'Scan employee code error, please scan again.',
            'error'
          );
          return;
        }
        this.employee = res.data.data;
        this.saleOrder.employee_id = this.employee.id;
        this.isLoading = false;
      } catch (e) {
        const data = e.response.data;
        let message = this.$t('Not found');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.isLoading = false;
        this.notification(message, 'error');
      }
    },
    handleFilterList(type) {
      return (
        (type === 2 && (this.saleOrder.orderTypeFormat ?? []).includes(3)) ||
        (type === 3 && (this.saleOrder.orderTypeFormat ?? []).includes(2))
      );
    },
    async fetchCountries() {
      const res = await countries();
      this.countries = res.data || [];
    },
    async fetchStoreDetails() {
      this.storeReturnAddress = {};
      if (!this.saleOrder.store_id) {
        return;
      }
      const res = await storeDetail(this.saleOrder.store_id);
      const storeAddress = res.data?.[0]?.store_address || [];
      const storeCode = res.data?.[0]?.code || "";

      if (!this.isStoreCreateOrderSample && STORE_CODE_SAMPLE_ORDER.includes(storeCode)) {
        this.isStoreCreateOrderSample = true;
        this.saleOrder.external_number = `SAMPLE${moment().format('MMDD')}${moment().unix()}`
      } else if (this.isStoreCreateOrderSample && !STORE_CODE_SAMPLE_ORDER.includes(storeCode)) {
        this.isStoreCreateOrderSample = false;
        this.saleOrder.external_number = "";
      }

      const returnAddress = storeAddress.find(
        (item) => item.type_address === 'return_address'
      );
      if (returnAddress) {
        this.storeReturnAddress = {
          name: returnAddress.name,
          company: returnAddress.company,
          country: returnAddress.country,
          street1: returnAddress.street1,
          street2: returnAddress.street2,
          state: returnAddress.state,
          city: returnAddress.city,
          zip: returnAddress.zip,
          email: returnAddress.email,
          phone: returnAddress.phone,
        };
      }
    },
    changeAddress(type, modalTitle) {
      EventBus.$emit('showAddressFormModal', {
        address: this.saleOrder[type],
        type,
        modalTitle,
        countries: this.countries,
      });
    },
    async fetchStore() {
      await this.$store.dispatch('getStores', { without_pagination: 1 });
    },
    setDefaultData() {
      return {
        line_items: [],
        external_number: '',
        sample_order: 0,
        shipping_method: 'standard',
        return_address: {},
        address: {},
        tax_id: '',
        tax_id_type: '',
        plastic_bag: false,
        orderTypeFormat: [],
        label_url: ''
      };
    },
    async fetchProductPrintSide() {
      this.isLoading = true;
      const { data } = await getList();
      this.productPrintSides = data.map(item => item.name);
      this.isLoading = false;
    },
    resetData() {
      this.currentReturnAddress = 'current_address_in_setting';
      this.storeReturnAddress = {};
      this.saleOrder = this.setDefaultData();
      this.hologram_sticker = false;
      this.sticker_and_bag = false;
      this.mug_packaging = false;
      this.openDialogCreateManual = false;
      this.$refs.scrollLeft.setScrollTop(0);
      this.$refs.scrollRight.setScrollTop(0);
    },
    validate() {
      let errorMessage = '';
      if (!this.saleOrder?.line_items?.length) {
        errorMessage += '<div>Order Items is required.</div>';
      }
      if (!Object.keys(this.saleOrder?.address)?.length) {
        errorMessage += '<div>Ship Address is required.</div>';
      }
      if (!this.saleOrder?.store_id) {
        errorMessage += '<div>Store is required.</div>';
      }
      if (!this.saleOrder?.external_number) {
        errorMessage += '<div>Ref number is required.</div>';
      }
      if (this.saleOrder?.tax_id) {
        if (!this.saleOrder?.tax_id_type) {
          errorMessage += '<div>Tax id type is required.</div>';
        }
      }
      return errorMessage;
    },
    async onSubmit() {
      let saleOrder = clone(this.saleOrder);
      saleOrder.is_reprint = this.isReprint;
      delete saleOrder.additional_services;

      let returnAddress = {};
      if (this.currentReturnAddress === 'current_address_in_setting') {
        returnAddress = Object.keys(this.storeReturnAddress)?.length
          ? this.storeReturnAddress
          : {};
      } else {
        returnAddress = this.saleOrder.return_address;
      }
      saleOrder = {
        ...saleOrder,
        return_address: returnAddress,
        order_id: saleOrder.external_number,
        sample_order: saleOrder.orderTypeFormat.includes(1) ? 1 : 0,
        order_type: saleOrder.orderTypeFormat.includes(2)
          ? 2
          : saleOrder.orderTypeFormat.includes(3)
          ? 3
          : saleOrder.orderTypeFormat.includes(6)
          ? 6
          : saleOrder.orderTypeFormat.includes(5)
          ? 5
          : null,
      };
      if (!saleOrder.order_type) delete saleOrder.order_type;
      delete saleOrder.orderTypeFormat;
      //FILTER LINEITEM'S PREVIEWFILES
      let lineItems = clone(saleOrder?.line_items ?? []);
      lineItems = lineItems.map((item) => {
        let lineItem = clone(item);
        let filteredPreviewFiles = lineItem.preview_files?.filter(
          (item) => {
            return item.url
          }
        );
        lineItem = {
          ...lineItem,
          preview_files: filteredPreviewFiles,
        };
        return lineItem;
      });
      saleOrder = {
        ...saleOrder,
        line_items: lineItems,
      };
      let additionalServices = [];
      let services = ["hologram_sticker", "sticker_and_bag", "mug_packaging"];
      services.forEach(service => {
        if (this[service]) {
          additionalServices.push(service);
        }
      });
      if (additionalServices.length > 0) {
        saleOrder.additional_services = additionalServices;
      }
      const errorMessage = this.validate();
      if (errorMessage) {
        this.notification(errorMessage, 'error', true);
        return;
      }
      this.isLoading = true;
      let params = await this.convertJsonToFormData(saleOrder);
      try {
        const { data } = await addManual(params);
        if (!data.status) {
          let message = '';
          if (data.errors && Object.keys(data.errors).length > 0) {
            for (let i = 0; i <= Object.keys(data.errors).length; i++) {
              const key = Object.keys(data.errors)[i];
              if (!key) continue;
              let msgError = data.errors[key][0];
              if (key === 'order_id') {
                msgError =
                  msgError && msgError.replace('order id', 'ref number');
              }
              message += `<div>${msgError}</div>`;
            }
          }
          this.notification(
            message || data?.message || 'Create manual order fails',
            'error',
            true
          );
          return;
        }
        this.notification('Create manual order successfully');
        this.openDialogCreateManual = false;
        this.$emit('refresh');
      } catch (e) {
        this.notification('Create manual order fails', 'error');
      } finally {
        this.isLoading = false;
      }
    },
    saveAddress({ type, address }) {
      this.saleOrder[type] = address;
    },
    changeReturnAddress() {
      if (Object.keys(this.saleOrder.return_address)?.length) {
        return;
      }
      this.changeAddress('return_address', 'Return Address');
    },
    saveLineItem({ lineItem, index }) {
      if (index === undefined) {
        this.saleOrder.line_items.push(lineItem);
        return;
      }
      for (let i = 0; i < this.saleOrder.line_items.length; i++) {
        if (index === i) {
          this.saleOrder.line_items[i] = lineItem;
          break;
        }
      }
      console.log(2222, this.saleOrder.line_items)
    },
    editLineItem(index) {
      this.changeLineItem(
        'Edit Line Item',
        this.saleOrder.line_items[index],
        index,
        true
      );
    },
    deleteLineItem(index) {
      this.saleOrder.line_items.splice(index, 1);
    },
    changeLineItem(
      modalTitle,
      lineItem,
      index,
      isEdit = false,
      isPretreated = this.saleOrder.orderTypeFormat.includes(2),
      isBlank = this.saleOrder.orderTypeFormat.includes(3)
    ) {
      let storeCode = this?.saleOrder?.store_id ? this.getStores.find(item => item.id == this.saleOrder.store_id)?.code : "";

      EventBus.$emit('showLineItemModal', {
        modalTitle,
        lineItem,
        index,
        lineItems: this.saleOrder.line_items,
        orderTypes: this.saleOrder.orderTypeFormat,
        isEdit: isEdit,
        isPretreated,
        isBlank,
        isReprint: this.isReprint,
        storeCode,
      });
    },
  },
};
