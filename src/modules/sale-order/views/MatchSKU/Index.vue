<style src="./Style.scss" lang="scss"></style>
<script src="./Script.js"></script>
<template>
  <div class="sale-order">
    <div class="top-head">
      <div class="top-head-left mb-5">
        <h1>{{ $t('Matching SKU') }}</h1>
      </div>
      <el-button
          type="primary"
          @click="create"
      >{{ $t('Create') }}</el-button
      >
    </div>
    <div class="table-content">
      <div class="filter">
        <div class="label">{{ $t('Filter by:') }}</div>
        <el-input
            :placeholder="$t('Shipstation SKU')"
            class="el-form-item-scan-employee max-w-fit mr-2"
            ref="sku"
            v-model="filter.shipstation_sku"
            @keyup.enter="onFilter"
        />
        <el-input
            :placeholder="$t('Swiftpod SKU')"
            class="el-form-item-scan-employee max-w-fit mr-2"
            ref="sku"
            v-model="filter.swiftpod_sku"
            @keyup.enter="onFilter"
        />
        <div class="filter-item">
          <el-select
              v-model="filter.style"
              @change="changeStyle"
              filterable
              :placeholder="$t('Product Style')"
              clearable
              @clear="onClearStyle"
          >
            <el-option
                v-for="item in styles"
                :key="item.style"
                :label="item.style"
                :value="item.style"
            >
            </el-option>
          </el-select>
        </div>
        <div class="filter-item">
          <el-select
              v-model="filter.color"
              filterable
              :placeholder="$t('Color')"
              clearable
              @change="onFilter"
          >
            <el-option
                v-for="item in colors"
                :key="item"
                :label="item"
                :value="item"
            >
            </el-option>
          </el-select>
        </div>
        <div class="filter-item">
          <el-select
              v-model="filter.size"
              filterable
              :placeholder="$t('Size')"
              clearable
              @change="onFilter"
          >
            <el-option
                v-for="item in sizes"
                :key="item"
                :label="item"
                :value="item"
            >
            </el-option>
          </el-select>
        </div>
        <div class="filter-item" v-if="hasFilter">
          <el-link type="danger" @click="onClearFilter" :underline="false">
            {{ $t('Clear') }}
          </el-link>
        </div>
      </div>
      <div class="border">
        <el-table
          stripe
          size="small"
          :data="items"
          style="width: 100%"
          :max-height="maxHeight"
          v-loading="isLoading"
          element-loading-text="Loading..."
          class="table-sale-order table-cus"
        >
          <el-table-column width="200" prop="name" :label="$t('Store')">
            <template #default="scope">
              {{ scope.row.name }}
            </template>
          </el-table-column>
          <el-table-column width="200" prop="shipstation_sku" :label="$t('Shipstation SKU')">
            <template #default="scope">
              {{ scope.row.shipstation_sku }}
            </template>
          </el-table-column>
          <el-table-column width="150" prop="swiftpod_sku" :label="$t('SwiftPod SKU')">
            <template #default="scope">
              {{ scope.row.swiftpod_sku }}
            </template>
          </el-table-column>
          <el-table-column prop="style" :label="$t('Style')">
            <template #default="scope">
              {{ scope.row.style }}
            </template>
          </el-table-column>
          <el-table-column prop="color" :label="$t('Color')">
            <template #default="scope">
              {{ scope.row.color }}
            </template>
          </el-table-column>
          <el-table-column prop="size" :label="$t('Size')">
            <template #default="scope">
              {{ scope.row.size }}
            </template>
          </el-table-column>
          <el-table-column prop="account" :label="$t('Print File')">
            <template #default="scope">
              <p v-for="item in JSON.parse(scope.row.options)">
              <el-link
                  class="el-link-edit block"
                  :underline="false"
                  type="primary"
                  @click="viewMore(item)"
              >{{ getImageName(item.name) }}</el-link>
              </p>
            </template>
          </el-table-column>
          <el-table-column
            prop="action"
            :width="['download_error', 'manual_uploaded'].includes(tabName) ? '150px' : '75px'"
            :label="$t('Action')"
            align="center"
          >
            <template #default="scope">
              <el-link
                  class="el-link-edit"
                  :underline="false"
                  type="primary"
                  @click="updateSkuMatching(scope.row)"
              ><icon :data="iconEdit"
              /></el-link>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="bottom">
        <div class="total">
          {{ $t('Total:') }} {{ items.length ? formatNumber(total) : 0 }}
        </div>
        <el-pagination
          :disabled="isLoading"
          background
          layout="prev, pager, next"
          :page-size="filter.limit"
          :total="total"
          @current-change="onFilter"
          v-model:currentPage="filter.page"
        />
        <div class="limit" :disabled="isLoading">
          <el-select
            v-model="filter.limit"
            :placeholder="$t('Select')"
            size="mini"
            @change="onFilter"
          >
            <el-option
              v-for="item in limits"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
      <MatchSKUDetail
          :styles="styles"
          class="mt-3"
          :printSides="printSides"
          :products="products"
      />
    <CreateMatchSKU
          :styles="styles"
          class="mt-3"
          :printSides="printSides"
          :products="products"
      />
  </div>
</template>
