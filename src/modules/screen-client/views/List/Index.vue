<style src="./Style.scss" lang="scss"></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head mb-4">
      <div class="top-head-left">
        <h1>{{ $t('CLIENTS / CLIENTES') }}</h1>
      </div>
      <div class="top-head-right">

      </div>
    </div>
    <div class="relative flex gap-6 pb-0 border-b border-gray-300">
      <div
        v-for="tab in tabs"
        class="cursor-pointer"
        :class="changeTab == tab.value ? 'border-b border-[#1A73E8]' : ''"
        @click="changeTab = tab.value"
      >
        <span>{{tab.name}}</span>
      </div>
    </div>

    <div class="table-content" v-if="changeTab == 'client'">
      <div class="filter justify-between">
        <div class="flex ">
          <div class="label m-auto">{{ $t("Filter by:") }}</div>
          <el-input class="mx-3 !w-[220px]" v-model="filter.name" @keyup.enter="onFilter()" placeholder="Search client/company name"></el-input>
          <el-button type="primary" @click="onFilter()">
            <span class=""><icon :data="iconFilter" /></span>
            {{ $t('Filter') }}
          </el-button>
          <el-button v-if="hasFilter" @click="resetFilter">
            <span class="icon-margin-right">{{ $t("Reset") }}</span>
          </el-button>
        </div>
        <div class="align-right">
          <el-button type="primary" @click="openDialogCreate()">
           <span class="icon-margin-right">
           </span>
            {{ $t('Create Client / Crear cliente') }}
          </el-button>
        </div>
      </div>
      <el-table
          size="small"
          stripe
          :data="listClient"
          style="width: 100%"
          :max-height="maxHeight"
          v-loading="isLoading"
          class="table-sale-order"
          @sort-change="sortTable"
          header-row-class-name="table-header"

      >
        <el-table-column
            :label="$t('ID')"
            min-width="50">
          <template #default="scope">
            {{  scope.row.id }}
          </template>
        </el-table-column>
        <el-table-column
            min-width="110"
            :label="$t('Client Name / Nombre del cliente')"
            class-name="break-word"
        >
        <template #default="scope">
            {{  scope.row.name }}
          </template>
        </el-table-column>


        <el-table-column
            min-width="110"
            :label="$t('Company Name / Nombre de la empresa')"
            class-name="break-word"
        >
          <template #default="scope">
            {{  scope.row.company }}
          </template>
        </el-table-column>


        <el-table-column

            class-name="break-word"
            :label="$t('Country / País')">
          <template #default="scope">
            {{  getCountryName(scope.row.address) }}
          </template>
        </el-table-column>

        <el-table-column

            class-name="break-word"
            :label="$t('City / Ciudad')">
          <template #default="scope">
            {{  getCityName(scope.row.address) }}

          </template>
        </el-table-column>

        <el-table-column

            class-name="break-word"
            :label="$t('State / Estado')">
          <template #default="scope">
            {{  getState(scope.row.address) }}

          </template>
        </el-table-column>

        <el-table-column
            min-width="110"
            class-name="break-word"
            :label="$t('Contact Name / Nombre de contacto')">
          <template #default="scope">
            {{  scope.row.contact_name }}
          </template>
        </el-table-column>

        <el-table-column
            min-width="150"
            class-name="break-word"
            :label="$t('Contact Email / Correo electrónico de contacto')">
          <template #default="scope">
            {{  scope.row.contact_email }}
          </template>
        </el-table-column>

        <el-table-column
            min-width="110"
            class-name="break-word"
            :label="$t('Contact Phone / Teléfono de contacto')">
          <template #default="scope">
            {{  scope.row.contact_phone }}
          </template>
        </el-table-column>
        <el-table-column
            prop="created_at"
            sortable="custom"
            min-width="150"
            class-name="break-word"
            :label="$t('Created Date / Fecha de creación')">
          <template #default="scope">
            {{  convertTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column

            class-name="break-word"
            :label="$t('Created By / Creado por')">
          <template #default="scope">
            {{ scope.row.created_by?.username }}
          </template>
        </el-table-column>

        <el-table-column
            
            :label="$t('Action / Acción')">
          <template #default="scope">
            <el-link
                class="el-link-edit mr-2"
                :underline="false"
                type="primary"
                @click="edit(scope.row)"
            ><icon :data="iconEdit"
            /></el-link>
          </template>
        </el-table-column>

      </el-table>
      <div class="bottom">
        <div class="total">
          {{ $t('Total:') }} {{ listClient.length ? formatNumber(total) : 0 }}
        </div>
        <el-pagination
            :disabled="isLoading"
            background
            layout="prev, pager, next"
            :page-size="filter.limit"
            :total="total"
            @current-change="changePage"
            v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
              v-model="filter.limit"
              :placeholder="$t('Select')"
              size="mini"
              @change="onFilter('limit')"
          >
            <el-option
                v-for="item in limits"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <DialogUpdateAndCreate @refreshData="refreshData" :lastItem="lastItem"/>

    </div>
    <div v-else-if="changeTab == 'packaging_instruction'">
      <ListPackaging />
    </div>
    <div v-else></div>
  </div>
</template>