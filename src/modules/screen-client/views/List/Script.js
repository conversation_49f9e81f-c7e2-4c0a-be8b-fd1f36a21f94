import {getDetail, getList, remove} from '@/api/screenClient';
import {equals} from "ramda";
import dateMixin from '@/mixins/date.js';
import EventBus from "@/utilities/eventBus";
import DialogUpdateAndCreate from '@/modules/screen-client/views/components/DialogUpdateAndCreate.vue';
import Detail from '@/modules/machine/views/components/Detail.vue';
import moment from 'moment';
import {fetchAll} from "@/api/locationMachine";
import filterMixin from '@/mixins/filter';
import formatNumberMixin from '@/mixins/formatNumber.js';
import ListPackaging from '@/modules/screen-client/views/ListPackaging/Index.vue';

export default {
    name: 'ClientList',
    components: { DialogUpdateAndCreate, Detail, ListPackaging },
    mixins: [dateMixin, filterMixin, formatNumberMixin],
    data() {
        return {
            employees: [],
            vendors: [],
            total: 0,
            tabs: [
                {
                    name: 'Clients / Clientes',
                    value: 'client',
                },
                {
                    name: 'Packing Instruction / Instrucciones de empaque',
                    value: 'packaging_instruction',
                }
            ],
            changeTab: 'client',
            listClient: [],
            serverErrors: [],
            locations: [],
            isLoading: false,
            today: new Date(),
            filter: this.setDefaultFilter(),
        }
    },
    computed: {
        hasFilter() {
            const defaultFilter = this.setDefaultFilter();
            return !equals(defaultFilter, this.filter);
        },
        maxHeight() {
            return parseInt(window.innerHeight - 278);
        },
    },
    created() {
        this.fetchClient();

    },
    mounted() {
    },
    beforeUnmount() {
        EventBus.$off('refreshList');
    },
    methods: {
        sortTable(data) {
            let sortColumn = '';
            let sortBy = '';
            if (data.prop && data.order) {
                sortColumn = data.prop;

                if (data.order === 'ascending') {
                    sortBy = 'ASC';
                } else if (data.order === 'descending') {
                    sortBy = 'DESC';
                }
            }

            this.filter.sort_column = sortColumn;
            this.filter.sort_by = sortBy;

            this.setRouteParam();

            this.$nextTick(() => {
                this.fetchClient();
            });
        },
        getCountryName(address = []) {
            let clientAddress = address.find(item => item.type === 'client_address');
            if (!clientAddress) return '';
            return clientAddress?.country;
        },
        getCityName(address = []) {
            let clientAddress = address.find(item => item.type === 'client_address');
            if (!clientAddress) return '';
            return clientAddress?.city;
        },
        getState(address = []) {
            let clientAddress = address.find(item => item.type === 'client_address');
            if (!clientAddress) return '';
            return clientAddress?.state;
        },
        changePage(page) {
            this.filter.page = page;
            this.$nextTick(() => {
                this.fetchClient();
            });
        },
        generateBGStatus(status) {
            switch (status) {
                case 'active':
                    return '!bg-[#6FC140]';
                case 'inactive':
                    return '!bg-[#808081]';
                default:
                    return '!bg-[#EC716F]';
            }
        },
        setRouteParam(routerName = null, params = null) {
            const queryParams = params || this.filter;
            this.$router.replace({
                name: routerName || this.$route.name,
                query: queryParams,
            });
        },
        onFilter() {
            this.fetchClient();
        },
        resetFilter() {
            this.filter = this.setDefaultFilter();
            this.fetchClient();
        },
        clearFilterItem(item) {
            this.filter[item] = '';
            if (this.$refs[item]) {
                this.$refs[item].handleClose();
            }
            this.onFilter();
        },
        hasChangeFilterByItem(name) {
            const query = this.$route.query;
            return !!query[name];
        },
        onClearFilter() {
            this.filter = this.setDefaultFilter();
            this.fetchClient();
        },
        setDefaultFilter() {
            return {
                name: '',
                sort_column: 'created_at',
                sort_by: 'desc',
                limit: 25,
                page: 1
            }
        },
        async fetchClient() {
            this.isLoading = true;
            this.setRouteParam();
            const response = await getList(this.filter);
            if (response.status === 200) {
                this.listClient = response.data.data;
                this.total = response.data.total;
            } else {
                this.notification('Serve error.', "error");
            }
            this.isLoading = false;

        },
        generateAge(date) {
            if (!date) return "";
            let nowPST = moment().tz('America/Los_Angeles').format('YYYY-MM-DD');
            let startDate = moment(date, 'YYYY-MM-DD').format('YYYY-MM-DD');
            let diffDay = moment(nowPST).diff(moment(startDate), 'days');
            if (diffDay < 2) {
                return `${diffDay} day`;
            }
            return `${diffDay} days`;
        },
        async edit(item) {
            const response = await getDetail(item.id);
            EventBus.$emit('openDialogClient', response?.data ?? {});
        },
        openDialogCreate() {
            EventBus.$emit('openDialogClient');
        },
        refreshData() {
            this.fetchClient();
        }
    }
}