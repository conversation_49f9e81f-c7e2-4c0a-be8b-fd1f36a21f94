<style src="./Style.scss" lang="scss"></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="table-content">
      <div class="filter justify-between">
        <div class="flex gap-3">
          <el-input
            class="!w-[200px]"
            v-model="filter.name"
            placeholder="P.I name"
          ></el-input>
          <el-input
            class="!w-[200px]"
            v-model="filter.client_name"
            placeholder="Client name"
          ></el-input>
          <el-select
            v-model="filter.order_type"
            placeholder="Order type"
            class="!w-[200px]"
          >
            <el-option
              v-for="item in orderTypes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-button type="primary" @click="onFilter()">
            <span class=""><icon :data="iconFilter" /></span>
            {{ $t('Filter') }}
          </el-button>
          <el-button v-if="hasFilter" @click="resetFilter">
            <span class="icon-margin-right">{{ $t('Reset') }}</span>
          </el-button>
        </div>
        <div class="align-right">
          <el-button type="primary" @click="openDialogCreate">
            <span class="icon-margin-right"> </span>
            {{ $t('Create P.I / Crear P.I') }}
          </el-button>
        </div>
      </div>
      <el-table
        size="small"
        stripe
        :data="listPacking"
        style="width: 100%"
        :max-height="maxHeight"
        v-loading="isLoading"
        class="table-sale-order"
        @sort-change="sortTable"
      >
        <el-table-column :label="$t('ID')" min-width="50">
          <template #default="scope">
            {{ scope.row.id }}
          </template>
        </el-table-column>
        <el-table-column
          min-width="110"
          :label="$t('P.I Name / Nombre de P.I')"
          class-name="break-word"
        >
          <template #default="scope">
            <span class="cursor-pointer text-[#1A73E8]" @click="openDialogView(scope.row.id)">
              {{ scope.row.name }}
            </span>
          </template>
        </el-table-column>
  
        <el-table-column class-name="break-word" :label="$t('Client Name / Nombre del cliente')">
          <template #default="scope">
            {{ scope.row.client?.name }}
          </template>
        </el-table-column>
  
        <el-table-column class-name="break-word" :label="$t('Order Type / Tipo de pedido')">
          <template #default="scope">
            {{ scope.row.order_type }}
          </template>
        </el-table-column>
  
        <el-table-column
          prop="created_at"
          sortable="custom"
          min-width="150"
          class-name="break-word"
          :label="$t('Created Date / Fecha de creación')"
        >
          <template #default="scope">
            {{ convertTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column class-name="break-word" :label="$t('Created By / Creado por')">
          <template #default="scope">
            {{ scope.row?.user?.username }}
          </template>
        </el-table-column>
  
        <el-table-column :label="$t('Action / Acción')">
          <template #default="scope">
            <el-link
              class="el-link-edit mr-2"
              :underline="false"
              type="primary"
              @click="openDialogEdit(scope.row.id)"
              ><icon :data="iconEdit"
            /></el-link>
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom">
        <div class="total">
          {{ $t('Total:') }} {{ listPacking.length ? formatNumber(total) : 0 }}
        </div>
        <el-pagination
          :disabled="isLoading"
          background
          layout="prev, pager, next"
          :page-size="filter.limit"
          :total="total"
          @current-change="changePage"
          v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
            v-model="filter.limit"
            :placeholder="$t('Select')"
            size="mini"
            @change="onFilter('limit')"
          >
            <el-option
              v-for="item in limits"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
    <CreateOrEditPackagingInstruction
      @refreshData="refreshData"
    />
    <ViewPackagingInstruction @showUpdate="openDialogEdit" />
  </div>
</template>
