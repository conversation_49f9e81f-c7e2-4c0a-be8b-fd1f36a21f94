import {getList} from '@/api/screenPackaging';
import {equals} from "ramda";
import dateMixin from '@/mixins/date.js';
import EventBus from "@/utilities/eventBus";
import filterMixin from '@/mixins/filter';
import formatNumberMixin from '@/mixins/formatNumber.js';
import CreateOrEditPackagingInstruction from '@/modules/screen-client/views/components/CreateOrEditPackagingInstruction.vue';
import ViewPackagingInstruction from '@/modules/screen-client/views/components/ViewPackagingInstruction.vue';

export default {
    name: 'PackagingInstructionList',
    components: { CreateOrEditPackagingInstruction, ViewPackagingInstruction },
    mixins: [dateMixin, filterMixin, formatNumberMixin],
    data() {
        return {
            total: 0,
            listClient: [],
            listPacking: [],
            serverErrors: [],
            isLoading: false,
            filter: this.setDefaultFilter(),
            orderTypes: [
                {
                    label: 'Ecom',
                    value: 'ecom'
                },
                {
                    label: 'Store',
                    value: 'store'
                },
            ]
        }
    },
    computed: {
        hasFilter() {
            const defaultFilter = this.setDefaultFilter();
            return !equals(defaultFilter, this.filter);
        },
        maxHeight() {
            return parseInt(window.innerHeight - 278);
        },
    },
    created() {
        this.fetchList();
    },
    mounted() {
    },
    beforeUnmount() {
        EventBus.$off('refreshList');
    },
    methods: {
        setDefaultFilter() {
            return {
                name: '',
                client_name: '',
                order_type: '',
                sort_column: 'created_at',
                sort_by: 'desc',
                limit: 25,
                page: 1
            }
        },
        sortTable(data) {
            let sortColumn = '';
            let sortBy = '';
            if (data.prop && data.order) {
                sortColumn = data.prop;

                if (data.order === 'ascending') {
                    sortBy = 'ASC';
                } else if (data.order === 'descending') {
                    sortBy = 'DESC';
                }
            }

            this.filter.sort_column = sortColumn;
            this.filter.sort_by = sortBy;

            this.setRouteParam();

            this.$nextTick(() => {
                this.fetchList();
            });
        },
        changePage(page) {
            this.filter.page = page;
            this.$nextTick(() => {
                this.fetchList();
            });
        },
        setRouteParam(routerName = null, params = null) {
            const queryParams = params || this.filter;
            this.$router.replace({
                name: routerName || this.$route.name,
                query: queryParams,
            });
        },
        onFilter() {
            this.fetchList();
        },
        resetFilter() {
            this.filter = this.setDefaultFilter();
            this.fetchList();
        },
        async fetchList() {
            this.isLoading = true;
            this.setRouteParam();
            const response = await getList(this.filter);
            if (response.status === 200) {
                this.listPacking = response.data.data;
                this.total = response.data.total;
            } else {
                this.notification('Serve error.', "error");
            }
            this.isLoading = false;

        },
        openDialogEdit(packagingInstructionId) {
            EventBus.$emit('openDialogCreateOrCreatePackagingInstruction', {id: packagingInstructionId});
        },
        openDialogCreate() {
            EventBus.$emit('openDialogCreateOrCreatePackagingInstruction', {});
        },
        openDialogView(packagingInstructionId) {
            EventBus.$emit('viewPackagingInstructionDialog', {id: packagingInstructionId});
        },
        refreshData() {
            this.fetchList();
        }
    }
}