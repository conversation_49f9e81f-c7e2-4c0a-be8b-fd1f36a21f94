<template>
  <el-dialog
    name="createOrUpdate"
    v-model="isOpenDialog"
    :title="title"
    custom-class="min-w-[400px] custom-dialog rounded-xl"
    @close="closeModal"
    destroy-on-close
    :close-on-click-modal="false"
    top="20px"
    width="1000px"
  >
    <div v-if="isLoading" class="p-5">
      <div
        v-loading="isLoading"
        element-loading-text="Loading..."
        class="loading-container"
      ></div>
    </div>
    <div v-else>
      <el-form
        :model="packagingInstruction"
        :label-position="'top'"
        :rules="formRules"
        ref="dialogForm"
        class="flex flex-col gap-3 overflow-y-auto px-1 !max-h-[705px]"
      >
        <div class="grid grid-cols-3 gap-3">
          <el-form-item prop="name" class="client-name" :label="$t('P.I Name / Nombre de P.I')">
            <el-input
              v-model="packagingInstruction.name"
              @blur="trimInput"
              maxlength="100"
              :placeholder="$t('Input P.I Name')"
            >
            </el-input>
          </el-form-item>
          <el-form-item
            prop="screen_client_id"
            class="client-name"
            :label="$t('Client Name / Nombre del cliente')"
          >
            <el-select
              v-model="packagingInstruction.screen_client_id"
              filterable
              placeholder="Client Name"
              class="!w-full"
            >
              <el-option
                v-for="item in listClient"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            prop="order_type"
            class="client-name"
            :label="$t('Order Type / Tipo de pedido')"
          >
            <el-select
              v-model="packagingInstruction.order_type"
              placeholder="Order type"
              class="!w-full"
            >
              <el-option
                v-for="item in orderTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <div v-for="item in packagingInstruction.data" :key="item.type">
          <div v-if="item.type == 'reference_image'">
            <span class="font-semibold">Reference Image / Imagen de referencia</span>
            <div class="gap-3 border border-gray-300 p-3 rounded-md">
              <el-form-item :label="$t('Reference image / Imagen de referencia')">
                <div class="">
                  <InputUploadMultipleFile v-model="item.files" />
                </div>
              </el-form-item>
            </div>
          </div>
          <div v-else>
            <span class="font-semibold">{{ item.label }}</span>
            <div
              class="grid grid-cols-3 gap-3 border border-gray-300 p-3 rounded-md !break-words content-between "
            >
              <div class="flex flex-col justify-space-between">
                <p>
                  <span class="!break-words"
                    >Supplied or Purchased by / Suministrado o comprado por</span
                  >
                </p>
                <el-form-item class="!break-words">
                  <el-input
                      v-model="item.supplied_purchased_by"
                      maxlength="1000"
                      type="textarea"
                      :rows="3"
                      :placeholder="$t('Input Supplied or Purchased by')"
                  >
                  </el-input>
                </el-form-item>
              </div>
              <div class="flex flex-col justify-space-between content-between">
                <p>
                  <span class="!break-words"
                  >Instruction / Instrucciones</span
                  >
                </p>
                <div>
                  <el-form-item class="!break-words">
                    <el-input
                        v-model="item.instruction"
                        type="textarea"
                        :rows="3"
                        :placeholder="$t('Input Instruction')"
                    >
                    </el-input>
                  </el-form-item>
                </div>

              </div>
              <div class="flex flex-col justify-space-between">
                <p>
                  <span class="!break-words"
                  >Reference image / Imagen de referencia</span
                  >
                </p>
                <el-form-item>
                  <div class="">
                    <InputUploadMultipleFile v-model="item.files" />
                  </div>
                </el-form-item>
              </div>
            </div>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="flex justify-end">
        <el-button @click="closeModal">Cancel / Cancelar</el-button>
        <el-button type="primary" v-if="id !== 0" @click="confirmUpdate()">
          Update / Actualizar
        </el-button>
        <el-button type="primary" v-else @click="onSubmit()">
          Create / Crear
        </el-button>
      </div>
    </template>
  </el-dialog>

  <el-dialog
    name="confirmUpdate"
    v-model="isOpenDialogConfirmUpdate"
    title="Update Packing Instruction / Actualizar instrucción de empaque"
    custom-class="min-w-[400px] custom-dialog rounded-xl"
    @close="closeModalConfirmUpdate"
    destroy-on-close
    :close-on-click-modal="true"
    top="20px"
    width="600px"
  >
    <div>
      Are you sure you want to update this packing instruction? / ¿Estás seguro de que quieres actualizar esta instrucción de embalaje?
    </div>
    <template #footer>
      <div class="flex justify-end">
        <el-button @click="closeModalConfirmUpdate">Cancel</el-button>
        <el-button type="primary" @click="onSubmit()">
          Update / Actualizar
        </el-button>
      </div>
    </template>
  </el-dialog>

</template>
<script>
import EventBus from '@/utilities/eventBus';
import { fetchAll } from '@/api/screenClient';
import { create, update, getDetail } from '@/api/screenPackaging';
import InputUploadMultipleFile from '@/modules/screen-client/views/components/InputUploadMultipleFile.vue';

export default {
  name: 'CreateOrEditPackagingInstruction',
  components: { InputUploadMultipleFile },
  props: {},
  data() {
    return {
      isOpenDialog: false,
      isOpenDialogConfirmUpdate: false,
      id: 0,
      title: '',
      serverErrors: {},
      packagingInstruction: this.setDefaultValue(),
      listClient: [],
      isLoading: true,
      orderTypes: [
        {
          label: 'Ecom',
          value: 'ecom',
        },
        {
          label: 'Store',
          value: 'store',
        },
      ],
      formRules: {
        name: [
          {
            required: true,
            message: 'This field cannot be left blank.',
            trigger: 'blur',
          },
          {
            max: 100,
            message: "Input cannot exceed 100 characters",
            trigger: "blur",
          },
        ],
        screen_client_id: [
          {
            required: true,
            message: 'This field cannot be left blank.',
            trigger: 'blur',
          },
        ],
        order_type: [
          {
            required: true,
            message: 'This field cannot be left blank.',
            trigger: 'change',
          },
        ],
      },
    };
  },
  beforeUnmount() {
    EventBus.$off('openDialogCreateOrCreatePackagingInstruction');
  },
  created() {
    EventBus.$on('openDialogCreateOrCreatePackagingInstruction', (item) => {
      this.isOpenDialog = true;
      this.title = item?.id ? 'Update Packing Instruction / Actualizar instrucción de empaque' : 'Create Packing Instruction / Crear Instrucción de empaque';
      this.fetchAllClient();
      this.setDefaultValue();

      if (item?.id) {
        this.id = item?.id;
        this.detail(this.id);
      }
    });
  },
  methods: {
    closeModal() {
      this.isOpenDialog = false;
      this.setDefaultValue();
      this.id = 0;
      this.serverErrors = {};
      this.$emit('refreshData');
    },
    closeModalConfirmUpdate() {
      this.isOpenDialogConfirmUpdate = false;
    },
    async onSubmit() {
      this.$refs.dialogForm.validate(async (valid) => {
        if (valid) {
          const params = {
            name: this.packagingInstruction?.name,
            order_type: this.packagingInstruction?.order_type,
            screen_client_id: this.packagingInstruction?.screen_client_id,
            data: this.packagingInstruction.data
              ?.filter(
                (item) =>
                  item.supplied_purchased_by !== '' ||
                  item.instruction !== '' ||
                  item.files.length > 0
              )
              ?.map((item) => {
                if (item?.files?.some((item) => "name" in item)) {
                  const image_1 = item?.files?.find((item) => item.name === "image_1");
                  const image_2 = item?.files?.find((item) => item.name === "image_2");
                  const imageNew = item?.files?.find((item) => item.is_new === true);

                  return {
                    type: item?.type || undefined,
                    supplied_purchased_by: item?.supplied_purchased_by || undefined,
                    instruction: item?.instruction || undefined,
                    image_1: image_1 ? image_1?.url : (imageNew?.raw || undefined),
                    image_2: image_2 ? image_2?.url : (imageNew?.raw || undefined),
                  };
                } else {
                  return {
                  type: item?.type || undefined,
                  supplied_purchased_by: item?.supplied_purchased_by || undefined,
                  instruction: item?.instruction || undefined,
                  image_1: item?.files?.[0]?.raw || undefined,
                  image_2: item?.files?.[1]?.raw || undefined,
                };
              }
            }),
          };

          const convertFormData = await this.convertJsonToFormData(params);
          try {
            const response =
              this.id !== 0
                ? await update(this.id, convertFormData)
                : await create(convertFormData);
            if (response.status === 200) {
              if (this.id !== 0) {
                this.notification('Update Packing Instruction successfully. / Instrucción de empaque actualizada con éxito.', 'success');
              } else {
                this.notification('Create Packing Instruction successfully. / Instrucción de empaque creada con éxito.', 'success');
              }
            }
          } catch (e) {
            if (e.response.data.message) {
              this.notification(e.response.data.message, 'error');
            }
            if (e.response.data.errors) {
              this.serverErrors = e.response.data.errors;
            }
          } finally {
            if (this.id === 0) {
                this.closeModal();
            } else {
              this.closeModalConfirmUpdate();
              this.closeModal();;
            }
          }
        } else {
          this.notification('Datos inválidos', 'error');
        }
      });
    },
    async confirmUpdate() {
      this.$refs.dialogForm.validate(async (valid) => {
        if (valid) {
          this.isOpenDialogConfirmUpdate = true;
        } else {
          this.notification('Datos inválidos', 'error');
        }
      });
    },
    async fetchAllClient() {
      this.isLoading = true;
      this.setRouteParam();
      const response = await fetchAll();
      this.listClient = response.data;
      this.isLoading = false;
    },
    async detail(id) {
      this.isLoading = true;
      this.setRouteParam();
      const response = await getDetail(id);
      const data = response.data.data;

      const dataPackaging = this.packagingInstruction.data.map((item) => {
        const match = data?.instructions.find(
          (value) => value?.type === item.type
        );

        if (match) {
          item.supplied_purchased_by =
            match?.supplied_purchased_by || item.supplied_purchased_by;
          item.instruction = match?.instruction || item.instruction;
          item.files = [
            {
              name: 'image_1',
              url: match?.image_1 || null,
            },
            {
              name: 'image_2',
              url: match?.image_2 || null,
            },
          ].filter((file) => file.url);
        }
        return item;
      });

      this.packagingInstruction = {
        name: data?.name || '',
        screen_client_id: data?.client?.id || '',
        order_type: data?.order_type || '',
        data: dataPackaging,
      };
      this.isLoading = false;
    },
    trimInput() {
      this.packagingInstruction.name = this.packagingInstruction.name.trim();
    },
    setDefaultValue() {
      this.packagingInstruction = {
        name: '',
        screen_client_id: '',
        order_type: '',
        data: [
          {
            type: 'artwork',
            label: 'Artwork / Diseño',
            supplied_purchased_by: '',
            instruction: '',
            files: [],
          },
          {
            type: 'reference_image',
            label: 'Reference Image / Imagen de referencia',
            supplied_purchased_by: '',
            instruction: '',
            files: [],
          },
          {
            type: 'price_sticker',
            label: 'Price Sticker',
            supplied_purchased_by: '',
            instruction: '',
            files: [],
          },
          {
            type: 'hang_tag',
            label: 'Hang Tag',
            supplied_purchased_by: '',
            instruction: '',
            files: [],
          },
          {
            type: 'plastic_bags',
            label: 'Plastic Bags / Balas de plastico',
            supplied_purchased_by: '',
            instruction: '',
            files: [],
          },
          {
            type: 'folded',
            label: 'Folded / Doblado',
            supplied_purchased_by: '',
            instruction: '',
            files: [],
          },
          {
            type: 'hologram',
            label: 'Hologram',
            supplied_purchased_by: '',
            instruction: '',
            files: [],
          },
          {
            type: 'size_sticker',
            label: 'Size Sticker',
            supplied_purchased_by: '',
            instruction: '',
            files: [],
          },
          {
            type: 'size_trip',
            label: 'Size strip',
            supplied_purchased_by: '',
            instruction: '',
            files: [],
          },
          {
            type: 'bags',
            label: 'Bags / Bolsas',
            supplied_purchased_by: '',
            instruction: '',
            files: [],
          },
          {
            type: 'polybag_sticker',
            label: 'Polybag Sticker',
            supplied_purchased_by: '',
            instruction: '',
            files: [],
          },
          {
            type: 'carton_liner',
            label: 'Carton Liner',
            supplied_purchased_by: '',
            instruction: '',
            files: [],
          },
          {
            type: 'prepack_set',
            label: 'PrePack Set / Juegos prepack',
            supplied_purchased_by: '',
            instruction: '',
            files: [],
          },
          {
            type: 'boxes',
            label: 'Boxes / Cajas',
            supplied_purchased_by: '',
            instruction: '',
            files: [],
          },
          {
            type: 'packing_list',
            label: 'Packing List',
            supplied_purchased_by: '',
            instruction: '',
            files: [],
          },
          {
            type: 'labels_for_boxes',
            label: 'Labels For Boxes / Etiquetas para cajas',
            supplied_purchased_by: '',
            instruction: '',
            files: [],
          },
          {
            type: 'transport_to_use',
            label: 'Transport To Use / Transporte a utilizar',
            supplied_purchased_by: '',
            instruction: '',
            files: [],
          },
          {
            type: 'shipping_address',
            label: 'Shipping Address / Direccion del envio',
            supplied_purchased_by: '',
            instruction: '',
            files: [],
          },
        ],
      };
    },
  },
};
</script>
