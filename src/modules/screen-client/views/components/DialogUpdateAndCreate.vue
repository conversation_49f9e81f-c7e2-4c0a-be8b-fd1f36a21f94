<template>
  <el-form
      :model="client"
      :label-position="'top'"
      :rules="formRules"
      ref="dialogForm"
      @submit.prevent="onSubmit()"
      @keydown.enter.exact.prevent="event.preventDefault()"
  >
  <el-dialog
      v-model="isOpenDialog"
      :title="title"
      custom-class="min-h-[500px] min-w-[400px] custom-dialog rounded-xl"
      @close="closeModal"
      destroy-on-close
      :close-on-click-modal="false"
      top="30px"
      width="650px"

  >
    <template #default>
      <el-scrollbar height="600px">
          <div class="w-full h-full flex flex-col rounded-sm">
            <el-form-item
                prop="name"
                class="client-name"
                :label="$t('Client Name / Nombre del cliente')"
            >
              <el-input
                  v-model="client.name"
                  :placeholder="$t('Input client name')"
              >
              </el-input>
            </el-form-item>
            <div v-if="serverErrors && serverErrors['name']" class="text-danger  text-[12px] mt-[-10px]">{{serverErrors['name'][0]}}</div>

            <div class="grid grid-cols-12 mb-2">
              <p class="col-span-12 font-bold">{{ $t('Company Information / InformaciÃ³n de la empresa') }}</p>
              <div class="col-span-12 border rounded-xl mt-2 px-2 py-2">
                <el-form-item
                    prop="company"
                    :label="$t('Company Name / Nombre de la empresa')"
                >
                  <el-input
                      class=""
                      v-model="client.company"
                      :placeholder="$t('Input company name')"
                  >
                  </el-input>
                </el-form-item>
                <div v-if="serverErrors && serverErrors['company']" class="text-danger  text-[12px] mt-[-10px]">{{serverErrors['company'][0]}}</div>

                <div class="grid grid-cols-12">
                  <div class="col-span-6 pr-2">
                    <el-form-item
                        prop="email"
                        :label="$t('Email / Correo electrÃ³nico')"
                    >
                      <el-input
                          class=""
                          v-model="client.email"
                          :placeholder="$t('Input email')"
                      >
                      </el-input>
                    </el-form-item>
                    <div v-if="serverErrors && serverErrors['email']" class="text-danger  text-[12px] mt-[-10px]">{{serverErrors['email'][0]}}</div>
                  </div>

                  <div class="col-span-6 pl-2">
                    <el-form-item
                        prop="phone"
                        :label="$t('Phone / TelÃ©fono')"
                    >
                      <el-input
                          class=""
                          v-model="client.phone"
                          :placeholder="$t('Input phone number')"
                      >
                      </el-input>
                    </el-form-item>
                    <div v-if="serverErrors && serverErrors['phone']" class="text-danger  text-[12px] mt-[-10px]">{{serverErrors['phone'][0]}}</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="grid grid-cols-12 mt-1">
              <p class="col-span-12 font-bold">{{ $t('Contact Information / Información de contacto') }}</p>
              <div class="col-span-12 border rounded-xl mt-2 px-2 py-2">
                <el-form-item
                    prop="contact_name"
                    :label="$t('Contact Name / Nombre del contacto')"
                >
                  <el-input
                      class=""
                      v-model="client.contact_name"
                      :placeholder="$t('Input contact name')"
                  >
                  </el-input>
                </el-form-item>
                <div v-if="serverErrors && serverErrors['contact_name']" class="text-danger  text-[12px] mt-[-10px]">{{serverErrors['contact_name'][0]}}</div>

                <div class="grid grid-cols-12">
                  <div class="col-span-6 pr-2">
                    <el-form-item
                        prop="contact_email"
                        :label="$t('Email / Correo electrÃ³nico')"
                    >
                      <el-input
                          class=""
                          v-model="client.contact_email"
                          :placeholder="$t('Input contact email')"
                      >
                      </el-input>
                    </el-form-item>
                    <div v-if="serverErrors && serverErrors['contact_email']" class="text-danger  text-[12px] mt-[-10px]">{{serverErrors['contact_email'][0]}}</div>
                  </div>

                  <div class="col-span-6  pl-2">
                    <el-form-item
                        prop="contact_phone"
                        :label="$t('Phone / TelÃ©fono')"
                    >
                      <el-input
                          class=""
                          v-model="client.contact_phone"
                          :placeholder="$t('Input contact phone number')"
                      >
                      </el-input>
                    </el-form-item>
                    <div v-if="serverErrors && serverErrors['contact_phone']" class="text-danger  text-[12px] mt-[-10px]">{{serverErrors['contact_phone'][0]}}</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="grid grid-cols-12 mt-3">
              <p class="col-span-12 font-bold">{{ $t('Address / DirecciÃ³n') }}</p>
              <div class="col-span-12 border rounded-xl mt-2 px-2 py-2">
                <el-form-item
                    prop="street1"
                    :label="$t('Street 1 / Calle 1')"
                >
                  <el-input
                      class=""
                      v-model="client.street1"
                      :placeholder="$t('Input street 1')"
                  >
                  </el-input>
                </el-form-item>
                <div v-if="serverErrors && serverErrors['street1']" class="text-danger  text-[12px] mt-[-10px]">{{serverErrors['street1'][0]}}</div>

                <el-form-item
                    prop="street2"
                    :label="$t('Street 2 / Calle 2')"
                >
                  <el-input
                      class=""
                      v-model="client.street2"
                      :placeholder="$t('Input street 2')"
                  >
                  </el-input>
                </el-form-item>
                <div v-if="serverErrors && serverErrors['street2']" class="text-danger  text-[12px] mt-[-10px]">{{serverErrors['street2'][0]}}</div>

                <div class="grid grid-cols-12">
                  <div class="col-span-6 pr-2">
                    <el-form-item
                        prop="city"
                        :label="$t('City / Ciudad')"
                    >
                      <el-input
                          class=""
                          v-model="client.city"
                          :placeholder="$t('Input city')"
                      >
                      </el-input>
                    </el-form-item>
                    <div v-if="serverErrors && serverErrors['city']" class="text-danger  text-[12px] mt-[-10px]">{{serverErrors['city'][0]}}</div>

                    <el-form-item
                        prop="state"
                        :label="$t('State / Estado')"
                    >
                      <el-select
                          :placeholder="$t('Select state')"
                          class="el-select-state !w-full"
                          v-model="currentState"
                          filterable
                      >
                        <el-option
                            v-for="item in states"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>
                    <div v-if="serverErrors && serverErrors['state']" class="text-danger  text-[12px] mt-[-10px]">{{serverErrors['state'][0]}}</div>
                  </div>

                  <div class="col-span-6  pl-2">
                    <el-form-item
                        prop="country"
                        :label="$t('Country / PaÃ­s')"
                    >
                      <el-select
                          :placeholder="$t('Select country')"
                          class="el-select-country !w-full"
                          v-model="currentCountry"
                          @change="changeCountry"
                          filterable
                      >
                        <el-option
                            v-for="item in countries"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                      </el-select>
<!--                      <el-input-->
<!--                          class=""-->
<!--                          v-model="client.country"-->
<!--                      >-->
<!--                      </el-input>-->
                    </el-form-item>
                    <div v-if="serverErrors && serverErrors['country']" class="text-danger  text-[12px] mt-[-10px]">{{serverErrors['country'][0]}}</div>

                    <el-form-item
                        prop="zipcode"
                        :label="$t('Zipcode / CÃ³digo postal')"
                    >
                      <el-input
                          class=""
                          v-model="client.zipcode"
                          :placeholder="$t('Input zipcode')"
                      >
                      </el-input>
                    </el-form-item>
                    <div v-if="serverErrors && serverErrors['zipcode']" class="text-danger  text-[12px] mt-[-10px]">{{serverErrors['zipcode'][0]}}</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="grid grid-cols-12 mt-3">
              <p class="col-span-12 font-bold">{{ $t('Billing Address / DirecciÃ³n de facturaciÃ³n') }}</p>
              <div class="col-span-12 border rounded-xl mt-2 px-2 py-2">
                <el-form-item
                    prop="is_has_billing_address"
                >
                    <el-radio-group v-model="client.is_has_billing_address" class="!grid" @change="updateBillingAddress()">
                      <el-radio :label="0">{{ $t('Use the address above / Usar la direcciÃ³n anterior') }}</el-radio>
                      <el-radio :label="1">{{ $t('Add new address / Agregar nueva direcciÃ³n') }}</el-radio>
                    </el-radio-group>

                </el-form-item>


                <div class="col-span-12 pt-2" v-if="client.is_has_billing_address == 1">
                  <el-form-item
                      prop="billing_name"
                      :label="$t('Name / Nombre')"
                  >
                    <el-input
                        class=""
                        v-model="client.billing_name"
                        :placeholder="$t('Name')"
                    >
                    </el-input>
                  </el-form-item>
                  <div v-if="serverErrors && serverErrors['billing_name']" class="text-danger  text-[12px] mt-[-10px]">{{serverErrors['billing_name'][0]}}</div>
                  <el-form-item
                      prop="billing_street1"
                      :label="$t('Street 1 / Calle 1')"
                  >
                    <el-input
                        class=""
                        v-model="client.billing_street1"
                        :placeholder="$t('Street 1')"
                    >
                    </el-input>
                  </el-form-item>
                  <div v-if="serverErrors && serverErrors['billing_street1']" class="text-danger  text-[12px] mt-[-10px]">{{serverErrors['billing_street1'][0]}}</div>

                  <el-form-item
                      prop="billing_street2"
                      :label="$t('Street 2 / Calle 2')"
                  >
                    <el-input
                        class=""
                        v-model="client.billing_street2"
                        :placeholder="$t('Street 2')"
                    >
                    </el-input>
                  </el-form-item>
                  <div v-if="serverErrors && serverErrors['billing_street2']" class="text-danger  text-[12px] mt-[-10px]">{{serverErrors['billing_street2'][0]}}</div>

                  <div class="grid grid-cols-12">
                    <div class="col-span-6 pr-2">
                      <el-form-item
                          prop="billing_city"
                          :label="$t('City / Ciudad')"
                      >
                        <el-input
                            class=""
                            v-model="client.billing_city"
                            :placeholder="$t('City')"
                        >
                        </el-input>
                      </el-form-item>
                      <div v-if="serverErrors && serverErrors['billing_city']" class="text-danger  text-[12px] mt-[-10px]">{{serverErrors['billing_city'][0]}}</div>

                      <el-form-item
                          prop="billing_state"
                          :label="$t('State / Estado')"
                      >

                        <el-select
                            :placeholder="$t('Select state')"
                            class="el-select-state !w-full"
                            v-model="currentBillingState"
                            filterable
                        >
                          <el-option
                              v-for="item in billingStates"
                              :key="item.id"
                              :label="item.name"
                              :value="item.id"
                          >
                          </el-option>
                        </el-select>
                      </el-form-item>
                      <div v-if="serverErrors && serverErrors['billing_state']" class="text-danger  text-[12px] mt-[-10px]">{{serverErrors['billing_state'][0]}}</div>

                      <el-form-item
                          prop="billing_phone"
                          :label="$t('Phone Number / NÃºmero de telÃ©fono')"
                      >
                        <el-input
                            class=""
                            v-model="client.billing_phone"
                            :placeholder="$t('Phone Number')"
                        >
                        </el-input>
                      </el-form-item>
                      <div v-if="serverErrors && serverErrors['billing_phone']" class="text-danger  text-[12px] mt-[-10px]">{{serverErrors['billing_phone'][0]}}</div>
                    </div>

                    <div class="col-span-6  pl-2">
                      <el-form-item
                          prop="billing_country"
                          :label="$t('Country / PaÃ­s')"
                      >
                        <el-select
                            :placeholder="$t('Select country')"
                            class="el-select-country !w-full"
                            v-model="currentBillingCountry"
                            filterable
                            @change="changeBillingCountry()"
                        >
                          <el-option
                              v-for="item in countries"
                              :key="item.id"
                              :label="item.name"
                              :value="item.id"
                          >
                          </el-option>
                        </el-select>
                      </el-form-item>
                      <div v-if="serverErrors && serverErrors['billing_country']" class="text-danger  text-[12px] mt-[-10px]">{{serverErrors['billing_country'][0]}}</div>

                      <el-form-item
                          prop="billing_zipcode"
                          :label="$t('Zipcode / CÃ³digo postal')"
                      >
                        <el-input
                            class=""
                            v-model="client.billing_zipcode"
                            :placeholder="$t('Zipcode')"
                        >
                        </el-input>
                      </el-form-item>
                      <div v-if="serverErrors && serverErrors['billing_zipcode']" class="text-danger  text-[12px] mt-[-10px]">{{serverErrors['billing_zipcode'][0]}}</div>
                      <el-form-item
                          prop="billing_email"
                          :label="$t('Email / Correo electrÃ³nico')"
                      >
                        <el-input
                            class=""
                            v-model="client.billing_email"
                            :placeholder="$t('Email')"
                        >
                        </el-input>
                      </el-form-item>
                      <div v-if="serverErrors && serverErrors['billing_email']" class="text-danger  text-[12px] mt-[-10px]">{{serverErrors['billing_email'][0]}}</div>
                    </div>
                  </div>
                </div>
                <div v-if="serverErrors && serverErrors['is_has_billing_address']" class="text-danger  text-[12px] mt-[-10px]">{{serverErrors['is_has_billing_address'][0]}}</div>
              </div>
            </div>
          </div>
      </el-scrollbar>

    </template>
    <template #footer>
      <div class="flex justify-end">
        <el-button  @click="cancel()"
        >Cancel / Cancelar</el-button
        >
        <el-button type="primary" @click="onSubmit()"
        >{{ client.id ? 'Update / Actualizar' : 'Create / Crear' }}</el-button
        >
      </div>
    </template>
  </el-dialog>
  </el-form>

</template>

<script>

import EventBus from "@/utilities/eventBus";
import { create, update, getDetail} from '@/api/screenClient';
import {countries, fetchStateByCountry} from "@/api/default";
import * as parser from "parse-address";

export default {
  name: 'DialogUpdateAndCreate',
  props: {
    lastItem: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      isOpenDialog: false,
      isOpenDialogBilling: false,
      serverErrors: {},
      client: {
        is_has_billing_address: 0,
      },
      originalClient: {},
      statusList: [
        {
          value: 'active',
          name: 'Active',
        },
        {
          value: 'inactive',
          name: 'Inactive',
        }
      ],
      formRules: {
        company: [
          { required: true, message: 'This field cannot be left blank. ', trigger: 'blur' },
        ],
        name: [
          { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
        ],
        street1: [
          { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
        ],
        city: [
          { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
        ],
        country: [
          { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
        ],
        zipcode: [
          { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
        ],


      },
      title: '',
      countries: [],
      states: [],
      billingStates: [],
      billingAddress: {},
      clientAddress: {},
      currentCountry: '',
      currentState: '',
      currentBillingCountry: '',
      currentBillingState: '',
      validateAddress: '',
      isValidateAddress: false,

    }
  },
  mounted() {
    this.fetchCountries();
    this.client.is_has_billing_address = 0;
    EventBus.$on('openDialogClient', async (data = null) => {
      if (data) {
        this.originalClient = data;
        if (Object.keys(this.originalClient).length !== 0) {
          this.clientAddress = this.originalClient.address.find((item) => item.type === 'client_address');
          this.billingAddress = this.originalClient.address.find((item) => item.type === 'billing_address');
          this.client.is_has_billing_address = this.originalClient.address.find((item) => item.type === 'billing_address') ? 1 : 0;
        }

        if (!this.clientAddress) {
          this.clientAddress = {};
        }
        if (!this.billingAddress) {
          this.billingAddress = {};
        }
        this.formatAddress();
        this.client = {
          id: this.originalClient.id,
          name: this.originalClient.name,
          company: this.originalClient.company,
          email: this.originalClient.email,
          phone: this.originalClient.phone,
          contact_name: this.originalClient.contact_name,
          contact_email: this.originalClient.contact_email,
          contact_phone: this.originalClient.contact_phone,
          street1: this.clientAddress.street1,
          street2: this.clientAddress.street2,
          city: this.clientAddress.city,
          state: this.clientAddress.state,
          country: this.clientAddress.country,
          zipcode: this.clientAddress.zipcode,
          is_has_billing_address: this.billingAddress?.name ? 1 : 0,
          billing_name: this.billingAddress?.name,
          billing_street1: this.billingAddress?.street1,
          billing_street2: this.billingAddress?.street2,
          billing_city: this.billingAddress?.city,
          billing_state: this.billingAddress?.state,
          billing_country: this.billingAddress?.country,
          billing_zipcode: this.billingAddress?.zipcode,
          billing_phone: this.billingAddress?.phone,
          billing_email: this.billingAddress?.email,
        };
        this.currentCountry = await this.countries.find((item) => item.iso2 == this.clientAddress.country)?.id || '';
        this.currentBillingCountry = await this.countries.find((item) => item.iso2 == this.billingAddress.country)?.id || '';
        var promise = [ fetchStateByCountry(this.currentCountry) ];
        if (this.currentBillingCountry) {
          promise.push(fetchStateByCountry(this.currentBillingCountry));
          const [res1, res2] = await Promise.all(promise)
          this.states = res1.data || [];
          this.states = this.states.map((item) => {
            return {
              id: item.id,
              name: item.name,
              iso2: item.iso2,
              label: item.name,
              value: item.id
            }
          })
          this.billingStates = res2.data || [];
          this.currentBillingState = this.billingStates.find((item) => item.iso2 == this.billingAddress.state)?.id || '';
        } else {
          const [res1] = await Promise.all(promise)
          this.states = res1.data || [];
        }
        this.currentState = this.states.find((item) => item.iso2 == this.clientAddress.state)?.id || '';
        this.title = 'Update Client / Actualizar cliente';
      } else {
        this.client.is_has_billing_address = 0
        this.title = 'Create Client / Crear cliente';
      }
      this.isOpenDialog = true;
    })
  },
  watch: {
    'client.is_has_billing_address': {
      handler: function (val) {
        if (val === 1) {
          this.formRules = {
            name: [
              { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
            ],
            company: [
              { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
            ],
            street1: [
              { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
            ],
            city: [
              { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
            ],
            country: [
              { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
            ],
            zipcode: [
              { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
            ],
            billing_name: [
              { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
            ],
            billing_street1: [
              { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
            ],
            billing_city: [
              { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
            ],
            billing_country: [
              { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
            ],
            billing_zipcode: [
              { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
            ],

          }

        } else {
          this.formRules = {
            name: [
              { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
            ],
            company: [
              { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
            ],
            street1: [
              { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
            ],
            city: [
              { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
            ],
            country: [
              { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
            ],
            zipcode: [
              { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
            ],
          }
          this.resetBillingAddress();

        }
        this.$nextTick(() => {
          this.$refs["dialogForm"].clearValidate();
        });
      },
      deep: true
    }
  },
  beforeUnmount() {
    EventBus.$off('openDialogClient');
  },
  methods: {
    showValidateAddress() {
      this.isValidateAddress = !this.isValidateAddress;
      this.validateAddress = '';
      this.$nextTick(() => {
        if (this?.$refs?.validateAddress) {
          this.$refs.validateAddress.focus();
        }
      });
    },
    async parseAddress() {
      const defaultCounty = 'US';
      const country = this.countries.find(
          (item) => item.iso2 === defaultCounty
      );
      const parsed = parser.parseLocation(this.validateAddress);
      const {
        number = "",
        prefix = "",
        street: _street = "",
        type = "",
        suffix = "",
        city: _city = "",
        state: _state = "",
        zip: _zip = "",
        sec_unit_type = "",
        sec_unit_num = ""
      } = parsed;
      const preStreet = `${number} ${prefix} ${_street} ${type} ${suffix}`;
      const secUnit = `${sec_unit_type} ${sec_unit_num}`;
      const city = _city;
      const state = _state;
      const street = (preStreet.trim() && secUnit.trim()) ? `${preStreet.trim()}, ${secUnit.trim()}` : (preStreet.trim() ? preStreet.trim() : secUnit.trim());
      const zip = _zip;
      if (country && country.id && state) {
        this.currentBillingCountry = country && country.id;
        await this.fetchStateByCountry(this.client.billing_country_id);
        const stateItem = this.states.find((item) => item.iso2 === state);
        this.currentBillingState = stateItem ? stateItem.id : this.address.state;
      }

      this.isValidateAddress = false;
      this.validateAddress = '';
    },
    async changeCountry() {
      this.currentState = '';
      await this.fetchStateByCountry(this.currentCountry, 'client');
    },
    async changeBillingCountry() {
      this.currentBillingState = '';
      await this.fetchStateByCountry(this.currentBillingCountry, 'billing');
    },
    async fetchStateByCountry(id, type = 'billing') {
      if (type === 'billing') {
        const res = await fetchStateByCountry(id);
        this.billingStates = res.data || [];
      } else {
        const res = await fetchStateByCountry(id);
        this.states = res.data || [];
      }
    },
    async fetchCountries() {
      const res = await countries();
      this.countries = res.data || [];
      this.countries = this.countries.map((item) => {
        return {
          id: item.id,
          name: item.name,
          iso2: item.iso2,
          label: item.name,
          value: item.id
        }
      })
    },
    updateBillingAddress() {
      if (this.client.is_has_billing_address === 1) {
      }
    },
    async getDetail(id) {
      const response = await getDetail(id);
      this.client = response.data?.data ?? {};
    },
    closeModal() {
      this.isOpenDialog = false
      this.client = {}
      this.originalClient = {}
      this.currentState = '';
      this.currentCountry = '';
      this.currentBillingState = '';
      this.currentBillingCountry = '';
      this.serverErrors = {};
      this.$emit('refreshData');
    },
    resetBillingAddress() {
      if (Object.keys(this.originalClient).length !== 0) {
        var billingAddress = this.originalClient.address.find((item) => item.type === 'billing_address');
        this.client.billing_name = billingAddress?.name;
        this.client.billing_street1 = billingAddress?.street1;
        this.client.billing_street2 = billingAddress?.street2;
        this.client.billing_city = billingAddress?.city;
        this.client.billing_state = billingAddress?.state;
        this.client.billing_country = billingAddress?.country;
        this.client.billing_zipcode = billingAddress?.zipcode;
        this.client.billing_phone = billingAddress?.phone;
        this.client.billing_email = billingAddress?.email;
      } else {
        this.client.billing_name = '';
        this.client.billing_street1 = '';
        this.client.billing_street2 = '';
        this.client.billing_city = '';
        this.client.billing_state = '';
        this.client.billing_country = '';
        this.client.billing_zipcode = '';
        this.client.billing_phone = '';
        this.client.billing_email = '';
        this.currentBillingState = '';
        this.currentBillingCountry = '';
        this.currentBillingState = '';
        this.currentBillingCountry = '';
      }
    },
    resetForm() {
      if (Object.keys(this.originalClient).length !== 0) {
        this.clientAddress = this.originalClient.address.find((item) => item.type === 'client_address');
        this.billingAddress = this.originalClient.address.find((item) => item.type === 'billing_address');
      }
      if (!this.clientAddress) {
        this.clientAddress = {};
      }
      if (!this.billingAddress) {
        this.billingAddress = {};
      }
      this.client = {
        id: this.originalClient.id,
        name: this.originalClient.name,
        company: this.originalClient.company,
        email: this.originalClient.email,
        phone: this.originalClient.phone,
        contact_name: this.originalClient.contact_name,
        contact_email: this.originalClient.contact_email,
        contact_phone: this.originalClient.contact_phone,
        street1: this.clientAddress.street1,
        street2: this.clientAddress.street2,
        city: this.clientAddress.city,
        state: this.clientAddress.state,
        country: this.clientAddress.country,
        zipcode: this.clientAddress.zipcode,
        is_has_billing_address: this.billingAddress?.name ? 1 : 0,
        billing_name: this.billingAddress?.name,
        billing_street1: this.billingAddress?.street1,
        billing_street2: this.billingAddress?.street2,
        billing_city: this.billingAddress?.city,
        billing_state: this.billingAddress?.state,
        billing_country: this.billingAddress?.country,
        billing_zipcode: this.billingAddress?.zipcode,
        billing_phone: this.billingAddress?.phone,
        billing_email: this.billingAddress?.email,
      };
      this.serverErrors = {};
    },
    formatAddress() {
      const country =
          (this.currentCountry &&
              this.countries.find((item) => item.id === this.currentCountry)) ||
          '';
      const state =
          (this.currentState &&
              this.states.find((item) => item.id === this.currentState)) ||
          '';
      this.client.country = (country && country.iso2) || '';
      this.client.state = (state && state.iso2) || '';
      const billingCountry =
          (this.currentBillingCountry &&
              this.countries.find((item) => item.id === this.currentBillingCountry)) ||
          '';
      const billingState = (this.currentBillingState &&
              this.billingStates.find((item) => item.id === this.currentBillingState)) ||
          '';
      this.client.billing_country = (billingCountry && billingCountry.iso2) || '';
      this.client.billing_state = (billingState && billingState.iso2) || '';
    },
    async onSubmit() {
      this.serverErrors = {};
      if (this.client.is_has_billing_address === 0) {
        this.client.billing_name = '';
        this.client.billing_street1 = '';
        this.client.billing_street2 = '';
        this.client.billing_city = '';
        this.client.billing_state = '';
        this.client.billing_country = '';
        this.client.billing_zipcode = '';
        this.client.billing_phone = '';
        this.client.billing_email = '';
      }
      this.formatAddress();
      this.$refs.dialogForm.validate(async (valid) => {
        if (valid) {
          try {
            const response =  this.client.id ? await update(this.client) : await create(this.client);
            if (response.status === 200) {
              this.notification('Success / Éxito', 'success');
              this.closeModal();
            }
          } catch (e) {
            if (e.response.data.message) {
              this.notification(e.response.data.message, 'error');
            }
            if (e.response.data.errors) {
              this.serverErrors = e.response.data.errors;
            }

          }
        } else {
          this.notification('Data invalid / Datos inválidos', 'error');

        }
      })
    },
    cancel() {
      this.closeModal()
    },
  },
}
</script>
<style lang="scss">
.el-form-item {
  margin-bottom: 10px!important;
}
.el-form--default.el-form--label-top .client-name .el-form-item__label {
  font-weight: bold;
}
.custom-font-normal .el-form-item__label {
  font-weight: normal!important;
}
</style>

