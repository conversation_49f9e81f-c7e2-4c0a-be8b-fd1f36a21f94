<template>
    <div :class="parentClass">
      <div
        v-for="(item, index) in fileList"
        :key="index"
        :class="itemClass"
        class="group border border-gray-300 rounded-md flex items-center justify-center relative overflow-hidden"
      >
        <div
          class="cursor-pointer absolute inset-0 z-[12] flex justify-center items-center gap-3 bg-black bg-opacity-0 rounded-md transition-opacity duration-300 group-hover:bg-opacity-50"
        >
            <icon
                class="!w-4 !h-4 !text-white opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                :data="iconSearch"
                @click="previewFile(index)"
            />
            <icon
                class="!w-4 !h-4 !text-white opacity-0 transition-opacity duration-300 group-hover:opacity-100" 
                :data="iconRemove"
                @click="removeFile(index)"
            />
        </div>
  
        <img class="max-h-full rounded-md" alt="" :src="item.url" />
      </div>
      <div
        class="relative border border-gray-300 border-dashed rounded-lg hover:border-[#1A73E8]"
        :class="[
          {
            'border-red-500': error || messageError !== '',
          },
          itemClass,
        ]"
        v-if="fileList.length < this.limit"
      >
        <input
          class="absolute w-full border-none opacity-0 !p-0 z-10 cursor-pointer"
          type="file"
          :class="itemClass"
          :accept="acceptFile"
          @change="
            ($event) => {
              changeFile($event);
            }
          "
          multiple
        />
  
        <div
          class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-1 w-full flex flex-col gap-0 items-center justify-center px-2 text-gray-300"
        >
          <icon class="!w-3" :data="iconPlus" />
          <span class="!text-[12px]">Upload</span>
        </div>
      </div>
    </div>
    <div v-if="messageError !== ''">
        <span class="text-[12px] text-red-500">{{ messageError }}</span>
    </div>
    <el-dialog
      v-model="dialogVisible"
      top="20px"
      width="500px"
      title="Preview Image"
    >
      <img width="100%" :src="previewImage" alt="Preview" />
    </el-dialog>
  </template>
  
  <script>
  export default {
    name: 'inputUploadMultipleFile',
    props: {
        error: {
            type: String,
            default: '',
        },
        acceptFile: {
            type: String,
            default: "image/png, image/jpg, image/jpeg",
        },
        parentClass: {
            type: String,
            default: "flex flex-row gap-2",
        },
        itemClass: {
            type: String,
            default: "h-[74px] w-[74px]",
        },
        modelValue: {
            type: Array,
            default: [],
        },
        hasRemove: {
            type: Boolean,
            default: true,
        },
        limit: {
            type: Number,
            default: 2,
        },
        maxSize: {
            type: Number,
            default: 2,
        },
    },
    data() {
      return {
        fileList: this.modelValue || [],
        dialogVisible: false,
        previewImage: '',
        messageError: '',
      };
    },
    watch: {
        modelValue: {
        immediate: true,
        handler(newValue) {
            if (newValue && newValue.length) {
                this.fileList = newValue.map((f) => ({
                    ...f,
                    url: f.url || URL.createObjectURL(f.raw),
                }));
            }
        },
        },
    },
    methods: {
        changeFile($event) {
            this.messageError = '';
            const files = $event.target?.files;
            if (!files) {
                return;
            }

            const acceptedFormats = this.acceptFile.split(",").map((format) => format.trim());
            if (files.length > this.limit || (files.length + this.fileList.length) > this.limit) {
                this.messageError = `Upload max ${this.limit} images`;
                return;
            }

            const URL = window.webkitURL || window.URL;

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const fileType = file.type;
                const fileSize = file.size;

                if (!acceptedFormats.includes(fileType)) {
                    this.messageError = `Accept file type: jpg, jpeg, png.`;
                    continue;
                }

                if (fileSize > this.maxSize * 1024 * 1024) {
                    this.messageError = `File exceeds ${this.maxSize}MB limit.`;
                    continue;
                }
            
                this.fileList.push({
                    is_new: true,
                    raw: file,
                    url: URL.createObjectURL(file),
                });
            }
            
            this.$emit('update:modelValue', this.fileList);

            $event.target.value = "";
        },
        removeFile(index) {
            this.fileList.splice(index, 1);
            this.$emit('update:modelValue', this.fileList);
        },
        previewFile(index) {
            this.dialogVisible = true;
            this.previewImage = this.fileList?.[index]?.url;
        }
    }
  };
  </script>
  