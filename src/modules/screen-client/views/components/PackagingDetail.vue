<template>
  <div class="bg-[#D9D9D9]">
    <div class="w-[800px] bg-white mx-auto px-8 rounded-xl">
      <div class="header mb-[48px] pt-[24px]">
      <span class="font-semibold text-[20px]">
            {{ packaging?.name }}
          </span>
      </div>
      <div>
        <el-row :gutter="20" class="mb-[16px]">
          <el-col :span="8" class="text-[#475467]">
            {{ $t('Client Name / Nombre del cliente') }}
          </el-col>
          <el-col :span="16">
            {{ packaging?.client?.name }}
          </el-col>
        </el-row>
        <el-row :gutter="20" class="mb-[16px]">
          <el-col :span="8" class="text-[#475467]">
            {{ $t('Order Type / Tipo de pedido') }}
          </el-col>
          <el-col :span="16">
            {{ capitalizeFirstString(packaging.order_type) }}
          </el-col>
        </el-row>
        <hr>

        <div v-for="item in instructions">
          <div v-if="item.type == 'reference_image'">
            <el-row  :gutter="20" class="mb-[16px]">
              <el-col :span="24" class="text-[#475467]">
                <span class="font-semibold">
                  {{ mapTitle(item.type) }}
                </span>
              </el-col>
            </el-row>
            <el-row :gutter="20" class="mb-[12px]" >
              <el-col :span="8" class="text-[#475467]">
                {{ mapTitle(item.type) }}
              </el-col>
              <el-col :span="16">
                <div class="flex">
                  <img v-if="item.image_1" :src="item.image_1" @click="showPreviewImage(item.image_1)" alt="reference_image" class="w-auto h-[60px] mr-3 rounded cursor-pointer">
                  <img v-if="item.image_2" :src="item.image_2" @click="showPreviewImage(item.image_2)" alt="reference_image" class="w-auto h-[60px] rounded cursor-pointer">
                  <span v-if="!item.image_1 && !item.image_2">
                  --
                </span>
                </div>
              </el-col>
            </el-row>
          </div>
          <div v-else>
            <el-row :gutter="20" class="mb-[16px]">
              <el-col :span="24" class="text-[#475467]">
                <span class="font-semibold">
                  {{ mapTitle(item.type) }}
                </span>
              </el-col>
            </el-row>
            <el-row :gutter="20" class="mb-[12px]" >
              <el-col :span="8" class="text-[#475467]">
                {{ $t('Supplied or Purchased by / Suministrado o comprado por') }}
              </el-col>
              <el-col :span="16">
                {{ item.supplied_purchased_by || '--' }}
              </el-col>
            </el-row>
            <el-row :gutter="20" class="mb-[12px]" >
              <el-col :span="8" class="text-[#475467]">
                {{ $t('Instruction / Instrucciones') }}
              </el-col>
              <el-col :span="16">
                {{ item.instruction || '--' }}
              </el-col>
            </el-row>
            <el-row :gutter="20" class="mb-[12px]" >
              <el-col :span="8" class="text-[#475467]">
                {{ $t('Reference image / Imagen de referencia') }}
              </el-col>
              <el-col :span="16" >
                <div class="flex">
                  <img v-if="item.image_1" :src="item.image_1" @click="showPreviewImage(item.image_1)" alt="reference_image" class="w-auto h-[60px] mr-3 rounded cursor-pointer">
                  <img v-if="item.image_2" :src="item.image_2" @click="showPreviewImage(item.image_2)" alt="reference_image" class="w-auto h-[60px] rounded cursor-pointer">
                  <span v-if="!item.image_1 && !item.image_2">
                  --
                </span>
              </div>

            </el-col>
          </el-row>
          <hr>
        </div>
        <el-dialog
            v-model="dialogPreview"
            width="500px"
            title="Preview Image"
        >
          <img class="w-auto" :src="previewImage" alt="Preview" />
        </el-dialog>
      </div>
    </div>
    </div>
  </div>
</template>
<script>

import { getDetail } from "@/api/screenPackaging";
import dateMixin from "@/mixins/date";
import {mapGetters} from "vuex";


export default {
  components: {},
  mixins: [dateMixin],
  props: {
    employees: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      isLoading: false,
      dialogPreview: false,
      previewImage: '',
      packaging: {},
      instructions: [],
      sortByType: [
        'artwork',
        'reference_image',
        'price_sticker',
        'hang_tag',
        'plastic_bags',
        'folded',
        'hologram',
        'size_sticker',
        'size_trip',
        'bags',
        'polybag_sticker',
        'carton_liner',
        'prepack_set',
        'boxes',
        'packing_list',
        'labels_for_boxes',
        'transport_to_use',
        'shipping_address',
      ]
    }
  },
  created() {
    let id = this.$route?.params?.id;
    if (id) {
      this.getDetail(id);
    }
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 220);
    },
    ...mapGetters({
      // getEmployees: 'getEmployees',
    }),
  },
  methods: {
    showPreviewImage(url) {
      this.previewImage = url;
      this.dialogPreview = true;
    },
    capitalizeAfterSpace(str) {
      return str
          .split('_')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
    },
    mapTitle(str) {
      switch (str) {
        case 'size_trip':
          return this.$t('Size strip');
        case 'artwork':
          return this.$t('Artwork / Diseño');
        case 'reference_image':
          return this.$t('Reference Image / Imagen de referencia');
        case 'plastic_bags':
          return this.$t('Plastic Bags / Balas de plastico');
        case 'folded':
          return this.$t('Folded / Doblado');
        case 'bags':
          return this.$t('Bags / Bolsas');
        case 'prepack_set':
          return this.$t('PrePack Set / Juegos prepack');
        case 'boxes':
          return this.$t('Boxes / Cajas');
        case 'labels_for_boxes':
          return this.$t('Labels For Boxes / Etiquetas para cajas');
        case 'transport_to_use':
          return this.$t('Transport To Use / Transporte a utilizar');
        case 'shipping_address':
          return this.$t('Shipping Address / Direccion del envio');
        default:
          return this.capitalizeAfterSpace(str);

      }
    },
    capitalizeFirstString(string) {
      if (!string) return "";
      return string.charAt(0).toUpperCase() + string.slice(1);
    },
    generateType(string) {
      if (!string) return "";
      return string.charAt(0).toUpperCase() + string.slice(1);
    },
    async getDetail(id) {
      try {
        this.isLoading = true;
        const response = await getDetail(id);
        this.packaging = response?.data?.data || {};
        this.instructions = this.packaging?.instructions?.sort(
            (a, b) =>
              this.sortByType.indexOf(a.type) - this.sortByType.indexOf(b.type)
          ) || [];
        this.isLoading = false;
      } catch (e) {
        this.isLoading = false;
        let message = this.$t('Error.');
        this.notification(message, 'error');
      }
    },
  },
}


</script>