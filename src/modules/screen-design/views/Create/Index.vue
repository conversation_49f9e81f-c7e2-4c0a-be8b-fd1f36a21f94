<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
      v-model="openDialogCreateDesign"
      :title="title"
      custom-class="el-dialog-custom"
      @close="resetData"
      :close-on-click-modal="false"
      width="500px"
    >
      <template #default>
        <div class="add-vendor">
          <el-form
            status-icon
            ref="createDesignForm"
            :model="design"
            :rules="formRules"
            :label-position="'top'"
            @submit.prevent="onSubmit('createDesignForm')"
            label-width="130px"
          >
            <el-form-item :label="$t('Client Name / Nombre del cliente')" prop="client_id">
              <el-select
                  filterable
                  v-model="design.client_id"
                  :placeholder="$t('Select Client')"
                  size="mini"
                  @change="onChangeClient"
                  class="!w-full"
              >
                <el-option
                    v-for="item in clients"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                >
                </el-option>
              </el-select>

              <div v-if="serverErrors && serverErrors['client_id']" class="text-danger  text-[12px] mt-[-5px]">{{serverErrors['client_id'][0]}}</div>

            </el-form-item>


            <el-form-item :label="$t('Company Name / Nombre de la empresa')" prop="company">
              <el-input class="" v-model="design.company"
                        disabled
                        :placeholder="$t('Input Company Name')"
              ></el-input>
            </el-form-item>

            <el-form-item :label="design.type === 'mockup' ? $t('Mockup ID / ID de mockup') : $t('Design ID / ID de diseño')" prop="design_id">
              <el-input class="" v-model="design.design_id"
                        :placeholder="$t('Input design ID')"
              ></el-input>
              <div v-if="serverErrors && serverErrors['design_id']" class="text-danger  absolute text-[12px] top-[25px]">{{serverErrors['design_id'][0]}}</div>

            </el-form-item>

            <el-form-item :label="design.type === 'mockup' ? $t('Mockup Name / Nombre de mockup') : $t('Design Name / Nombre del diseño')" prop="name">
              <el-input class="" v-model="design.name"
                        :placeholder="$t('Input name')"
              ></el-input>
              <div v-if="serverErrors && serverErrors['name']" class="text-danger absolute text-[12px] top-[25px]">{{serverErrors['name'][0]}}</div>

            </el-form-item>
            <el-form-item class="!mb-0" :label="design.type === 'mockup' ? $t('Mockup File / Archivo de mockup') : $t('Design File / Archivo de diseño')" prop="design_url">
              <el-input v-model="design.design_url" @input="handleInputUrl('print_file')"
                        :placeholder="$t('Input design')"
                        disabled
                        class="custom-input"
              ></el-input>
            </el-form-item>
            <el-form-item prop="design_file">
              <el-upload
                  name="image"
                  class="mt-4"
                  :accept="'image/png'"
                  :on-change="onChangePrintFile"
                  :auto-upload="false"
                  :multiple="false"
                  :show-file-list="false"
              >
                <el-button class="float-left mt-[6px]" type="primary">
                  <icon class="cursor-pointer !fill-none !stroke-current" style="fill: none;"  :data="iconUpload" />
                  Upload</el-button>
              </el-upload>
              <div v-if="serverErrors && serverErrors['design_url']" class="text-danger  absolute text-[12px] top-[25px]">{{serverErrors['design_url'][0]}}</div>

            </el-form-item>
          </el-form>
        </div>
        <hr>

      </template>
      <template #footer>
        <div class="dialog-footer mt-[-20px]">
          <el-button @click="resetData">{{ $t('Cancel / Cancelar') }}</el-button>
          <el-popconfirm
              confirm-button-text="Yes"
              cancel-button-text="No"
              :title="titleConfirm"
              @confirm="onSubmit('createDesignForm')"
          >
            <template #reference>
              <el-button
                  type="primary"
                  :disabled="isLoading"
                  :loading="isLoading"
              >{{ $t('Create / Crear') }}</el-button
              >
            </template>
          </el-popconfirm>

        </div>
      </template>
    </el-dialog>
  </div>
</template>
