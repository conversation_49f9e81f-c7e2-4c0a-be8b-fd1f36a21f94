import EventBus from "@/utilities/eventBus.js";
import { add, getLastItem } from "@/api/customDesign.js";
import {clone} from "ramda";
import moment from 'moment';

export default {
  name: "CreateDesign",
  components: {},
  props: {
    clients: {
      type: Array,
      default: []
    },
  },
  data() {
    return {
      design: this.defaultData(),
      isLoading: false,
      openDialogCreateDesign: false,
      title: this.$t('CREATE DESIGN / CREAR DISEÑO'),
      titleConfirm: this.$t('Are you sure you want to create this new design? / ¿Está seguro de que desea crear este nuevo diseño?'),
      serverErrors: {},
      formRules: {
        design_id: [
          { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
        ],
        company: [
          { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
        ],
        client_id: [
          { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
        ],
        design_url: [
          { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
        ],
        name: [
          { required: true, message: 'This field cannot be left blank.', trigger: 'blur' },
        ],

      },
    };
  },
  mounted() {},
  created() {
    EventBus.$on("showCreateDesign", (data = {}) => {
      this.design.type = data?.tab || 'design';
      this.title = this.design.type === 'design' ? this.$t('CREATE DESIGN / CREAR DISEÑO') : this.$t('CREATE MOCKUP / CREAR MOCKUP');
      this.titleConfirm = this.design.type === 'design' ? this.$t('Are you sure you want to create this new design? / ¿Está seguro de que desea crear este nuevo diseño?') : this.$t('Are you sure you want to create this new mockup? / ¿Está seguro de que desea crear este nuevo mockup?');
      this.openDialogCreateDesign = true;
      this.generateDesignId();

    });
  },
  watch: {
    'design.client_id': function (val) {
      let company = this.clients.find(item => item.id === val);
      this.design.company = company?.company ?? '';
    }
  },
  methods: {
    async getLastItem() {
      const response = await getLastItem();
      return response.data;
    },
    async generateDesignId() {
      let lastItem = await this.getLastItem();
      let designId = lastItem?.design_id ?? '';
      let count = designId.slice(6, 11);
      if (!count || designId.length > 12) {
        count = (parseInt(lastItem.id) + 1).toString().padStart(5, '0')
      }
      let year = moment().tz('America/Los_Angeles').format('YY');
      let type = this.design.type === 'design' ? 'DESN' : 'MOCK';
      designId = year + type + (parseInt(count) + 1).toString().padStart(5, '0') + 'A';
      this.design.design_id = designId;
    },
    onChangePrintFile(file) {
      if (file.raw) {
        this.design.design_file = file.raw;
        this.design.design_url = file.name;
      }
      this.$refs["createDesignForm"].validate();
    },
    handleInputUrl() {
      if (this.design.design_file && (this.design.design_url != this.design.design_file.name)) {
        this.design.design_file = null;
      }

    },
    resetData() {
      this.$refs["createDesignForm"].resetFields();
      this.design = this.defaultData();
      this.openDialogCreateDesign = false;
      this.serverErrors = {};
    },
    defaultData(){
      return {
        design_url: '',
        design_file: null,
        type: '',
        name: '',
        design_id: '',
        client_id: '',
      };
    },
    async onSubmit(formName) {
      const isValid = await this.$refs[formName].validate();
      if (!isValid) {
        return;
      }
      this.isLoading = true;
      try {
        let data = clone(this.design);
        data = await this.convertJsonToFormData(data);
        await add(data);
        this.isLoading = false;
        this.notification('Create ' + this.design.type + ' successfully.');
        this.openDialogCreateDesign = false;
        this.$emit("refresh");
      } catch (e) {
        this.isLoading = false;
        this.serverErrors = e?.response?.data?.errors;
        let message = e?.response?.data?.message ?? this.$t('Error.');
        this.notification(message, "error");
      }
    },
  },
};
