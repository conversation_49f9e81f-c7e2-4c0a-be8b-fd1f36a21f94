<style src="./Style.scss" lang="scss" ></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left"><h1>{{ $t('Library / Biblioteca') }}</h1></div>
      <div class="top-head-right">

      </div>
    </div>
    <div class="table-content">
      <div>
        <el-tabs
            class="el-tab-filter"
            v-model="tab"
            @tab-change="onChangeTab"
            type="card"
        >
          <el-tab-pane
              :label="item.key"
              :name="item.key"
              v-for="(item, index) in tabs"
              :key="index"
          >
            <template #label>
              <span class="custom-tabs-label">
                <span>{{ item.name }}</span>
              </span>
            </template>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="filter justify-between !mt-0">
        <div class="flex">
          <el-input class="!w-[200px] mr-2" :placeholder="$t('Search client name')" v-model="filter.client_name" @keyup.enter="onFilter" />
          <el-input class="!w-[200px] mr-2" :placeholder="searchTypeName" v-model="filter.name" @keyup.enter="onFilter" />
          <el-input class="!w-[200px] mr-2" :placeholder="$t('Search company name')" v-model="filter.company" @keyup.enter="onFilter" />

          <el-select
              v-model="filter.design_id"
              :placeholder="searchTypeID"
              filterable
              size="mini"
              @change="onFilter"
              class="mr-2 !w-[200px]"
          >
            <el-option
                v-for="item in designs"
                :key="item.design_id"
                :label="item.design_id"
                :value="item.design_id"
            >
            </el-option>
          </el-select>
          <div class="">
            <el-button type="primary" @click="onFilter">
                                <span class="icon-margin-right">
                                    <icon :data="iconFilter" /></span>{{ $t("Filter") }}
            </el-button>
            <el-button v-if="hasFilter" @click="resetFilter">
              <span class="icon-margin-right">{{ $t("Reset") }}</span>
            </el-button>
          </div>
        </div>
        <div class="align-right">
          <el-button type="primary" @click="createDesign">
            <span class="icon-margin-right">
            </span>{{ typeCreate }}
          </el-button>
        </div>
      </div>
      <el-table
          stripe
          size="small"
          :data="items"
          style="width: 100%"
          :max-height="maxHeight"
          v-loading="isLoading"
          element-loading-text="Loading..."
          header-row-class-name="table-header"
          :row-class-name="tableRowClassName"
          @sort-change="sortTable"
      >
        <el-table-column
            
            :label="$t('ID')" width="100">
          <template #default="scope">
            {{ scope.row.id }}
          </template>
        </el-table-column>
        <el-table-column
            class-name="break-words"
            width="150"
            :label="tab === 'mockup' ? $t('Mockup File / Archivo de mockup') : $t('Design File / Archivo de diseño')">
          <template #default="scope">
            <img
                @click="openLink(generateLink(scope.row.design_url))"
                class="h-[60px] w-auto cursor-pointer m-auto"
                :src="generateLink(scope.row.design_url)"
                alt="">

          </template>
        </el-table-column>
        <el-table-column
            class-name="break-words"

            :label="tab === 'mockup' ? $t('Mockup ID / ID de mockup') : $t('Design ID / ID de diseño')" min-width="150">
          <template #default="scope">
            {{ scope.row.design_id }}
          </template>
        </el-table-column>
        <el-table-column
            class-name="break-words"

            :label="tab === 'mockup' ? $t('Mockup Name / Nombre de mockup') : $t('Design Name / Nombre del diseño')" min-width="150">
          <template #default="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>

        <el-table-column
            class-name="break-words"

            :label="$t('Client Name / Nombre del cliente')" min-width="150">
          <template #default="scope">
            {{ scope.row?.client_name }}
          </template>
        </el-table-column>

        <el-table-column
            class-name="break-words"

            :label="$t('Company Name / Nombre de la empresa')" min-width="150">
          <template #default="scope">
            {{ scope.row?.company }}
          </template>
        </el-table-column>

        <el-table-column
            sortable
            prop="created_at"
            class-name="break-words"

            :label="$t('Created Date / Fecha de creaciÃ³n')" min-width="200">
          <template #default="scope">
            {{ convertTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column
            class-name="break-words"

            :label="$t('Created By / Creado por')" min-width="200">
          <template #default="scope">
            {{ scope.row?.employee_created?.username }}
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom">
        <div class="total">{{$t('Total:')}} {{ items.length ? formatNumber(total) : 0 }}</div>
        <el-pagination
            :disabled="isLoading"
            background
            layout="prev, pager, next"
            :page-size="filter.limit"
            :total="total"
            @current-change="changePage"
            v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
              v-model="filter.limit"
              :placeholder="$t('Select')"
              size="mini"
              @change="onFilter"
          >
            <el-option
                v-for="item in limits"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>

    <CreateDesign :clients="clients" @refresh="fetchData"/>
  </div>
</template>
