
import { list, fetchAll as fetchAllDesign } from "@/api/customDesign.js";
import { fetchAll } from "@/api/screenClient.js";
import {getList as getListSide} from "@/api/productPrintSide";
import EventBus from "@/utilities/eventBus.js";
import filterMixin from "@/mixins/filter";
import dateMixin from "@/mixins/date.js";
import { STORAGE_URL , S3_URL } from "@/utilities/constants";

import {equals} from "ramda";
import CreateDesign from "@/modules/screen-design/views/Create/Index.vue"

export default {
  name: "CustomerDesign",
  mixins: [filterMixin, dateMixin],
  components: {
    CreateDesign,
  },
  data() {
    return {
      items: [],
      typeCreate: this.$route.query.tab == "design" ? "Create Design / Crear diseño" : "Create Mockup / Crear mockup",
      isLoading: false,
      filter: this.setDefaultFilter(),
      printSides: [],
      clients: [],
      designs: [],
      allDesigns: [],
      tab: this.$route.query.tab || "design",
      searchTypeName: 'Search design name',
      searchTypeID: 'Design ID',
      searchMockupName: 'Search mockup name',
      mockupID: 'Mockup ID / ID de mockup',
      designID: 'Design ID / ID de diseño',
      tabs: [
        {
          name: 'Design / Diseño',
          key: 'design',
        },
        {
          name: 'Mockup',
          key: 'mockup',
        }

      ],
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 230);
    },
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },
  },
  watch: {
    tab(val){
        this.typeCreate = val === 'design' ? "Create Design / Crear diseño" : "Create Mockup / Crear mockup";
        this.searchTypeName = val === 'design' ? 'Search design name' : 'Search mockup name';
        this.searchTypeID = val === 'design' ? 'Design ID' : 'Mockup ID';
        this.filter = this.setDefaultFilter();
        this.filter.tab = val;
        this.designs = this.allDesigns.filter(item => item.type === val);

      this.fetchDesign();
    },
  },
  beforeUnmount() {
  },
  mounted() {
    this.typeCreate = this.tab === 'design' ? "Create Design / Crear diseño" : "Create Mockup / Crear mockup";
    this.fetchAllDesign();
    this.getProductPrintSide();
    this.fetchData();
    this.getClient();
  },
  methods: {
    sortTable(data) {
      let sortColumn = '';
      let sortBy = '';
      if (data.prop && data.order) {
        sortColumn = data.prop;

        if (data.order === 'ascending') {
          sortBy = 'ASC';
        } else if (data.order === 'descending') {
          sortBy = 'DESC';
        }
      }

      this.filter.sort_column = sortColumn;
      this.filter.sort_by = sortBy;

      this.setRouteParam();

      this.$nextTick(() => {
        this.fetchData();
      });
    },
    resetFilter() {
        this.filter = this.setDefaultFilter();
        this.fetchDesign();
    },
    async getClient(){
      const res = await fetchAll();
      this.clients = res.data || [];
    },
    async fetchAllDesign(){
      const res = await fetchAllDesign();
      this.allDesigns = res.data || [];
      this.designs = this.allDesigns.filter(item => item.type === (this.$route.query.tab || "design"));
    },
    generateLink(design_url){
        return `${S3_URL}/${design_url}`;
    },
    showLink(design_url){
      let url = `${STORAGE_URL}/${design_url}`;
      if (url.length < 20) {
        return url;
      }
      return url.slice(0, 20) + '...';
    },
    openLink(design_url){
        return window.open(design_url, '_blank');
    },
    async getProductPrintSide() {
      const res = await getListSide();
      this.printSides = res.data || [];
    },
    setDefaultFilter() {
      let params = {
        limit: 25,
        page: 1,
        name: "",
        client_name: "",
        company: "",
        client_id: "",
        sort_by: "desc",
        sort_column: "created_at",
        tab: this.filter?.tab ? this.filter.tab : "design",
      };
      return params;
    },
    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);
      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }
      filter.page = +filter.page || 1;
      return filter;
    },
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchDesign();
      });
    },
    async fetchData() {
      this.filter = this.getRouteParam();
      this.fetchDesign();
    },
    async fetchDesign() {
      this.isLoading = true;
      this.setRouteParam();
      const res = await list(this.filter);
      this.isLoading = false;
      const data = res.data || [];
      this.total = data.total;
      this.items = data.data;
    },
    tableRowClassName(data) {
      return data.row.is_deleted ? "is-delete" : "";
    },
    onFilter(item = "") {
      this.filter.page = 1;
      this.$nextTick(() => {
        this.fetchDesign();
        if (item) {
          this.$refs[item].handleClose();
        }
      });
    },
    createDesign(){
      EventBus.$emit("showCreateDesign", {
        tab: this.tab
      });
    },
  },
};
