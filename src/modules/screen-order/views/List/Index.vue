<style src="./Style.scss" lang="scss"></style>
<script src="./Script.js"></script>
<template>
  <div v-loading="isLoading">
    <div class="top-head">
      <div class="top-head-left">

      </div>
      <div class="top-head-right">
        <el-button :type="'success'" @click="openDialogImport">
          {{ $t('Import / Importar') }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <div>
        <el-tabs class="el-tab-filter" v-model="filterName" @tab-click="onChangeFilter" type="card">
          <el-tab-pane :label="item.name" :name="item.key" v-for="(item, index) in tabs" :key="index"
                       :disabled="filterName == item.key">
            <template #label>
                <span class="custom-tabs-label">
                  <span>
                    {{ item.label }}
                    <span v-if="item.count > 0" class="text-white bg-[#FF4D4F] rounded-full px-2 py-1 text-[11px]">
                      {{ formatNum(item.count) }}
                    </span>
                  </span>
                </span>
            </template>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="filter">
        <div class="label">{{ $t('Filter By:') }}</div>

        <div class="filter-item">
          <el-dropdown ref="external_number" trigger="click" class="el-dropdown-filter-item"
                       :class="{ 'is-active': hasChangeFilterByItem('external_number') }">
              <span class="el-dropdown-link">
                <template v-if="hasChangeFilterByItem('external_number')">
                  <el-tooltip effect="dark" :content="$t('Reference Number')" placement="top-start">
                    <span>
                      {{ filter?.external_number?.trim() || $t(' Reference Number') }}
                    </span>
                  </el-tooltip>
                </template>
                <template v-else>{{ $t(' Reference Number') }}</template>
                <span class="icon">
                  <icon :data="iconChevronDown" />
                </span>
              </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input :placeholder="$t('Enter Reference Number')" class="search" v-model="filter.external_number"
                          @keydown.enter="onFilter('external_number')" clearable @clear="clearFilterItem('external_number')" />
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown trigger="click" class="el-dropdown-filter-item"
                         :class="{ 'is-active': hasChangeFilterByItem('client_id') }">
                <span class="el-dropdown-link">
                  <template v-if="hasChangeFilterByItem('client_id')">
                    <el-tooltip effect="dark" :content="$t('Client')" placement="top-start">
                      <span>{{ getClientById(filter.client_id) }}</span>
                    </el-tooltip>
                  </template>
                  <template v-else>{{ $t('Client / Cliente') }}</template>
                  <span class="icon">
                    <icon :data="iconChevronDown" />
                  </span>
                </span>
              <template #dropdown>
                <div class="el-dropdown-menu-filter-item">
                  <el-select filterable v-model="filter.client_id" :placeholder="$t('Select client')" @change="onFilter">
                    <el-option v-for="item in clients" :key="item.id" :label="item.name"
                               :value="String(item.id)">
                    </el-option>
                  </el-select>
                </div>
              </template>
            </el-dropdown>
        </div>

        <div class="filter-item">
          <el-dropdown ref="order_number" trigger="click" class="el-dropdown-filter-item"
                       :class="{ 'is-active': hasChangeFilterByItem('order_number') }">
              <span class="el-dropdown-link">
                <template v-if="hasChangeFilterByItem('order_number')">
                  <el-tooltip effect="dark" :content="$t('Order Number / Número de pedido')" placement="top-start">
                    <span>
                      {{ filter?.external_number?.trim() || $t(' Order Number / Número de pedido') }}
                    </span>
                  </el-tooltip>
                </template>
                <template v-else>{{ $t(' Order Number / Número de pedido') }}</template>
                <span class="icon">
                  <icon :data="iconChevronDown" />
                </span>
              </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input :placeholder="$t('Enter Order Number')" class="search" v-model="filter.order_number"
                          @keydown.enter="onFilter('order_number')" clearable @clear="clearFilterItem('order_number')" />
              </div>
            </template>
          </el-dropdown>
        </div>


        <div class="filter-item">
          <el-dropdown trigger="click" class="el-dropdown-filter-item"
                       :class="{ 'is-active': hasChangeFilterByItem('order_type') }">
                      <span class="el-dropdown-link">
                        <template v-if="hasChangeFilterByItem('order_type')">
                          <el-tooltip effect="dark" :content="$t('Order Type / Tipo de pedido')" placement="top-start">
                            <span>{{ getOrderType(filter.order_type) }}</span>
                          </el-tooltip>
                        </template>
                        <template v-else>{{ $t('Order Type / Tipo de pedido') }}</template>
                        <span class="icon">
                          <icon :data="iconChevronDown" />
                        </span>
                      </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-select filterable v-model="filter.order_type" :placeholder="$t('Select Order Type')" @change="onFilter">
                  <el-option v-for="item in orderTypes" :key="item.value" :label="item.name"
                             :value="String(item.value)">
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item">
          <el-dropdown trigger="click" class="el-dropdown-filter-item"
                       :class="{ 'is-active': hasChangeFilterByItem('order_status') }">
                      <span class="el-dropdown-link">
                        <template v-if="hasChangeFilterByItem('order_status')">
                          <el-tooltip effect="dark" :content="$t('Order Status / Estado del pedido')" placement="top-start">
                            <span>{{ getOrderStatus(filter.order_status) }}</span>
                          </el-tooltip>
                        </template>
                        <template v-else>{{ $t('Order Status / Estado del pedido') }}</template>
                        <span class="icon">
                          <icon :data="iconChevronDown" />
                        </span>
                      </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-select filterable v-model="filter.order_status" :placeholder="$t('Select Order Status')" @change="changeOrderStatus">
                  <el-option v-for="item in orderStatus" :key="item.value" :label="item.name"
                             :value="String(item.value)">
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-dropdown>
        </div>


        <div class="filter-item">
          <el-dropdown trigger="click" class="el-dropdown-filter-item"
                       :class="{ 'is-active': orderDate && orderDate.length }">
              <span class="el-dropdown-link">
                <template v-if="orderDate && orderDate.length">
                  <el-tooltip effect="dark" :content="$t('Order Date / Fecha de pedido')" placement="top-start">
                    <span>
                      {{ templateDateRange(orderDate[0], orderDate[1]) }}</span>
                  </el-tooltip>
                </template>
                <template v-else> {{ $t('Order Date / Fecha de pedido') }}</template>
                <span class="icon">
                  <icon :data="iconChevronDown" />
                </span>
              </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-date-picker format="YYYY-MM-DD" v-model="orderDate" type="daterange" range-separator="To"
                                :start-placeholder="$t('Start date')" :end-placeholder="$t('End date')" @change="onChangeDate">
                </el-date-picker>
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item" v-if="hasFilter">
          <el-link type="danger" @click="onClearFilter" :underline="false">
            {{ $t('Clear') }}
          </el-link>
        </div>
      </div>

      <el-table
          stripe size="small"
          :data="orders"
          style="width: 100%"
          :max-height="maxHeight"
          element-loading-text="Loading..."
          :row-class-name="tableRowClassName"
          header-row-class-name="table-header"
          @sort-change="sortTable"
      >
        <el-table-column
            class-name="break-words"

            :label="$t('Customer\'s Reference Number / PO')" min-width="150">
          <template #default="scope">
            <div class="flex justify-between content-center items-center">
              <el-link :underline="false" type="primary" @click="showDetails(scope.row)">{{ scope.row.external_number
                }}</el-link>
<!--              <icon v-if="scope.row.is_error" class="!w-[13px] !h-[13px] !text-[#FF4747]" :data="iconError2" />-->
            </div>
          </template>
        </el-table-column>
        <el-table-column
            class-name="break-words"
            :label="$t('Client / Cliente')" min-width="100">
          <template #default="scope">
            {{ scope.row.client?.name }}
          </template>
        </el-table-column>
        <el-table-column
            class-name="break-words"
            :label="$t('Order Number / Número de pedido')" min-width="150">
          <template #default="scope">
            {{ scope.row.order_number }}
          </template>
        </el-table-column>

        <el-table-column

            :label="$t('Quantity / Cantidad')" min-width="100">
          <template #default="scope">
            {{ scope.row.order_quantity }}
          </template>
        </el-table-column>
        <el-table-column
            class-name="break-words"
            prop="created_at"
            sortable="custom"
            :label="$t('Order Date / Fecha de pedido')"
            min-width="150">
          <template #default="scope">
            {{ convertTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column
            class-name="break-words"
            :label="$t('Shipped By / Enviado por')" min-width="150">
          <template #default="scope">
            {{ scope.row.shipped_at }}
          </template>
        </el-table-column>

        <el-table-column
            class-name="break-words"
            :label="$t('Order Type / Tipo de pedido')" min-width="150">
          <template #default="scope">
            <el-tag :type="generateColor(scope.row?.order_type)">{{ 'For ' + capitalizeFirstString(scope.row?.order_type) }}</el-tag>

          </template>
        </el-table-column>

        <el-table-column
            class-name="break-words"
            :label="$t('Order Status / Estado del pedido')" min-width="150">
          <template #default="scope">
            <el-tag v-if="scope.row?.order_status" :type="generateColorOrderStatus(scope.row?.order_status)">{{ getStatus(scope.row?.order_status) }}</el-tag>

          </template>
        </el-table-column>
      </el-table>
      <div class="bottom">
        <div class="total">
          {{ $t('Total:') }} {{ orders.length ? formatNumber(total) : 0 }}
        </div>
        <el-pagination
            :disabled="isLoading"
            background
            layout="prev, pager, next"
            :page-size="filter.limit"
            :total="total"
            @current-change="changePage"
            v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
              v-model="filter.limit"
              :placeholder="$t('Select')"
              size="mini"
              @change="onFilter('limit')"
          >
            <el-option
                v-for="item in limits"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
    <ImportScreenOrder @refresh="fetchOrder"/>
  </div>
</template>