import dateMixin from '@/mixins/date.js';
import numberMixin from '@/mixins/formatNumber.js';
import filterMixin from '@/mixins/filter';
import EventBus from "@/utilities/eventBus";
import {fetchAll} from "@/api/screenClient";
import {list, getCountByStatus} from "@/api/screenOrder";
import ImportScreenOrder from '@/modules/screen-order/views/component/Import.vue';
import Detail from '@/modules/screen-order/views/component/Detail.vue';

export default {
    name: "ScreenOrderList",
    mixins: [dateMixin, numberMixin, filterMixin],
    components: {
        ImportScreenOrder,
        Detail
    },
    data() {
        return {
            items: [],
            isLoading: false,
            filter: this.setDefaultFilter(),
            filterName: "all",
            clients: [],
            orderDate: '',
            orders: [],
            total: 0,
            orderTypes: [
                {
                    value: "all",
                    name: "All",
                },
                {
                    value: "store",
                    name: "Store",
                },
                {
                    value: "ecom",
                    name: "Ecom",
                }
            ],
            orderStatus: [
                {
                    value: "new_order",
                    name: "New Order / Nuevo Pedido",
                    color: "primary",
                },
                {
                    value: "in_production",
                    name: "In Production / En Producción",
                    color: "warning",
                },
                {
                    value: "ready_to_ship",
                    name: "Ready To Ship / Listo para Envío",
                    color: "info",
                },
                {
                    value: "completed",
                    name: "Completed / Completado",
                    color: "success",

                },
                {
                    value: "cancelled",
                    name: "Cancelled / Cancelado",
                    color: "danger",
                }
            ],
            tabs: [
                {
                    key: "all",
                    label: "All",
                },
                {
                    key: "new_order",
                    label: "New Order / Nuevo Pedido",
                },
                {
                    key: "in_production",
                    label: "In Production / En Producción",
                },
                {
                    key: "ready_to_ship",
                    label: "Ready To Ship / Listo para Envío",
                },
                {
                    key: "completed",
                    label: "Completed / Completado",
                },
                {
                    key: "cancelled",
                    label: "Cancelled / Cancelado",
                }

            ],
            countOrders: []
        }
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 189);
        }
    },
    mounted() {
        this.filter = this.getRouteParam();
        this.getCountByStatus();
        this.getClient();
        this.fetchOrder();
    },
    methods: {
        changeOrderStatus(status) {
            this.filterName = status;
            this.filter.filter_name = status;
            this.fetchOrder();
        },
        changePage(page) {
            this.filter.page = page;
            this.$nextTick(() => {
                this.fetchOrder();
            });
        },
        async getCountByStatus() {
            let total = 0;
            let promises = [];
            for (const status of this.orderStatus) {
                promises.push(getCountByStatus({ order_status: status.value }));
            }
            const res = await Promise.all(promises);
            res.forEach((item, index) => {
                total += (item.data ?? 0);
                this.tabs[index + 1].count = (item.data ?? 0);
            });
            this.tabs.find((item) => item.key === 'all').count = total;
        },
        openDialogImport() {
            EventBus.$emit("showModalImportScreenOrders");

        },
        showDetails(order) {
            this.$router.push({ name: 'screen_order_detail',
                params: { id: order.id }
            })
            // EventBus.$emit("showScreenOrderDetails", order);
        },
        capitalizeFirstString(string) {
            if (!string) return "";
            return string.charAt(0).toUpperCase() + string.slice(1);
        },
        getStatus(orderStatus) {
            let status = this.orderStatus.find((item) => item.value === orderStatus);
            return status ? status.name : '';
        },
        sortTable(data) {
            let sortColumn = '';
            let sortBy = '';
            if (data.prop && data.order) {
                sortColumn = data.prop;

                if (data.order === 'ascending') {
                    sortBy = 'ASC';
                } else if (data.order === 'descending') {
                    sortBy = 'DESC';
                }
            }

            this.filter.sort_column = sortColumn;
            this.filter.sort_by = sortBy;

            this.setRouteParam();

            this.$nextTick(() => {
                this.fetchOrder();
            });
        },
        generateColor(type) {
            switch (type) {
                case 'store':
                    return 'primary';
                default:
                    return 'success';

            }
        },
        generateColorOrderStatus(orderStatus) {
            let status = this.orderStatus.find((item) => item.value === orderStatus);
            return status ? status.color : 'primary';
        },
        onClearFilter() {
            this.filterName = 'all';
            this.filter = { ...this.setDefaultFilter() };
            this.orderDate = '';
            this.orderType = [];
            this.$nextTick(() => {
                this.fetchOrder();
            });
        },
        onChangeDate() {
            if (this.orderDate && this.orderDate.length) {
                this.filter.order_date_start = this.formatDate(
                    this.orderDate[0],
                    false
                );
                this.filter.order_date_end = this.formatDate(this.orderDate[1], false);
            } else {
                this.filter.order_date_start = '';
                this.filter.order_date_end = '';
            }

            this.onFilter();
        },

        setRouteParam() {
            const params = this.filter;
            this.$router.replace({ name: 'screen_orders', query: params });
        },
        async fetchOrder() {
            this.isLoading = true;
            const res = await list(this.filter);
            this.setRouteParam();
            this.orders = res.data?.data || [];
            this.total = res.data.total;

            this.isLoading = false;
        },
        getClientById(id) {
            const selectItem = this.clients.find((item) => +item.id === +id);

            return (selectItem && selectItem.name) || '';
        },

        getOrderType(value) {
            const selectItem = this.orderTypes.find((item) => item.value === value);

            return (selectItem && selectItem.name) || '';
        },

        getOrderStatus(value) {
            const selectItem = this.orderStatus.find((item) => item.value === value);

            return (selectItem && selectItem.name) || '';
        },
        async getClient(){
            const res = await fetchAll();
            this.clients = res.data || [];
        },
        hasChangeFilterByItem(name) {
            const query = this.$route.query;

            if (query[name] && query[name].trim()) {
                return true;
            }
            return false;
        },
        clearFilterItem(item) {
            this.filter[item] = '';

            if (this.$refs[item]) {
                this.$refs[item].handleClose();
            }

            this.onFilter();
        },
        onFilter(item = '', keepCurrentPage = false) {
            if (!keepCurrentPage) {
                this.filter.page = 1;
            }

            this.$nextTick(() => {
                this.fetchOrder();

                if (item && this.$refs[item]) {
                    this.$refs[item].handleClose();
                }
            });
        },
        setDefaultFilter() {
            let params = {
                limit: 25,
                page: 1,
                order_date_start: '',
                order_date_end: '',
                order_status: '',
                filter_name: 'all',
                client_id: '',
                external_number: '',
                order_number: '',
                order_type: '',

            };

            return params;
        },
        onChangeFilter(tab = null) {
            if (tab?.props) {
                this.filterName = tab.props.name;
            }
            this.filter.filter_name = this.filterName;
            if (this.filterName !== 'all') {
                this.filter.order_status = this.filter.filter_name;
            } else {
                this.filter.order_status = '';
            }
            this.fetchOrder();
        }
    }
}