 <template>
  <div class="" v-loading="isLoading" v-if="order.external_number">
    <div class="text-[20px] font-bold flex items-center">
          <span class="font-bold">
            {{ 'PO ' + order?.external_number + ' - ' + order.client?.name }}
          </span>
      <span class="border-[#1A73E8] border rounded !text-[14px] ml-4 px-6 py-[4px] bg-[#D9E9FF] font-normal mr-2	">
            {{ 'For ' + capitalizeFirstString(order.order_type)}}
          </span>
<!--      <el-tag size="large">Large</el-tag>-->

      <span v-if="!editKeys.order_status" class="border-[#747AF8] flex w-fit border rounded !text-[14px] px-2  my-auto py-[4px] bg-[#E2E3F9] font-normal	text-[#747AF8]">
            {{ getStatus(order.order_status) }}
        <!--            <icon class="cursor-pointer" :data="iconEditScreen"/>-->
            <div class="ml-2 cursor-pointer my-auto]" @click="showUpdate('order_status')">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="19" viewBox="0 0 18 19" fill="none">
                <path d="M9.58275 4.16684L12.8902 7.46534M2.8425 16.4376H15.1575M3.108 13.0513L3.47775 10.4826C3.49764 10.3148 3.57534 10.1591 3.6975 10.0423L10.9462 2.79434C11.0394 2.69933 11.1556 2.63 11.2834 2.59307C11.4113 2.55615 11.5465 2.55288 11.676 2.58359C12.3448 2.76113 12.9529 3.11675 13.4355 3.61259C13.9346 4.09413 14.2934 4.70233 14.4735 5.37209C14.5013 5.50173 14.4965 5.63623 14.4596 5.76358C14.4227 5.89092 14.3548 6.00714 14.262 6.10184L7.014 13.3498C6.88903 13.4653 6.73321 13.5419 6.5655 13.5703L3.98775 13.9393C3.86768 13.9556 3.74548 13.9437 3.63077 13.9047C3.51606 13.8656 3.41197 13.8005 3.3267 13.7145C3.24142 13.6284 3.17729 13.5237 3.13934 13.4086C3.10139 13.2936 3.09066 13.1713 3.108 13.0513Z" stroke="black" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </span>
      <div
          v-else-if="editKeys.order_status"
          class="ml-[24px]"
      >
        <el-select
            v-model="dataEdit.order_status"
        >
          <el-option
              v-for="item in orderStatus"
              :key="item.value"
              :label="item.name"
              :value="item.value"
              :disabled="checkIsValidStatus(item.value)"
          >
          </el-option>
        </el-select>
        <div class="float-right ml-[12px]">
          <el-button
              @click="editKeys.order_status = false" class="!text-[14px]">
            {{ $t('Cancel') }}
          </el-button>
          <el-button
              @click="updateOrder('order_status')"
              type="primary"
              class=" !text-[14px]">
            {{ $t('Save') }}
          </el-button>
        </div>
      </div>
    </div>
    <div>
      <div class="mb-[10px]">
        <p class="text-[14px] ]">
          {{ $t('Order Number: ' + (order.order_number || '') + ' - ' + $t('Order Date: ')) + convertTime(order.created_at, 'YYYY-MM-DD') }}
        </p>
      </div>
      <el-row  :gutter="20">
        <el-col :span="18" >
          <el-row :gutter="20" class="rounded-lg border mb-[24px] !mx-0">
            <el-col :span="6" class="p-2  !pr-[0px]">
              <div class="border-r  text-[14px] h-full">
                <div class="mb-1 flex justify-space-between relative">
                      <span class="font-bold">
                        {{ $t('Target Date') }}
                      </span>
                  <div class="cursor-pointer absolute top-[3px] right-[10px]" @click="showUpdate('shipped_at')">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="19" viewBox="0 0 18 19" fill="none">
                      <path d="M9.58275 4.16684L12.8902 7.46534M2.8425 16.4376H15.1575M3.108 13.0513L3.47775 10.4826C3.49764 10.3148 3.57534 10.1591 3.6975 10.0423L10.9462 2.79434C11.0394 2.69933 11.1556 2.63 11.2834 2.59307C11.4113 2.55615 11.5465 2.55288 11.676 2.58359C12.3448 2.76113 12.9529 3.11675 13.4355 3.61259C13.9346 4.09413 14.2934 4.70233 14.4735 5.37209C14.5013 5.50173 14.4965 5.63623 14.4596 5.76358C14.4227 5.89092 14.3548 6.00714 14.262 6.10184L7.014 13.3498C6.88903 13.4653 6.73321 13.5419 6.5655 13.5703L3.98775 13.9393C3.86768 13.9556 3.74548 13.9437 3.63077 13.9047C3.51606 13.8656 3.41197 13.8005 3.3267 13.7145C3.24142 13.6284 3.17729 13.5237 3.13934 13.4086C3.10139 13.2936 3.09066 13.1713 3.108 13.0513Z" stroke="black" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </div>
                </div>
                <p class="flex justify-space-between" v-if="!editKeys.shipped_at">
                      <span class="font-bold">
                        {{ $t('Shipped By ') }}
                      </span>
                  <span class="mr-2">
                        {{ convertTime(order.shipped_at, 'YYYY-MM-DD') }}
                      </span>
                </p>
                <div
                    v-else="editKeys.shipped_at"
                >
                  <span class="font-bold">{{ $t('Shipped By ')}}</span>
                  <div>
                    <el-date-picker
                        v-model="dataEdit.shipped_at"
                        type="date"
                        placeholder="Select date"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        class="!w-11/12 mt-2"
                        :clearable="false"
                    />

                    <div class="float-right mr-4">
                      <el-button size="small" @click="editKeys.shipped_at = false" class="!text-[14px] !mt-2">
                        {{ $t('Cancel') }}
                      </el-button>
                      <el-button
                          @click="updateOrder('shipped_at')"
                          type="primary"
                          size="small"
                          class=" !text-[14px] !mt-2">
                        {{ $t('Save') }}
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="6" class="p-2 !pr-[0px]">
              <div class="border-r h-full relative">
                <p class="font-bold text-[14px] mb-1 ">
                  {{ $t('Shipping Address') }}
                </p>
                <div class="cursor-pointer absolute top-[3px] right-[10px]" @click="showUpdate('shipping_address')">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="19" viewBox="0 0 18 19" fill="none">
                    <path d="M9.58275 4.16684L12.8902 7.46534M2.8425 16.4376H15.1575M3.108 13.0513L3.47775 10.4826C3.49764 10.3148 3.57534 10.1591 3.6975 10.0423L10.9462 2.79434C11.0394 2.69933 11.1556 2.63 11.2834 2.59307C11.4113 2.55615 11.5465 2.55288 11.676 2.58359C12.3448 2.76113 12.9529 3.11675 13.4355 3.61259C13.9346 4.09413 14.2934 4.70233 14.4735 5.37209C14.5013 5.50173 14.4965 5.63623 14.4596 5.76358C14.4227 5.89092 14.3548 6.00714 14.262 6.10184L7.014 13.3498C6.88903 13.4653 6.73321 13.5419 6.5655 13.5703L3.98775 13.9393C3.86768 13.9556 3.74548 13.9437 3.63077 13.9047C3.51606 13.8656 3.41197 13.8005 3.3267 13.7145C3.24142 13.6284 3.17729 13.5237 3.13934 13.4086C3.10139 13.2936 3.09066 13.1713 3.108 13.0513Z" stroke="black" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <p>
                  {{ order?.client?.name}}
                </p>
                <div v-if="!editKeys.shipping_address" class="flex">
                  {{ address.street1 || "" }}, <template v-if="address.street2" >{{ address.street2 || "" }}, </template>
                  {{ address.city || "" }}, <br /> {{ address.state || "" }},
                  {{ address.zipcode || "" }},
                  {{ address.country || "" }}
                </div>
                <el-form
                    v-else
                    status-icon
                    label-width="80px"
                    :label-position="'left'"
                    ref="addressForm"
                    :model="address"
                    :rules="addressRules"
                    :show-message="false"
                    class="mr-2 mt-2"
                >
                  <el-form-item :class="{ 'mb-0': !isValidateAddress }">
                    <div class="text-right w-full">
                      <el-link
                          class="el-link-edit"
                          :underline="false"
                          type="primary"
                          @click="showValidateAddress"
                      >{{ $t('Paste US Address') }}</el-link
                      >
                    </div>
                    <el-input
                        v-if="isValidateAddress"
                        v-model="validateAddress"
                        :placeholder="$t('Paste or type address here')"
                        type="textarea"
                        @keyup.enter="parseAddress"
                        ref="validateAddress"
                    ></el-input>
                  </el-form-item>
                  <el-form-item :label="$t('Country')" prop="country_id">
                    <el-select
                        :placeholder="$t('Select country')"
                        class="el-select-country w-full"
                        v-model="address.country_id"
                        filterable
                        @change="changeCountry"
                    >
                      <el-option
                          v-for="item in countries"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item :label="$t('Address')" prop="street1">
                    <el-input
                        v-model="address.street1"
                        :placeholder="$t('Address1')"
                    ></el-input>
                    <el-input
                        v-model="address.street2"
                        :placeholder="$t('Address2')"
                        class="mt-3"
                    ></el-input>
                  </el-form-item>

                  <el-form-item :label="$t('City')" prop="city">
                    <el-input v-model="address.city" :placeholder="$t('City')"></el-input>
                  </el-form-item>

                  <div class="flex justify-between">
                    <el-form-item class="w-full" :label="$t('State')" prop="state_id">
                      <el-select
                          :placeholder="$t('Select state')"
                          class="el-select-state w-full"
                          v-model="address.state_id"
                          @change="changeState"
                          filterable
                      >
                        <el-option
                            v-for="item in states"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>

                  </div>
                  <div>
                    <el-form-item
                        :label="$t('Zip code')"
                        prop="zipcode"
                        placeholder="Zip code"
                    >
                      <el-input
                          v-model="address.zipcode"
                          :placeholder="$t('Zip code')"
                      ></el-input>
                    </el-form-item>
                  </div>
                  <div class="flex justify-end">
                    <div>
                      <el-button
                          size="small"
                          @click="editKeys.shipping_address = false">{{ $t("Cancel") }}</el-button>
                      <el-button
                          size="small"
                          type="primary"
                          @click="onSubmit('addressForm')"
                          :disabled="isLoading"
                          :loading="isLoading"
                      >{{ $t("Save") }}</el-button
                      >
                    </div>
                  </div>
                </el-form>
              </div>
            </el-col>
            <el-col :span="6" class="p-2 !pr-[0px]">
              <div class="h-full border-r relative">
                <div class="cursor-pointer absolute top-[3px] right-[10px]" @click="showUpdate('client_note')">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="19" viewBox="0 0 18 19" fill="none">
                    <path d="M9.58275 4.16684L12.8902 7.46534M2.8425 16.4376H15.1575M3.108 13.0513L3.47775 10.4826C3.49764 10.3148 3.57534 10.1591 3.6975 10.0423L10.9462 2.79434C11.0394 2.69933 11.1556 2.63 11.2834 2.59307C11.4113 2.55615 11.5465 2.55288 11.676 2.58359C12.3448 2.76113 12.9529 3.11675 13.4355 3.61259C13.9346 4.09413 14.2934 4.70233 14.4735 5.37209C14.5013 5.50173 14.4965 5.63623 14.4596 5.76358C14.4227 5.89092 14.3548 6.00714 14.262 6.10184L7.014 13.3498C6.88903 13.4653 6.73321 13.5419 6.5655 13.5703L3.98775 13.9393C3.86768 13.9556 3.74548 13.9437 3.63077 13.9047C3.51606 13.8656 3.41197 13.8005 3.3267 13.7145C3.24142 13.6284 3.17729 13.5237 3.13934 13.4086C3.10139 13.2936 3.09066 13.1713 3.108 13.0513Z" stroke="black" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <div class=" font-bold text-[14px] mb-1">
                  {{ $t('Client Comments') }}
                </div>
                <p v-if="!editKeys.client_note">
                  {{ order.client_note || "" }}
                </p>
                <div v-else>
                  <el-input
                      v-model="dataEdit.client_note"
                      class="w-11/12 pr-2"
                      :rows="2"
                      type="textarea"
                      placeholder="Input Client Comments"
                  />

                  <div class="float-right mr-2">
                    <el-button size="small" @click="editKeys.client_note = false" class="!text-[14px] !mt-2">
                      {{ $t('Cancel') }}
                    </el-button>
                    <el-button
                        @click="updateOrder('client_note')"
                        type="primary"
                        size="small"
                        class=" !text-[14px] !mt-2">
                      {{ $t('Save') }}
                    </el-button>

                  </div>


                </div>
              </div>
            </el-col>
            <el-col :span="6" class="p-2">
              <div class="text-[14px] flex relative">
                <div class="cursor-pointer absolute top-[3px] right-[0px]" @click="showUpdate('buyer_name')">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="19" viewBox="0 0 18 19" fill="none">
                    <path d="M9.58275 4.16684L12.8902 7.46534M2.8425 16.4376H15.1575M3.108 13.0513L3.47775 10.4826C3.49764 10.3148 3.57534 10.1591 3.6975 10.0423L10.9462 2.79434C11.0394 2.69933 11.1556 2.63 11.2834 2.59307C11.4113 2.55615 11.5465 2.55288 11.676 2.58359C12.3448 2.76113 12.9529 3.11675 13.4355 3.61259C13.9346 4.09413 14.2934 4.70233 14.4735 5.37209C14.5013 5.50173 14.4965 5.63623 14.4596 5.76358C14.4227 5.89092 14.3548 6.00714 14.262 6.10184L7.014 13.3498C6.88903 13.4653 6.73321 13.5419 6.5655 13.5703L3.98775 13.9393C3.86768 13.9556 3.74548 13.9437 3.63077 13.9047C3.51606 13.8656 3.41197 13.8005 3.3267 13.7145C3.24142 13.6284 3.17729 13.5237 3.13934 13.4086C3.10139 13.2936 3.09066 13.1713 3.108 13.0513Z" stroke="black" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <span class="font-bold mb-1 min-w-[70px]">
                      {{ $t('Contact') }}
                    </span>
              </div>
              <div class="text-[14px] flex justify-space-between mb-1">
                    <span class="font-bold min-w-[70px]">
                      {{ $t('Seller') }}
                    </span>
                <span class="">
                      {{ order.created_by?.username }}
                    </span>
              </div>
              <div class="text-[14px] flex justify-space-between">
                    <p class="font-bold min-w-[70px]">
                      {{ $t('Buyer') }}
                    </p>
                <span v-if="!editKeys.buyer_name" class="">
                      {{ order.buyer_name || '--' }}
                    </span>
              </div>
              <div v-if="editKeys.buyer_name">
                <el-input
                    v-model="dataEdit.buyer_name"
                    class="w-11/12 pr-2"
                    placeholder="Input Buyer"
                />

                <div class="float-right mr-2">
                  <el-button size="small" @click="editKeys.buyer_name = false" class="!text-[14px] !mt-2">
                    {{ $t('Cancel') }}
                  </el-button>
                  <el-button
                      @click="updateOrder('buyer_name')"
                      type="primary"
                      size="small"
                      class=" !text-[14px] !mt-2">
                    {{ $t('Save') }}
                  </el-button>
                </div>
              </div>
            </el-col>

          </el-row>
          <el-tabs type="card" @tab-click="handleClick">
            <el-tab-pane label="Item">
              <el-row>
                <el-col :span="24">
                  <el-table
                      :data="items"
                      style="width: 100%"
                      header-row-class-name="bg-[#f5f5f5] word-break table-header"
                  >
                    <el-table-column
                        prop="sku"
                        label="SKU"
                        min-width="100px"
                    >
                      <template #default="scope">
                        <div>
                          <p class="!font-semibold">
                            {{ scope.row?.product?.sku }}
                          </p>
                          <p>
                            {{ scope.row?.product?.name }}
                          </p>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                        prop="print_area"
                        label="Print Area"
                    >
                      <template #default="scope">
                        <div class="h-full flex flex-col">
                          <div class="flex-1 flex items-center" v-for="image in scope.row.images">
                            {{ getPrintArea(image.print_side) }}
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                        prop="design_image"
                        label="Design Image"
                        min-width="150px"
                    >
                      <template #default="scope">
                        <div v-for="image in scope.row.images" class="flex">
                          <img
                              @click="openLink(generateLink(image?.design?.design_url))"
                              class="h-[34px] w-auto cursor-pointer my-auto mr-1"
                              :src="generateLink(image?.design?.design_url)"
                              alt="">
                          <span class="text-[14px] ml-3 my-auto">
                          {{ image?.design?.design_id }}
                        </span>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                        prop="mockup_image"
                        label="Mockup Image"
                        min-width="170px"
                    >
                      <template #default="scope">
                        <div v-for="image in scope.row.images" class="flex">
                          <img
                              @click="openLink(generateLink(image?.mockup?.design_url))"
                              class="h-[34px] w-auto cursor-pointer my-auto mr-1"
                              :src="generateLink(image?.mockup?.design_url)"
                              alt="">
                          <span class="text-[14px] ml-3 my-auto">
                          {{ image?.mockup?.design_id }}
                        </span>
                        </div>
                      </template>
                    </el-table-column>

                    <el-table-column
                        prop="quantity"
                        label="QTY"
                        width="70"
                    >
                      <template #default="scope">
                        {{ formatNumber(scope.row.quantity) }}
                      </template>
                    </el-table-column>
                    <el-table-column
                        prop="unit_price"
                        label="Unit Cost"
                        width="100"
                    >
                      <template #default="scope">
                        {{ formatNumberFloat(scope.row.unit_price) }}
                      </template>
                    </el-table-column>
                    <el-table-column
                        prop="line_total_cost"
                        label="Line Total Cost"
                    >
                      <template #default="scope">
                        {{ calculateTotalCost(scope.row) }}
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="Packing Instruction">
              <packaging-instruction ></packaging-instruction>
            </el-tab-pane>
          </el-tabs>
        </el-col>
        <el-col :span="6" class="">
          <div>
            <el-row :gutter="20" class="rounded border text-[14px] ml-2 flex justify-around bg-[#e5e5f7] mb-4">
              <div
                  v-for="tab in tabs"
                  class="tab"
                  @click="selectTab(tab)">
                <div
                    :ref="tab.value"
                    :class="[activeTab === tab.value ? 'active border bg-white' : '']"
                    class="w-full rounded cursor-pointer text-center py-[8px] px-[10px]"
                >
                  {{ tab.label }}
                </div>
              </div>
            </el-row>
          </div>
          <el-row v-if="activeTab == 'timeline'" :gutter="20" class="text-[14px] ml-2">
            <el-col :span="24" class="border rounded py-[10px]">
              <div class="timeline relative pl-3">
                <div class="timeline-item" v-for="(item, index) in order.history">
                  <div class="circle">{{ order.history.length - index }}</div>
                  <div class="content">
                    <p class="title leading-[20px] mr-[5px] !text-[14px]">{{ item.message}}</p>
                    <div class="meta flex">
                      <svg class="mr-[3px]" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path d="M16.975 17.7084C16.975 17.8742 16.9092 18.0331 16.792 18.1504C16.6748 18.2676 16.5158 18.3334 16.35 18.3334H3.65002C3.48426 18.3334 3.32529 18.2676 3.20808 18.1504C3.09087 18.0331 3.02502 17.8742 3.02502 17.7084C3.02502 14.2917 6.77502 11.6417 10 11.6417C13.225 11.6417 16.975 14.2917 16.975 17.7084ZM14.25 5.92508C14.2484 6.7653 13.9977 7.58617 13.5297 8.28398C13.0617 8.98179 12.3974 9.52523 11.6207 9.84562C10.8439 10.166 9.98966 10.249 9.16579 10.0841C8.34192 9.91914 7.58543 9.51371 6.99189 8.91901C6.39835 8.3243 5.9944 7.56701 5.83109 6.74282C5.66777 5.91863 5.75242 5.06453 6.07434 4.28842C6.39626 3.51232 6.94099 2.84906 7.63972 2.38243C8.33845 1.9158 9.15981 1.66675 10 1.66675C11.128 1.66896 12.2089 2.11857 13.0057 2.91693C13.8025 3.71528 14.25 4.79714 14.25 5.92508Z" fill="#D0D5DD"/>
                      </svg>
                      <span class="mr-[10px]">{{ item?.user?.username }}</span>
                      <span class="date">{{ convertTime(item.created_at) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <div v-else>
            <el-row :gutter="20" class="text-[14px] ml-2">
              <el-col :span="24" class="border rounded py-[10px]">
                <div class="mb-[10px] relative">
                  <div class="cursor-pointer absolute top-[3px] right-[0px]" @click="showUpdate('internal_note')">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="19" viewBox="0 0 18 19" fill="none">
                      <path d="M9.58275 4.16684L12.8902 7.46534M2.8425 16.4376H15.1575M3.108 13.0513L3.47775 10.4826C3.49764 10.3148 3.57534 10.1591 3.6975 10.0423L10.9462 2.79434C11.0394 2.69933 11.1556 2.63 11.2834 2.59307C11.4113 2.55615 11.5465 2.55288 11.676 2.58359C12.3448 2.76113 12.9529 3.11675 13.4355 3.61259C13.9346 4.09413 14.2934 4.70233 14.4735 5.37209C14.5013 5.50173 14.4965 5.63623 14.4596 5.76358C14.4227 5.89092 14.3548 6.00714 14.262 6.10184L7.014 13.3498C6.88903 13.4653 6.73321 13.5419 6.5655 13.5703L3.98775 13.9393C3.86768 13.9556 3.74548 13.9437 3.63077 13.9047C3.51606 13.8656 3.41197 13.8005 3.3267 13.7145C3.24142 13.6284 3.17729 13.5237 3.13934 13.4086C3.10139 13.2936 3.09066 13.1713 3.108 13.0513Z" stroke="black" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </div>
                  <span class="font-bold ">

                     {{ $t('Internal Comments') }}
                  </span>
                </div>
                <div v-if="!editKeys.internal_note">
                  {{ order.internal_note || "--" }}
                </div>
                <div v-else>
                  <el-input
                      v-model="dataEdit.internal_note"
                      class="w-11/12 pr-2"
                      :rows="3"
                      type="textarea"
                      placeholder="Input Internal Comments"
                  />

                  <div class="float-right mr-2">
                    <el-button size="small" @click="editKeys.internal_note = false" class="!text-[14px] !mt-2">
                      {{ $t('Cancel') }}
                    </el-button>
                    <el-button
                        @click="updateOrder('internal_note')"
                        type="primary"
                        size="small"

                        class=" !text-[14px] !mt-2">
                      {{ $t('Save') }}
                    </el-button>
                  </div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20" class="text-[14px] ml-2">
              <el-col :span="24" class="border rounded mt-4 py-[10px]">
                <div class="mb-[10px] relative">
                  <div class="cursor-pointer absolute top-[3px] right-[0px]" @click="showUpdate('ship_via')">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="19" viewBox="0 0 18 19" fill="none">
                      <path d="M9.58275 4.16684L12.8902 7.46534M2.8425 16.4376H15.1575M3.108 13.0513L3.47775 10.4826C3.49764 10.3148 3.57534 10.1591 3.6975 10.0423L10.9462 2.79434C11.0394 2.69933 11.1556 2.63 11.2834 2.59307C11.4113 2.55615 11.5465 2.55288 11.676 2.58359C12.3448 2.76113 12.9529 3.11675 13.4355 3.61259C13.9346 4.09413 14.2934 4.70233 14.4735 5.37209C14.5013 5.50173 14.4965 5.63623 14.4596 5.76358C14.4227 5.89092 14.3548 6.00714 14.262 6.10184L7.014 13.3498C6.88903 13.4653 6.73321 13.5419 6.5655 13.5703L3.98775 13.9393C3.86768 13.9556 3.74548 13.9437 3.63077 13.9047C3.51606 13.8656 3.41197 13.8005 3.3267 13.7145C3.24142 13.6284 3.17729 13.5237 3.13934 13.4086C3.10139 13.2936 3.09066 13.1713 3.108 13.0513Z" stroke="black" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </div>
                  <span class="font-bold ">
                    {{ $t('Ship Via') }}
                  </span>
                </div>
                <div class="flex justify-space-between">
                  <span class="font-bold mb-1 min-w-[100px]">
                    {{ $t('Ship Via') }}
                  </span>
                  <span v-if="!editKeys.ship_via" class="">
                    {{ order.ship_via }}
                  </span>
                </div>
                <div v-if="editKeys.ship_via">
                  <el-input
                      v-model="dataEdit.ship_via"
                      class="w-11/12 pr-2"
                      placeholder="Input Ship Via"
                  />

                  <div class="float-right mr-2">
                    <el-button size="small" @click="editKeys.ship_via = false" class="!text-[14px] !mt-2">
                      {{ $t('Cancel') }}
                    </el-button>
                    <el-button
                        @click="updateOrder('ship_via')"
                        type="primary"
                        size="small"

                        class=" !text-[14px] !mt-2">
                      {{ $t('Save') }}
                    </el-button>
                  </div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20" class="text-[14px] ml-2">
              <el-col :span="24" class="border rounded mt-4 py-[10px]">
                <div class="mb-[10px] relative">
                  <div class="cursor-pointer absolute top-[3px] right-[0px]" @click="showUpdate('terms')">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="19" viewBox="0 0 18 19" fill="none">
                      <path d="M9.58275 4.16684L12.8902 7.46534M2.8425 16.4376H15.1575M3.108 13.0513L3.47775 10.4826C3.49764 10.3148 3.57534 10.1591 3.6975 10.0423L10.9462 2.79434C11.0394 2.69933 11.1556 2.63 11.2834 2.59307C11.4113 2.55615 11.5465 2.55288 11.676 2.58359C12.3448 2.76113 12.9529 3.11675 13.4355 3.61259C13.9346 4.09413 14.2934 4.70233 14.4735 5.37209C14.5013 5.50173 14.4965 5.63623 14.4596 5.76358C14.4227 5.89092 14.3548 6.00714 14.262 6.10184L7.014 13.3498C6.88903 13.4653 6.73321 13.5419 6.5655 13.5703L3.98775 13.9393C3.86768 13.9556 3.74548 13.9437 3.63077 13.9047C3.51606 13.8656 3.41197 13.8005 3.3267 13.7145C3.24142 13.6284 3.17729 13.5237 3.13934 13.4086C3.10139 13.2936 3.09066 13.1713 3.108 13.0513Z" stroke="black" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </div>
                  <span class="font-bold ">
                    {{ $t('Terms') }}
                  </span>
                </div>
                <div class="flex justify-space-between mb-[10px]">
                  <span class="font-bold">
                    {{ $t('F.O.B') }}
                  </span>
                  <span v-if="!editKeys.terms" class="">
                    {{ order.free_on_board || "--" }}
                  </span>
                </div>
                <el-input
                    v-if="editKeys.terms"
                    v-model="dataEdit.free_on_board"
                    class="w-11/12 pr-2 mt-[-10px] mb-[10px]"
                    placeholder="Input F.O.B"
                />

                <div class="flex justify-space-between mb-[10px]">
                  <span class="font-bold">
                    {{ $t('Pay Terms') }}
                  </span>
                  <span v-if="!editKeys.terms" class="">
                    {{ order.pay_terms || "--" }}
                  </span>
                </div>
                <el-input
                    v-if="editKeys.terms"
                    v-model="dataEdit.pay_terms"
                    class="w-11/12 pr-2 mt-[-10px] mb-[10px]"
                    placeholder="Input Pay Terms"
                />
                <div class="flex justify-space-between mb-[10px]">
                  <span class="font-bold min-w-[100px]">
                    {{ $t('Freight Terms') }}
                  </span>
                  <span v-if="!editKeys.terms" class="">
                    {{ order.freight_terms || "--" }}
                  </span>
                </div>
                <el-input
                    v-if="editKeys.terms"
                    v-model="dataEdit.freight_terms"
                    class="w-11/12 pr-2 mt-[-10px] mb-[10px]"
                    placeholder="Input Freight Terms"
                />
                <div v-if="editKeys.terms">
                  <div class="float-right mr-2">
                    <el-button size="small" @click="editKeys.terms = false" class="!text-[14px] !mt-2">
                      {{ $t('Cancel') }}
                    </el-button>
                    <el-button
                        @click="updateOrder('terms')"
                        type="primary"
                        size="small"

                        class=" !text-[14px] !mt-2">
                      {{ $t('Save') }}
                    </el-button>
                  </div>
                </div>

              </el-col>

            </el-row>
            <el-row :gutter="20" class="text-[14px] ml-2 mt-4">
              <el-col :span="24" class="border rounded py-[10px] relative">

                <div class="mb-[10px]">
                  <span class="font-bold">
                    {{ $t('Billing Address') }}
                  </span>
                </div>
                <p>
                  {{ billingAddress?.name ? billingAddress.name : order?.client?.name }}
                </p>
                <div class="flex" v-if="billingAddress?.name">
                  {{ billingAddress.street1 || "" }}, <template v-if="billingAddress.street2" >{{ billingAddress.street2 || "" }}, </template>
                  {{ billingAddress.city || "" }}, {{ billingAddress.state || "" }},
                  {{ billingAddress.zipcode || "" }},
                  {{ billingAddress.country || "" }}
                </div>
                <div v-else class="flex">
                  {{ order?.client?.client_address?.street1 || "" }}, <template v-if="order?.client?.client_address?.street2" >{{ order?.client?.client_address?.street2 || "" }}, </template>
                  {{ order?.client?.client_address?.city || "" }}, {{ order?.client?.state || "" }},
                  {{ order?.client?.client_address?.zipcode || "" }},
                  {{ order?.client?.client_address?.country || "" }}
                </div>
              </el-col>

            </el-row>
            <el-row :gutter="20" class="text-[14px] ml-2">
              <el-col :span="24" class="border rounded mt-4 py-[10px] relative">
                <div class="cursor-pointer absolute top-[3px] right-[10px]" @click="showUpdate('billing')">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="19" viewBox="0 0 18 19" fill="none">
                    <path d="M9.58275 4.16684L12.8902 7.46534M2.8425 16.4376H15.1575M3.108 13.0513L3.47775 10.4826C3.49764 10.3148 3.57534 10.1591 3.6975 10.0423L10.9462 2.79434C11.0394 2.69933 11.1556 2.63 11.2834 2.59307C11.4113 2.55615 11.5465 2.55288 11.676 2.58359C12.3448 2.76113 12.9529 3.11675 13.4355 3.61259C13.9346 4.09413 14.2934 4.70233 14.4735 5.37209C14.5013 5.50173 14.4965 5.63623 14.4596 5.76358C14.4227 5.89092 14.3548 6.00714 14.262 6.10184L7.014 13.3498C6.88903 13.4653 6.73321 13.5419 6.5655 13.5703L3.98775 13.9393C3.86768 13.9556 3.74548 13.9437 3.63077 13.9047C3.51606 13.8656 3.41197 13.8005 3.3267 13.7145C3.24142 13.6284 3.17729 13.5237 3.13934 13.4086C3.10139 13.2936 3.09066 13.1713 3.108 13.0513Z" stroke="black" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <div class="mb-[10px]">
                  <span class="font-bold ">
                    {{ $t('Billing') }}
                  </span>
                </div>
                <div class="flex justify-space-between mb-[10px]">
                  <span class="font-bold">
                    {{ $t('Total Quantity') }}
                  </span>
                  <span class="">
                    {{ order.order_quantity || "--" }}
                  </span>
                </div>

                <div class="flex justify-space-between mb-[10px]">
                  <span class="font-bold">
                    {{ $t('Sub Total Value') }}
                  </span>
                  <span class="">
                    {{ calculateTotalCostOrder() }}
                  </span>
                </div>
                <div class="flex justify-space-between mb-[10px]">
                  <span class="font-bold">
                    {{ $t('Tax Rate') }}
                  </span>
                  <span v-if="!editKeys.billing" class="">
                    {{ order.tax_rate || "--" }}
                  </span>
                </div>
                <el-input
                    v-if="editKeys.billing"
                    v-model="dataEdit.tax_rate"
                    class="w-11/12 pr-2 mt-[-10px] mb-[10px]"
                    placeholder="Input Tax Rate"
                />

                <div class="flex justify-space-between mb-[10px]">
                  <span class="font-bold">
                    {{ $t('Tax') }}
                  </span>
                  <span v-if="!editKeys.billing" class="">
                    {{ order.tax || "--" }}
                  </span>
                </div>
                <el-input
                    v-if="editKeys.billing"
                    v-model="dataEdit.tax"
                    class="w-11/12 pr-2 mt-[-10px] mb-[10px]"
                    placeholder="Input Tax"
                />
                <div class="flex justify-space-between mb-[10px]">
                  <span class="font-bold">
                    {{ $t('S&H') }}
                  </span>
                  <span v-if="!editKeys.billing" class="">
                    {{ order.shipping_and_handling_fee || "--" }}
                  </span>
                </div>
                <el-input
                    v-if="editKeys.billing"
                    v-model="dataEdit.shipping_and_handling_fee"
                    class="w-11/12 pr-2 mt-[-10px] mb-[10px]"
                    placeholder="Input S&H"
                />
                <div class="flex justify-space-between mb-[10px]">
                  <span class="font-bold">
                    {{ $t('Other Fees') }}
                  </span>
                  <span v-if="!editKeys.billing" class="">
                    {{ order.other_fee || "--" }}
                  </span>
                </div>
                <el-input
                    v-if="editKeys.billing"
                    v-model="dataEdit.other_fee"
                    class="w-11/12 pr-2 mt-[-10px] mb-[10px]"
                    placeholder="Input Other Fees"
                />
                <div class="flex justify-space-between mb-[10px]">
                  <span class="font-bold">
                    {{ $t('Total Value') }}
                  </span>
                  <span v-if="!editKeys.billing" class="">
                    {{ formatNumberFloat(order.order_total) }}
                  </span>
                </div>
                <el-input
                    v-if="editKeys.billing"
                    v-model="dataEdit.order_total"
                    class="w-11/12 pr-2 mt-[-10px] mb-[10px]"
                    placeholder="Input Total Value"
                />
                <div v-if="editKeys.billing">
                  <div class="float-right mr-2">
                    <el-button size="small" @click="editKeys.billing = false" class="!text-[14px] !mt-2">
                      {{ $t('Cancel') }}
                    </el-button>
                    <el-button
                        @click="updateOrder('billing')"
                        type="primary"
                        size="small"

                        class=" !text-[14px] !mt-2">
                      {{ $t('Save') }}
                    </el-button>
                  </div>
                </div>


              </el-col>

            </el-row>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import EventBus from '@/utilities/eventBus.js';
import {details, updateOrder, updateAddress, getOrderPackagingDetail} from '@/api/screenOrder';
import {S3_URL, STORAGE_URL} from '@/utilities/constants';
import dateMixin from '@/mixins/date.js';
import helperMixin from '@/mixins/helpers.js';
import { countries, fetchStateByCountry } from "@/api/default.js";
import formatNumber from "@/mixins/formatNumber";
import AddressForm from '@/modules/sale-order/components/AddressForm.vue';
import PackagingInstruction from "@/modules/screen-order/views/component/PackagingInstruction.vue";

import moment from "moment/moment";
import {isEmpty} from "ramda";
import * as parser from "parse-address";

export default {
  name: 'OrderDetail',
  mixins: [dateMixin, formatNumber, helperMixin],
  props: {
  },
  components: {
    AddressForm,
    PackagingInstruction
  },
  data() {
    return {
      states: [],
      activeTab: 'orderInfo',
      orderStatus: [
        {
          value: "new_order",
          name: "New Order",
          color: "primary",
        },
        {
          value: "in_production",
          name: "In Production",
          color: "warning",
        },
        {
          value: "ready_to_ship",
          name: "Ready to ship",
          color: "info",
        },
        {
          value: "completed",
          name: "Completed",
          color: "success",

        },
        {
          value: "cancelled",
          name: "Cancelled",
          color: "danger",
        }
      ],
      isLoading: false,
      order: {},
      billingAddress: {},
      editKeys: {
      },
      dataEdit: {
      },
      items: [],
      address: {},
      isLoadingVefifyAddress: false,
      validateAddress: '',
      isValidateAddress: false,
      addressRules: {
        name: [
          {
            required: true,
            message: this.$t("This field cannot be left blank."),
            trigger: "change",
          },
        ],
        country_id: [
          {
            required: true,
            message: this.$t("This field cannot be left blank."),
            trigger: "change",
          },
        ],
        street1: [
          {
            required: true,
            message: this.$t("This field cannot be left blank."),
            trigger: "change",
          },
        ],
        city: [
          {
            required: true,
            message: this.$t("This field cannot be left blank."),
            trigger: "change",
          },
        ],
        // state: [
        //   {
        //     required: true,
        //     message: this.$t("This field cannot be left blank."),
        //     trigger: "change",
        //   },
        // ],
        // state_id: [
        //   {
        //     required: true,
        //     message: this.$t("This field cannot be left blank."),
        //     trigger: "change",
        //   },
        // ],
        zipcode: [
          {
            required: true,
            message: this.$t("This field cannot be left blank."),
            trigger: "change",
          },
        ],
        email: [
          {
            type: "email",
            message: this.$t("Invalid email."),
            trigger: "change",
          },
        ],
      },
      dataEditShipAddress: {},
      tabSelected: 'order_info',
      tabs: [
        { label: 'Order Info', value: 'orderInfo' },
        { label: 'Timeline', value: 'timeline' },
      ],
      countries: [],
      activeName: 'Item'
    };
  },
  watch: {
  },
  async created() {
    let id = this.$route?.params?.id;
    await this.fetchCountries();

    if (id) {
      this.getDetail(id);
    }

    // EventBus.$on('showScreenOrderDetails', async (data) => {
    //   this.openDialogDetail = true;
    //   await this.getDetail(data.id);
    // });
  },
  unmounted() {
    EventBus.$off("showScreenOrderDetails");
    this.closeModal();
  },
  methods: {
    checkIsValidStatus(status) {
      let currentStatus = this.order.order_status;
      let arrayAllow = [];
      if (['new_order'].includes(currentStatus)) {
        arrayAllow = ['cancelled', 'in_production'];
      } else if (['in_production'].includes(currentStatus)) {
        arrayAllow = ['cancelled', 'ready_to_ship'];
      } else if (currentStatus == 'ready_to_ship') {
        arrayAllow = ['completed', 'cancelled'];
      }
      arrayAllow.push(currentStatus)

      return !arrayAllow.includes(status);
    },
    selectTab(tab) {
      this.activeTab = tab.value;
      // this.tabs.forEach((item) => {
      //   this.$refs[item.value].classList.remove('active', 'border', 'bg-white');
      // });
      // this.$refs[tab.value].classList.add('active border bg-white');
    },
    async parseAddress() {
      const defaultCounty = 'US';
      const country = this.countries.find(
          (item) => item.iso2 === defaultCounty
      );
      const parsed = parser.parseLocation(this.validateAddress);
      const {
        number = "",
        prefix = "",
        street: _street = "",
        type = "",
        suffix = "",
        city: _city = "",
        state: _state = "",
        zip: _zip = "",
        sec_unit_type = "",
        sec_unit_num = ""
      } = parsed;
      const preStreet = `${number} ${prefix} ${_street} ${type} ${suffix}`;
      const secUnit = `${sec_unit_type} ${sec_unit_num}`;
      const city = _city;
      const state = _state;
      const street = (preStreet.trim() && secUnit.trim()) ? `${preStreet.trim()}, ${secUnit.trim()}` : (preStreet.trim() ? preStreet.trim() : secUnit.trim());
      const zip = _zip;
      if (country && country.id && state) {
        this.address.country_id = country && country.id;
        this.address.country = 'US';
        await this.fetchStateByCountry(this.address.country_id);
        const stateItem = this.states.find((item) => item.iso2 === state);
        this.address.state_id = stateItem ? stateItem.id : this.address.state;
      }
      this.address.street1 = street;
      this.address.zipcode = zip;
      this.address.city = city;

      this.isValidateAddress = false;
      this.validateAddress = '';
    },
    showValidateAddress() {
      this.isValidateAddress = !this.isValidateAddress;
      this.validateAddress = '';
      this.$nextTick(() => {
        if (this?.$refs?.validateAddress) {
          this.$refs.validateAddress.focus();
        }
      });
    },
    async generateAddress() {
      this.address.country_id = await this.countries.find((item) => item.iso2 == this.address.country)?.id || '';
      const res = await fetchStateByCountry(this.address.country_id)
      this.states = res.data || [];
      this.address.state_id = this.states.find((item) => item.iso2 == this.address.state)?.id || '';
    },
    async onSubmit(formName) {
      const isValid = await this.$refs[formName].validate();
      if (!isValid) {
        return;
      }
      this.isLoading = true;
      try {
        const country = this.countries.find(
            (item) => +item.id === +this.address.country_id
        );
        const state = this.states.find(
            (item) => +item.id === +this.address.state_id
        );
        const address = {
          ...this.address,
          country: country && country.iso2,
          state: state ? state.iso2 : this.address.state,
        };
        if (country.iso2 != 'US') {
          address.state = this.address.state;
          const state = this.states.find((item) => item.name == this.address.state || item.iso2 == this.address.state);
          address.state_id = state ? state.id : null;
        }
        console.log('address', address);
        const res = await updateAddress(address, address.id);
        this.getDetail(this.order.id);
        this.editKeys = {};
        this.notification(this.$t("Update address successfully."));
        this.isEdit = false;
        this.isLoading = false;
      } catch (e) {
        this.isLoading = false;
        const data = e.response.data?.errors;
        let message = this.$t('Update address error.');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.notification(message, 'error');
      }
    },
    closeModalEditAddress() {
      this.editKeys.shipping_address = false;
    },
    closeModal() {
      this.openDialogDetail = false;
      this.editKeys = {};
      this.dataEdit = {};
      this.order = {};
      this.items = [];
      this.billingAddress = {};
      this.address = {};


    },
    async updateOrder(field = '') {
      try {
        switch (field) {
          case 'terms':
            var params = {
              free_on_board: this.dataEdit.free_on_board,
              pay_terms: this.dataEdit.pay_terms,
              freight_terms: this.dataEdit.freight_terms,
            };
            break;
          case 'billing':
            var params = {
              tax_rate: this.dataEdit.tax_rate,
              tax: this.dataEdit.tax,
              shipping_and_handling_fee: this.dataEdit.shipping_and_handling_fee,
              other_fee: this.dataEdit.other_fee,
              order_total: this.dataEdit.order_total,
            };
            break;
          default:
            var params = {
              [field]: this.dataEdit[field],
            };
            break;
        }

        const res = await updateOrder(params, this.order.id);
        this.order = res.data;
        this.editKeys = {};
        this.getDetail(this.order.id);
      } catch (e) {
        let message = e.response.data.message || this.$t('Error.');
        this.notification(message, 'error');
      }
    },
    async changeCountry() {
      this.address.state_id = "";
      this.address.state = "";
      await this.fetchStateByCountry(this.address.country_id);
    },
    async fetchStateByCountry(id) {
      const res = await fetchStateByCountry(id);
      this.states = res.data || [];
    },
    changeState(){
      this.address.state = this.states.find(item => item.id == this.address.state_id).iso2;
    },
    showUpdate(field) {
      this.editKeys[field] = true;
      switch (field) {
        case 'terms':
          this.dataEdit.free_on_board = this.order.free_on_board;
          this.dataEdit.pay_terms = this.order.pay_terms;
          this.dataEdit.freight_terms = this.order.freight_terms;
          break;
        case 'billing':
          this.dataEdit.tax_rate = this.order.tax_rate;
          this.dataEdit.tax = this.order.tax;
          this.dataEdit.shipping_and_handling_fee = this.order.shipping_and_handling_fee;
          this.dataEdit.other_fee = this.order.other_fee;
          this.dataEdit.order_total = this.order.order_total;
          break;
        default:
          this.dataEdit[field] = this.order[field];
          break;

      }

    },
    getStatus(orderStatus) {
      let status = this.orderStatus.find((item) => item.value === orderStatus);
      return status ? status.name : '';
    },
    calculateTotalCost(row) {
      return this.formatNumberFloat(row.quantity * row.unit_price);
    },
    capitalizeFirstString(string) {
      if (!string) return "";
      return string.charAt(0).toUpperCase() + string.slice(1);
    },
    calculateTotalCostOrder() {
      let totalCost = this.items.reduce((acc, item) => {
        return acc + item.quantity * parseFloat(item.unit_price);
      }, 0);
      return this.formatNumberFloat(totalCost);
    },
    openLink(url){
      return window.open(url, '_blank');
    },
    async fetchCountries() {
      const res = await countries();
      this.countries = res.data || [];
    },
    generateLink(url){
      return `${S3_URL}/${url}`;
    },
    getPrintArea(print_side) {
      return print_side?.replaceAll('_', ' ')?.toUpperCase();
    },
    async getDetail(id) {
      try {
        this.isLoading = true;
        const response = await details(id);
        this.order = response.data;
        this.items = this.order?.items || [];
        this.billingAddress = this.order.client?.billing_address || {};
        this.address = this.order.shipping_address[0];
        this.generateAddress();
        this.isLoading = false;
      } catch (e) {
        this.isLoading = false;
        let message = this.$t('Error.');
        this.notification(message, 'error');
      }
    },
    handleClick(tab, event) {
      // console.log(tab, event);
    }
  },
};
</script>
<style lang="scss">
.word-break th.el-table__cell {
  word-break: break-word;
  background-color: #E6EFFC;
}

.timeline-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  position: relative;
}

.timeline-item .circle {
  width: 26px;
  height: 26px;
  background-color: #e5e5f7;
  color: #555;
  font-weight: bold;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  position: absolute;
  top: -3px;
  left: -13px;
}

.timeline-item .content {
  margin-left: 20px;
}

.timeline-item .title {
  margin: 0;
  font-weight: bold;
  font-size: 16px;
  color: #333;
}

.timeline-item .meta {
  margin-top: 5px;
  font-size: 14px;
  color: #666;
}

.timeline-item .meta .user {
  margin-right: 10px;
}

.timeline-item .meta .date {
  color: #999;
}

.tabs {
  display: flex;
  border: 1px solid #ddd;
  border-radius: 5px;
  overflow: hidden;
  background-color: #f9f9f9;
}

.tab {
  flex: 1;
  padding: 4px 4px;
  text-align: center;
  cursor: pointer;
  color: #666;
  background-color: #f9f9f9;
  transition: background-color 0.3s ease;
}

.tab:last-child {
  border-right: none;
}

.tab .active {
  background-color: #fff;
  color: #333;
  //font-weight: bold;
}

//.content {
//  margin-top: 20px;
//  padding: 20px;
//  border: 1px solid #ddd;
//  border-radius: 5px;
//  background-color: #fff;
//}

</style>
