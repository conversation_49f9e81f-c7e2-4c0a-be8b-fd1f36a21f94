<template>
  <div class="">
    <el-dialog
        v-model="openDialogImport"
        custom-class="el-dialog-custom el-dialog-custom2"
        title=" Import Order / Importar Orden"
        :close-on-click-modal="false"
        width="80%"
        @close="closeModal"
    >
      <div class="relative flex gap-6 pb-0 border-b border-gray-300 mb-[16px] mt-[10px]">
        <div
            v-for="tab in tabs"
            class="cursor-pointer"
            :class="tabActive == tab.value ? 'border-b border-[#1A73E8]' : ''"
            @click="tabActive = tab.value"
        >
          <span>{{tab.label}}</span>
        </div>
      </div>
      <div v-if="tabActive == 'import'" v-loading="isLoading">
        <div>

          <div>
            <div class="mb-1 flex items-center">
              Upload file CSV
              <span class="ml-1"
              >(
              <el-link
                  type="primary"
                  :underline="false"
                  @click="downloadCsvTemplate"
              >
                {{ $t('Download Template / Descargar plantilla') }}
              </el-link>
              )
            </span>
            </div>
            <el-upload
                accept=".csv"
                class="upload-import-reroute"
                drag
                :limit="1"
                :auto-upload="false"
                :on-change="uploadFile"
                :on-remove="removeFile"
                v-model:file-list="file"
                :multiple="false"
                :key="uploadKey"
            >
              <icon :data="iconAdd" class="!w-12 !h-12 !text-gray-400 mb-1" />
              <div class="el-upload__text">
                <p class="text-[16px] text-[#667085]">
                  Drop file here or <em>click to upload</em>
                </p>
                <p class="text-[14px] text-[#667085]">
                  Supports a single file upload in CSV format / Hace posible la carga de un solo archivo en formato CSV
                </p>
              </div>
            </el-upload>
          </div>
        </div>

        <div>
          <div class="mb-3" v-if="orderImport.order_imported || orderImport.order_failed" >
            <div class="text-base flex mt-[10px]">
              <div class="mr-4 text-success">
                {{ $t(`Success : ${orderImport.order_imported || 0} order(s)`) }}
              </div>
              <div class="text-danger mr-8">
                {{ $t(`Fail: ${orderImport.order_failed || 0} order(s)`) }}
              </div>
            </div>
          </div>
          <div v-if="orderImport.file_failed" class="rounded-lg border px-2 py-1 flex">
            <div class="my-auto mr-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M9.79036 7.67495L4.25586 6.69995V13.9045C4.25579 13.9828 4.27118 14.0603 4.30115 14.1326C4.33112 14.205 4.37507 14.2707 4.43048 14.326C4.4859 14.3814 4.55169 14.4252 4.62409 14.455C4.69648 14.4849 4.77405 14.5001 4.85236 14.5H14.4024C14.4807 14.5003 14.5584 14.4851 14.6309 14.4553C14.7034 14.4255 14.7694 14.3817 14.8249 14.3264C14.8804 14.2711 14.9244 14.2053 14.9545 14.1329C14.9845 14.0605 14.9999 13.9828 14.9999 13.9045V11.25L9.79036 7.67495Z" fill="#185C37"/>
                <path d="M9.79036 1.5H4.85236C4.77405 1.4998 4.69648 1.51507 4.62409 1.54491C4.55169 1.57475 4.4859 1.6186 4.43048 1.67392C4.37507 1.72924 4.33112 1.79496 4.30115 1.8673C4.27118 1.93965 4.25579 2.0172 4.25586 2.0955V4.75L9.79036 8L12.7209 8.975L14.9999 8V4.75L9.79036 1.5Z" fill="#21A366"/>
                <path d="M4.25586 4.75H9.79036V8H4.25586V4.75Z" fill="#107C41"/>
                <path opacity="0.1" d="M8.21686 4.1001H4.25586V12.2251H8.21686C8.37469 12.2243 8.52586 12.1614 8.63761 12.0499C8.74935 11.9384 8.81268 11.7874 8.81386 11.6296V4.6956C8.81268 4.53777 8.74935 4.38676 8.63761 4.27529C8.52586 4.16383 8.37469 4.10088 8.21686 4.1001Z" fill="black"/>
                <path opacity="0.2" d="M7.89136 4.42505H4.25586V12.55H7.89136C8.04919 12.5493 8.20036 12.4863 8.31211 12.3749C8.42385 12.2634 8.48718 12.1124 8.48836 11.9545V5.02055C8.48718 4.86272 8.42385 4.71171 8.31211 4.60024C8.20036 4.48878 8.04919 4.42583 7.89136 4.42505Z" fill="black"/>
                <path opacity="0.2" d="M7.89136 4.42505H4.25586V11.9H7.89136C8.04919 11.8993 8.20036 11.8363 8.31211 11.7249C8.42385 11.6134 8.48718 11.4624 8.48836 11.3045V5.02055C8.48718 4.86272 8.42385 4.71171 8.31211 4.60024C8.20036 4.48878 8.04919 4.42583 7.89136 4.42505Z" fill="black"/>
                <path opacity="0.2" d="M7.56586 4.42505H4.25586V11.9H7.56586C7.72369 11.8993 7.87486 11.8363 7.98661 11.7249C8.09835 11.6134 8.16168 11.4624 8.16286 11.3045V5.02055C8.16168 4.86272 8.09835 4.71171 7.98661 4.60024C7.87486 4.48878 7.72369 4.42583 7.56586 4.42505Z" fill="black"/>
                <path d="M1.597 4.42505H7.566C7.72411 4.42492 7.87581 4.48757 7.98776 4.59923C8.0997 4.7109 8.16273 4.86244 8.163 5.02055V10.9796C8.16273 11.1377 8.0997 11.2892 7.98776 11.4009C7.87581 11.5125 7.72411 11.5752 7.566 11.5751H1.597C1.51865 11.5753 1.44103 11.5601 1.36857 11.5303C1.29612 11.5005 1.23027 11.4566 1.1748 11.4013C1.11933 11.346 1.07534 11.2802 1.04534 11.2079C1.01534 11.1355 0.999934 11.0579 1 10.9796V5.02055C0.999934 4.94221 1.01534 4.86462 1.04534 4.79224C1.07534 4.71986 1.11933 4.65412 1.1748 4.59879C1.23027 4.54346 1.29612 4.49963 1.36857 4.46982C1.44103 4.44 1.51865 4.42479 1.597 4.42505Z" fill="url(#paint0_linear_2053_1504)"/>
                <path d="M2.8501 9.93648L4.1056 7.99448L2.9556 6.06348H3.8791L4.5066 7.29998C4.5646 7.41698 4.6066 7.50398 4.6256 7.56198H4.6341C4.6751 7.46831 4.71843 7.37731 4.7641 7.28898L5.4351 6.06548H6.2851L5.1056 7.98548L6.3151 9.93798H5.4106L4.6856 8.58248C4.65203 8.52415 4.62343 8.4631 4.6001 8.39998H4.5881C4.56691 8.4615 4.53872 8.52038 4.5041 8.57548L3.7576 9.93648H2.8501Z" fill="white"/>
                <path d="M14.403 1.5H9.79053V4.75H15V2.0955C15.0001 2.01716 14.9847 1.93957 14.9547 1.86719C14.9247 1.79481 14.8807 1.72907 14.8252 1.67374C14.7698 1.61841 14.7039 1.57458 14.6315 1.54477C14.559 1.51495 14.4814 1.49974 14.403 1.5Z" fill="#33C481"/>
                <path d="M9.79053 8H15V11.25H9.79053V8Z" fill="#107C41"/>
                <defs>
                  <linearGradient id="paint0_linear_2053_1504" x1="2.247" y1="3.95705" x2="6.916" y2="12.0431" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#18884F"/>
                    <stop offset="0.5" stop-color="#117E43"/>
                    <stop offset="1" stop-color="#0B6631"/>
                  </linearGradient>
                </defs>
              </svg>
            </div>

            {{ getFileName(orderImport?.file_failed) }}
            <el-link class="el-link-edit !leading-[0px]" :underline="false"
                     type="primary" @click="downloadFile(orderImport?.file_failed)">
            <span class="ml-4">
              {{ $t('Download Error File / Descargar archivo de error') }}
            </span>
            </el-link>
            <el-link
                type="primary"
                :underline="false"
                @click="downloadFile(orderImport.file_failed)"
            ></el-link>

          </div>
          <div class="flex justify-end mt-4">
            <el-button @click="closeModal()">{{ $t('Cancel / Cancelar') }}</el-button>
            <el-button type="primary" :disabled="!uploadFileRaw.name" @click="insertData">
              {{ $t('Import / Importar') }}
            </el-button>
          </div>
        </div>
      </div>
      <div v-if="tabActive == 'history'" v-loading="isLoading">
        <el-table
            size="small"
            stripe
            :data="importHistory"
            style="width: 100%"
            class="table-sale-order"
            :max-height="maxHeight"
            element-loading-text="Loading..."
            header-row-class-name="table-header"
        >
          <el-table-column
              prop="file_name"
              label="File Name / Nombre del archivo"
              min-width="100px"
          >
            <template #default="scope">
              {{ scope.row.file_name }}
            </template>

          </el-table-column>
          <el-table-column
              prop="user_id"
              label="Uploaded By / Subido por"
              min-width="80px"
          >
            <template #default="scope">
              {{ scope.row.user?.username }}
            </template>

          </el-table-column>
          <el-table-column
              prop="created_at"
              label="Uploaded At / Subido el"
              min-width="80px"
          >
            <template #default="scope">
              {{ convertTime(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column
              prop="order_total"
              label="Order Quantity / Cantidad de pedidos"
              min-width="60px"
          >
            <template #default="scope">
              {{ scope.row.order_total }}
            </template>
          </el-table-column>

          <el-table-column
              prop="order_imported"
              label="Success / Éxito"
              class-name=""
              min-width="40px"
          >
            <template #default="scope">
              <span class="text-[#34C759]">
                              {{ scope.row.order_imported }}
              </span>
            </template>
          </el-table-column>

          <el-table-column
              prop="order_failed"
              label="Failed / Fallo"
              class-name=""
              min-width="40px"
          >
            <template #default="scope">
              <span  class="text-[#FF3B30]">
                {{ scope.row.order_failed }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
              label="Original File / Archivo original"
              min-width="50px"
          >
            <template #default="scope">
              <el-link
                  type="file_imported"
                  :underline="false"
                  @click="downloadFile(scope.row.file_imported)"
              >
                {{ $t('Download / Descargar') }}
              </el-link>
            </template>
          </el-table-column>

          <el-table-column
              label="Error File / Archivo de error"
              min-width="50px"

          >
            <template #default="scope">
              <el-link
                  type="file_imported"
                  :underline="false"
                  @click="downloadFile(scope.row.file_failed)"
              >
                {{ $t('Download / Descargar') }}
              </el-link>
            </template>
          </el-table-column>
        </el-table>
        <div class="flex justify-between items-center mt-4">
          <div class="total">
            {{ $t('Total:') }} {{ total ? formatNumber(total) : 0 }}
          </div>
          <el-pagination
              :disabled="isLoading"
              background
              layout="prev, pager, next"
              :page-size="filter.limit"
              :total="total"
              @current-change="changePage"
              v-model:currentPage="filter.page"
          >
          </el-pagination>
          <div class="limit" :disabled="isLoading">
            <el-select
                v-model="filter.limit"
                :placeholder="$t('Select')"
                size="mini"
                @change="onFilter('limit')"
            >
              <el-option
                  v-for="item in limits"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EventBus from '@/utilities/eventBus.js';
import { importOrders, importOrderHistories } from '@/api/screenOrder';
import { STORAGE_URL, S3_URL } from '@/utilities/constants';
import dateMixin from '@/mixins/date';
import numberMixin from '@/mixins/formatNumber';

export default {
  name: 'ImportScreenOrder',
  mixins: [dateMixin, numberMixin],
  data() {
    return {
      openDialogImport: false,
      filter: {
        page: 1,
        limit: 10,
      },
      file: [],
      uploadKey: Date.now(),
      data: {},
      orderImport: {},
      step: 1,
      isLoading: false,
      uploadFileRaw: '',
      tabActive: 'import',
      importHistory: [],
      total: 0,
      tabs: [
        {
          label: 'Import / Importar',
          value: 'import',
        },
        {
          label: 'History / Historial',
          value: 'history',
        },
      ],
    };
  },
  created() {
    EventBus.$on('showModalImportScreenOrders', () => {
      this.openDialogImport = true;
    });
  },
  watch: {
    tabActive(val) {
      if (val === 'history') {
        this.filter.page = 1;
        this.fetchImportHistories();
      }
    },
  },
  methods: {
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchImportHistories();
      });
    },
    onFilter(item = '', keepCurrentPage = false) {
      if (!keepCurrentPage) {
        this.filter.page = 1;
      }

      this.$nextTick(() => {
        this.fetchImportHistories();

        if (item && this.$refs[item]) {
          this.$refs[item].handleClose();
        }
      });
    },
    async fetchImportHistories() {
      this.isLoading = true;
      const res = await importOrderHistories(this.filter);
      this.importHistory = res.data.data || [];
      this.total = res.data.total || 0;
      this.isLoading = false;
    },
    getFileName(path) {
      if (path) {
        return path.split('/').pop();
      }
      return '';
    },
    downloadCsvTemplate() {
      window.open(
          `${STORAGE_URL}/csv-template/import_screen_printing_order_template.csv`,
          '_blank'
      );
    },
    downloadFile(path) {
      window.open(
          `${S3_URL}/${path}`,
          '_blank'
      );
    },
    reset() {
      this.step = 1;
      this.orderImport = {};
      this.uploadFileRaw = '';
      this.uploadFileRaw = '';
      this.file = [];
      this.filter.page = 1;
      this.uploadKey = Date.now();

    },
    closeModal() {
      this.$emit("refresh");
      this.reset();
      this.openDialogImport = false;
    },
    back() {
      this.reset();
    },
    async uploadFile(uploadFile, newFileList) {
      console.log(uploadFile, newFileList)
      this.file.value = [uploadFile];
      this.uploadFileRaw = uploadFile.raw;

    },
    removeFile() {
      this.uploadFileRaw = '';
    },
    async insertData() {
      try {
        this.isLoading = true;
        let formData = new FormData();
        formData.append('file', this.uploadFileRaw);

        const res = await importOrders(formData);
        if (res.status === 200) {
          this.step = 2;
          if (!res.data?.status) {
            this.notification(res.data.message, "error");
          } else {
            this.orderImport = res.data.data;
            this.uploadKey = Date.now();
            this.notification('Import order(s) successfully. / Pedido(s) importado(s) con éxito.');
          }
          this.isLoading = false;

          // this.openDialogImport = false;
          this.$emit('refresh');
        } else {
          this.notification('Import Error. / Error de importación.', 'error');
        }
      } catch (e) {
        let errors = e?.response?.data?.errors;
        if (errors) {
          let message = '';
          for (let key in errors) {
            message += errors[key][0] + '\n';
          }
          this.notification(message, 'error');
        } else if (e?.response?.data?.message) {
          let message = e?.response?.data?.message;
          this.notification(message || 'Import Error. / Error de importación.', 'error');
        }

        this.isLoading = false;

        // let message = this.$t('Import Error.');
        this.notification(message, 'error');
      }
    },
  },
};
</script>
<style lang="scss">
.upload-import-reroute {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
    }
  }

  &.disabled {
    .el-upload {
      pointer-events: none;
      background-color: var(--el-disabled-bg-color);

      .el-upload-dragger {
        background-color: var(--el-disabled-bg-color);
        pointer-events: none;
      }
    }
  }
}
</style>
