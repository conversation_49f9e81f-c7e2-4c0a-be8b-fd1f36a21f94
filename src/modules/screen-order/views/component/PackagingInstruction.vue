<template>
  <div>
    <div class="">
      <div class="rounded-xl border h-[600px]">
        <div class="flex justify-space-between bg-[#E6EFFC] rounded-t-xl h-[40px]">
          <div class="flex pt-1 px-2 rounded-t-xl">
            <div
                v-for="(tab, index) in dataPackaging"
                :key="index"
                class="flex pl-2 pr-3 cursor-pointer transition focus:outline-none rounded-t-xl max-w-[150px]"
                :class="{ 'bg-white': activeTab === tab?.id }"
                @click="changeTab(tab)"
            >
              <div class="mr-1 my-auto truncate">
                <el-tooltip class="item" effect="light" :content="tab.name" placement="top">
                  {{ tab.name }}
                </el-tooltip>
              </div>
              <div class="my-auto ml-2"
                   @click="removeTab(tab)"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="11" viewBox="0 0 10 11" fill="none">
                  <path d="M5.0001 6.5501L1.3251 10.2251C1.1876 10.3626 1.0126 10.4313 0.800097 10.4313C0.587597 10.4313 0.412597 10.3626 0.275097 10.2251C0.137597 10.0876 0.0688477 9.9126 0.0688477 9.7001C0.0688477 9.4876 0.137597 9.3126 0.275097 9.1751L3.9501 5.5001L0.275097 1.8251C0.137597 1.6876 0.0688477 1.5126 0.0688477 1.3001C0.0688477 1.0876 0.137597 0.912597 0.275097 0.775097C0.412597 0.637597 0.587597 0.568848 0.800097 0.568848C1.0126 0.568848 1.1876 0.637597 1.3251 0.775097L5.0001 4.4501L8.6751 0.775097C8.8126 0.637597 8.9876 0.568848 9.2001 0.568848C9.4126 0.568848 9.58759 0.637597 9.7251 0.775097C9.8626 0.912597 9.93135 1.0876 9.93135 1.3001C9.93135 1.5126 9.8626 1.6876 9.7251 1.8251L6.0501 5.5001L9.7251 9.1751C9.8626 9.3126 9.93135 9.4876 9.93135 9.7001C9.93135 9.9126 9.8626 10.0876 9.7251 10.2251C9.58759 10.3626 9.4126 10.4313 9.2001 10.4313C8.9876 10.4313 8.8126 10.3626 8.6751 10.2251L5.0001 6.5501Z" fill="#344054"/>
                </svg>
              </div>
            </div>
            <div
                @click="addTab()"
                class="my-auto cursor-pointer border-l-[#667085] pl-[12px]"
                :class="getActiveTabData?.instructions?.length > 0 ? 'border-l' : '' "
            >
              <svg class="" xmlns="http://www.w3.org/2000/svg" width="21" height="21" viewBox="0 0 21 21" fill="none">
                <path d="M10.5 1.96875C5.796 1.96875 1.96875 5.796 1.96875 10.5C1.96875 15.204 5.796 19.0312 10.5 19.0312C15.204 19.0312 19.0312 15.204 19.0312 10.5C19.0312 5.796 15.204 1.96875 10.5 1.96875ZM10.5 3.28125C14.4946 3.28125 17.7188 6.50541 17.7188 10.5C17.7188 14.4946 14.4946 17.7188 10.5 17.7188C6.50541 17.7188 3.28125 14.4946 3.28125 10.5C3.28125 6.50541 6.50541 3.28125 10.5 3.28125ZM9.84375 6.5625V9.84375H6.5625V11.1562H9.84375V14.4375H11.1562V11.1562H14.4375V9.84375H11.1562V6.5625H9.84375Z" fill="black"/>
              </svg>
              <!--          <span class="text-white bg-black"><icon :data="iconAddCircle" /></span>-->
            </div>
          </div>
          <div class="my-auto mr-[20px] cursor-pointer"  @click="showDetailPrinter(orderId)">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M6 17.983C4.447 17.936 3.52 17.763 2.879 17.121C2 16.243 2 14.828 2 12C2 9.172 2 7.757 2.879 6.879C3.757 6 5.172 6 8 6H16C18.828 6 20.243 6 21.121 6.879C22 7.757 22 9.172 22 12C22 14.828 22 16.243 21.121 17.121C20.48 17.763 19.554 17.936 18 17.983" stroke="black" stroke-width="1.5"/>
              <path d="M9 10H6M19 15H5" stroke="black" stroke-width="1.5" stroke-linecap="round"/>
              <path d="M18.75 15C18.75 14.8011 18.671 14.6103 18.5303 14.4697C18.3897 14.329 18.1989 14.25 18 14.25C17.8011 14.25 17.6103 14.329 17.4697 14.4697C17.329 14.6103 17.25 14.8011 17.25 15H18.75ZM6.75 15C6.75 14.8011 6.67098 14.6103 6.53033 14.4697C6.38968 14.329 6.19891 14.25 6 14.25C5.80109 14.25 5.61032 14.329 5.46967 14.4697C5.32902 14.6103 5.25 14.8011 5.25 15H6.75ZM17.25 16C17.25 17.435 17.248 18.436 17.147 19.192C17.048 19.926 16.867 20.314 16.591 20.591L17.651 21.651C18.254 21.05 18.512 20.291 18.634 19.391C18.752 18.513 18.75 17.393 18.75 16H17.25ZM12 22.75C13.393 22.75 14.513 22.752 15.392 22.634C16.292 22.512 17.05 22.254 17.652 21.652L16.59 20.59C16.313 20.867 15.925 21.047 15.19 21.146C14.435 21.247 13.434 21.249 11.999 21.249L12 22.75ZM12 2.75C13.435 2.75 14.437 2.752 15.192 2.853C15.926 2.952 16.314 3.133 16.591 3.409L17.651 2.349C17.05 1.746 16.291 1.488 15.391 1.367C14.513 1.248 13.393 1.25 12 1.25V2.75ZM12 1.25C10.607 1.25 9.487 1.248 8.608 1.367C7.708 1.487 6.95 1.747 6.348 2.348L7.41 3.41C7.687 3.133 8.075 2.953 8.81 2.854C9.564 2.753 10.566 2.751 12.001 2.751L12 1.25ZM5.25 16C5.25 17.393 5.248 18.513 5.367 19.392C5.487 20.292 5.747 21.05 6.348 21.652L7.41 20.59C7.133 20.313 6.953 19.925 6.854 19.19C6.753 18.435 6.751 17.434 6.751 15.999L5.25 16ZM12 21.25C10.565 21.25 9.563 21.248 8.808 21.147C8.074 21.048 7.686 20.867 7.409 20.591L6.349 21.651C6.95 22.254 7.709 22.512 8.609 22.634C9.487 22.752 10.607 22.75 12 22.75V21.25ZM18.732 5.977C18.686 4.435 18.524 3.22 17.652 2.348L16.59 3.41C17 3.82 17.185 4.459 17.232 6.024L18.732 5.977ZM6.767 6.023C6.814 4.458 6.998 3.82 7.409 3.409L6.349 2.349C5.476 3.22 5.314 4.435 5.268 5.977L6.767 6.023ZM18.75 16V15H17.25V16H18.75ZM6.75 16V15H5.25V16H6.75Z" fill="black"/>
              <path d="M17 11C17.5523 11 18 10.5523 18 10C18 9.44772 17.5523 9 17 9C16.4477 9 16 9.44772 16 10C16 10.5523 16.4477 11 17 11Z" fill="black"/>
            </svg>
          </div>
        </div>
      <div class="w-full overflow-y-auto overflow-x-hidden max-h-[calc(100vh-380px)] mt-5">
        <div class="m-4" v-if="getActiveTabData?.instructions">
          <el-row :gutter="20" class="mb-[16px]">
            <el-col :span="8" class="!font-normal">
              {{ $t('Client Name / Nombre del cliente') }}
            </el-col>
            <el-col :span="16">
              {{ getActiveTabData?.client?.name }}
            </el-col>
          </el-row>
          <el-row :gutter="20" class="mb-[16px]">
            <el-col :span="8" class="text-[#475467]">
              {{ $t('Order Type / Tipo de pedido') }}
            </el-col>
            <el-col :span="16">
              {{ capitalizeFirstString(getActiveTabData?.order_type) }}
            </el-col>
          </el-row>
          <hr>
        </div>
        <div v-else class="m-4 h-[400px] m-auto flex justify-center items-center">
          <div>
            <img class="m-auto" src="/src/assets/images/image-file.png" width="40" height="40" />
            <div class="m-auto text-center">No P.I selected / Ningún P.I. seleccionado.</div>
            <div class="m-auto text-center">
              <el-link @click="addTab()" ><span class="bold text-blue-600"> Click here to select P.I / Haga clic aquí para seleccionar P.I.</span></el-link>
            </div>
          </div>
        </div>
        <div v-for="item in getActiveTabData?.instructions" class="m-4">
          <el-row :gutter="20" class="mb-[16px]">
            <el-col :span="12" class="text-[#475467]">
                  <span class="font-semibold">
                    {{ mapTitle(item.type) }}
                  </span>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="mb-[12px]">
            <el-col :span="8" class="text-[#475467] my-auto">
              {{ $t('Supplied or Purchased by / Suministrado o comprado por') }}
            </el-col>
            <el-col :span="16">
              {{ item.supplied_purchased_by || '--' }}
            </el-col>
          </el-row>
          <el-row :gutter="20" class="mb-[12px]">
            <el-col :span="8" class="text-[#475467] my-auto">
              {{ $t('Instruction / Instrucciones') }}
            </el-col>
            <el-col :span="16">
              {{ item.instruction || '--' }}
            </el-col>
          </el-row>
          <el-row :gutter="20" class="mb-[12px]">
            <el-col :span="8" class="text-[#475467] my-auto">
              {{ $t('Reference image / Imagen de referencia') }}
            </el-col>
            <el-col :span="16">
              <div class="flex">
                <img v-if="item.image_1" :src="item.image_1" @click="showPreviewImage(item.image_1)" alt="reference_image" class="w-auto h-[60px] mr-3 rounded cursor-pointer">
                <img v-if="item.image_2" :src="item.image_2" @click="showPreviewImage(item.image_2)" alt="reference_image" class="w-auto h-[60px] rounded cursor-pointer">
              </div>

            </el-col>
          </el-row>
          <hr>
        </div>
      </div>
    </div>

    <el-dialog
        v-model="openDialogAdd"
        :title="$t('SELECT PACKING INSTRUCTION / SELECCIONAR INSTRUCCIONES DE EMPAQUE')"
        custom-class="el-dialog-custom el-dialog-box"
        class="w-full overflow-auto max-h-[calc(100vh-220px)] border border-gray-200 mt-5 !text-black"
        @close="closeModalAddOrderPackaging"
    >
      <template #default>
        <div class="top-head filter mb-5" >
          <el-input
              :placeholder="$t('P.I Name')"
              v-model="filter.name"
              class="search mr-2"
          />
          <el-input
              :placeholder="$t('Client Name')"
              v-model="filter.client_name"
              class="search mr-2"
          />
          <el-select v-model="filter.order_type" placeholder="Order Type" class="mr-2">
            <el-option
                v-for="item in orderTypes"
                :key="item.value"
                :label="item.name"
                :value="item.value">
            </el-option>
          </el-select>
          <el-button type="primary" @click="fetchDataOrderPackaging">
            <span class="icon-margin-right"><icon :data="iconFilter" /></span
            >{{ $t('Filter') }}
          </el-button>
        </div>
        <table class="w-full" >
          <thead class="sticky top-0 z-[10] bg-gray-200 border-b border-gray-200">
            <tr class="bg-gray-100 font-bold">
              <th  class="text-center p-2 break-normal whitespace-nowrap border-r border-gray-200"></th>
              <th  class="text-center p-2 break-normal whitespace-nowrap border-r border-gray-200">
                ID
              </th>
              <th class="text-center p-2 break-normal whitespace-nowrap border-r border-gray-200" style="width: 50px;">
                P.I Name / Nombre de P.I
              </th>
              <th  class="text-center p-2 break-normal whitespace-nowrap border-r border-gray-200">
                Client name / Nombre del cliente
              </th>
              <th  class="text-center p-2 break-normal whitespace-nowrap border-r border-gray-200">
                Order type / Tipo de pedido
              </th>
            </tr>
          </thead>
          <tbody>
          <tr
              class="hover:bg-gray-100 border-b border-gray-200 group"
              v-for="item in dataOrderPackaging"
              :key="item.id"
          >
            <th class="px-2 py-1 border-r border-gray-200 w-3">
              <div class="flex items-center">
                <input v-model="packagingIds" :key="item.id" :value="item.id" name="packagingIds[]"
                       id="disabled-checked-checkbox"
                       type="checkbox"
                       class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
              </div>
            </th>
            <th class="px-2 py-1 border-r w-16 font-normal">{{ item?.id}}</th>
            <th class="px-2 py-1 border-r break-normal font-normal" >
              <el-link
                  :underline="false"
                  type="primary"
                  @click="showDetailPI(item?.id)"
              > {{ item?.name}}
              </el-link>
            </th>
            <th class="px-2 py-1 border-r w-16">{{ item?.client?.name}}</th>
            <th class="px-2 py-1 border-r w-16 font-normal">
              <el-tag :type="generateColor(item?.order_type)">{{ 'For ' + capitalizeFirstString(item?.order_type) }}</el-tag>
            </th>
          </tr>
          </tbody>
        </table>

      </template>
      <template #footer>
        <div class="dialog-footer">
          <el-button
              type="primary"
              @click="addOrderPackagingInstruction"

          >{{ $t('Apply / Aplicar') }}</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
  </div>
</template>

<script>

import {getList, getListScreenPackagingOrder} from '@/api/screenPackaging';
import {destroy, create } from "@/api/screenOrderPackaging";
import numberMixin from '@/mixins/formatNumber.js';
import PrintPackaging from '@/modules/screen-order/views/component/PrintPackaging.vue';
import helperMixin from '@/mixins/helpers.js';
import dateMixin from "@/mixins/date";
import filterMixin from "@/mixins/filter";
import {equals} from "ramda";
import EventBus from "@/utilities/eventBus";

export default {
  name: "PackagingInstruction",
  mixins: [dateMixin, numberMixin, filterMixin, helperMixin],
  props: {},
  components: {PrintPackaging},
  watch: {},
  created() {
    this.orderId = this.$route?.params?.id;
  },
  unmounted() {
  },
  computed: {
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },
    getActiveTabData() {
      return this.dataPackaging?.find((t) => t.id === this.activeTab);
    },
  },
  mounted() {
    this.fetchDataOrderPackaging()
  },
  data() {
    return {
      orderId: null,
      activeTab: '',
      dataPackaging: [],
      dataOrderPackaging: [],
      openDialogAdd: false,
      total: 0,
      orderTypes: [
        {
          value: "",
          name: "All",
        },
        {
          value: "store",
          name: "Store",
        },
        {
          value: "ecom",
          name: "Ecom",
        }
      ],
      packagingIds: [],
      isLoading: false,
      packaging: {},
      filter: this.setDefaultFilter(),
      sortByType: [
        'artwork',
        'reference_image',
        'price_sticker',
        'hang_tag',
        'plastic_bags',
        'folded',
        'hologram',
        'size_sticker',
        'size_trip',
        'bags',
        'polybag_sticker',
        'carton_liner',
        'prepack_set',
        'boxes',
        'packing_list',
        'labels_for_boxes',
        'transport_to_use',
        'shipping_address',
      ]
    }
  },
  methods: {
    mapTitle(str) {
      switch (str) {
        case 'size_trip':
          return this.$t('Size strip');
        case 'artwork':
          return this.$t('Artwork / Diseño');
        case 'reference_image':
          return this.$t('Reference Image / Imagen de referencia');
        case 'plastic_bags':
          return this.$t('Plastic Bags / Balas de plastico');
        case 'folded':
          return this.$t('Folded / Doblado');
        case 'bags':
          return this.$t('Bags / Bolsas');
        case 'prepack_set':
          return this.$t('PrePack Set / Juegos prepack');
        case 'boxes':
          return this.$t('Boxes / Cajas');
        case 'labels_for_boxes':
          return this.$t('Labels For Boxes / Etiquetas para cajas');
        case 'transport_to_use':
          return this.$t('Transport To Use / Transporte a utilizar');
        case 'shipping_address':
          return this.$t('Shipping Address / Direccion del envio');
        default:
          return this.capitalizeAfterSpace(str);

      }
    },
    async closeModalAddOrderPackaging(){
      await this.onClearFilter();
      this.openDialogAdd = false;
    },
    showDetailPI(orderId) {
      const routeData = this.$router.resolve({ name: 'screen_packaging_detail', params: { id: orderId } });
      window.open(routeData.href, '_blank');
    },
    showDetailPrinter(orderId) {

      const routeData = this.$router.resolve({ name: 'screen_order_packaging_detail', params: { id: orderId } });
      window.open(routeData.href, '_blank');
    },
    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.$nextTick(() => {
        this.fetchDataOrderPackaging();
      });
    },
    setDefaultFilter() {
      return {
        limit: 10,
        page: 1,
        name: '',
        client_name: '',
        order_type: ''
      };
    },
    handleSelectionChange(data) {
      this.packagingIds = [];
      for (let item of data) {
        this.packagingIds.push(item.id);
      }
    },
    generateColor(type) {
      switch (type) {
        case 'store':
          return 'primary';
        default:
          return 'success';

      }
    },
    addTab() {
      this.openDialogAdd = true
    },
    changeTab(tab) {
      this.activeTab = tab.id;
      this.packaging = tab;
    },
    async removeTab(packaging) {
      try {
        if (packaging.order_packagings.length > 0) {
          await destroy(packaging.order_packagings[0].id)
        }
      } catch (e) {
        let message = e.response.data.message || this.$t('Error.');
        this.notification(message, 'error');
      } finally {
        this.fetchDataOrderPackaging();
      }
    },
    async fetchDataOrderPackaging() {
      try {
        this.isLoading = true;
        this.packagingIds = [];
        this.filter.order_id = this.orderId
        const data = await getListScreenPackagingOrder(this.filter)
        this.dataOrderPackaging = data.data
        this.total = data.data.total || 0;
        if (data.data.length > 0) {
          this.dataPackaging = data.data.filter(item => Array.isArray(item?.order_packagings) && item.order_packagings.length > 0);
          this.activeTab = this.dataPackaging[0]?.id || '';
          this.dataPackaging.map(item => {
            this.packagingIds.push(item.id);
          });
        }
        this.dataPackaging?.forEach((item) => {
          if (item?.instructions) {
            item?.instructions?.sort(
              (a, b) => this.sortByType.indexOf(a.type) - this.sortByType.indexOf(b.type)
            );
          }
        });
      } catch (e) {
        let message = e.response || this.$t('Error.');
        this.notification(message, 'error');
      } finally {
        this.isLoading = false;
      }
    },
    changePage(page) {
      this.filter.page = page;
      this.fetchDataOrderPackaging();
    },
    onFilter() {
      this.filter.page = 1;
      this.fetchDataOrderPackaging();
    },
    async addOrderPackagingInstruction() {
      try {
        const param = {
          order_id: this.orderId,
          packaging_ids: this.packagingIds
        }
        await create(param)
      } catch (e) {
        let message = e.response.data.message || this.$t('Error.');
        this.notification(message, 'error');
      } finally {
        this.openDialogAdd = false,
        this.fetchDataOrderPackaging();
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>