 <template>
  <div v-loading="isLoading" class="">

    <div class="bg-[#D9D9D9]">
      <div class="w-[800px] bg-white mx-auto px-8 rounded-xl">
        <div class="header mb-[12px] pt-[20px]">
          <span class="text-[16px]">{{ $t('Order Number / Número de pedido:' ) }}</span>
          <span class="font-bold text-[16px]">
            {{ order.order_number }} - {{ order.client?.name }}
          </span>
        </div>
        <div>
          <div class="flex mb-[12px]">
                <span class="mr-4">
                  {{ $t('Customer’s Ref Number: ') + order.external_number }}
                </span>
            <span>
                  {{ $t('Total: ') + (orderPackaging.length || 0) + $t(' P.I(s)') }}
                </span>
          </div>
          <div>
            <el-row :gutter="20" class="mb-[12px]">
              <el-col :span="8">
                {{ $t('Ship By') }}
              </el-col>
              <el-col :span="16">
                {{ formatDate(order.shipped_at, false) || '--' }}
              </el-col>
            </el-row>

            <el-row :gutter="20" class="mb-[12px]">
              <el-col :span="8">
                {{ $t('Shipping Address / Direccion del envio') }}
              </el-col>
              <el-col :span="16" v-if="order?.shipping_address?.length > 0" >
                {{ order?.shipping_address[0].street1 || "" }}, <template v-if="order?.shipping_address[0].street2" >{{ order?.shipping_address[0].street2 || "" }}, </template>
                {{ order?.shipping_address[0].city || "" }}, <br /> {{ order?.shipping_address[0].state || "" }},
                {{ order?.shipping_address[0].zipcode || "" }},
                {{ order?.shipping_address[0].country || "" }}
              </el-col>
              <el-col :span="16" v-else >
                --
              </el-col>
            </el-row>


            <el-row :gutter="20" class="mb-[12px]">
              <el-col :span="8">
                {{ $t('Order Type / Tipo de pedido') }}
              </el-col>
              <el-col :span="16">
                {{ capitalizeFirstString(order.order_type) || '--' }}
              </el-col>
            </el-row>

          </div>
          <hr>
          <div v-for="item in orderPackaging">
            <div>
              <el-row :gutter="20" class="mb-[12px]">
                <div class="text-black text-base font-bold mx-auto">{{ $t('P.I: ') + item.name}}</div>

              </el-row>
            </div>
            <div v-for="item in item.instructions">
              <div v-if="item.type == 'reference_image'">
                <el-row  :gutter="20" class="mb-[16px]">
                  <el-col :span="24" class="text-[#475467]">
                <span class="font-semibold">
                  {{ capitalizeAfterSpace(item.type) }}
                </span>
                  </el-col>
                </el-row>
                <el-row :gutter="20" class="mb-[12px]" >
                  <el-col :span="8" class="text-[#475467]">
                    {{ capitalizeAfterSpace(item.type) }}
                  </el-col>
                  <el-col :span="16">
                    <div class="flex">
                      <img v-if="item.image_1" :src="item.image_1" @click="showPreviewImage(item.image_1)" alt="reference_image" class="w-auto h-[60px] mr-3 rounded cursor-pointer">
                      <img v-if="item.image_2" :src="item.image_2" @click="showPreviewImage(item.image_2)" alt="reference_image" class="w-auto h-[60px] rounded cursor-pointer">
                      <span v-if="!item.image_1 && !item.image_2">
                  --
                </span>
                    </div>
                  </el-col>
                </el-row>
              </div>
              <div v-else>
                <el-row :gutter="20" class="mb-[16px]">
                  <el-col :span="24" class="text-[#475467]">
                  <span class="font-semibold">
                    {{ capitalizeAfterSpace(item.type) }}
                  </span>
                  </el-col>
                </el-row>
                <el-row :gutter="20" class="mb-[12px]" >
                  <el-col :span="8" class="text-[#475467]">
                    {{ $t('Supplied or Purchased by / Suministrado o comprado por') }}
                  </el-col>
                  <el-col :span="16">
                    {{ item.supplied_purchased_by || '--' }}
                  </el-col>
                </el-row>

                <el-row :gutter="20" class="mb-[12px]" >
                  <el-col :span="8" class="text-[#475467]">
                    {{ $t('Instruction / Instrucciones') }}
                  </el-col>
                  <el-col :span="16">
                    {{ item.instruction || '--' }}
                  </el-col>
                </el-row>
                <el-row :gutter="20" class="mb-[12px]" >
                  <el-col :span="8" class="text-[#475467]">
                    {{ $t('Reference image / Imagen de referencia') }}
                  </el-col>
                  <el-col :span="16" >
                    <div class="flex">
                      <img v-if="item.image_1" :src="item.image_1" @click="showPreviewImage(item.image_1)" alt="reference_image" class="w-auto h-[60px] mr-3 rounded cursor-pointer">
                      <img v-if="item.image_2" :src="item.image_2" @click="showPreviewImage(item.image_2)" alt="reference_image" class="w-auto h-[60px] rounded cursor-pointer">
                      <span v-if="!item.image_1 && item.image_1">--</span>
                    </div>

                  </el-col>
                </el-row>
              </div>
              <hr>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
        v-model="dialogPreview"
        width="500px"
        title="Preview Image"
    >
      <img class="w-auto" :src="previewImage" alt="Preview" />
    </el-dialog>
  </div>
</template>

<script>
import EventBus from '@/utilities/eventBus.js';
import {getOrderPackagingDetail} from '@/api/screenOrder';
import dateMixin from '@/mixins/date.js';
import helperMixin from '@/mixins/helpers.js';
import formatNumber from "@/mixins/formatNumber";
import AddressForm from '@/modules/sale-order/components/AddressForm.vue';

export default {
  name: 'OrderDetail',
  mixins: [dateMixin, formatNumber, helperMixin],
  props: {
  },
  components: {
    AddressForm
  },
  data() {
    return {
      openDialogDetail: false,
      order: {},
      orderPackaging: [],
      isLoading: false,
      instructions: [],
      dialogPreview: false,
      previewImage: '',
      sortByType: [
        'artwork',
        'reference_image',
        'price_sticker',
        'hang_tag',
        'plastic_bags',
        'folded',
        'hologram',
        'size_sticker',
        'size_trip',
        'bags',
        'polybag_sticker',
        'carton_liner',
        'prepack_set',
        'boxes',
        'packing_list',
        'labels_for_boxes',
        'transport_to_use',
        'shipping_address',
      ]
    }
  },
  watch: {
  },
  async created() {
    let id = this.$route?.params?.id;
    if (id) {
      this.isLoading = true;
      await this.getOrderPackagingDetail(id);
      this.isLoading = false;
    }
  },
  unmounted() {
    EventBus.$off("showPrintScreenOrderPackaging");
  },
  methods: {
    showPreviewImage(url) {
      this.previewImage = url;
      this.dialogPreview = true;
    },
    async getOrderPackagingDetail(id) {
      const res = await getOrderPackagingDetail(id);
      this.order = res?.data.data || {};
      this.orderPackaging = this.order.packagings;
      this.orderPackaging?.forEach((item) => {
        if (item?.instructions) {
          item?.instructions?.sort(
            (a, b) => this.sortByType.indexOf(a.type) - this.sortByType.indexOf(b.type)
          );
        }
      });      
    },
  }
};
</script>
<style lang="scss">
.word-break th.el-table__cell {
  word-break: break-word;
  background-color: #E6EFFC;
}

.timeline-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  position: relative;
}

.timeline-item .circle {
  width: 26px;
  height: 26px;
  background-color: #e5e5f7;
  color: #555;
  font-weight: bold;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  position: absolute;
  top: -3px;
  left: -13px;
}

.timeline-item .content {
  margin-left: 20px;
}

.timeline-item .title {
  margin: 0;
  font-weight: bold;
  font-size: 16px;
  color: #333;
}

.timeline-item .meta {
  margin-top: 5px;
  font-size: 14px;
  color: #666;
}

.timeline-item .meta .user {
  margin-right: 10px;
}

.timeline-item .meta .date {
  color: #999;
}

.tabs {
  display: flex;
  border: 1px solid #ddd;
  border-radius: 5px;
  overflow: hidden;
  background-color: #f9f9f9;
}

.tab {
  flex: 1;
  padding: 4px 4px;
  text-align: center;
  cursor: pointer;
  color: #666;
  background-color: #f9f9f9;
  transition: background-color 0.3s ease;
}

.tab:last-child {
  border-right: none;
}

.tab .active {
  background-color: #fff;
  color: #333;
  //font-weight: bold;
}

//.content {
//  margin-top: 20px;
//  padding: 20px;
//  border: 1px solid #ddd;
//  border-radius: 5px;
//  background-color: #fff;
//}

</style>
