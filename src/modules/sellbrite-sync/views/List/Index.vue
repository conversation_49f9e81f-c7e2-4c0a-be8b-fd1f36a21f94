<style src="./Style.scss" lang="scss"></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head mb-4">
      <div class="top-head-left">
        <h1>{{ $t('Sellbrite Sync') }}</h1>
      </div>
      <div class="top-head-right">
        <el-button
          type="primary"
          @click="$router.push({ name: 'sellbrite_sync_warehouses' })"
        >
          {{ $t('Map Sellbrite Warehouse') }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <div class="filter">
        <el-input
          :placeholder="$t('Search sku')"
          class="search mr-3"
          v-model="filter.sku"
          @keyup.enter="onFilter"
        />
        <div class="btn-filter">
          <template v-if="hasFilter">
            <el-link type="danger" @click="onClearFilter" :underline="false">{{
              $t('Clear')
            }}</el-link>
          </template>
          <el-button type="primary" @click="onFilter">
            <span class="icon-margin-right"><icon :data="iconFilter" /></span
            >{{ $t('Filter') }}
          </el-button>
        </div>
      </div>
      <el-table
        border
        stripe
        size="small"
        :data="items"
        :max-height="maxHeight"
        v-loading="isLoading"
        element-loading-text="Loading..."
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column
          prop="id"
          :label="$t('ID')"
          width="60"
        ></el-table-column>
        <el-table-column
          prop="sku"
          :label="$t('Sku')"
          min-width="100"
        ></el-table-column>
        <el-table-column :label="$t('Style')" min-width="80">
          <template #default="scope">
            {{ scope.row.product?.style }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Color')" min-width="80">
          <template #default="scope">
            {{ scope.row.product?.color }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Size')" min-width="80">
          <template #default="scope">
            {{ scope.row.product?.size }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('In stock')" min-width="70">
          <template #default="scope">
            {{ getQuantity(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column min-width="150" :label="$t('Cost')">
          <template #default="scope">
            <template v-if="scope.row.edit">
              <div class="flex flex-col gap-2">
                <el-input v-model="scope.row.cost" size="small" />
                <div>
                  <el-button
                    v-if="scope.row.edit"
                    type="success"
                    size="small"
                    @click="confirmEdit(scope.row)"
                  >
                    OK
                  </el-button>
                  <el-button
                    size="small"
                    type="warning"
                    class="!ml-1"
                    @click="cancelEdit(scope.row)"
                  >
                    Cancel
                  </el-button>
                </div>
              </div>
            </template>
            <template v-else>
              <span
                @click="scope.row.edit = !scope.row.edit"
                class="text-blue-500 cursor-pointer"
              >
                {{ formatCost(scope.row.cost) }}
              </span>
            </template>
          </template>
        </el-table-column>
        <el-table-column :label="$t('Status')" min-width="100">
          <template #default="scope">
            <el-tag :type="scope.row.added_sync_at ? 'success' : ''">{{
              scope.row.added_sync_at ? 'In queue' : 'Wait to add'
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="$t('Last Sync')" min-width="140">
          <template #default="scope">
            {{ listViewDateFormat(scope.row.last_synced_at) }}
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom">
        <div class="total">
          {{ $t('Total:') }} {{ items.length ? formatNumber(total) : 0 }}
        </div>
        <el-pagination
          :disabled="isLoading"
          background
          layout="prev, pager, next"
          :page-size="filter.limit"
          :total="total"
          @current-change="changePage"
          v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
            v-model="filter.limit"
            :placeholder="$t('Select')"
            size="mini"
            @change="onFilter"
          >
            <el-option
              v-for="item in limits"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
  </div>
</template>
