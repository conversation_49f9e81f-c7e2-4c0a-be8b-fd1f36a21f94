import {
  getList,
  update,
} from '@/api/sellbriteSync';

export default {
  name: 'SellbriteSyncList',
  data() {
    return {
      items: [],
      isLoading: false,
      filter: this.setDefaultFilter(),
      selection: [],
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 330);
    },
  },
  mounted() {
    this.filter = this.getRouteParam();
    this.getSellbriteSyncList();
  },
  methods: {
    getQuantity(row) {
      return row.product?.product_quantities?.find(
        (pq) => pq.warehouse_id == row.sellbrite_warehouse.warehouse_id
      )?.quantity;
    },
    setDefaultFilter() {
      return {
        limit: 25,
        page: 1,
        sku: '',
      };
    },
    async getSellbriteSyncList() {
      this.isLoading = true;
      try {
        this.setRouteParam();
        const { data } = await getList(this.filter);
        this.items = data.data?.map((item) => {
          item.edit = false;
          item.originalCost = item.cost;
          return item;
        });
        this.total = data.total;
      } catch (e) {
        this.serverErrors = e.response.data.errors;
        let message = e.response.data.message;
        this.notification(message, 'error', false, { duration: 10000 });
      } finally {
        this.isLoading = false;
      }
    },
    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.getSellbriteSyncList();
    },
    changePage(page) {
      this.filter.page = page;
      this.getSellbriteSyncList();
    },
    onFilter() {
      this.filter.page = 1;
      this.getSellbriteSyncList();
    },
    handleSelectionChange(val) {
      this.selection = val;
    },
    formatCost(value) {
      return `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },
    cancelEdit(row) {
      row.cost = row.originalCost;
      row.edit = false;
    },
    async confirmEdit(row) {
      this.isLoading = true;
      try {
        await update(row.id, { cost: row.cost });

        row.edit = false;
        row.originalCost = row.cost;
      } catch (e) {
        this.serverErrors = e.response.data.errors;
        let message = e.response.data.message;
        this.notification(message, 'error', false, { duration: 10000 });
      } finally {
        this.isLoading = false;
      }
    },
  },
};
