<style src="./Style.scss" lang="scss"></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head mb-4">
      <div class="top-head-left">
        <h1>{{ $t('Sellbrite Warehouses') }}</h1>
      </div>
      <div class="top-head-right">
        <el-button
          type="primary"
          @click="$router.push({ name: 'sellbrite_sync' })"
        >
          {{ $t('Back to Sellbrite Sync') }}
        </el-button>
        <el-button type="primary" @click="save()">
          {{ $t('Save') }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <el-table
        border
        stripe
        size="small"
        :data="items"
        :max-height="maxHeight"
        v-loading="isLoading"
        element-loading-text="Loading..."
      >
        <el-table-column
          prop="id"
          :label="$t('ID')"
          width="100"
        ></el-table-column>
        <el-table-column
          prop="uuid"
          :label="$t('UUID')"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="name"
          :label="$t('Name')"
          min-width="100"
        ></el-table-column>
        <el-table-column :label="$t('Warehouse')" min-width="100">
          <template #default="scope">
            <el-select
              v-model="scope.row.warehouse_id"
              class="m-2"
              placeholder="Select warehouse"
            >
              <el-option
                v-for="item in warehouses"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
