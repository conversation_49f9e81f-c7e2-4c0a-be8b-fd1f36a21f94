import { getList, map } from '@/api/sellbriteWarehouse';
import { list as getListWarehouse } from '@/api/warehouse';

export default {
  name: 'SellbriteWarehouseList',
  data() {
    return {
      items: [],
      warehouses: [],
      isLoading: false,
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 230);
    },
  },
  mounted() {
    this.getSellbriteWarehouseList();
  },
  methods: {
    async getSellbriteWarehouseList() {
      this.isLoading = true;
      try {
        const { data } = await getList();
        const { data: warehouses } = await getListWarehouse();
        this.items = data;
        this.warehouses = warehouses;
      } catch (e) {
        this.serverErrors = e.response.data.errors;
        let message = e.response.data.message;
        this.notification(message, 'error', false, { duration: 10000 });
      } finally {
        this.isLoading = false;
      }
    },
    async save() {
      this.isLoading = true;
      try {
        const { updatedItems } = await map(
          this.items.reduce(
            (data, item) => {
              data.ids.push(item.id);
              data.warehouse_ids.push(item.warehouse_id);
              return data;
            },
            { ids: [], warehouse_ids: [] }
          )
        );
        this.items = updatedItems;
      } catch (e) {
        this.serverErrors = e.response.data.errors;
        let message = e.response.data.message;
        this.notification(message, 'error', false, { duration: 10000 });
      } finally {
        this.isLoading = false;
        return this.$router.push({ name: 'sellbrite_sync' });
      }
    },
  },
};
