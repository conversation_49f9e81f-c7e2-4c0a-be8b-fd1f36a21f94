<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left"><h1>{{ $t('Setting move warehouse') }}</h1></div>
      <div class="top-head-right">
        <!-- <el-button type="info" @click="exportExcel">
          <span class="icon-margin-right"><icon :data="iconExport" /></span
          >{{ $t('Export') }}
        </el-button> -->
        <el-button type="primary" @click="createEvent()">
          <span class="icon-margin-right"></span>{{ $t('Create') }}
        </el-button>
      </div>
    </div>
    <div class="table-content mt-2">
      <el-table
        border
        stripe
        size="small"
        :data="listEventSetting"
        style="width: 100%"
        :max-height="maxHeight"
      >
        <el-table-column prop="name" :label="$t('Name')" width="250">
        </el-table-column>
        <el-table-column prop="code" :label="$t('Code')" width="250">
        </el-table-column>
        <el-table-column prop="created_at" :label="$t('Date')" width="180">
        </el-table-column>
        <el-table-column prop="is_active" :label="$t('Active')"></el-table-column>
        <el-table-column :label="$t('Action')" width="120">
          <template #default="scope">
            <el-button type="text" size="small" @click="editEvent(scope.row)"
              >{{ $t('Edit') }}</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog
      custom-class="el-dialog-custom"
      :title="$t('Setting move warehouse')"
      v-model="dialogTableVisible"
      :close-on-click-modal="false"
    >
      <el-form
        :model="settingEvent"
        :rules="rules"
        ref="ruleForm"
        label-width="168px"
        class="demo-ruleForm"
        :label-position="'left'"
      >
        <el-form-item :label="$t('Active')" prop="delivery">
          <el-switch v-model="settingEvent.active"></el-switch>
        </el-form-item>
        <el-form-item :label="$t('Daily limit ')" prop="limit">
          <el-input-number
            v-model="settingEvent.limit_daily"
            :min="1"
            :max="9999999"
            style="width: 20%"
          ></el-input-number>
        </el-form-item>
        <el-form-item :label="$t('Name')" prop="name">
          <el-input v-model="settingEvent.name"></el-input>
        </el-form-item>
        <el-form-item :label="$t('Code')" prop="code">
          <el-input v-model="settingEvent.code" readonly></el-input>
        </el-form-item>
        <el-form-item :label="$t('(Excluding time period)')" required></el-form-item>
        <el-form-item :label="$t('Start date')" required>
          <el-col :span="11">
            <el-form-item prop="startDate">
              <el-select v-model="settingEvent.startDate" :placeholder="$t('Select')">
                <el-option
                  v-for="item in listDay"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col class="line" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="startHours">
              <el-time-select
                v-model="settingEvent.startHours"
                start="00:00"
                step="00:15"
                end="24:00"
                :placeholder="$t('Select time')"
              >
              </el-time-select>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item :label="$t('End date')" required>
          <el-col :span="11">
            <el-form-item prop="endDate">
              <el-select v-model="settingEvent.endDate" :placeholder="$t('Select')">
                <el-option
                  v-for="item in listDay"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col class="line" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="endHours">
              <el-time-select
                v-model="settingEvent.endHours"
                start="00:00"
                step="00:15"
                end="24:00"
                :placeholder="$t('Select time')"
              >
              </el-time-select>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item :label="$t('List sku')" required>
          <div
            v-for="(item, index) in settingEvent.listSku"
            :key="index"
            class="w-full"
          >
            <el-row class="input-sku" :gutter="20">
              <el-col :span="18">
                <el-input
                  :placeholder="$t('Please input')"
                  @keyup.enter="addNewSku"
                  v-model="settingEvent.listSku[index].value"
                ></el-input>
              </el-col>
              <el-col :span="2">
                <el-switch v-model="settingEvent.listSku[index].status">
                </el-switch>
              </el-col>
              <el-col :span="2">
                <el-button
                  type="danger"
                  size="mini"
                  class="right"
                  @click="removeItem(index)"
                  ><icon :data="iconDelete"
                /></el-button>
              </el-col>
              <el-col :span="2" v-if="index == settingEvent.listSku.length - 1">
                <el-button @click="addNewSku()" type="primary" size="mini"
                  ><icon :data="iconAdd"
                /></el-button>
              </el-col>
            </el-row>
          </div>

          <!--          <el-tag-->
          <!--              :key="tag"-->
          <!--              v-for="tag in settingEvent.listSku"-->
          <!--              closable-->
          <!--              :disable-transitions="false"-->
          <!--              @close="handleClose(tag)"-->
          <!--          >-->
          <!--            {{tag}}-->
          <!--          </el-tag>-->
          <!--          <el-input-->
          <!--              class="input-new-tag"-->
          <!--              v-if="inputVisible"-->
          <!--              v-model="inputValue"-->
          <!--              ref="saveTagInput"-->
          <!--              size="mini"-->
          <!--              @keyup.enter="handleInputConfirm"-->
          <!--              @blur="handleInputConfirm"-->
          <!--          >-->
          <!--          </el-input>-->
          <!--          <el-button v-else class="button-new-tag" size="small" @click="showInput"-->
          <!--          >+ New Sku</el-button-->
          <!--          >-->
        </el-form-item>

        <!-- <el-form-item>
          <el-button type="primary" @click="submitForm('ruleForm')"
            >{{ $t('Save') }}</el-button
          >
          <el-button @click="resetForm('ruleForm')">{{ $t('Reset') }}</el-button>
        </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetForm('ruleForm')">{{ $t('Reset') }}</el-button>
          <el-button type="primary" @click="submitForm('ruleForm')"
            >{{ $t('Save') }}</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>
