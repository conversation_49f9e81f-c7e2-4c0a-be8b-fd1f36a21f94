import { store, list } from "@/api/settingEventWarehouse.js";
import { listImageAxr4 } from "../../../api/product";
export default {
  name: "SettingMoveWarehouse",
  components: {},
  mixins: [],
  data() {
    return {
      dialogTableVisible: false,
      settingEvent: {
        name: "",
        code: "order_texas2",
        startHours: "",
        startDate: "",
        endHours: "",
        endDate: "",
        listSku: [],
        active: false,
      },
      listEventSetting: [],
      listDay: [
        {
          label: this.$t('Monday'),
          value: "1",
        },
        {
          label: this.$t('Tuesday'),
          value: "2",
        },
        {
          label: this.$t('Wednesday'),
          value: "3",
        },
        {
          label: this.$t('Thursday'),
          value: "4",
        },
        {
          label: this.$t('Friday'),
          value: "5",
        },
        {
          label: this.$t('Saturday'),
          value: "6",
        },
        {
          label: this.$t('Sunday'),
          value: "7",
        },
      ],
      rules: {
        name: [
          {
            required: true,
            message: this.$t('Please input name'),
            trigger: "blur",
          },
          {
            min: 3,
            max: 20,
            message: this.$t('Length should be 3 to 20'),
            trigger: "blur",
          },
        ],
        startDate: [
          {
            required: true,
            message: this.$t('Please input start date'),
            trigger: "blur",
          },
        ],
        startHours: [
          {
            required: true,
            message: this.$t('Please input start hours'),
            trigger: "blur",
          },
        ],
        endDate: [
          {
            required: true,
            message: this.$t('Please input end date'),
            trigger: "blur",
          },
        ],
        endHours: [
          {
            required: true,
            message: this.$t('Please input end hours'),
            trigger: "blur",
          },
        ],
      },
      inputVisible: false,
      inputValue: "",
    };
  },
  beforeUnmount() {},
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 230);
    },
  },
  mounted() {
    this.listEvent();
  },
  methods: {
    async listEvent() {
      const res = await list();
      this.listEventSetting = res.data;
      console.log(this.listEventSetting);
    },
    createEvent() {
      this.settingEvent = {
        name: "",
        code: "order_texas2",
        startHours: "",
        startDate: "",
        endHours: "",
        endDate: "",
        listSku: [],
        active: false,
      };
      this.dialogTableVisible = true;
    },
    editEvent(row) {
      console.log(row);
      this.settingEvent = {
        name: row.name,
        code: row.code,
        startHours: row.rule.startHours,
        startDate: row.rule.startDate,
        endHours: row.rule.endHours,
        endDate: row.rule.endDate,
        listSku: row.rule.listSku,
        active: row.is_active == 0 ? false : true,
        id: row.id,
        limit_daily: row.limit_daily,
      };
      if (this.settingEvent.listSku == undefined) {
        this.settingEvent.listSku = [];
      }
      console.log(this.settingEvent.listSku);
      this.settingEvent.listSku.push({ value: "", status: false });
      this.dialogTableVisible = true;
    },
    async storeEvent() {
      try {
        // console.log(this.settingEvent);
        const res = await store(this.settingEvent);
        this.dialogTableVisible = false;
        this.$message({
          type: "success",
          message: this.$t('Update event success'),
        });
        this.listEvent();
      } catch (e) {
        console.log("tievmmmm");
        console.log(e);
        this.dialogTableVisible = false;
        this.$message({
          type: "warning",
          message: this.$t("code exist !"),
        });
        this.listEvent();
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.storeEvent();
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    removeItem(index) {
      this.settingEvent.listSku.splice(index, 1);
    },

    showInput() {
      this.inputVisible = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },

    addNewSku() {
      this.settingEvent.listSku.push({ value: "", status: false });
    },
  },
};
