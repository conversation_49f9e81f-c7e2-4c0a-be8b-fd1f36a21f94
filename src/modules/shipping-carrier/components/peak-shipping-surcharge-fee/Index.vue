<style src="./Style.scss" lang="scss" scoped></style>
<!-- <script src="./Script.js"></script> -->

<template>
  <div class="top-head mb-4">
    <div class="top-head-left"></div>
    <div class="top-head-right">
      <el-button v-if="!isEdit" type="primary" @click="enableEditStatus">
        {{ $t('Edit') }}
      </el-button>
      <el-button v-else type="danger" @click="cancelEditStatus">
        {{ $t('Cancel') }}
      </el-button>
    </div>
  </div>
  <div class="table-content">
    <el-row>
      <el-col :span="10">
        <div class="grid-content bg-purple">
          <span>
            This surcharge fee will apply to orders shipped within the effective
            date range and shipped via USPS and DHL carriers
          </span>
          <br />
          <br />
          <div>
            <el-switch v-model="status" @change="openConfirmDialog"></el-switch>
            Visible on the Seller Dashboard

            <!-- Dialog xác nhận -->

            <el-dialog title="Confirmation" :width="350" :model-value="isConfirmDialogVisible"
              @close="cancelUpdateStatus">
              <span>{{ $t('Are you sure you want to update this?') }}</span>
              <template #footer>
                <el-button @click="cancelUpdateStatus">{{
                  $t('Cancel')
                }}</el-button>
                <el-button type="primary" @click="confirmUpdateStatus">{{
                  $t('Confirm')
                }}</el-button>
              </template>
            </el-dialog>
          </div>
        </div>
      </el-col>

      <el-col :span="12">
        <div class="grid-content bg-purple-light">
          <el-form :model="form" :rules="rules" ref="formRef" label-width="120px" :disabled="!isEdit">
            <!-- Standard Fee -->
            <el-form-item label="Standard Fee" prop="standard_fee">
              <el-input v-model="form.standard_fee" type="number"></el-input>
            </el-form-item>

            <!-- Priority Fee -->
            <el-form-item label="Priority Fee" prop="priority_fee">
              <el-input v-model="form.priority_fee" type="number"></el-input>
            </el-form-item>

            <!-- Express Fee -->
            <el-form-item label="Express Fee" prop="express_fee">
              <el-input v-model="form.express_fee" type="number"></el-input>
            </el-form-item>

            <!-- Start Date with Time -->
            <el-form-item label="Start Date" prop="start_date">
              <el-date-picker v-model="form.start_date" type="datetime" placeholder="Select start date and time"
                format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss">
              </el-date-picker>
            </el-form-item>

            <!-- End Date with Time -->
            <el-form-item label="End Date" prop="end_date">
              <el-date-picker v-model="form.end_date" type="datetime" placeholder="Select end date and time"
                format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss">
              </el-date-picker>
            </el-form-item>

            <!-- Submit Button -->
            <el-form-item>
              <el-button v-if="isEdit" type="primary" @click="onSubmit" :disabled="isSubmit">Submit</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-col>
    </el-row>
  </div>
  <div class="table-content">
    <div class="top-head-left mb-4">
      <h1>{{ $t('History of change') }}</h1>
    </div>

    <el-table border stripe size="small" :data="hitories" :max-height="maxHeight" v-loading="isLoading"
      element-loading-text="Loading...">
      <el-table-column prop="name" :label="$t('Effective date - Start at')" min-width="100" align="center">
        <template #default="scope">
          {{ scope.row.start_date}}
        </template>
      </el-table-column>
      <el-table-column prop="code" :label="$t('Effective date - End at')" min-width="100" align="center">
        <template #default="scope">
          {{ scope.row.end_date }}
        </template>
      </el-table-column>
      <el-table-column prop="standard" :label="$t('Standard')" align="center">
        <template #default="scope">
          {{ scope.row.standard_fee }}
        </template>
      </el-table-column>
      <el-table-column prop="priority" :label="$t('Priority')" align="center">
        <template #default="scope">
          {{ scope.row.priority_fee }}
        </template>
      </el-table-column>
      <el-table-column prop="express" :label="$t('Express')" align="center">
        <template #default="scope">
          {{ scope.row.express_fee }}
        </template>
      </el-table-column>
      <el-table-column prop="express" :label="$t('Update By')" min-width="100" align="center">
        <template #default="scope">
          {{ scope.row.name }}
        </template>
      </el-table-column>
      <el-table-column prop="express" :label="$t('Update At')" min-width="100" align="center">
        <template #default="scope">
          {{ scope.row.created_at_pst }}

        </template>
      </el-table-column>
    </el-table>
    <div class="bottom">
      <div class="total">
        {{ $t('Total:') }} {{ this.total ? formatNumber(this.total) : 0 }}
      </div>
      <el-pagination :disabled="isLoading" background layout="prev, pager, next" :page-size="filter.limit"
        :total="total" @current-change="changePage" v-model:currentPage="filter.page">
      </el-pagination>
      <div class="limit" :disabled="isLoading">
        <el-select v-model="filter.limit" :placeholder="$t('Select')" size="mini" @change="onFilter">
          <el-option v-for="item in limits" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getList,
  getDetail,
  create,
  getStatus,
  updateStatus,
} from '@/api/PeakShippingSurcharge';

import EventBus from '@/utilities/eventBus';
import ShippingCarrierPackage from '@/modules/shipping-carrier/components/shipping-carrier-package/List/Index.vue';

export default {
  name: 'ProductStyleList',
  components: {
    ShippingCarrierPackage,
  },
  data() {
    return {
      filter: this.setDefaultFilter(),
      data: [],
      hitories: [],
      items: [],
      isLoading: false,
      status: false,
      isEdit: false,
      isSubmit: false,
      isConfirmDialogVisible: false,
      originalStatus: false,
      total: 0,
      history_price: {
        standard_fee: 0.0,
        priority_fee: 0.0,
        express_fee: 0.0,
        start_date: '',
        end_date: '',
      },
      form: {
        standard_fee: 0.0,
        priority_fee: 0.0,
        express_fee: 0.0,
        start_date: '',
        end_date: '',
      },
      rules: {
        standard_fee: [
          {
            required: true,
            message: 'Standard fee is required',
            trigger: 'blur',
          },
        ],
        // Priority Fee validation
        priority_fee: [
          {
            required: true,
            message: 'Priority fee is required',
            trigger: 'blur',
          },
        ],
        // Express Fee validation
        express_fee: [
          {
            required: true,
            message: 'Express fee is required',
            trigger: 'blur',
          },
        ],

        start_date: [
          {
            required: true,
            message: 'Start date is required',
            trigger: 'change',
          },
        ],
        end_date: [
          {
            required: true,
            message: 'End date is required',
            trigger: 'change',
          },
          { validator: this.validateEndDate, trigger: 'change' },
        ],
      },
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 189);
    },
  },
  mounted() {
    this.isLoading = true;
    this.getStatus();
    this.detail();
    this.list();
    this.isLoading = false;
  },
  methods: {
    async list() {
      console.log(this.filter);
      this.isLoading = true;
      const { data } = await getList(this.filter);
      this.hitories = data.data;
      this.total = data.total;
      this.isLoading = false;
    },
    async detail() {
      const { data } = await getDetail();

      this.form.standard_fee = data.standard_fee ?? 0;
      this.form.priority_fee = data.priority_fee ?? 0;
      this.form.express_fee = data.express_fee ?? 0;
      this.form.start_date = data.start_date ?? null;
      this.form.end_date = data.end_date ?? null;

      this.history_price.standard_fee = data.standard_fee ?? 0;
      this.history_price.priority_fee = data.priority_fee ?? 0;
      this.history_price.express_fee = data.express_fee ?? 0;
      this.history_price.start_date = data.start_date ?? null;
      this.history_price.end_date = data.end_date ?? null;
    },
    async getStatus() {
      this.isLoading = true;
      const { data } = await getStatus();
      console.log(data);
      if (data.status === '1') {
        this.status = true;
      } else {
        this.status = false;
      }
      this.isLoading = false;
    },
    setDefaultFilter() {
      return {
        limit: 10,
        page: 1,
      };
    },

    validateEndDate(rule, value, callback) {
      if (value === '') {
        callback(new Error('End date is required'));
      } else if (new Date(value) <= new Date(this.form.start_date)) {
        callback(new Error('End date cannot be earlier than start date'));
      } else {
        callback();
      }
    },
    async onSubmit() {
      this.isSubmit = true;
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          this.isLoading = true;
          const params = {
            standard_fee: this.form.standard_fee,
            priority_fee: this.form.priority_fee,
            express_fee: this.form.express_fee,
            start_date: this.form.start_date,
            end_date: this.form.end_date,
          };

          // Pass the form data to the async function
          await create(params);
          this.notification(this.$t('Update successfully.'));
          this.isEdit = false;
          this.list();
          this.isLoading = false;
        } else {
          this.isSubmit = false;
          this.notification('Invalid data', "error");
          return false;
        }
        this.isSubmit = false;

      });
    },
    enableEditStatus() {
      this.isEdit = true;
    },
    cancelEditStatus() {

      this.form.standard_fee = this.history_price.standard_fee ?? 0;
      this.form.priority_fee = this.history_price.priority_fee ?? 0;
      this.form.express_fee = this.history_price.express_fee ?? 0;
      this.form.start_date = this.history_price.start_date ?? null;
      this.form.end_date = this.history_price.end_date ?? null;

      this.isEdit = false;
    },
    openConfirmDialog() {
      if (this.status === true) {
        this.originalStatus = false;
      } else {
        this.originalStatus = true;
      }
      this.isConfirmDialogVisible = true; // Hiển thị dialog
    },
    // Hủy cập nhật trạng thái, khôi phục trạng thái trước đó
    cancelUpdateStatus() {
      this.isConfirmDialogVisible = false; // Đóng dialog
      this.status = this.originalStatus; // Khôi phục trạng thái
    },
    confirmUpdateStatus() {
      this.isConfirmDialogVisible = false;
      this.updateStatus(); // Thực hiện cập nhật trạng thái
    },
    changePage(page) {
      this.filter.page = page;
      this.list();
    },
    onFilter() {
      this.filter.page = 1;
      this.list();
    },

    async updateStatus() {
      try {
        const params = {
          status: this.status,
        };
        const { data } = await updateStatus(params);
        this.status = params.status;
        this.cancelEditStatus();
        this.notification(this.$t('Update successfully.'));
      } catch (error) {
        this.notification('Invalid data', "error");
      }
    },
  },
};
</script>
