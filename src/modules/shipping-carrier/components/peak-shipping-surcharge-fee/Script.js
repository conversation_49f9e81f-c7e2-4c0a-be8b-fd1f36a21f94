import { getList, getDetail, create, getStatus, updateStatus } from "@/api/PeakShippingSurcharge";
import EventBus from "@/utilities/eventBus";
import ShippingCarrierPackage from "@/modules/shipping-carrier/components/shipping-carrier-package/List/Index.vue";
export default {
    name: "ProductStyleList",
    components: {
        ShippingCarrierPackage,
    },
    data() {
        return {
            data: [],
            hitories: [],
            items: [],
            isLoading: false,
            status: false,
            isEdit: false,
            form: {
                standard_fee: 0.00,
                priority_fee: 0.00,
                express_fee: 0.00,
                start_date: '',
                end_date: ''
            },
            rules: {
                start_date: [
                    { required: true, message: 'Start date is required', trigger: 'change' }
                ],
                end_date: [
                    { required: true, message: 'End date is required', trigger: 'change' },
                    { validator: this.validateEndDate, trigger: 'change' }
                ]
            }
        }
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 189);
        }
    },
    mounted() {
        this.isLoading = true;
        this.getStatus();
        this.detail();
        this.list();
        this.isLoading = false;
    },
    methods: {
        async list() {
            this.isLoading = true;
            const { data } = await getList();
            this.hitories = data;
            this.isLoading = false;
        },
        async detail() {
            const { data } = await getDetail();
            this.form.standard_fee = data.standard_fee ?? 0;
            this.form.priority_fee = data.priority_fee ?? 0;
            this.form.express_fee = data.express_fee ?? 0;
            this.form.start_date = data.start_date ?? null;
            this.form.end_date = data.end_date ?? null;
        },
        async getStatus() {
            this.isLoading = true;
            const { data } = await getStatus();
            console.log(data);
            if(data.status === '1') {
                this.status = true;
            } else {
                this.status = false;
            }
            console.log(this.status);
            this.isLoading = false;
        },
        validateEndDate(rule, value, callback) {
            if (value === '') {
                callback(new Error('End date is required'));
            } else if (new Date(value) < new Date(this.form.start_date)) {
                callback(new Error('End date cannot be earlier than start date'));
            } else {
                callback();
            }
        },
        async onSubmit() {
            this.$refs.formRef.validate(async (valid) => {
                if (valid) {
                    this.isLoading = true;
                    const params = {
                        standard_fee: this.form.standard_fee,
                        priority_fee: this.form.priority_fee,
                        express_fee: this.form.express_fee,
                        start_date: this.form.start_date,
                        end_date: this.form.end_date
                    };
                    // Pass the form data to the async function
                    await create(params);
                    this.notification(this.$t('Update successfully.'));
                    this.list();
                    this.isLoading = false;
                } else {
                    this.notification(this.$t('Error.'));
                    return false;
                }
            })
        },
        enableEditStatus() {
            this.isEdit = true;
        },
        cancelEditStatus() {
            this.isEdit = false;
        },
        async updateStatus() {
            try {
                const params = {
                    status: this.status,
                }
                const { data } = await updateStatus(params);
                this.notification(this.$t('Update successfully.'));
            } catch (error) {
                this.notification(this.$t('Error.'));
            }
        },
    },
}