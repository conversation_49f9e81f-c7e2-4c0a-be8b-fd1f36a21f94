<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
    <div v-show="show == 2 ?? false">
        <div class="top-head mb-4">
            <div class="top-head-left">
                <h1>{{ $t('Shipping Package') }}</h1>
            </div>
            <div class="top-head-right">
                <el-button type="primary" @click="backShippingCarrier">
                    {{ $t('Shipping Carrier') }}
                </el-button>
                <el-button type="primary" @click="createShippingCarrierPackage">
                    <span class="icon-margin-right">
                        <icon :data="iconAdd" /></span>{{ $t('Create') }}
                </el-button>
            </div>
        </div>
        <div class="table-content">
            <el-table border stripe size="small" :data="dataShow" :max-height="maxHeight" v-loading="isLoading" element-loading-text="Loading...">
                <el-table-column prop="name" :label="$t('API Name')" min-width="200" align="center"></el-table-column>
                <el-table-column prop="predefined_package" :label="$t('Display Name')" min-width="200" align="center"></el-table-column>
                <el-table-column prop="dimensions" :label="$t('Dimensions')" min-width="200" align="center"></el-table-column>
                <el-table-column prop="weight" :label="$t('Weight')" min-width="200" align="center"></el-table-column>
                <el-table-column prop="action" :label="$t('Action')" fixed="right" width="150" align="center">
                    <template #default="scope">
                        <el-link class="el-link-edit mr-2" :underline="false" type="primary" @click="updateShippingCarrierPackage(scope.row)">
                            <icon :data="iconEdit" />
                        </el-link>
                        <el-popconfirm :title="'Are you sure to delete ' + scope.row.name + '?'" @confirm="deleteShippingCarrierPackage(scope.row)">
                            <template #reference>
                                <el-link :underline="false" type="danger">
                                    <icon :data="iconDelete" />
                                </el-link>
                            </template>
                        </el-popconfirm>
                    </template>
                </el-table-column>
            </el-table>
            <div class="bottom">
                <div class="total">{{$t('Total:')}} {{ dataShow.length }}</div>
            </div>
        </div>
    </div>
    <create-shipping-carrier-package :carrierId="carrierId" />
    <update-shipping-carrier-package />
</template>