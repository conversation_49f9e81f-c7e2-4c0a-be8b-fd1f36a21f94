import { destroy } from "@/api/shippingCarrierPackage";
import EventBus from "@/utilities/eventBus";
import CreateShippingCarrierPackage from "@/modules/shipping-carrier/components/shipping-carrier-package/Create/Index.vue";
import UpdateShippingCarrierPackage from "@/modules/shipping-carrier/components/shipping-carrier-package/Update/Index.vue";

export default {
    name: "ProductStyleList",
    components: {
        CreateShippingCarrierPackage,
        UpdateShippingCarrierPackage
    },
    props: ['show', 'dataShow', 'carrierId'],
    data() {
        return {
            items: [],
            isLoading: false,
        }
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 189);
        }
    },
    mounted() {
    },
    methods: {
        setDefaultFilter() {
            return {
                limit: 25,
                page: 1,
            };
        },
        createShippingCarrierPackage() {
            EventBus.$emit("showCreateShippingCarrierPackage");
        },
        updateShippingCarrierPackage(item) {
            EventBus.$emit("showUpdateShippingCarrierPackage", item);
        },
        async deleteShippingCarrierPackage(item) {
            if (item.is_default === 1) {
                this.notification("Can't delete default package", "error");
                return
            }
            const res = await destroy(item.id);
            if (res.data) {
                EventBus.$emit("deleteShippingPackage", item);
            }
            this.notification(res.data.message);
        },
        backShippingCarrier() {
            this.$emit("showBack");
        }
    }
}