<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
    <div>
        <el-dialog v-model="dialogVisible" destroy-on-close :title="$t('Update Shipping Carrier Package') + ` #${shippingCarrierPackageId}`" custom-class="el-dialog-custom" :destroy-on-close="true">
            <el-form status-icon ref="updateShippingCarrierPackage" :model="data" @submit.prevent="onSubmit" :label-position="right" label-width="150px" class="mt-4">
                <el-form-item :label="$t('API Name')" :class="{'is-error': isError('name')}" required>
                    <el-input v-model="data.name" @keyup.enter="onSubmit"></el-input>
                    <div v-if="isError('name')" class="el-form-item__error">{{getErrorMessage('name')}}</div>
                </el-form-item>
                <el-form-item :label="$t('Display Name')" :class="{'is-error': isError('predefined_package')}">
                    <el-input v-model="data.predefined_package" @keyup.enter="onSubmit"></el-input>
                    <div v-if="isError('predefined_package')" class="el-form-item__error">{{getErrorMessage('predefined_package')}}</div>
                </el-form-item>
                <el-form-item :label="$t('Dimensions')" :class="{'is-error': isError('dimensions')}">
                    <el-input v-model="data.dimensions" @keyup.enter="onSubmit"></el-input>
                    <div v-if="isError('dimensions')" class="el-form-item__error">{{getErrorMessage('dimensions')}}</div>
                </el-form-item>
                <el-form-item :label="$t('Weight')" :class="{'is-error': isError('weight')}">
                    <el-input v-model="data.weight" @keyup.enter="onSubmit"></el-input>
                    <div v-if="isError('weight')" class="el-form-item__error">{{getErrorMessage('weight')}}</div>
                </el-form-item>
                <el-form-item :label="$t('Default Package')">
                    <el-switch v-model="data.is_default" :disabled="disable ?? true"/>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="onSubmit" :disabled="isLoading" :loading="isLoading">{{ $t('Submit') }}</el-button>
            </template>
        </el-dialog>
    </div>
</template>