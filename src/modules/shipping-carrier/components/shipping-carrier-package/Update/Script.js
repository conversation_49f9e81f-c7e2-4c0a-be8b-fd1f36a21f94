import EventBus from "@/utilities/eventBus";
import { update } from "@/api/shippingCarrierPackage";

export default {
    name: "UpdateProductStyle",
    data() {
        return {
            shippingCarrierPackageId: '',
            dialogVisible: false,
            data: this.setDefaultData(),
            serverErrors: [],
            isLoading: false,
            disable: false
        }
    },
    created() {
        EventBus.$on("showUpdateShippingCarrierPackage", (item) => {
            this.shippingCarrierPackageId = item.id;
            this.data = Object.assign(this.data, item);
            this.data.is_default = this.data.is_default === 1 ? true : false;
            this.disable = item.is_default === 1 ?? true; 
            this.serverErrors = [];
            this.dialogVisible = true;
        });
    },
    methods: {
        isError(field) {
            return !!this.serverErrors[field];
        },
        getErrorMessage(field) {
            return this.serverErrors[field][0];
        },
        setDefaultData() {
            return {
                name: "",
                predefined_package: "",
                dimensions: "",
                weight: "",
                is_default: false
            };
        },
        async onSubmit() {
            if (this.isLoading) return;
            const isValid = await this.$refs.updateShippingCarrierPackage.validate();
            if (!isValid) {
                return;
            }
            this.isLoading = true;
            try {
                const res = await update(this.shippingCarrierPackageId, this.data);
                 if (res.data && res.data.data) {
                    EventBus.$emit("updateShippingPackage", res.data.data);
                }
                this.dialogVisible = false;
                this.notification(res.data.message);
            } catch (e) {
                this.serverErrors = e.response.data.errors;
                let message = e.response.data.message;
                this.notification(message, "error");
            } finally {
                this.isLoading = false;
            }
        },
    }
}
