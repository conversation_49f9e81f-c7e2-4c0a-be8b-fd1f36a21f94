import EventBus from "@/utilities/eventBus";
import { create } from "@/api/shippingCarrierService";

export default {
    name: "CreateShippingCarrierService",
    props: ['carrierId'],
    data() {
        return {
            dialogVisible: false,
            data: this.setDefaultData(),
            serverErrors: [],
            isLoading: false
        }
    },
    created() {
        EventBus.$on("showCreateShippingCarrierService", () => {
            this.data = this.setDefaultData();
            this.serverErrors = [];
            this.dialogVisible = true;
        });
    },
    methods: {
        isError(field) {
            return !!this.serverErrors[field];
        },
        getErrorMessage(field) {
            return this.serverErrors[field][0];
        },
        setDefaultData() {
            return {
                name: "",
                display_name: "",
                carrier_id: "",
            };
        },
        async onSubmit() {
            if (this.isLoading) return;
            const isValid = await this.$refs.createShippingCarrierService.validate();
            if (!isValid) {
                return;
            }
            this.data.carrier_id = this.carrierId;
            this.isLoading = true;
            try {
                const res = await create(this.data);
                if (res.data && res.data.data) {
                    EventBus.$emit("createShippingService", res.data.data);
                }
                this.dialogVisible = false;
                this.notification(res.data.message);
            } catch (e) {
                this.serverErrors = e.response.data.errors;
                let message = e.response.data.message;
                this.notification(message, "error");
            } finally {
                this.isLoading = false;
            }
        },
    }
}
