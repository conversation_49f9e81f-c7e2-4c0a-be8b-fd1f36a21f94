import { destroy } from "@/api/shippingCarrierService";
import EventBus from "@/utilities/eventBus";
import CreateShippingCarrierService from "@/modules/shipping-carrier/components/shipping-carrier-service/Create/Index.vue";
import UpdateShippingCarrierService from "@/modules/shipping-carrier/components/shipping-carrier-service/Update/Index.vue";
    
export default {
    name: "ShippingCarrierService",
    components: {
        CreateShippingCarrierService,
        UpdateShippingCarrierService
    },
    props: ['show', 'dataShow', 'carrierId'],
    data() {
        return {
            isLoading: false,
        }
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 189);
        }
    },
    mounted() {
    },
    methods: {
        createShippingCarrierService() {
            EventBus.$emit("showCreateShippingCarrierService");
        },
        updateShippingCarrierService(item) {
            EventBus.$emit("showUpdateShippingCarrierService", item);
        },
        async deleteShippingCarrierService(item) {
            const res = await destroy(item.id);
            if (res.data && res.data.data) {
                EventBus.$emit("deleteShippingService", item);
            }
            this.notification(res.data.message);
        },
        backShippingCarrier() {
            this.$emit("showBack");
        }
    }
}