<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
    <div>
        <el-dialog v-model="dialogVisible" destroy-on-close :title="$t('Update Shipping Carrier Service') + ` #${productStyleId}`" custom-class="el-dialog-custom" :destroy-on-close="true">
            <el-form status-icon ref="updateProductStyle" :model="data" @submit.prevent="onSubmit" :label-position="right" label-width="150px" class="mt-4">
                <el-form-item :label="$t('API Name')" :class="{'is-error': isError('name')}" required>
                    <el-input v-model="data.name" @keyup.enter="onSubmit"></el-input>
                    <div v-if="isError('name')" class="el-form-item__error">{{getErrorMessage('name')}}</div>
                </el-form-item>
                <el-form-item :label="$t('Display name')" :class="{'is-error': isError('display_name')}" required>
                    <el-input v-model="data.display_name" @keyup.enter="onSubmit"></el-input>
                    <div v-if="isError('display_name')" class="el-form-item__error">{{getErrorMessage('display_name')}}</div>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="onSubmit" :disabled="isLoading" :loading="isLoading">{{ $t('Submit') }}</el-button>
            </template>
        </el-dialog>
    </div>
</template>