import EventBus from "@/utilities/eventBus";
import { update } from "@/api/shippingCarrierService";

export default {
    name: "UpdateShippingCarrierService",
    data() {
        return {
            productStyleId: '',
            dialogVisible: false,
            data: this.setDefaultData(),
            serverErrors: [],
            isLoading: false
        }
    },
    created() {
        EventBus.$on("showUpdateShippingCarrierService", (item) => {
            this.productStyleId = item.id;
            this.data = Object.assign(this.data, item);
            this.serverErrors = [];
            this.dialogVisible = true;
        });
    },
    methods: {
        isError(field) {
            return !!this.serverErrors[field];
        },
        getErrorMessage(field) {
            return this.serverErrors[field][0];
        },
        setDefaultData() {
            return {
                name: "",
                display_name: "",
            };
        },
        async onSubmit() {
            if (this.isLoading) return;
            const isValid = await this.$refs.updateProductStyle.validate();
            if (!isValid) {
                return;
            }
            this.isLoading = true;
            try {
                const res = await update(this.productStyleId, this.data);
                if (res.data && res.data.data) {
                    EventBus.$emit("updateShippingService", res.data.data);
                }
                this.dialogVisible = false;
                this.notification(res.data.message);
            } catch (e) {
                this.serverErrors = e.response.data.errors;
                let message = e.response.data.message;
                this.notification(message, "error");
            } finally {
                this.isLoading = false;
            }
        },
    }
}
