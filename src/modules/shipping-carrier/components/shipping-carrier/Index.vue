<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
 
<template>
        <div v-show="show == 0 ?? false">
        <div class="top-head mb-4">
            <div class="top-head-left">
                <h1>{{ $t('Shipping Carrier') }}</h1>
            </div>
            <div class="top-head-right">
                <el-button type="primary" @click="createShippingCarrier">
                    <span class="icon-margin-right">
                        <icon :data="iconAdd" /></span>{{ $t('Create') }}
                </el-button>
            </div>
        </div>
        <div class="table-content">
            <div class="filter-top">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-input :placeholder="$t('Carrier Name')" v-model="filter.name" @keyup.enter="onFilter" />
                    </el-col>
                    <el-col :span="6">
                        <div class="btn-filter">
                            <el-button type="primary" @click="onFilter">
                                <span class="icon-margin-right">
                                    <icon :data="iconFilter" /></span>{{ $t("Filter") }}
                            </el-button>
                            <el-button @click="resetFilter">
                                <span class="icon-margin-right">{{ $t("Reset") }}</span>
                            </el-button>
                        </div>
                    </el-col>
                </el-row>
            </div>
            <el-table border stripe size="small" :data="items" :max-height="maxHeight" v-loading="isLoading" element-loading-text="Loading...">
                <el-table-column prop="name" :label="$t('Carrier Name')" min-width="100" align="center"></el-table-column>
                <el-table-column prop="code" :label="$t('Code')" min-width="100" align="center"></el-table-column>
                <el-table-column prop="actionService" :label="$t('Services Manage')" min-width="100" align="center">
                    <template #default="scope">
                        <el-button type="primary" @click="showService(scope.row.id)" size="small" plain>{{ $t('Services') }} ({{ getCountService(scope.row.id) }})</el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="actionPackage" :label="$t('Package Manage')" min-width="100" align="center">
                    <template #default="scope">
                        <el-button type="primary" @click="showPackage(scope.row.id)" size="small" plain>{{ $t('Packages') }} ({{ getCountPackage(scope.row.id) }})</el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="action" :label="$t('Action')" fixed="right" width="150" align="center">
                    <template #default="scope">
                        <el-link class="el-link-edit mr-2" :underline="false" type="primary" @click="updateShippingCarrier(scope.row)">
                            <icon :data="iconEdit" />
                        </el-link>
                        <el-popconfirm :title="'Are you sure to delete ' + scope.row.name + '?'" @confirm="deleteShippingCarrier(scope.row)">
                            <template #reference>
                                <el-link :underline="false" type="danger">
                                    <icon :data="iconDelete" />
                                </el-link>
                            </template>
                        </el-popconfirm>
                    </template>
                </el-table-column>
            </el-table>
            <div class="bottom">
                <div class="total">{{$t('Total:')}} {{ items.length ? formatNumber(total) : 0 }}</div>
                <el-pagination :disabled="isLoading" background layout="prev, pager, next" :page-size="filter.limit" :total="total" @current-change="changePage" v-model:currentPage="filter.page">
                </el-pagination>
                <div class="limit" :disabled="isLoading">
                    <el-select v-model="filter.limit" :placeholder="$t('Select')" size="mini" @change="onFilter">
                        <el-option v-for="item in limits" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </div>
            </div>
        </div>
    </div>
    <shipping-carrier-package :show="show" @showBack="showBack" :carrierId="carrier_id" :dataShow="packageDataShow || []" />
    <shipping-carrier-service :show="show" @showBack="showBack" :dataShow="serviceDataShow || []" :carrierId="carrier_id" />
    <create-shipping-carrier @refresh="getShippingCarrier" />
    <update-shipping-carrier @refresh="getShippingCarrier" />

</template>

