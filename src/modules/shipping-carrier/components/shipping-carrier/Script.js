import { getList, destroy } from "@/api/shippingCarrier";
import { fetchAll as getPackage} from "@/api/shippingCarrierPackage";
import { fetchAll as getSerice } from "@/api/shippingCarrierService";
import EventBus from "@/utilities/eventBus";
import ShippingCarrierPackage from "@/modules/shipping-carrier/components/shipping-carrier-package/List/Index.vue";
import ShippingCarrierService from "@/modules/shipping-carrier/components/shipping-carrier-service/List/Index.vue";
import CreateShippingCarrier from "@/modules/shipping-carrier/views/Create/Index.vue";
import UpdateShippingCarrier from "@/modules/shipping-carrier/views/Update/Index.vue";
export default {
    name: "ProductStyleList",
    components: {
        ShippingCarrierPackage,
        ShippingCarrierService,
        CreateShippingCarrier,
        UpdateShippingCarrier
    },
    data() {
        return {
            items: [],
            isLoading: false,
            filter: this.setDefaultFilter(),
            listPackage: [],
            listService: [],
            packageDataShow: [],
            serviceDataShow: [],
            show: 0,
            carrier_id: "",
        }
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 189);
        }
    },
    mounted() {
        this.filter = this.getRouteParam();
        this.getShippingCarrier();
        this.getPackageService();
        EventBus.$on("createShippingService", (item) => {
            this.listService.push(item);
            this.showService(item.carrier_id);
        });
        EventBus.$on("updateShippingService", (item) => {
            let objIndex = this.listService.findIndex((obj => obj.id === item.id));
            this.listService[objIndex].name = item.name;
            this.listService[objIndex].display_name = item.display_name;
            this.listService[objIndex].updated_at = item.updated_at;
            this.showService(item.carrier_id);
        });
        EventBus.$on("deleteShippingService", (item) => {
            this.listService = this.listService.filter((obj => obj.id !== item.id));
            this.showService(item.carrier_id);
        });
        EventBus.$on("createShippingPackage", (item) => {
            if (item.is_default === 1) {
                let objIndexOld = this.listPackage.findIndex((ob => ob.is_default === 1));
                this.listPackage[objIndexOld].is_default = 0;
            };
            this.listPackage.push(item);
            this.showPackage(item.carrier_id);
        });
        EventBus.$on("updateShippingPackage", (item) => {
            if (item.is_default === 1) {
                let objIndexOld = this.listPackage.findIndex((ob => ob.is_default === 1));
                this.listPackage[objIndexOld].is_default = 0;
            };
            let objIndex = this.listPackage.findIndex((obj => obj.id === item.id));
            this.listPackage[objIndex].name = item.name;
            this.listPackage[objIndex].predefined_package = item.predefined_package;
            this.listPackage[objIndex].dimensions = item.dimensions;
            this.listPackage[objIndex].weight = item.weight;
            this.listPackage[objIndex].is_default = item.is_default;
            this.listPackage[objIndex].updated_at = item.updated_at;
            this.showPackage(item.carrier_id);
        });
        EventBus.$on("deleteShippingPackage", (item) => {
            this.listPackage = this.listPackage.filter((obj => obj.id !== item.id));
            this.showPackage(item.carrier_id);
        });
        window.onpopstate = this.action;
    },
    methods: {
        action(event) {
            if (this.show !== 0) {
                this.show = 0;
                history.go(1);
            }
        },
        setDefaultFilter() {
            return {
                limit: 25,
                page: 1,
                name: "",
            };
        },
        async getShippingCarrier() {
            this.isLoading = true;
            this.setRouteParam();
            const { data } = await getList(this.filter);
            this.items = data.data;
            this.total = data.total;
            this.isLoading = false;
        },
        async getPackageService() {
            let packages = await getPackage();
            this.listPackage = packages.data;
            let services = await getSerice();
            this.listService = services.data;
        },
        changePage(page) {
            this.filter.page = page;
            this.getShippingCarrier();
        },
        onFilter() {
            this.filter.page = 1;
            this.getShippingCarrier();
        },
        createShippingCarrier() {
            EventBus.$emit("showCreateShippingCarrier");
        },
        updateShippingCarrier(item) {
            EventBus.$emit("showUpdateShippingCarrier", item);
        },
        async deleteShippingCarrier(item) {
            let ob = this.listPackage.filter((i) => {
                if (i.carrier_id === item.id && i.is_default === 1) {
                    return i;
                }
            });
            if (ob.length > 0) {
                this.notification("Can't delete carrier containing default package", "error");
                return
            }
            const res = await destroy(item.id);
            this.notification(res.data.message);
            this.getShippingCarrier()
        },
        resetFilter(){
            this.filter = this.setDefaultFilter()
            this.getShippingCarrier()
        },
        showService(id) {
            this.show = 1;
            this.carrier_id = id;
            this.serviceDataShow = this.listService.filter(item => item.carrier_id === id);
        },
        showPackage(id) {
            this.show = 2;
            this.carrier_id = id;
            this.packageDataShow = this.listPackage.filter(item => item.carrier_id === id);
        },
        showBack() {
            this.show = 0;
        },
        getCountService(id) {
            return this.listService.filter(item => item.carrier_id === id).length;
        },
        getCountPackage(id) {
            return this.listPackage.filter(item => item.carrier_id === id).length;
        }
    }
}