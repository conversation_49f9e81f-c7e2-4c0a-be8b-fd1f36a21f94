<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
    <el-dialog v-model="dialogVisible" destroy-on-close :title="$t('Create Shipping Carrier')" custom-class="el-dialog-custom" :destroy-on-close="true">
        <el-form status-icon ref="createShippingCarrier" :model="data" @submit.prevent="onSubmit" :label-position="right" label-width="150px" class="mt-4">
            <el-form-item :label="$t('Shipping Carrier')" :class="{'is-error': isError('name')}" required>
                <el-input v-model="data.name" @keyup.enter="onSubmit"></el-input>
                <div v-if="isError('name')" class="el-form-item__error">{{getErrorMessage('name')}}</div>
            </el-form-item>
            <el-form-item :label="$t('Code')" :class="{'is-error': isError('code')}" required>
                <el-input v-model="data.code" @keyup.enter="onSubmit"></el-input>
                <div v-if="isError('code')" class="el-form-item__error">{{getErrorMessage('code')}}</div>
            </el-form-item>
            <el-form-item :label="$t('Tracking Url')" :class="{'is-error': isError('tracking_url')}">
                <el-input v-model="data.tracking_url" @keyup.enter="onSubmit"></el-input>
                <div v-if="isError('tracking_url')" class="el-form-item__error">{{getErrorMessage('tracking_url')}}</div>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button type="primary" @click="onSubmit" :disabled="isLoading" :loading="isLoading">{{ $t('Submit') }}</el-button>
        </template>
    </el-dialog>
</template>