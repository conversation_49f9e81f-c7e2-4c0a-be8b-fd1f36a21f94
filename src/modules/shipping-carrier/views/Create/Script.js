import EventBus from "@/utilities/eventBus";
import { create } from "@/api/shippingCarrier";

export default {
    name: "CreateProductStyle",
    data() {
        return {
            dialogVisible: false,
            data: this.setDefaultData(),
            serverErrors: [],
            isLoading: false
        }
    },
    created() {
        EventBus.$on("showCreateShippingCarrier", () => {
            this.data = this.setDefaultData();
            this.serverErrors = [];
            this.dialogVisible = true;
        });
    },
    methods: {
        isError(field) {
            return !!this.serverErrors[field];
        },
        getErrorMessage(field) {
            return this.serverErrors[field][0];
        },
        setDefaultData() {
            return {
                name: "",
                code: "",
                tracking_url: "",
            };
        },
        async onSubmit() {
            if (this.isLoading) return;
            const isValid = await this.$refs.createShippingCarrier.validate();
            if (!isValid) {
                return;
            }
            // this.data.code = this.data.name.trim().toUpperCase().replaceAll(' ', '_');
            this.isLoading = true;
            try {
                const res = await create(this.data);
                this.dialogVisible = false;
                this.notification(res.data.message);
                this.$emit("refresh");
            } catch (e) {
                this.serverErrors = e.response.data.errors;
                let message = e.response.data.message;
                this.notification(message, "error");
            } finally {
                this.isLoading = false;
            }
        },
    }
}
