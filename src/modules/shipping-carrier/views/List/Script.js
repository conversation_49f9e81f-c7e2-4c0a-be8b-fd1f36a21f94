import { getList, destroy } from "@/api/shippingCarrier";
import { fetchAll as getPackage} from "@/api/shippingCarrierPackage";
import { fetchAll as getSerice } from "@/api/shippingCarrierService";
import EventBus from "@/utilities/eventBus";
import shippingCarrier from "@/modules/shipping-carrier/components/shipping-carrier/Index.vue";
import PeakShippingSurchargeFee from "@/modules/shipping-carrier/components/peak-shipping-surcharge-fee/Index.vue";

export default {
    name: "ProductStyleList",
    components: {
        PeakShippingSurchargeFee,
        shippingCarrier
    },
    data() {
        return {
            activeTab: 'all',
        }
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 189);
        }
    },
    mounted() {
    },
    methods: {
        handleTabChange(tab, event) {
            switch (tab.paneName) {
              case '1st':
                this.type = 1;
                break;
              case '2nd':
                this.type = 2;
                break;
              case '3rd':
                this.type = 3;
                break;
              default:
                this.type = 1; // or set it to a default value
            }
          },

    }
}