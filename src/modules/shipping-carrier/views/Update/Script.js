import EventBus from "@/utilities/eventBus";
import { update } from "@/api/shippingCarrier";

export default {
    name: "UpdateProductStyle",
    data() {
        return {
            productStyleId: '',
            dialogVisible: false,
            data: this.setDefaultData(),
            serverErrors: [],
            isLoading: false
        }
    },
    created() {
        EventBus.$on("showUpdateShippingCarrier", (item) => {
            this.productStyleId = item.id;
            this.data = {
                name: item.name || "",
                code: item.code || "",
                tracking_url: item.tracking_url || "",
            };
            this.serverErrors = [];
            this.dialogVisible = true;
        });
    },
    methods: {
        isError(field) {
            return !!this.serverErrors[field];
        },
        getErrorMessage(field) {
            return this.serverErrors[field][0];
        },
        setDefaultData() {
            return {
                name: "",
                code: "",
                tracking_url: "",
            };
        },
        async onSubmit() {
            if (this.isLoading) return;
            const isValid = await this.$refs.updateShippingCarrier.validate();
            if (!isValid) {
                return;
            }
            // this.data.code = this.data.name.trim().toUpperCase().replaceAll(' ', '_');
            this.isLoading = true;
            try {
                const res = await update(this.productStyleId, this.data);
                this.dialogVisible = false;
                this.notification(res.data.message);
                this.$emit("refresh");
            } catch (e) {
                this.serverErrors = e.response.data.errors;
                let message = e.response.data.message;
                this.notification(message, "error");
            } finally {
                this.isLoading = false;
            }
        },
    }
}
