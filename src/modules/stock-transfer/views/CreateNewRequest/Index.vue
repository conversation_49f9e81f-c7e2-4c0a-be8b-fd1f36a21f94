<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
        v-model="openDialogCreateNewRequest"
        :title="$t('Create New Request')"
        custom-class="el-dialog-custom el-dialog-create-fulfill-request"
        @close="closeModal"
        :close-on-click-modal="false"
        width="88%"
    >
      <template #default>
        <div class="fulfill-request-product flex-col">
          <div class="bg-gray-50 p-3 border rounded mb-3">
            <!-- Employee ID section -->
            <el-form-item v-show="!Object.keys(employee).length">
              <label class="text-center block mb-1">{{ $t('Employee ID') }}<span class="ml-1 text-danger">*</span></label>
              <el-input class="el-form-manual-item-employee" :class="{ error: employeeError }" v-model="employeeID" @keyup.enter="scanEmployeeID"></el-input>
              <span v-if="employeeError" class="ml-1 text-danger">{{employeeError}}</span>
            </el-form-item>
            <div v-if="Object.keys(employee).length">
              <div class="flex justify-between">
                <b class="text-base">Hi {{ employee.name }}, Have a nice day!</b>
                <el-link type="danger" @click="resetEmployee" :underline="false">{{ $t('Logout') }}</el-link>
              </div>
              <div class="text-lg text-fuchsia-500" >
                <IncrementTimer/>
              </div>
            </div>
          </div>
          <div class="p-3 border rounded mb-3">
            <div class="flex justify-between mb-4 items-center">
              <span class="text-bold">{{ $t('From') }}:</span>
              <span class="font-bold">{{warehouse}}</span>
            </div>
            <div class="flex justify-between border-t border-gray-300 mb-3 items-center">
              <span class="text-bold mt-3 mr-3">{{ $t('To') }}<span class="text-red-500 ml-1">*</span></span>
              <div class="flex-1 mt-3" :class="{ error: warehouseError }">
                <el-select
                    v-model="destination_warehouse_id"
                    :placeholder="$t('Select Warehouse')"
                    filterable
                    class="w-full"
                >
                  <el-option
                      v-for="item in warehouseOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                  />
                </el-select>
              </div>
            </div>
            <span v-if="warehouseError" class="ml-1 text-danger">{{warehouseError}}</span>

            <div class="flex justify-between border-t border-gray-300 mb-3 items-center">
              <span class="text-bold mt-3">{{ $t('Request Number') }}:</span>
              <span class="font-bold mt-3">{{requestNumber}}</span>
            </div>
          </div>
        </div>
        <div class="fulfill-request-box border rounded">
          <div class="p-3 mb-3">
            <el-form :inline="true" class="demo-form-inline flex items-center">
              <el-form-item class="flex-1">
                <label slot="label">Product name or SKU</label>
                <el-select
                    v-model="product_id"
                    placeholder="Enter product name or SKU"
                    filterable
                    remote
                    reserve-keyword
                    :remote-method="remoteSearchProduct"
                    :remote-method-throttle="300"
                    :class="{'error': productsError !== ''}"
                >
                  <el-option
                      v-for="item in productOptions"
                      :key="item.id"
                      :label="`${item.name} - ${item.sku}`"
                      :value="item.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item class="input-form-item input-form-item-button">
                <el-button type="primary" class="custom-button" @click="addProduct">Add</el-button>
              </el-form-item>
            </el-form>
            <div class="flex-1">
              <span class="text-danger" v-if="productsError">Please select at least one product to request</span>
            </div>
            <el-table :data="productItems" style="width: 100%" height="450">
              <el-table-column label="Product Name">
                <template #default="{ row, $index }">
                  <div class="flex flex-col">
                    <span :class="{ 'text-red-500': errorItems[`items.${$index}.request_box`] }">{{ row.product_name }}</span>
                    <span class="text-red-500 ml-1" v-if="errorItems[`items.${$index}.request_box`]">
                      {{ errorItems[`items.${$index}.request_box`][0] }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="Quantity" width="100">
                <template #default="{ row }">
                  {{ formatNumber(row.quantity) }}
                </template>
              </el-table-column>
              <el-table-column label="Box(es)" width="250">
                <template #default="{ row , $index}">
                  <div class="box-input-container flex gap-8 items-center">
                    <el-input-number
                        v-model="row.request_box"
                        class="box-input"
                        :min="1"
                        @change="updateBoxValue(row)"
                    />
                    <el-icon class="remove-icon" @click="removeItem(row, $index)">
                      <icon :data="iconClose" />
                    </el-icon>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </template>
      <template #footer>
        <!-- Buttons section (footer) -->
        <div class="flex justify-end">
          <el-button @click="resetData" class="custom-button">{{ $t('Cancel') }}</el-button>
          <el-button
              type="primary"
              class="custom-button"
              :disabled="isLoading"
              :loading="isLoading"
              @click="addNewRequest"
          >{{ $t('Create') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<style scoped>
.demo-form-inline .el-form-item {
  display: inline-block;
  margin-right: 10px;
  vertical-align: middle;
}

.mr-7 {
  margin-right: 7px;
}

.input-form-item {
  display: flex;
  align-items: center;
}
.input-form-item-button{
  margin-top: 30px !important;
}

.custom-button {
  width: 100px;
}
.remove-icon {
  cursor: pointer;
}
.error {
  border: 1px solid #f56c6c; /* Màu đỏ cho viền */
}
</style>