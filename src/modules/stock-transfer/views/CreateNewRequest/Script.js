import EventBus from "@/utilities/eventBus.js";
import { addNewRequest, generateRequestNumber } from "@/api/stockTransfer.js";
import { list } from "@/api/warehouse.js";
import { listWithAttribute } from "@/api/product.js";
import { employeeLogoutTimeChecking, employeeTimeChecking } from "@/api/employee.js";

export default {
  name: "CreateNewFulfillRequest",
  components: { addNewRequest },
  props: ['employees'],
  data() {
    return {
      data: {},
      isLoading: false,
      openDialogCreateNewRequest: false,
      isLoadingSearch: false,
      employeeID: '',
      employee: {},
      employeeError: '',
      warehouseError: '',
      productsError: '',
      job_type: "stock_transfer_create_request",
      id_time_checking: null,
      warehouseId: '',
      warehouse: '',
      requestNumber: '',
      warehouseOptions :[],
      destination_warehouse_id : '',
      product_id : '',
      productOptions : [],
      productItems : [],
      errorItems : [],
    };
  },
  computed: {
  },
  mounted() {
  },
  watch: {
    productItems: {
      handler(newProductItems, oldProductItems) {
        if (newProductItems.length > 0) {
          this.productsError = "";
        }
      },
      deep: true
    },
    destination_warehouse_id: {
      handler(newValue, oldValue) {
        if (newValue) {
          this.warehouseError = "";
        }
      },
      deep: true
    },

  },
  created() {
    this.warehouseId = this.$author.warehouseId();
    this.fetchWarehouse();
    this.generateRequestNumber();
    EventBus.$on("showCreateNewRequest", () => {
      this.openDialogCreateNewRequest = true;
      this.focusByElClass('el-form-distributor-item-employee');
    });
  },
  methods: {
    async remoteSearchProduct(keyword) {
      if (keyword && keyword.length >= 3) {
        const res = await listWithAttribute({ keyword: keyword });
        const dataProducts = res.data || [];
        this.productOptions = dataProducts.map(({ id, name, sku, gtin_case }) => ({ id, name, sku, gtin_case }));
      }
    },
    addProduct() {
      const foundProduct = this.productOptions.find(item => item.id == this.product_id);
      if (foundProduct) {
        const existingItem = this.productItems.find(item => item.product_id == foundProduct.id);
        if (existingItem) {
          existingItem.request_box += 1;
          existingItem.quantity = existingItem.request_box * existingItem.gtin_case;
        } else {
          const item = {
            product_id: foundProduct.id,
            product_name: foundProduct.name,
            quantity: foundProduct.gtin_case,
            request_box: 1,
            gtin_case: foundProduct.gtin_case
          };
          this.productItems.push(item);
        }
      }
    },
    updateBoxValue(row) {
      this.productItems.forEach((item) => {
        if (item.product_id == row.product_id) {
          console.log(item)
          item.quantity = row.request_box * row.gtin_case
        }
      });
    },
    removeItem(row, $index) {
      console.log(row)
      this.productItems.forEach((item, index) => {
        if (item.product_id == row.product_id) {
          this.productItems.splice(index, 1); // Remove item
        }
      });
      if (this.errorItems[`items.${$index}.request_box`]) {
        delete this.errorItems[`items.${$index}.request_box`];
      }
    },
    async resetEmployee() {
      const res = await employeeLogoutTimeChecking(this.id_time_checking);
      this.employee = {};
      this.employeeError = '';
      this.employeeID = '';
      this.id_time_checking = null
    },
    async scanEmployeeID() {

      try {
        if (this.id_time_checking) {
          return true;
        }
        if (!this.employeeID) {
          this.employeeError = "Employee ID field cannot be left blank.";
          return false;
        }
        const res = await employeeTimeChecking({
          code: Number(this.employeeID),
          job_type: this.job_type
        })
        if (!res.data.data) {
          this.employeeError = "Can't find your employee ID, please scan again";
          return false;
        }
        this.employeeError = "";
        this.employee = res.data.data;
        this.id_time_checking = res.data.id_time_checking;
        this.focusByElClass();
        return true;
      }catch (e){
        this.employeeError = "The Employee Code is invalid"
        this.notification("The Employee Code is invalid", "error");
        this.employeeID = ""
        this.focusByElClass();
        return false;
      }

    },
    async fetchWarehouse() {
      const res = await list();
      const data = res.data || [];
      const dataWarehouseOptions = data.map(({ id, name }) => ({ id, name }));
      this.warehouseOptions = dataWarehouseOptions.filter(option => option.id !== this.warehouseId);
      this.warehouse = dataWarehouseOptions.find(option => option.id == this.warehouseId).name;
    },
    async generateRequestNumber() {
      const res = await generateRequestNumber();
      const data = res.data || '';
      this.requestNumber = data;
    },
    async addNewRequest() {
      let valid = false;
      if(!this.employeeID){
        this.employeeError = "Employee ID field cannot be left blank.";
        valid = true;
      }
      if(!this.destination_warehouse_id){
        this.warehouseError = "Destination warehouse is required";
        valid = true;
      }
      if (this.productItems.length == 0) {
        this.productsError = "Please select at least one product to request";
        this.notification(this.productsError, 'error');
        valid = true;
      }
      if(valid) return;
      this.isLoading = true;
      try {
        const params = {
          employee_id : this.employee.id,
          destination_warehouse_id : this.destination_warehouse_id,
          from_warehouse_id : this.warehouseId,
          items : this.productItems,
          id_time_checking : this.id_time_checking
        }
        const res = await addNewRequest(params);
        this.notification(res.data.message);
        this.openDialogCreateNewRequest = false;
        this.$emit("refresh");
      } catch (e) {
        this.errorItems = e.response.data;
        const allMessages = Object.values(this.errorItems).flat();
        const errorMessageText = allMessages.join('<br />');
        this.notification(errorMessageText, "error", true);
      } finally {
        this.isLoading = false;
      }
    },

    closeModal() {
      this.resetData();
      this.resetEmployee();
      this.$emit("refresh");
    },
    resetData(){
      this.openDialogCreateNewRequest = false;
     this.destination_warehouse_id = '',
      this.product_id = '',
      this.productOptions = [],
      this.productItems = [],
      this.errorItems = []
      this.employeeError = "",
      this.warehouseError = "",
      this.productsError = ""
    },

  },
};
