<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
        v-model="openDialogFulfill"
        :title="$t('Fulfill Request')"
        custom-class="el-dialog-custom el-dialog-fulfill-request"
        :close-on-click-modal="false"
        width="88%"
        @close="closeModal"
    >
      <template #default>
        <div class="fulfill-request-product flex-col">
          <div class="bg-gray-50 p-3 border rounded mb-3">
            <!-- Employee ID section -->
            <el-form-item v-show="!Object.keys(employee).length" :error="employeeError" >
              <label class="text-center block mb-1">{{ $t('Employee ID') }}<span class="ml-1 text-danger">*</span></label>
              <el-input class="el-form-manual-item-employee" v-model="employeeID" @keyup.enter="scanEmployeeID"></el-input>
              <span v-if="employeeError" class="ml-1 text-danger">{{employeeError}}</span>

            </el-form-item>
            <div v-if="Object.keys(employee).length">
              <div class="flex justify-between">
                <b class="text-base">Hi {{ employee.name }}, Have a nice day!</b>
                <el-link type="danger" @click="resetEmployee" :underline="false">{{ $t('Logout') }}</el-link>
              </div>
              <div class="text-lg text-fuchsia-500" >
                <IncrementTimer/>
              </div>
            </div>
          </div>
          <div class="p-3 border rounded mb-3">
            <div class="flex justify-between mb-4 items-center">
              <span class="text-bold">{{ $t('From') }}:</span>
              <span class="font-bold">{{stockTransfer.from_warehouse.name}}</span>
            </div>
            <div class="flex justify-between border-t border-gray-300 mb-3 items-center">
               <span class="text-bold mt-3">{{ $t('To') }}:</span>
               <span class="font-bold mt-3">{{stockTransfer.warehouse_destination.name}}</span>
            </div>
            <div class="flex justify-between border-t border-gray-300 mb-3 items-center">
              <span class="text-bold mt-3">{{ $t('Request Number') }}:</span>
              <span class="font-bold mt-3">{{stockTransfer.request_number}}</span>
            </div>
          </div>
          <div class="p-3 border rounded mb-3">
          <el-table :data="stockTransfer.items" height="300" style="width: 100%">
            <el-table-column label="Product Name" min-width="400">
              <template #default="{ row}">
                <div class="flex flex-col">
                  <span>{{ row.product.name }}</span>
                  <span>SKU : {{ row.product.sku }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="Box(es)">
              <template #default="{ row}">
                <div class="flex flex-col">
                <span :class="{ 'text-red-500': row.scanned > row.request_box }">
                  {{ row.scanned }}/{{ row.request_box }}
                </span>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        </div>
        <div class="fulfill-request-box border rounded">
          <div class="p-3 mb-3">
            <div class="box-id-input text-center">
              <el-form-item>
                <label class="text-center block mb-1">{{ $t('Box ID') }}<span class="ml-1 text-danger">*</span></label>
                <el-input
                    ref="boxNumber"
                    @keyup.enter="scanBox"
                    v-model="box_number"
                    :class="{'error': boxError !== ''}"
                >
                </el-input>
                <span class="text-danger" v-if="boxError">Please scan at least one box</span>
              </el-form-item>
            </div>
            <div class="box-id-summary text-center">
              <p class="scanned-text text-right">{{ $t('Scanned') }}: {{boxScanned.length}} {{ $t('boxes') }}</p>
            </div>
            <div class="box-id-scanned">
              <el-table :data="boxScanned" stripe height="500" style="width: 100%">
                <el-table-column label="Box ID">
                  <template #default="scope">
                    <div class="flex justify-between items-center">
                      <el-tooltip v-if="scope.row.invalid" :content="scope.row.tooltip" placement="top">
                        <span :class="{ 'text-danger': scope.row.invalid }">{{ scope.row.barcode }}</span>
                      </el-tooltip>
                      <span v-else>{{ scope.row.barcode }}</span>
                      <el-popconfirm
                          title="Are you sure to delete this?"
                          @confirm="removeItem(scope.row)"
                      >
                        <template #reference>
                          <el-icon :class="{ 'text-danger': scope.row.invalid, 'remove-icon': true}"
                                   v-if="stockTransfer.destination_warehouse_id != warehouseMexico">
                            <icon :data="iconClose"/>
                          </el-icon>
                        </template>
                      </el-popconfirm>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="Country of origin" v-if="stockTransfer.destination_warehouse_id == warehouseMexico">
                  <template #default="scope">
                    <div class="flex justify-between items-center">
                      <el-select
                          v-if="scope.row.is_edit"
                          :ref="'countrySelect_' + scope.row.barcode"
                          :placeholder="$t('Select country')"
                          class="el-select-country w-full"
                          filterable
                          v-model="scope.row.country"
                          @keyup.enter="searchCountry(scope.row, $event)"
                          @change="changeCountryInBox(scope.row)"
                          @blur="blurCountry(scope.row)"
                      >
                        <el-option
                            v-for="item in countries"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                      </el-select>
                      <div class="flex items-center cursor-pointer" v-else   @click="showSelectCountry(scope.row)">
                        <div :class="{ 'text-danger': scope.row.invalid }">
                          {{ getCountryName(scope.row.country) }}
                        </div>
                        <div
                            class="cursor-pointer"
                        >
                          <el-icon :class="{ 'text-danger': scope.row.invalid, 'remove-icon': true, 'ml-2': true }" size="small">
                            <icon :data="iconEdit" />
                          </el-icon>
                        </div>
                      </div>
                      <el-popconfirm
                          title="Are you sure to delete this?"
                          @confirm="removeItem(scope.row)"
                      >
                        <template #reference>
                          <el-icon :class="{ 'text-danger': scope.row.invalid, 'remove-icon': true}">
                            <icon :data="iconClose"/>
                          </el-icon>
                        </template>
                      </el-popconfirm>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </template>
      <template #footer>
        <!-- Buttons section (footer) -->
        <div class="flex justify-end">
          <el-button
              @click="save"
              style="width: 9%"
              :disabled="isLoading"
              :loading="isLoading"
          >{{ $t('Save') }}</el-button>
          <el-button
              style="width: 9%"
              type="primary"
              @click="confirmFulfill"
              :disabled="isLoading"
              :loading="isLoading"
          >{{ $t('Fulfill') }}</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        custom-class="el-dialog-custom el-dialog-medium"
        v-model="showConfirmDialog"
        title=" "
        width="30%"
        :close-on-click-modal="false"
        @close="handleCloseConfirmDialog"
    >
      <template #title>
        <div class="flex items-center">
          <icon class="text-warning mr-2" :data="iconWarning" />
          <span>Mismatch box quantity</span>
        </div>
      </template>
      <p>{{ confirmMessage }}</p><br>
      <p>{{ confirmMessage2 }}</p>
      <template #footer>
        <el-button @click="showConfirmDialog= false">Cancel</el-button>
        <el-button type="primary" @click="handleConfirm">OK</el-button>
      </template>
    </el-dialog>

  </div>
</template>
<style scoped>
.error {
  border: 1px solid #f56c6c;
}
</style>