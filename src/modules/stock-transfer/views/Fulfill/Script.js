import EventBus from "@/utilities/eventBus.js";
import {details, scanBox, fulfill, saveTemporaryFulfill} from "@/api/stockTransfer.js";
import ListTrackingNumber from "@/modules/inventory-addition/components/ListTrackingNumber.vue";
import {employeeLogoutTimeChecking, employeeTimeChecking} from "@/api/employee.js";
import IncrementTimer from "@/components/IncrementTimer.vue";
import {countries} from '@/api/default.js';
import {WAREHOUSE_MEXICO} from '@/utilities/constants';

export default {
    name: "CreateFulFillRequest",
    components: {ListTrackingNumber, IncrementTimer},
    props: ['employees'],
    data() {
        return {
            data: {},
            isLoading: false,
            openDialogFulfill: false,
            isLoadingSearch: false,
            products: [],
            employeeID: '',
            employee: {},
            employeeError: '',
            job_type: "stock_transfer_fulfill",
            id_time_checking: null,
            warehouseId: '',
            box_number: '',
            stockTransfer: {
                id: "",
                from_warehouse_id: "",
                destination_warehouse_id: "",
                request_number: "",
                total_box: "1",
                total_quantity: "",
                employee_id: "",
                status: "",
                created_at: "",
                updated_at: "",
                warehouse_destination: {
                    id: "",
                    name: ""
                },
                from_warehouse: {
                    id: "",
                    name: "S"
                },
                items: []
            },
            tableData: [],
            showConfirmDialog: false,
            confirmMessage: '',
            confirmMessage2: '',
            boxScanned: [],
            boxSelected: '',
            boxError: '',
            countries: [],
            countriesArr: [],
            coo: [],
            warehouseMexico: WAREHOUSE_MEXICO,
        };
    },
    computed: {},
    mounted() {
         this.fetchCountries();
    },
    created() {
        this.warehouseId = this.$author.warehouseId();
        EventBus.$on("showCreateFulfill", (data) => {
            this.stockTransfer = {
                ...data,
            };
            if (this.stockTransfer) {
                this.fetchDetail();
                this.openDialogFulfill = true;
                this.focusByElClass('el-form-distributor-item-employee');
            }
        });
    },
    watch: {},
    methods: {
        getCountryName(country_id) {
            return this.countries.find((item) => item.id === country_id)?.name;
        },
         fetchDetail() {
            this.countries = this.countriesArr;
            const fulfillLog = this.stockTransfer.fulfill_log.length ? this.stockTransfer.fulfill_log : [];
            const productIdsLog = {};
            fulfillLog.forEach(item => {
                const { product_id } = item;
                if (productIdsLog[product_id]) {
                    productIdsLog[product_id]++;
                } else {
                    productIdsLog[product_id] = 1;
                }
            });
            const productCounts = Object.keys(productIdsLog).map(product_id => ({
                product_id: parseInt(product_id),
                count: productIdsLog[product_id],
            }));
            const productIdsMap = new Map(productCounts.map(item => [item.product_id, item.count]));

            console.log(productIdsMap)
            this.stockTransfer.items = this.stockTransfer.items.map(item => ({
                ...item,
                scanned: (productIdsMap.size > 0 && productIdsMap.has(item.product_id))
                    ? (item.scanned ? item.scanned + productIdsMap.get(item.product_id) : productIdsMap.get(item.product_id))
                    : (item.scanned ? item.scanned : 0),
            }));
            this.boxScanned = fulfillLog.map((item) => ({
                barcode : item.box_number,
            }));
            if (this.stockTransfer.destination_warehouse_id == WAREHOUSE_MEXICO) {
                this.coo = fulfillLog.map(item => ({
                    [item.box_number]: item.coo_id
                }));
                this.boxScanned = this.boxScanned.map(item => {
                    const matchingItem = fulfillLog.find(yItem => yItem.box_number === item.barcode);
                    if (matchingItem) {
                        return {
                            ...item,
                            country: matchingItem.coo_id,
                            product_id: matchingItem.product_id
                        };
                    }
                    return item;
                });
            }else{
                this.boxScanned = this.boxScanned.map(item => {
                    const matchingItem = fulfillLog.find(yItem => yItem.box_number === item.barcode);
                    if (matchingItem) {
                        return {
                            ...item,
                            product_id: matchingItem.product_id
                        };
                    }
                    return item;
                });
            }
        },
        async resetEmployee() {
            const res = await employeeLogoutTimeChecking(this.id_time_checking);
            this.employee = {};
            this.employeeError = '';
            this.employeeID = '';
            this.id_time_checking = null
        },
        async scanEmployeeID() {

            try {
                if (this.id_time_checking) {
                    return true;
                }
                if (!this.employeeID) {
                    this.employeeError = "Employee ID field cannot be left blank.";
                    return false;
                }
                const res = await employeeTimeChecking({
                    code: Number(this.employeeID),
                    job_type: this.job_type
                })
                if (!res.data.data) {
                    this.employeeError = "Can't find your employee ID, please scan again";
                    return false;
                }
                this.employeeError = "";
                this.employee = res.data.data;
                this.id_time_checking = res.data.id_time_checking;
                this.$refs.boxNumber.focus();
                return true;
            } catch (e) {
                this.employeeError = "The Employee Code is invalid"
                this.notification("The Employee Code is invalid", "error");
                this.employeeID = ""
                this.focusByElClass();
                return false;
            }

        },
        resetData() {
            this.stockTransfer = {
                id: "",
                from_warehouse_id: "",
                destination_warehouse_id: "",
                request_number: "",
                total_box: "1",
                total_quantity: "",
                employee_id: "",
                status: "",
                created_at: "",
                updated_at: "",
                warehouse_destination: {
                    id: "",
                    name: ""
                },
                from_warehouse: {
                    id: "",
                    name: "S"
                },
                items: []
            }
            this.box_number = "",
            this.boxScanned = [],
            this.boxError = ""
            this.coo = []
            this.openDialogFulfill = false;
        },
        closeModal() {
            this.resetData();
            this.resetEmployee();
        },

        async confirmFulfill() {
            try {
                if (!this.employeeID) {
                    this.employeeError = "Employee ID field cannot be left blank.";
                    return
                }
                if (this.boxScanned.length == 0) {
                    this.boxError = "Please scan at least one box ID ";
                    this.notification(this.boxError, "warning");
                    return
                }
                const totalRequestBox = this.stockTransfer.items.reduce((accumulator, currentItem) => {
                    return accumulator + currentItem.request_box;
                }, 0);
                if (this.stockTransfer.destination_warehouse_id == WAREHOUSE_MEXICO) {
                    const itemsWithoutCountry = this.boxScanned.filter(item => !item.country);
                    if (itemsWithoutCountry.length > 0) {
                        let messages = '';
                        itemsWithoutCountry.forEach(item => {
                            messages += "Please select country of origin for Box ID# " + item.barcode + '<br/>'
                        });
                        this.notification(messages, "error", true);
                        return;
                    }
                }
                if (totalRequestBox > this.boxScanned.length) {
                    this.confirmMessage = "We've noticed a mismatch between the requested box and the exported one. Confirm if you want to proceed with finalizing this export request?";
                    this.confirmMessage2 = "Số lượng box yêu cầu và số lượng box được scan không khớp nhau. Bạn có chắc chắn muốn hoàn thành yêu cầu này không?";
                    this.showConfirmDialog = true;
                    return;
                }
                this.boxScanned.forEach((item, index) => {
                    if (item.invalid) {
                        item.invalid = false;
                        item.tooltip = "";
                    }
                });
                const params = {
                    employee_id: this.employee.id,
                    stock_transfer_id: this.stockTransfer.id,
                    box_scanned: this.boxScanned.map(item => item.barcode),
                    id_time_checking: this.id_time_checking
                }
                if (this.stockTransfer.destination_warehouse_id == WAREHOUSE_MEXICO) {
                    params.coo = this.coo;
                }
                const res = await fulfill(params);
                this.notification("Fulfill has been successfully completed");
                this.openDialogFulfill = false;
                this.$emit("refresh");
            } catch (e) {
                console.log(e)
                this.errorItems = e.response.data;
                const keysWithBoxScanned = Object.keys(this.errorItems).filter(key => key.includes('box_scanned'));
                const extractedNumbers = keysWithBoxScanned.map(key => parseInt(key.split('.')[1]));
                const allMessages = Object.values(this.errorItems).flat();
                if (extractedNumbers.length) {
                    this.boxScanned.forEach((item, index) => {
                        if (extractedNumbers.includes(index)) {
                            item.invalid = true;
                        }
                        const foundMessage = allMessages.find(message => message.includes(item.barcode));
                        if (foundMessage) {
                            item.tooltip = foundMessage;
                        }
                    });
                    this.boxScanned.sort((a, b) => {
                        if (a.invalid && !b.invalid) {
                            return -1;
                        } else if (!a.invalid && b.invalid) {
                            return 1;
                        }
                        return 0;
                    });
                }
                const errorMessageText = allMessages.join('<br/>')
                this.notification(errorMessageText, "error", true);
            } finally {
                this.isLoading = false;
            }
        },
        async scanBox() {
            try {
                this.boxError = ""
                if (!this.employeeID) {
                    this.employeeError = "Employee ID field cannot be left blank.";
                    return
                }
                if (!this.box_number) {
                    this.notification("Please enter Box ID", "warning");
                    return;
                }
                const trimmedBoxNumber = this.box_number.trim();
                const matchingBox = this.boxScanned.find(item => item.barcode == trimmedBoxNumber);
                if (matchingBox) {
                    this.box_number = ""
                    this.notification("This Box ID has been added to the inventory", "warning");
                    return;
                }
                this.countries = this.countriesArr;
                const res = await scanBox({
                    stock_transfer_id: this.stockTransfer.id,
                    box_number: trimmedBoxNumber,
                });
                this.stockTransfer.items.find((item) => {
                    if (item.product_id == res.data.product_id) {
                        item.scanned += 1;
                        if (item.scanned > item.request_box) {
                            item.scanned -= 1;
                            this.notification("The quantity of boxes for this SKU exceeds the one that was requested", "error");
                        } else {
                            this.boxScanned.unshift({
                                ...res.data,
                                is_edit: true
                            })
                            this.$nextTick(() => this.$refs['countrySelect_' + res.data.barcode].focus())
                        }
                    }
                });
                this.box_number = ""
            } catch (e) {
                let errorMessage = "";
                if (e.response.data.message) {
                    errorMessage = e.response.data.message[0];
                    this.confirmMessage = errorMessage;
                    this.showConfirmDialog = true;
                    this.boxSelected = e.response.data.box;
                    return;

                } else if (e.response.data.box_number) {
                    errorMessage = e.response.data.box_number[0];
                } else if (e.response.data.stock_transfer_id) {
                    errorMessage = e.response.data.stock_transfer_id[0];
                } else {
                    errorMessage = "An error occurred";
                }
                this.notification(errorMessage, "error");
            }
        },
        tableRowClassName(data) {
            return data.row.is_deleted ? "is-delete" : "";
        },
        async handleConfirm() {
            this.showConfirmDialog = false;
            try {
                this.boxScanned.forEach((item, index) => {
                    if (item.invalid) {
                        item.invalid = false;
                        item.tooltip = "";
                    }
                });
                const params = {
                    employee_id: this.employee.id,
                    stock_transfer_id: this.stockTransfer.id,
                    box_scanned: this.boxScanned.map(item => item.barcode),
                    id_time_checking: this.id_time_checking
                }
                if (this.stockTransfer.destination_warehouse_id == WAREHOUSE_MEXICO) {
                    params.coo = this.coo;
                }
                const res = await fulfill(params);
                this.notification("Fulfill has been successfully completed");
                this.openDialogFulfill = false;
                this.$emit("refresh");
            } catch (e) {
                this.errorItems = e.response.data;
                const keysWithBoxScanned = Object.keys(this.errorItems).filter(key => key.includes('box_scanned'));
                const extractedNumbers = keysWithBoxScanned.map(key => parseInt(key.split('.')[1]));
                const allMessages = Object.values(this.errorItems).flat();
                if (extractedNumbers.length) {
                    this.boxScanned.forEach((item, index) => {
                        if (extractedNumbers.includes(index)) {
                            item.invalid = true;
                        }
                        const foundMessage = allMessages.find(message => message.includes(item.barcode));
                        if (foundMessage) {
                            item.tooltip = foundMessage;
                        }
                    });
                    this.boxScanned.sort((a, b) => {
                        if (a.invalid && !b.invalid) {
                            return -1;
                        } else if (!a.invalid && b.invalid) {
                            return 1;
                        }
                        return 0;
                    });


                }
                const errorMessageText = allMessages.join('<br/>')
                this.notification(errorMessageText, "error", true);
            } finally {
                this.isLoading = false;
            }
        },
        handleCloseConfirmDialog(done) {
            this.showConfirmDialog = false;
            this.box_number = ""
        },
        removeItem(row) {
            this.boxScanned.forEach((item, index) => {
                if (item.barcode == row.barcode) {
                   this.boxScanned.splice(index, 1);
                }
            });
            this.stockTransfer.items.find((item) => {
                if (item.product_id == row.product_id) {
                    item.scanned -= 1;
                }
            });
            if(this.coo.length){
                for (let i = this.coo.length - 1; i >= 0; i--) {
                    if (this.coo[i][row.barcode]) {
                        this.coo.splice(i, 1);
                    }
                }
            }
        },
        async fetchCountries() {
            const res = await countries();
            this.countriesArr = res.data || [];
        },
        changeCountryInBox(row) {
            if (row) {
                this.countries = this.countriesArr;
                const existingIndex = this.coo.findIndex(item => item.hasOwnProperty(row.barcode));
                if (existingIndex !== -1) {
                    this.coo[existingIndex][row.barcode] = row.country;
                } else {
                    this.coo.push({[row.barcode]: row.country});
                }
                row.is_edit = false;
            }
        },
        searchCountry(row, evt) {
            this.countries = this.countriesArr;
            const value = (evt.target.value || '').toLowerCase();
            if (value) {
                const countries = this.countries.filter(country =>
                    country.iso2.toLowerCase() == value ||
                    (country.code && country.code.split(',').includes(value))
                );
                row.country = countries[0]?.id || undefined
                const existingIndex = this.coo.findIndex(item => item.hasOwnProperty(row.barcode));
                if (existingIndex !== -1) {
                    this.coo[existingIndex][row.barcode] = row.country;
                } else {
                    this.coo.push({[row.barcode]: row.country});
                }
                this.$nextTick(() => {
                    this.$refs['countrySelect_' + row.barcode].blur();
                    this.$refs.boxNumber.focus();
                });
            }
        },
        async save() {
            try {
                if (!this.employeeID) {
                    this.employeeError = "Employee ID field cannot be left blank.";
                    return
                }
                if (this.boxScanned.length == 0 && this.stockTransfer.fulfill_log.length == 0) {
                    this.boxError = "Please scan at least one box ID ";
                    this.notification(this.boxError, "warning");
                    return
                }

                if (this.stockTransfer.destination_warehouse_id == WAREHOUSE_MEXICO) {
                    const itemsWithoutCountry = this.boxScanned.filter(item => !item.country);
                    if (itemsWithoutCountry.length > 0) {
                        let messages = '';
                        itemsWithoutCountry.forEach(item => {
                            messages += "Please select country of origin for Box ID# " + item.barcode + '<br/>'
                        });
                        this.notification(messages, "error", true);
                        return;
                    }
                }
                this.boxScanned.forEach((item, index) => {
                    if (item.invalid) {
                        item.invalid = false;
                        item.tooltip = "";
                    }
                });
                const params = {
                    employee_id: this.employee.id,
                    stock_transfer_id: this.stockTransfer.id,
                    id_time_checking: this.id_time_checking,
                    box_scanned: this.boxScanned.length ? this.boxScanned.map(item => item.barcode) : []
                }
                if (this.stockTransfer.destination_warehouse_id == WAREHOUSE_MEXICO) {
                    params.coo = this.coo;
                }
                const res = await saveTemporaryFulfill(params);
                this.notification(res.data.message);
                this.openDialogFulfill = false;
                this.$emit("refresh");
            } catch (e) {
                this.errorItems = e.response.data;
                const keysWithBoxScanned = Object.keys(this.errorItems).filter(key => key.includes('box_scanned'));
                const extractedNumbers = keysWithBoxScanned.map(key => parseInt(key.split('.')[1]));
                const allMessages = Object.values(this.errorItems).flat();
                if (extractedNumbers.length) {
                    this.boxScanned.forEach((item, index) => {
                        if (extractedNumbers.includes(index)) {
                            item.invalid = true;
                        }
                        const foundMessage = allMessages.find(message => message.includes(item.barcode));
                        if (foundMessage) {
                            item.tooltip = foundMessage;
                        }
                    });
                    this.boxScanned.sort((a, b) => {
                        if (a.invalid && !b.invalid) {
                            return -1;
                        } else if (!a.invalid && b.invalid) {
                            return 1;
                        }
                        return 0;
                    });
                }
                const errorMessageText = allMessages.join('<br/>')
                this.notification(errorMessageText, "error", true);
            } finally {
                this.isLoading = false;
            }
        },
        blurCountry(row){
            nextTick(() => {
                row.is_edit = false;
            });
        },
        showSelectCountry(row){
            row.is_edit = true
            this.$nextTick(() => {
                this.$refs['countrySelect_' + row.barcode].focus();
            });
        }
    },
};
