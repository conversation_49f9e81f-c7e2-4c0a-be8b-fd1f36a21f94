<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ $t("Stock Transfer") }}</h1>
      </div>
      <div class="top-head-right">
        <el-button type="primary" @click="createNewRequest">
          <span class="icon-margin-right"><icon :data="iconAdd" /></span
          >{{ $t("Create new request") }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <div class="">
        <el-tabs
            class="el-tab-filter"
            type="card"
            v-model="defaultTab"
            @tab-click="handleTabClick"
        >
          <el-tab-pane
              :label="item.name"
              :name="item.key"
              v-for="item in tabs"
              :key="item.key"
          >
            <template #label>
              <span class="custom-tabs-label">
                <span>
                  {{ item.name }}
                  <span
                      v-if="item.total > 0"
                      class="text-white bg-orange-400 rounded-full px-2 py-1 text-[11px]"
                  >{{ item.total }}</span
                  >
                </span>
              </span>
            </template>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="filter">
        <div class="label">{{ $t("Filter by:") }}</div>
        <div class="filter-item">
          <el-dropdown
            ref="sku"
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('sku') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('sku')">
                <el-tooltip effect="dark" content="SKU" placement="top-start">
                  <span>{{ filter.sku }}</span>
                </el-tooltip>
              </template>
              <template v-else> SKU </template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input
                  :placeholder="$t('Enter search keyword')"
                  class="search"
                  v-model="filter.sku"
                  @keydown.enter="onFilter('sku')"
                  clearable
                  @clear="clearFilterItem('sku')"
                />
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item">
          <el-dropdown
            ref="request_number"
            trigger="click"
            v-model="filter.request_number"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('request_number') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('request_number')">
                <el-tooltip
                  effect="dark"
                  :content="$t('Request #')"
                  placement="top-start"
                >
                  <span>{{ filter.request_number }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(" Request #") }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input
                  :placeholder="$t('Enter search keyword')"
                  class="search"
                  v-model="filter.request_number"
                  @keydown.enter="onFilter('request_number')"
                  clearable
                  @clear="clearFilterItem('request_number')"
                />
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown
              trigger="click"
              class="el-dropdown-filter-item"
              :class="{ 'is-active': hasChangeFilterByItem('employee_id') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('employee_id')">
                <el-tooltip
                    effect="dark"
                    :content="$t('Employee')"
                    placement="top-start"
                >
                  <span>{{ getEmployeeNameById(filter.employee_id) }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(' Employee ') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-select
                    filterable
                    v-model="filter.employee_id"
                    :placeholder="$t('Select employee')"
                    @change="onFilter"
                >
                  <el-option
                      v-for="item in getEmployees"
                      :key="item.id"
                      :label="item.name"
                      :value="String(item.id)"
                  >
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item">
          <el-dropdown
              trigger="click"
              class="el-dropdown-filter-item"
              :class="{ 'is-active': filter.date && filter.date.length }"
          >
            <span class="el-dropdown-link">
              <template v-if="filter.date && filter.date.length">
                <el-tooltip
                    effect="dark"
                    :content="$t('Date')"
                    placement="top-start"
                >
                  <span>
                    {{
                      templateDateRange(filter.date[0], filter.date[1])
                    }}</span
                  >
                </el-tooltip>
              </template>
              <template v-else> {{ $t('Date') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-date-picker
                    format="YYYY-MM-DD"
                    v-model="filter.date"
                    type="daterange"
                    range-separator="To"
                    :start-placeholder="$t('Start date')"
                    :end-placeholder="$t('End date')"
                    @change="onChangeDate"
                >
                </el-date-picker>
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item" v-if="hasFilter">
          <el-link type="danger" @click="onClearFilter" :underline="false">
            {{ $t("Clear") }}
          </el-link>
        </div>
      </div>
      <el-table
        stripe
        size="small"
        border
        :data="items"
        style="width: 100%"
        :max-height="maxHeight"
        v-loading="isLoading"
        element-loading-text="Loading..."
        :row-class-name="tableRowClassName"
      >
        <el-table-column
            :label="$t('Action')"
            v-if="defaultTab === 'pending'"
        >
          <template #default="scope">
          <el-button
              type="primary"
              style="width: 35%"
              @click="showCreateFulfill(scope.row)"
          >
            {{ $t('Fulfill') }}
          </el-button>
          </template>
        </el-table-column>
        <el-table-column
          prop="request_number"
          :label="$t('Request Number')"
        >
          <template #default="scope">
            {{ scope.row.request_number || "N/A" }}
          </template>
        </el-table-column>
        <el-table-column
            prop="warehouse_destination"
            :label="$t('Destination')"
        >
          <template #default="scope">
            {{ scope.row.warehouse_destination ? scope.row.warehouse_destination.name : "N/A" }}
          </template>
        </el-table-column>
        <el-table-column
            prop="total_box"
            :label="$t('Box(es)')"
        >
          <template #default="scope">
          <span v-if="defaultTab !== 'completed'">
            {{ formatNumber(scope.row.total_box_received) }} / {{ formatNumber(scope.row.total_box) }}
          </span>
            <span v-else>
            {{ formatNumber(scope.row.total_box) }}
        </span>
          </template>
        </el-table-column>
        <el-table-column prop="total_quantity" :label="$t('Quantity')">
          <template #default="scope">
             <span v-if="defaultTab !== 'completed'">
            {{ formatNumber(scope.row.total_quantity_received)}} / {{ formatNumber(scope.row.total_quantity)}}
            </span>
            <span v-else>
            {{ formatNumber(scope.row.total_quantity)}}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" :label="$t('Created at')">
          <template #default="scope">
            {{ formatDate(scope.row.created_at, false) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="employee"
          :label="$t('Created by')"
        >
          <template #default="scope">
            {{ scope.row.employee.name }}
          </template>
        </el-table-column>
        <el-table-column
            prop="updated_at"
            :label="$t('Fulfilled at')"
            v-if="defaultTab === 'completed'"
        >
          <template #default="scope">
            {{ formatDate(scope.row.updated_at, false) }}
          </template>
        </el-table-column>
        <el-table-column
            prop="fulfillBy"
            :label="$t('Fulfilled by')"
            v-if="defaultTab === 'completed'"
        >
          <template #default="scope">
            {{ scope.row.employee_fulfill ? scope.row.employee_fulfill.name : null}}
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('Action')"
          v-if="defaultTab === 'completed'"
        >
          <template #default="scope">
             <span class="icon"  :title="'Download Packing list ' + scope.row.request_number" v-if="scope.row.destination_warehouse_id == warehouseMexico" @click="exportExcel(scope.row.id)">
                <icon :data="iconExcel" class="custom-icon" />
              </span>
            <span class="icon"  :title="'Export Stock Transfer detail ' + scope.row.request_number" @click="exportReportStockTransfer(scope.row.id)">
                <icon :data="iconStockTransferReport" class="custom-icon-report" />
              </span>
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom">
        <div class="total">
          {{ $t("Total:") }} {{ items.length ? formatNumber(total) : 0 }}
        </div>
        <el-pagination
          :disabled="isLoading"
          background
          layout="prev, pager, next"
          :page-size="filter.limit"
          :total="total"
          @current-change="changePage"
          v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
            v-model="filter.limit"
            :placeholder="$t('Select')"
            size="mini"
            @change="onFilter"
          >
            <el-option
              v-for="item in limits"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
    <CreateFulFillRequest @refresh="fetchData" :employees="employees" />
    <CreateNewRequest @refresh="fetchData" :employees="employees" />
  </div>
</template>

<style scoped>
.custom-icon {
  color: green;
  transition: color 0.3s;
  width: 22px;
  height: 22px;
}
.custom-icon-report {
  color: red;
  transition: color 0.3s;
  width: 22px;
  height: 22px;
}

.custom-icon:hover {
  color: #cccccc;
  cursor: pointer;
}
.custom-icon-report:hover {
  color: #cccccc;
  cursor: pointer;
}
</style>