import { list} from "@/api/stockTransfer.js";
import {list as listEmployee} from "@/api/employee.js";

import EventBus from "@/utilities/eventBus.js";
import CreateFulFillRequest from "@/modules/stock-transfer/views/Fulfill/Index.vue";
import CreateNewRequest from "@/modules/stock-transfer/views/CreateNewRequest/Index.vue";
import {clone, equals} from "ramda";
import { mapGetters } from "vuex";
import warehouseMixin from "@/mixins/warehouse";
import dateMixin from "@/mixins/date.js";
import filterMixin from "@/mixins/filter";
import {WAREHOUSE_MEXICO} from '@/utilities/constants';
import { API_URL, STORAGE_URL } from "@/utilities/constants";

export default {
  name: "stockTransferList",
  mixins: [warehouseMixin, filterMixin, dateMixin],
  components: {
    CreateNewRequest,
    CreateFulFillRequest
  },
  data() {
    return {
      items: [],
      status  : '',
      isLoading: false,
      filter: this.setDefaultFilter(),
      tabs: [
        {
          name : "New",
          key : "pending",
        },
        {
          name : "Completed",
          key : "completed",
          total : 0,
        }
      ],
      defaultTab : "pending",
      getEmployees : [],
      warehouseMexico: WAREHOUSE_MEXICO,
      buildLinkDownload : `${API_URL}/export-packing-slip`,
      buildLinkReportDownload : `${API_URL}/stock-transfer/report`,
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 300);
    },
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      delete defaultFilter.status;
      const newFilter = clone(this.filter);
      delete newFilter.status;

      return !equals(defaultFilter, newFilter);
    },
  },
  beforeUnmount() {
    EventBus.$off("showCreateNewRequest");
    EventBus.$off("showCreateFulFill");
  },
  mounted() {
    this.fetchData();
    this.fetchVendor();
    this.fetchEmployee();
  },
  methods: {
    exportExcel(id) {
      const link = this.buildLinkDownload + '?stock_transfer_id=' + id;
      return (window.location.href = link);
    },
    exportReportStockTransfer(id) {
      const link = this.buildLinkReportDownload + '/' +id;
      return (window.location.href = link);
    },
    handleTabClick(){
      if(this.defaultTab === 'completed'){
        this.filter.status = 'completed';
      }else{
        this.filter.status = 'pending';
      }
      this.onClearFilter()
    },
    async fetchEmployee() {
      const res = await listEmployee();
      const data = (res.data && res.data.data) || undefined;
      this.getEmployees = data;
    },
    async fetchVendor() {
      await this.$store.dispatch("getVendors");
    },
    onFilter(item = "") {
      this.filter.page = 1;
      this.$nextTick(() => {
        this.fetchStockTransfer();
        if (item) {
          this.$refs[item].handleClose();
        }
      });
    },
    getEmployeeNameById(id) {
      const employee = this.getEmployees.find(
          (item) => +item.id === +id
      );
      return employee && employee.name;
    },
    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.filter = {
        ...this.filter,
        status: this.defaultTab
      }
      this.$nextTick(() => {
        this.fetchStockTransfer();
      });
    },
    onChangeDate() {
      if (this.filter.date && this.filter.date.length) {
        this.filter.date[0] = this.formatDate(this.filter.date[0], false);
        this.filter.date[1] = this.formatDate(this.filter.date[1], false);
        this.onFilter();
      }
    },
    clearFilterItem(item) {
      this.filter[item] = "";
      this.$refs[item].handleClose();
      this.onFilter();
    },
    setDefaultFilter() {
      let params = {
        limit: 25,
        page: 1,
        status: "pending",
        request_number: "",
        employee_id: "",
        date: "",
      };
      return params;
    },
    setRouteParam() {
      if (!['stock_transfer'].includes(this.$route.name)) {
        return;
      }
      const params = this.filter;
      this.$router.replace({ name: 'stock_transfer', query: params });
    },
    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);
      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }
      filter.page = +filter.page || 1;
      return filter;
    },
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchStockTransfer();
      });
    },
    showCreateFulfill(request) {
      EventBus.$emit("showCreateFulfill", request);
    },
    createNewRequest() {
      EventBus.$emit("showCreateNewRequest");
    },
    async fetchData() {
      this.filter = this.getRouteParam();
      this.defaultTab = this.filter.status || 'pending';
      this.fetchStockTransfer();
    },
    async fetchStockTransfer() {
      this.isLoading = true;
      this.setRouteParam();
      if(this.filter.status === 'pending'){
        this.filter.status = ['pending', 'partial_completed']
      }
      const res = await list(this.filter);
      this.isLoading = false;
      const data = res.data || [];
      this.total = data.total;
      this.items = data.data;
    },

    tableRowClassName(data) {
      if (data.row.status === 'partial_completed') {
        return 'is-warning'
      }
    },
    hasChangeFilterByItem(name) {
      const query = this.$route.query;
      if (query[name]) {
        return true;
      }
      return false;
    },
  },
};
