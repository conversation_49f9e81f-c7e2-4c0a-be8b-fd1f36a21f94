<template>
  <div>
    <el-form-item label="Product Type" prop="product_types">
      <el-select
          v-model="promotion.product_types"
          placeholder="Select Product Type"
          style="width: 240px"
          multiple
          filterable
      >
        <el-option
            v-for="item in productTypes"
            :key="Number(item.id)"
            :label="item.name"
            :value="item.name"
        />
      </el-select>
    </el-form-item>

    <el-form-item label="Print Area" prop="print_areas">
      <el-select
          v-model="promotion.print_areas"
          placeholder="Select Print Area"
          style="width: 240px"
          multiple
          filterable
      >
        <el-option
            v-for="item in printAreas"
            :key="Number(item.id)"
            :label="item.name"
            :value="item.name"
        />
      </el-select>
    </el-form-item>

    <el-form-item label="Amount" prop="amount">
      <el-input-number v-model="promotion.amount" style="width: 200px" placeholder="Please input"  :precision="2" :step="0.1" :min="0" >
      </el-input-number>
    </el-form-item>

    <el-form-item label="Start Date" prop="start_time">
      <el-col :span="12">
        <el-date-picker
            v-model="promotion.start_time"
            type="datetime"
            placeholder="Pick a date"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
        />
      </el-col>
    </el-form-item>

    <el-form-item label="End Date"  prop="end_time">
      <el-col :span="12">
        <el-date-picker
            v-model="promotion.end_time"
            type="datetime"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="Select date and time"
            style="width: 100%"
        />
      </el-col>
    </el-form-item>

  </div>
</template>
<script>

import moment from "moment-timezone";
import EventBus from "@/utilities/eventBus";


export default {
  name: 'AdditionalPrintArea',
  props: ['promotion', 'productTypes', 'printAreas'],
  data() {
    return {
    }
  },
  async mounted() {
  },
  methods: {

  }
}
</script>