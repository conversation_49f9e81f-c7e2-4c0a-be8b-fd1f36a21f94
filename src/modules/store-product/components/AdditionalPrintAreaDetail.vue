<template>
  <el-dialog
      v-model="dialogDetail"
      :title="promotionType"
      width="600px"
  >
    <div>
      <el-row class="mb-4">
        <el-col :span="12" class="font-bold">
          {{ $t('Promotion Type') }}
        </el-col>
        <el-col :span="12">
          {{ promotion.promotion_type.name }}
        </el-col>
      </el-row>

      <el-row class="mb-4">
        <el-col :span="12" class="font-bold">
          {{ $t('Product Type') }}
        </el-col>
        <el-col :span="12">
          <p v-for="item in promotion.product_types">
            {{ item.name }}
          </p>
        </el-col>
      </el-row>
      <el-row class="mb-4">
        <el-col :span="12" class="font-bold">
          {{ $t('Product Print Area') }}
        </el-col>
        <el-col :span="12">
          <p v-for="item in promotion.print_areas">
            {{ item.name }}
          </p>
        </el-col>
      </el-row>

      <el-row class="mb-4">
        <el-col :span="12" class="font-bold">
          {{ $t('Amount') }}
        </el-col>
        <el-col :span="12">
          $ {{ formatNumberFloat(promotion.detail.amount, 2, true) }}
        </el-col>
      </el-row>

      <el-row class="mb-4">
        <el-col :span="12" class="font-bold">
          {{ $t('Start Date') }}
        </el-col>
        <el-col :span="12">
          {{ convertTime(promotion.start_time) }}
        </el-col>
      </el-row>

      <el-row class="mb-4">
        <el-col :span="12" class="font-bold">
          {{ $t('End Date') }}
        </el-col>
        <el-col :span="12">
          {{ convertTime(promotion.end_time) }}
        </el-col>
      </el-row>

    </div>

    <template #footer>
      <div class="flex justify-center">
        <el-button type="info" @click="dialogDetail = false">Close</el-button>
      </div>
    </template>
  </el-dialog>

</template>
<script>

import moment from "moment-timezone";
import EventBus from "@/utilities/eventBus";
import { detail } from '@/api/promotion';
import formatNumberMixin from "@/mixins/formatNumber.js";
import dateMixin from "@/mixins/date.js";




export default {
  name: 'AdditionalPrintAreaDetail',
  props: ['promotion', 'productTypes', 'printAreas'],
  mixins: [formatNumberMixin, dateMixin],
  data() {
    return {
      promotionType: '',
      dialogDetail: false,
      promotion: {}
    }
  },
  watch: {
  },
  async mounted() {
    EventBus.$on('showPromotionDetail', async (data) => {
      this.promotionType = data.promotion_type.name;
      const res = await detail(data.id);
      this.promotion = res.data;
      this.dialogDetail = true;
    });
  },
  methods: {

  }
}
</script>