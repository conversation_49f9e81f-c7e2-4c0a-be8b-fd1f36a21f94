<template>
  <el-form label-width="150px" style="max-width: 1600px" class="w-full">
    <!-- Select Store -->
    <el-form-item label="Select Store" class="form-item">
      <el-select
          filterable
          v-model="storeIdSelected"
          :placeholder="$t('Select store')"
          class="w-1/2"
          clearable
      >
        <el-option
            v-for="item in filteredStores"
            :key="item.id"
            :label="item.name"
            :value="String(item.id)"
        />
      </el-select>
    </el-form-item>

    <!-- Conditional Rendering -->
    <template v-if="storeIdSelected">
      <!-- Product Pricing -->
      <el-form-item label="Product Pricing" class="form-item label-bold">
        <el-switch v-model="importAllProductPricing" active-text="Import All" class="form-control"/>
      </el-form-item>

      <el-form-item label="Select Style" class="form-item">
        <el-select
            class="w-1/2"
            v-model="productStyles"
            filterable
            multiple
            :placeholder="$t('Select style')"
            popper-class="mb-8"
            :disabled="importAllProductPricing"
        >
          <el-option
              v-for="item in productStyleOptions"
              :key="item.id"
              :label="item.style"
              :value="String(item.style)"
          />
        </el-select>
      </el-form-item>
      <!-- Shipping Pricing -->
      <el-form-item label="Shipping Pricing" class="form-item label-bold">
        <el-switch v-model="importAllShippingPricing" active-text="Import All" class="form-control"/>
      </el-form-item>

      <el-form-item label="Select Type" class="form-item">
        <el-select
            class="w-1/2"
            v-model="productTypes"
            filterable
            multiple
            :placeholder="$t('Select type')"
            popper-class="mb-8"
            :disabled="importAllShippingPricing"
        >
          <el-option
              v-for="item in productTypeOptions"
              :key="item.type"
              :label="item.type"
              :value="item.type"
          />
        </el-select>
      </el-form-item>

      <!-- Surcharge Pricing -->
      <el-form-item label="Surcharge Fee" class="form-item label-bold">
        <el-switch v-model="importAllSurchargePricing" active-text="Import All" class="form-control"/>
      </el-form-item>

      <el-form-item label="Surcharge Type" class="form-item">
        <el-select
            class="w-1/2"
            v-model="surchargeFees"
            filterable
            multiple
            :placeholder="$t('Select type')"
            popper-class="mb-8"
            :disabled="importAllSurchargePricing"
        >
          <el-option
              v-for="item in surchargeFeeOptions"
              :key="item.id"
              :label="item.name"
              :value="String(item.name)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="Submit On (PST)" class="form-item">
        <el-date-picker
            v-model="selectedDate"
            type="date"
            placeholder="Select Date"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            class="w-1/3"
            :disabled-date="disabledPastDates"
        />
        <el-time-picker
            v-model="selectedTime"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="Time"
            class="w-1/3 ml-2"
            :disabled-hours="disabledPastHours"
            :disabled-minutes="disabledPastMinutes"
        />
      </el-form-item>
      <el-form-item>
        <div class="flex justify-end mt-4 w-1/2">
          <el-button @click="handleSubmit()" type="primary">Update</el-button>
        </div>
      </el-form-item>
    </template>
  </el-form>
</template>
<style scoped>
.label-bold {
  font-weight: bold;
}

.form-item {
  display: flex;
  align-items: center;
}
</style>
<script>
import {clonePricing, list} from '@/api/storeProduct';
import {getToken} from "@/utilities/jwt";
import ShowMessage from "@/modules/store-product/components/ShowMessage.vue";
import {mapGetters} from "vuex";
import {getSurchargeFees} from "@/api/storeSurcharge";
import {fetch} from "@/api/storeShipment";

export default {
  name: 'clone-pricing',
  components: {ShowMessage},
  props: ['storeId'],
  data() {
    return {
      headerInfo: {
        'Authorization': getToken()
      },
      isLoading: false,
      showTable: false,
      storeIdSelected: '',
      importAllProductPricing: true,
      importAllShippingPricing: true,
      importAllSurchargePricing: true,
      productStyles: [],
      productTypes: [],
      surchargeFees: [],
      surchargeFeeOptions: [],
      productStyleOptions: [],
      productTypeOptions: [],
      selectedDate: null,
      selectedTime : null,
      effective_from : null
    };
  },
  watch: {
    currentFilter: {
      handler() {
        this.formatDataTable();
      }
    },
    storeIdSelected: "handleStoreChange",
    importAllProductPricing(newValue) {
      if (newValue) {
        this.productStyles = [];
      }
    },
    importAllShippingPricing(newValue) {
      if (newValue) {
        this.productTypes = [];
      }
    },
    importAllSurchargePricing(newValue) {
      if (newValue) {
        this.surchargeFees = [];
      }
    },
  },
  async mounted() {
    const result = await Promise.all([
      this.fetchStore(),
      list({store_id: this.storeId}),
      fetch(this.storeId),
      getSurchargeFees({store_id: this.storeId}),
    ]);
    const [stores, productStyleData, ProductTypesData, surchargeFeesData] = result;
    this.productStyleOptions = productStyleData.data;
    this.productTypeOptions = ProductTypesData.data.data;
    this.surchargeFeeOptions = surchargeFeesData.data;
    this.isLoading = false;
  },
  computed: {
    ...mapGetters(['getStores']),
    filteredStores() {
      if (!this.storeId || !Array.isArray(this.getStores)) {
        return this.getStores || [];
      }
      return this.getStores.filter(store => store.id != this.storeId);
    },
    enableButton() {
      return this.products.length || this.shipments.length;
    },
    enableButtonDownloadErrorFile() {
      return this.errorProduct.length || this.errorShipment.length;
    },
    buildParams() {
      return {
        product_pricing: {
          include_all: this.importAllProductPricing,
          product_styles: this.importAllProductPricing ? [] : this.productStyles,
        },
        shipping_pricing: {
          include_all: this.importAllShippingPricing,
          product_types: this.importAllShippingPricing ? [] : this.productTypes,
        },
        surcharge_pricing: {
          include_all: this.importAllSurchargePricing,
          surcharges: this.importAllSurchargePricing ? [] : this.surchargeFees,
        },
        store_id :this.storeId,
        source_store_id :this.storeIdSelected,
        effective_from  : (!this.selectedDate || !this.selectedTime) ? null : `${this.selectedDate} ${this.selectedTime}:00`,
      };
    },
  },
  methods: {
    async fetchStore() {
      await this.$store.dispatch('getStores', {without_pagination: 1});
    },
    async handleSubmit() {
      try {
        if ((this.selectedDate === null) !== (this.selectedTime === null)) {
          this.$notify.error("Both selectedDate and selectedTime must be null or both must have a value.");
          return ;
        }
        this.isLoading = true;
        const params = this.buildParams;
        console.log("Generated Params:", params);
        const result = await clonePricing(params);
        this.$notify.success('Import successfully!');
        window.location.reload();
      } catch (error) {
        const errorMessage = this.getErrorMessage(error.response.data);
        this.$notify.error(errorMessage);
      } finally {
        this.isLoading = false;
      }
    },
    async handleStoreChange() {
      this.productStyles = '';
      this.productTypes = '';
      this.surchargeFees = '';
      this.productStyleOptions = [];
      this.productTypeOptions = [];
      this.surchargeFeeOptions = [];
      this.isLoading = true;
      if (!this.storeIdSelected) {
        this.isLoading = false;
        return;
      }
      const result = await Promise.all([
        this.fetchStore(),
        list({ store_id: this.storeIdSelected }),
        fetch(this.storeIdSelected),
        getSurchargeFees({ store_id: this.storeIdSelected }),
      ]);
      const [stores, productStyleData, productTypesData, surchargeFeesData] = result;
      this.productStyleOptions = productStyleData.data || [];
      this.productTypeOptions = productTypesData.data?.data || [];
      this.surchargeFeeOptions = surchargeFeesData.data || [];
      this.isLoading = false;
    },
    getErrorMessage(errorData) {
      if (errorData.errors) {
        return Object.values(errorData.errors)
            .flat()
            .join(", ");
      }
      return errorData.message || "An error occurred.";
    },
    disabledPastDates(date) {
      const now = new Date();
      const pstNow = new Date(now.toLocaleString("en-US", { timeZone: "America/Los_Angeles" }));
      pstNow.setHours(0, 0, 0, 0); // Đặt về 00:00:00 để so sánh chính xác
      return date.getTime() < pstNow.getTime();
    },
    disabledPastHours() {
      if (!this.selectedDate) return [];
      const now = new Date();
      const pstNow = new Date(
          now.toLocaleString("en-US", { timeZone: "America/Los_Angeles" })
      );

      const selected = new Date(this.selectedDate);

      console.log("🕒 Giờ hiện tại PST:", pstNow.getHours());
      console.log("📅 Ngày được chọn:", selected.toDateString());

      if (selected.toDateString() === pstNow.toDateString()) {
        return Array.from({ length: pstNow.getHours() }, (_, i) => i);
      }

      return [];
    }
    ,
    disabledPastMinutes(hour) {
      if (!this.selectedDate) return [];
      const now = new Date();
      const pstNow = new Date(now.toLocaleString("en-US", { timeZone: "America/Los_Angeles" }));
      const selected = new Date(this.selectedDate);
      return selected.toDateString() === pstNow.toDateString() && hour === pstNow.getHours()
          ? [...Array(pstNow.getMinutes()).keys()]
          : [];
    }
  },
}
</script>
