<template>
  <div class="flex mt-3">
    <el-select
      class="w-1/2 mr-3"
      v-model="selected"
      placeholder="Export All"
      filterable multiple :placeholder="$t('Select tag')" popper-class="mb-8">
      <el-option
        v-for="item in dataTable"
        :key="item.name"
        :label="item.name"
        :value="String(item.sku)"
      >
      </el-option>
    </el-select>
    <el-button @click="exportSku()" type="success">{{ $t('Export') }}</el-button>
  </div>
</template>
<script>
import {productStyle, exportExcelSku} from '@/api/product.js';

export default {
  name: 'export',
  props: ['storeId'],
  data() {
    return {
      dataTable: [],
      selected: []
    }
  },
  async mounted() {
    const {data} = await productStyle();
    this.dataTable = data;
  },
  methods: {
    async exportSku() {
      const fileName = `pricing-${this.storeId}.xlsx`
      const {data} = await exportExcelSku({store_id: this.storeId, sku: this.selected.join(',')})
      const url = URL.createObjectURL(new Blob([data], {
        type: 'application/vnd.ms-excel'
      }))
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();

    }
  }
}
</script>