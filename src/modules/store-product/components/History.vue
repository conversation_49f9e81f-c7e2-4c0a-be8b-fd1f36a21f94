<template>
  <table class="table-auto border w-full mt-5" v-if="dataTable.length > 0">
    <thead>
    <tr class="font-bold bg-gray-200">
      <td class="border p-2 w-3">{{ $t('File Name') }}</td>
      <td class="border p-2">{{ $t('User') }}</td>
      <td class="border p-2">{{ $t('Date') }}</td>
      <td class="border p-2">{{ $t('File') }}</td>
      <td class="border p-2">{{ $t('Effective From (PST)') }}</td>
      <td class="border p-2">{{ $t('Status') }}</td>
    </tr>
    </thead>
    <tbody>
    <tr class="border" v-for="data in dataTable">
      <td class="border p-2 w-3">{{ data.name }}</td>
      <td class="border p-2">{{ data.user?.username }}</td>
      <td class="border p-2">{{ formatDate(data.created_at_pst) }}</td>
      <td class="border p-2"><a :href="data.url">Download</a></td>
      <td class="border p-2">{{ formatDate(data.effective_from ?? data.created_at_pst) }}</td>
      <td class="border p-2 font-bold" :class="getStatusClass(data.status)">
        {{ getStatusText(data.status) }}
        <el-popconfirm
            v-if="data.status === 'pending'"
            title="Are you sure to cancel pricing submission?"
            @confirm="cancelImport(data.id)"
        >
          <template #reference>
            <el-link :underline="false" type="danger" class="ml-2">
              <icon :data="iconDelete" />
            </el-link>
          </template>
        </el-popconfirm>
      </td>
    </tr>
    </tbody>
  </table>
</template>
<script>
import {fetchHistory, cancelImportPricing} from '@/api/storeProduct'
import moment from "moment";

export default {
  name: 'pricing-history',
  props: ['storeId'],
  data() {
    return {
      dataTable: []
    }
  },
  async mounted() {
    this.fetchDataHistory();
  },
  methods: {
    formatDate(date) {
      return moment(date).format("YYYY-MM-DD HH:mm:ss");
    },
    async fetchDataHistory(){
      const {data} = await fetchHistory(this.storeId);
      this.dataTable = data;
    },
    getStatusText(status) {
      if (status === "completed" || status === null) return "Completed";
      if (status === "canceled") return "Canceled";
      if (status === "error") return "Error";
      if (status === "pending") return "Pending";
      return "Unknown";
    },
    getStatusClass(status) {
      if (status === "completed" || status === null) return "text-green-600";
      if (status === "canceled") return "text-yellow-600";
      if (status === "error") return "text-red-600";
      if (status === "pending") return "text-gray-600";
      return "text-black";
    },
    async cancelImport(id) {
      try {
        const { data } = await cancelImportPricing(id, 'upload-pricing');
        this.$notify.success('Upload pricing cancelled successfully!');
        await this.fetchDataHistory();
      } catch (error) {
        this.$notify.error(error.response?.data?.message || 'Failed to cancel upload. Please try again.');
      }
    }
  }
}
</script>