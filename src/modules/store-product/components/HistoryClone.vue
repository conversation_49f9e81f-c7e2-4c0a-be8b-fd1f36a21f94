<template>
  <table class="table-auto border w-full mt-5" v-if="dataTable.length > 0">
    <thead>
    <tr class="font-bold bg-gray-200">
      <td class="border p-2">{{ $t('Date') }}</td>
      <td class="border p-2">{{ $t('Store') }}</td>
      <td class="border p-2">{{ $t('Product Pricing') }}</td>
      <td class="border p-2">{{ $t('Shipping Pricing') }}</td>
      <td class="border p-2">{{ $t('Surcharge Fee') }}</td>
      <td class="border p-2">{{ $t('Effective From (PST)') }}</td>
      <td class="border p-2">{{ $t('Status') }}</td>
    </tr>
    </thead>
    <tbody>
    <tr class="border" v-for="data in dataTable" :key="data.id">
      <td class="border p-2">{{ formatDate(data.created_at_pst) }}</td>
      <td class="border p-2">{{ data.source_store.name }}</td>
      <td class="border p-2">{{ data.product_pricing_log }}</td>
      <td class="border p-2">{{ data.shipping_pricing_log }}</td>
      <td class="border p-2">{{ data.surcharge_pricing_log }}</td>
      <td class="border p-2">{{ formatDate(data.effective_from ?? data.created_at_pst) }}</td>
      <td class="border p-2 font-bold" :class="getStatusClass(data.status)">
        {{ getStatusText(data.status) }}
        <el-popconfirm
            v-if="data.status === 'pending'"
            title="Are you sure to cancel pricing submission?"
            @confirm="cancelImport(data.id)"
        >
          <template #reference>
            <el-link :underline="false" type="danger" class="ml-2">
              <icon :data="iconDelete" />
            </el-link>
          </template>
        </el-popconfirm>
      </td>
    </tr>
    </tbody>
  </table>
</template>

<script>
import {fetchClonePricingHistory, cancelImportPricing} from '@/api/storeProduct'
import moment from "moment";

export default {
  name: 'clone-pricing-history',
  props: ['storeId'],
  data() {
    return {
      dataTable: []
    }
  },
   mounted() {
    this.fetchDataHistory()
  },
  methods: {
    async fetchDataHistory(){
      const {data} = await fetchClonePricingHistory(this.storeId);
      this.dataTable = data;
    },
    formatDate(date) {
      return moment(date).format("YYYY-MM-DD HH:mm:ss");
    },
    getStatusText(status) {
      if (status === "completed" || status === null) return "Completed";
      if (status === "canceled") return "Canceled";
      if (status === "error") return "Error";
      if (status === "pending") return "Pending";
      return "Unknown";
    },
    getStatusClass(status) {
      if (status === "completed" || status === null) return "text-green-600";
      if (status === "canceled") return "text-yellow-600";
      if (status === "error") return "text-red-600";
      if (status === "pending") return "text-gray-600";
      return "text-black";
    },
    async cancelImport(id) {
      try {
        const { data } = await cancelImportPricing(id, 'import-pricing');
        this.$notify.success('Import cancelled successfully!');
        await this.fetchDataHistory();
      } catch (error) {
        this.$notify.error(error.response?.data?.message || 'Failed to cancel import. Please try again.');
      }
    }
  },

}
</script>