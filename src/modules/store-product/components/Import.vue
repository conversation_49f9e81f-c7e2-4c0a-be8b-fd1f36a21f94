<template>
  <el-upload
    v-loading="isLoading"
    class="el-upload-drag-full mb-1"
    limit="1"
    accept=".xlsx"
    drag
    ref="upload"
    :on-change="onChange"
    :on-success="onSuccess"
    :on-error="onError"
    :action="uploadApi"
    :headers="headerInfo"
    :data="dataUpload"
  >
    <el-icon class="el-icon--upload">
      <icon :data="iconPlus"/>
    </el-icon>
    <div class="el-upload__text">
      {{ $t('Drop file here or') }} <em>{{ $t('click to upload') }}</em>
    </div>
  </el-upload>
  <div v-if="errorFile.length > 0">
    <div v-for="error in errorFile">
      <el-tag type="error">{{ error }}</el-tag>
    </div>
  </div>

  <div v-if="showTable" class="flex flex-row justify-between items-center">
    <!-- Pricing Section -->
    <div class="flex items-center space-x-2">
      <span class="text-xs font-bold py-1 px-2">{{ $t('Product Pricing') }}</span>
      <el-button
          class="mr-2"
          v-for="btn in buttons.filter(i => i.type === 'product')"
          size="small"
          round
          @click="activeButton(btn.id)"
          :type="btn.id == currentFilter ? 'primary' : 'default'"
      >
        <span :class="colorText(btn)">{{ btn.label }}{{ btn.value }}</span>
      </el-button>

      <span class="text-xs font-bold py-1 px-2">{{ $t('Shipping Pricing') }}</span>
      <el-button
          class="mr-2"
          v-for="btn in buttons.filter(i => i.type === 'shipment')"
          size="small"
          round
          :type="btn.id == currentFilter ? 'primary' : 'default'"
          @click="activeButton(btn.id)"
      >
        <span :class="colorText(btn)">{{ btn.label }}{{ btn.value }}</span>
      </el-button>
    </div>
    <!-- Submit On Section -->
    <div class="flex items-center space-x-2">
      <span class="text-xs font-bold">{{ $t('Submit On (PST)') }}:</span>
      <el-date-picker
          v-model="selectedDate"
          type="date"
          placeholder="Select Date"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          class="w-1/4 min-w-[150px]"
          :disabled-date="disabledPastDates"
      />
      <el-time-picker
          v-model="selectedTime"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="Time"
          class="w-[12.5%] min-w-[90px]"
          :disabled-hours="disabledPastHours"
          :disabled-minutes="disabledPastMinutes"
      />
    </div>
  </div>

  <!-- Buttons Section -->
  <div v-if="showTable" class="flex justify-end mt-2">
    <el-button type="primary" round :disabled="!enableButtonDownloadErrorFile" @click="exportFileError()">
      {{ $t('Download Error') }}
    </el-button>
    <el-button type="primary" :disabled="!enableButton" round @click="importButton()" class="ml-2">
      {{ $t('Import') }}
    </el-button>
  </div>

  <div v-if="showTable">
    <table class="table-auto border w-full mt-5" v-if="[1,2,3].includes(currentFilter)">
      <thead>
      <tr class="bg-gray-600 text-white font-bold">
        <td class="border  p-2 text-center" v-for="title in headerTable">{{ $t(title) }}</td>
      </tr>
      </thead>
      <tbody>
      <tr class="border" v-for="data in dataTable">
        <td class="border p-2" v-for="title in headerTable">
        <span v-if="title == 'message'">
          <span v-if="data[title] === true">
            <el-tag type="success">
              {{ $t('Valid') }}
            </el-tag>
          </span>
          <span v-else>
          <ShowMessage :messages="data[title]"/>
          </span>
        </span>
          <span v-else>
{{
              !['SKU', 'status'].includes(title) && isNumber(data[title])
                  ? `$${convertNumber(data[title])}`
                  : data[title]
            }}        </span>
        </td>
      </tr>
      </tbody>
    </table>
    <table class="table-auto border w-full mt-5" v-else>
      <thead>
      <tr class="bg-gray-600 text-white font-bold">
        <td class="border p-2 text-center">{{ $t('Product Type') }}</td>
        <td class="border p-2 text-center">{{ $t('Destination') }}</td>
        <td class="border p-2 text-center">{{ $t('Service Type') }}</td>
        <td class="border p-2 text-center">{{ $t('Price') }}</td>
        <td class="border p-2 text-center">{{ $t('Addition Price') }}</td>
        <td class="border p-2 text-center">{{ $t('Message') }}</td>
      </tr>
      </thead>
      <tbody>
      <tr class="border" v-for="data in dataTable">
        <td class="border p-2">{{ data.product_type }}</td>
        <td class="border p-2">{{ data.destination }}</td>
        <td class="border p-2">{{ data.service_type }}</td>
        <td class="border p-2">{{ isNumber(data.price) ? `$${convertNumber(data.price)}` : data.price }}</td>
        <td class="border p-2">{{ isNumber(data.addition_price) ? `$${convertNumber(data.addition_price)}` : data.addition_price }}</td>
        <td class="border p-2">
        <span v-if="!data.message">
           <el-tag type="success">
              {{ $t('Valid') }}
            </el-tag>
        </span>
          <span v-else>
          <div v-for="(mess) in data.message" :class="data.message.length > 0 ? 'mb-1' : ''">
            <el-tag type="error">
              {{ mess }}
            </el-tag>
          </div>
        </span>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
import {importCsv} from '@/api/storeProduct';
import {isEmpty, uniq} from "ramda";
import {API_URL, S3_URL} from "@/utilities/constants";
import {getToken} from "@/utilities/jwt";
import ShowMessage from "@/modules/store-product/components/ShowMessage.vue";
import * as XLSX from 'xlsx';

export default {
  name: 'import-csv',
  components: {ShowMessage},
  data() {
    return {
      headerInfo: {
        'Authorization': getToken()
      },
      dataUpload: {},
      uploadApi: API_URL + '/store-product/import/excel',
      dataTable: [],
      buttons: [{
        id: 1,
        type: 'product',
        label: 'Total: ',
        value: 0,
      }, {
        id: 2,
        type: 'product',
        label: 'Valid: ',
        value: 0,
      }, {
        id: 3,
        type: 'product',
        label: 'Invalid: ',
        value: 0,
      }, {
        id: 4,
        type: 'shipment',
        label: 'Total: ',
        value: 0,
      }, {
        id: 5,
        type: 'shipment',
        label: 'Valid: ',
        value: 0,
      }, {
        id: 6,
        type: 'shipment',
        label: 'Invalid: ',
        value: 0,
      }],
      currentFilter: 3,
      headerTable: ['SKU', 'Blank Price', 'Handling Fee'],
      products: [],
      shipments: [],
      errorShipment: false,
      errorProduct: false,
      isLoading: false,
      showTable: false,
      file: null,
      urlDownloadExample: S3_URL + '/pricing/history/piAh4C4Zj6MXF5UAkkQbdWpFPXNShsU8DKBo1gSr.xlsx',
      errorFile: [],
      selectedDate: null,
      selectedTime : null,
      effective_from : null
    };
  },
  watch: {
    currentFilter: {
      handler() {
        this.formatDataTable();
      }
    }
  },
  mounted() {
    this.dataUpload = {
      store_id: this.storeId,
    };
  },
  computed: {
    enableButton() {
      return this.products.length || this.shipments.length;
    },
    enableButtonDownloadErrorFile() {
      return this.errorProduct.length|| this.errorShipment.length;
    }
  },
  props: ['storeId'],
  methods: {
    isNumber(value) {
      return !isNaN(parseFloat(value)) && isFinite(value);
    },
    activeButton(id) {
      this.currentFilter = id;
    },
    colorText(btn) {
      let className = '';
      switch (true) {
        case btn.id == this.currentFilter:
          className = 'text-white';
          break;
        case btn.label.toLowerCase().includes('invalid'):
          className = 'text-red-600';
          break;
        case btn.label.toLowerCase().includes('valid'):
          className = 'text-green-600';
          break;
      }
      return className;
    },
    convertNumber(num) {
      if (isEmpty(num)) {
        return 0;
      }
      return Number(num).toFixed(2);
    },
    valueButton() {
      const countErrorProd = this.errorProduct.length;
      const countErrorShip = this.errorShipment.length;
      this.buttons = this.buttons.map(button => {
        switch (button.id) {
          case 1:
            return {...button, value: this.products.length + countErrorProd}
          case 2:
            return {...button, value: this.products.length}
          case 3:
            return {...button, value: countErrorProd}
          case 4:
            return {...button, value: this.shipments.length + countErrorShip}
          case 5:
            return {...button, value: this.shipments.length}
          case 6:
            return {...button, value: countErrorShip}
        }
      })
    },
    formatDataTable() {
      if ([1, 2, 3].includes(this.currentFilter)) {
        this.headerTable = uniq(Object.keys(this.products[0] ?? this.errorProduct[0]));
        switch (this.currentFilter) {
          case 1:
            this.dataTable = this.products.slice(0, 20).concat(this.errorProduct.slice(0, 20));
            break;
          case 2:
            this.dataTable = this.products.slice(0, 20);
            break;
          case 3:
            this.dataTable = this.errorProduct.slice(0, 20);
            break;
        }
        console.log(this.dataTable)
      } else {
        switch (this.currentFilter) {
          case 4:
            this.dataTable = this.shipments.concat(this.errorShipment);
            break;
          case 5:
            this.dataTable = this.shipments;
            break;
          case 6:
            this.dataTable = this.errorShipment;
            break;
        }
      }
    },

    onChange() {
      // this.showTable = false;
    },
    async onSuccess(result, file) {
      this.isLoading = false;
      this.file = file.raw;
      this.$refs.upload?.clearFiles();
      this.currentFilter = 3;
      if (result.type === 'excel') {
        this.errorFile = result.errors || [];
        this.showTable = false;
      } else {
        this.errorFile = [];
        this.errorProduct = result?.error_product || [];
        this.products = result?.products || [];
        this.errorShipment = result?.error_shipment || [];
        this.shipments = result?.shipments || [];
        this.formatDataTable();
        this.valueButton();
        this.showTable = true;
      }
    },
    async importButton() {
      if ((this.selectedDate === null) !== (this.selectedTime === null)) {
        this.$notify.error("Both selectedDate and selectedTime must be null or both must have a value.");
        return ;
      }
      this.isLoading = true;
      const bodyFormData = new FormData();
      bodyFormData.append('file', this.file);
      bodyFormData.append('store_id', this.dataUpload.store_id)
      bodyFormData.append('import', 1)
      const effective_from = (!this.selectedDate || !this.selectedTime) ? null : `${this.selectedDate} ${this.selectedTime}:00`
      if(effective_from != null){
        bodyFormData.append('effective_from', effective_from)
      }
      const result = await importCsv(bodyFormData);
      if (result.status === 200) {
        this.$notify.success('Import successfully!');
        window.location.reload();
      }
      this.isLoading = false;
    },
    onError() {
      this.showTable = true;
      this.isLoading = false;
      this.$refs.upload.clearFiles()
    },
    exportFileError() {
      const workbook = XLSX.utils.book_new();
      // Hàm tạo dữ liệu sheet và thêm vào workbook
      const createSheet = (data, headers, sheetName) => {
        if (data.length) {
          const sheetData = [
            headers,
            ...data.map(row =>
                headers.map(header =>
                    Array.isArray(row[header]) ? row[header].join("; ") : row[header] || ""
                )
            ),
          ];
          const sheet = XLSX.utils.aoa_to_sheet(sheetData);
          XLSX.utils.book_append_sheet(workbook, sheet, sheetName);
        }
      };
      createSheet(
          this.errorProduct,
          ['SKU', 'message'], // Headers
          'Apparel' // Sheet name
      );
      createSheet(
          this.errorShipment,
          ['destination', 'service_type', 'product_type', 'size', 'price', 'addition_price', 'message'], // Headers
          'Shipping' // Sheet name
      );

      // Xuất file Excel
      XLSX.writeFile(workbook, 'pricing_error.xlsx');
    },
    disabledPastDates(date) {
      const now = new Date();
      const pstNow = new Date(now.toLocaleString("en-US", { timeZone: "America/Los_Angeles" }));
      pstNow.setHours(0, 0, 0, 0); // Đặt về 00:00:00 để so sánh chính xác
      return date.getTime() < pstNow.getTime();
    },
    disabledPastHours() {
      if (!this.selectedDate) return [];
      const now = new Date();
      const pstNow = new Date(
          now.toLocaleString("en-US", { timeZone: "America/Los_Angeles" })
      );
      const selected = new Date(this.selectedDate);
      if (selected.toDateString() === pstNow.toDateString()) {
        return Array.from({ length: pstNow.getHours() }, (_, i) => i);
      }
      return [];
    },
    disabledPastMinutes(hour) {
      if (!this.selectedDate) return [];
      const now = new Date();
      const pstNow = new Date(now.toLocaleString("en-US", { timeZone: "America/Los_Angeles" }));
      const selected = new Date(this.selectedDate);
      return selected.toDateString() === pstNow.toDateString() && hour === pstNow.getHours()
          ? [...Array(pstNow.getMinutes()).keys()]
          : [];
    }
  }
}
</script>
