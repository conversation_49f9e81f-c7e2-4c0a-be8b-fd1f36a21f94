<template>
  <div class="mb-4 w-3/5">
    <div class="flex justify-between text-md">
      <div class="font-bold">{{ $t(`${label}`) }}</div>
      <div>
        <button
          @click="$emit('on-selected-all', isObj ? list.map(el => el.id) : list.map(el => el))"
          v-if="list.length"
        >
          {{ $t('Select all')}}
        </button>
        |
        <button
          @click="$emit('on-clear')"
        >
          {{ $t('Deselect all')}}
        </button>
      </div>
    </div>
    <div class="flex flex-wrap border p-2 mt-2">
      <el-checkbox-group v-model="changeSelectedList" @change="selectItem">
        <template v-if="!isObj">
          <el-checkbox class="w-44 capitalize" v-for="(item, index) in list" :label="item" :key="index" />
        </template>
        <template v-else>
          <el-checkbox class="w-44 capitalize" v-for="item in list" :label="item.id" :key="item.id">
            {{ item.name }}
          </el-checkbox>
        </template>
      </el-checkbox-group>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Selection',
  emits: ['on-selected-all', 'on-clear'],
  props: {
    list: {
      type: Array,
      default: []
    },
    isObj: {
      type: Boolean,
      default: false
    },
    selectedList: {
      type: Array,
      default: []
    },
    label: {
      type: String
    },
  },
  data() {
    return {
      changeSelectedList: this.selectedList
    }
  },
  watch: {
    selectedList: {
      handler() {
        this.changeSelectedList = this.selectedList;
      },
      deep: true,
    }
  },
  methods: {
    selectItem() {
      this.$emit('on-selected-all', [...this.changeSelectedList]);
    },
  }
}
</script>