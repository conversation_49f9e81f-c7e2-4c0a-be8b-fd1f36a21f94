<template>
  <div>
    <el-tooltip
      v-for="(color, index) in colors"
      effect="dark"
      :content="color.name"
      placement="top-start"
    >
      <el-button
        v-show="showAll ?  true : index < 6"
        style="height: 10px; width: 10px; margin: 0px 5px 0px 0px;"
        circle
        :color="color.color_code"
        :dark="isDark"
      />
    </el-tooltip>
    <el-button
      v-show="colors.length > 6"
      :type="!showAll ? 'primary' : 'info'"
      @click="showAll = !showAll"
      style="height: 10px; width: 10px; margin: 0px;"
      circle
      :dark="isDark"
    >
      <span v-if="!showAll">+</span>
      <span v-else>-</span>
    </el-button>
  </div>
</template>

<script>
export default {
  data() {
    return {
      showAll: false,
    }
  },
  props: {
    colors: {
      type: Array,
      default: []
    }
  }
}
</script>

<style scoped>
.el-button svg {
  margin: 0px;
  top: 0;
}
</style>
