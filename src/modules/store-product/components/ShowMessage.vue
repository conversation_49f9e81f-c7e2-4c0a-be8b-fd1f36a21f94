<template>
  <div v-if="showAll">
    <el-tag type="error" class="mr-1">
      {{ messages[0] }}
    </el-tag>
    <el-button
      v-if="messages.length > 1"
      type="primary"
      style="height: 10px; width: 10px; margin: 0px;"
      circle
      @click="toggle()"
    >+</el-button>
  </div>
  <div v-else>
    <div v-for="(mess) in messages" :class="messages.length > 0 ? 'mb-1 mr-1' : ''">
      <el-tag type="error">
        {{ mess }}
      </el-tag>
    </div>
    <el-button
      type="primary"
      style="height: 10px; width: 10px; margin: 0px;"
      circle
      @click="toggle()"
    ><span class="">-</span></el-button>
  </div>
</template>
<script>
export default {
  name: 'show-message',
  props: ['messages'],
  data() {
    return {
      showAll: true
    }
  },
  mounted() {
  },
  methods:{
    toggle(){
      this.showAll = !this.showAll
    }
  }
}
</script>
