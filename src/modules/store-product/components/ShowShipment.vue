<template>
  <el-dialog
    v-if="detailShipment"
    v-model="dialogVisible"
    @close="$emit('on-close-popup')"
  >
    <div v-loading="isLoading">
      <div class="flex flex-row justify-space-between items-center">
        <div><h1>{{ detailShipment.type + $t(' Shipping') }}</h1></div>
        <div>
          <span class="mr-1">Active/Inactive shipping pricing for product type weight</span>
          <el-switch v-model="detailShipment.status" @click="setCurrentState(detailShipment.status)"/>
        </div>
      </div>
      <el-dialog
        v-model="innerVisible"
        width="30%"
        :title="$t('Are you sure to update this ?')"
        @close="cancelChange"
        :show-close="false"
      >
        <template #footer>
          <div class="flex justify-end">
            <el-button type="info" @click="cancelChange">{{ $t('Cancel') }}</el-button>
            <el-button type="primary" @click="confirmChange">{{ $t('Confirm') }}</el-button>
          </div>
        </template>
      </el-dialog>
      <div class="flex justify-end">
        <table class="border-collapse border w-full mt-5" v-loading="isLoading">
          <thead>
          <tr class="bg-gray-200 font-bold">
            <td class="text-left p-2">{{ $t('Shipping Type') }}</td>
            <td class="text-left p-2">{{ $t('Service Type') }}</td>
            <td class="text-left p-2">{{ $t('Size') }}</td>
            <td class="text-left p-2">{{ $t('1st Item') }}</td>
            <td class="text-left p-2">{{ $t('Additional Item') }}</td>
            <td class="text-left p-2">{{ $t('Status') }}</td>
          </tr>
          </thead>
          <tbody>
          <tr
            class="border"
            v-for="(item, index) in shipments"
            :key="index"
          >
            <td class="px-2 py-1">{{ item.destination }}</td>
            <td class="px-2 py-1">
              <el-tag
                type="info"
                :class="getClass(item.service_type)"
                class="rounded-xl !text-white"
                round
                size="small"
              >
                {{ item.service_type }}
              </el-tag>
            </td>
            <td class="px-2 py-1">{{ item.size || '' }}</td>
            <td class="px-2 py-1">${{ item.price }}</td>
            <td class="px-2 py-1">${{ item.addition_price }}</td>
            <td class="px-2 py-1">
              <el-switch v-model="item.status" @click="setCurrentStateDetail(item.id, item.status)"/>
            </td>

          </tr>
          </tbody>
        </table>
        <el-dialog
          v-model="innerVisibleDetail"
          width="30%"
          :title="$t('Are you sure to update this ?')"
          @close="cancelChangeDetail()"
          :show-close="false"
        >
          <template #footer>
            <div class="flex justify-end">
              <el-button type="info" @click="cancelChangeDetail()">{{ $t('Cancel') }}</el-button>
              <el-button type="primary" @click="confirmChangeDetail()">{{ $t('Confirm') }}</el-button>
            </div>
          </template>
        </el-dialog>
      </div>

    </div>
  </el-dialog>
</template>

<script>

import {isEmpty} from "ramda";
import {fetchDetailShipping, setStatusShipping, setStatusShippingDetail} from '@/api/storeShipment.js';

export default {
  name: 'show-shipment',
  data() {
    return {
      shipmentStatus: false,
      dialogVisible: false,
      isLoading: false,
      shipments: [],
      innerVisible: false,
      innerVisibleDetail: false,
      currentState: null,
      currentStateDetail: null
    }
  },
  emits: ['on-close-popup'],
  components: {},
  props: {
    detailShipment: {
      type: Object,
      default: {}
    },
  },
  watch: {
    detailShipment(newVal, oldVal) {
      if (isEmpty(newVal)) {
        this.dialogVisible = false;
      } else {
        this.dialogVisible = true;
        this.fetchData();
      }
    }
  },

  methods: {
    async fetchData() {
      this.isLoading = true;
      if (!this.detailShipment) {
        return;
      }
      const {data} = await fetchDetailShipping({
        store_id: this.detailShipment.store_id,
        type: this.detailShipment.type
      })
      this.shipments = data.data;
      this.isLoading = false;
    },
    getClass(type) {
      switch (type) {
        case "standard":
          return `bg-indigo-500`;
        case "express":
          return `!bg-red-500`;
        default:
          return 'bg-green-500';
      }
    },
    setCurrentState(status) {
      this.currentState = status;
      this.innerVisible = true;
    },
    cancelChange() {
      if (this.innerVisible) {
        this.detailShipment.status = this.currentState;
        this.innerVisible = false;
      }
    },
    async confirmChange() {
      try {
        const {data} = await setStatusShipping(this.detailShipment.store_id, {
          style: this.detailShipment.type,
          status: this.currentState
        });
        this.$notify({
          type: data.status ? 'success' : 'error',
          message: data.message,
          showClose: false,
          duration: 2000
        });
      } catch (e) {
        this.$notify({
          type: 'error',
          message: 'Update status failed!',
          showClose: false,
          duration: 2000
        });
      }
      this.innerVisible = false;
    },
    cancelChangeDetail() {
      if (this.innerVisibleDetail) {
        this.innerVisibleDetail = false;
        this.shipments = this.shipments.map(ship => {
          if (ship.id == this.currentStateDetail.id) {
            return {...ship, status: !this.currentStateDetail.status};
          }
          return {...ship};
        })
      }
    },
    async confirmChangeDetail() {
      try {
        const {data} = await setStatusShippingDetail(this.currentStateDetail?.id)
        this.$notify({
          type: data.status ? 'success' : 'error',
          message: data.message,
          showClose: false,
          duration: 2000
        });
      } catch (e) {
        this.$notify({
          type: 'error',
          message: 'Update status failed!',
          showClose: false,
          duration: 2000
        });
      }
      this.innerVisibleDetail = false;
    },
    setCurrentStateDetail(id, status) {
      this.currentStateDetail = {
        id,
        status
      };
      this.innerVisibleDetail = true;
    }
  }
}
</script>
