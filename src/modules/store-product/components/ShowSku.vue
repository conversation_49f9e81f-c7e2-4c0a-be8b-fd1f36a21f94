<template>
  <el-dialog
    custom-class="el-dialog-custom el-dialog-custom3"
    v-model="dialogVisible"
    @close="$emit('on-close-popup')"
  >
    <div v-loading="isLoading">
      <div class="flex flex-row justify-space-between mt-4" >
        <div>
          <h1>{{ $t('Style: ') + data.style }}</h1>
        </div>
        <div>
          <span class="mr-1">Active/Inactive product pricing for style</span>
          <el-switch
            v-model="data.status"
            @click="setCurrentState(!data.status)"
          />
          <el-dialog
            v-model="innerVisible"
            width="30%"
            :title="$t('Are you sure to update this?')"
            @close="cancelChange"
            :show-close="false"
          >
            <template #footer>
              <div class="flex justify-end">
                <el-button type="info" @click="cancelChange">
                  {{
                    $t('Cancel')
                  }}
                </el-button>
                <el-button type="primary" @click="confirmChange">
                  {{
                    $t('Confirm')
                  }}
                </el-button>
              </div>
            </template>
          </el-dialog>
        </div>
      </div>
      <div
        class="w-full overflow-auto max-h-[calc(100vh-220px)] border border-gray-200 mt-5"
        ref="scroller"
        @scroll="onScroll"
      >
        <table class="w-full" ref="table">
          <thead class="sticky top-0 z-[10] bg-gray-200 border-b border-gray-200">
            <tr class="bg-gray-100 font-bold">
              <td
                class="text-left p-2 break-normal whitespace-nowrap border-r border-gray-200"
                v-for="title in titleTable"
              >
                {{ $t(title.label) }}
              </td>
              <td
                class="text-left p-2 w-10 break-normal sticky right-0 bg-gray-100 z-[1]"
              >
                {{ $t('Status') }}
              </td>
            </tr>
          </thead>
          <tbody>
            <tr
              class="hover:bg-gray-100 border-b border-gray-200 group"
              v-for="(item, index) in products.slice(0, step)"
              :key="index"
            >
              <td
                class="px-2 py-1 whitespace-nowrap border-r border-gray-200"
                v-for="title in titleTable"
              >
                <span class="break-normal">
                  {{
                    !['sku', 'status', 'color', 'size'].includes(title.value) && item[title.value] !== ''
                        ? '$' + item[title.value]
                        : item[title.value]
                  }}
                </span>
              </td>
              <td class="px-2 py-1 sticky right-0 bg-white z-[1] group-hover:bg-gray-100">
                <el-switch
                  v-model="item.status"
                  @click="setDetailProductStatus(!item.status, index)"
                />
              </td>
            </tr>
          </tbody>
        </table>
        <el-dialog
          v-model="innerVisibleDetail"
          width="30%"
          :title="$t('Are you sure to update this ?')"
          @close="cancelChangeDetailProduct"
          :show-close="false"
        >
          <template #footer>
            <div class="flex justify-end">
              <el-button type="info" @click="cancelChangeDetailProduct">{{ $t('Cancel') }}</el-button>
              <el-button type="primary" @click="confirmChangeDetailStatus">{{ $t('Confirm') }}</el-button>
            </div>
          </template>
        </el-dialog>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import {getProduct, setProductStyleStatus, setDetailStatus} from '@/api/storeProduct'
import {head} from "ramda";

export default {
  data() {
    return {
      dialogVisible: false,
      isLoading: false,
      products: [],
      innerVisible: false,
      confirmClick: false,
      isAllowChange: false,
      currentState: '',
      currentIndex: '',
      titleTable: [],
      innerVisibleDetail: false,
      detailStatus: null,
      maxScrollItem: 50,
      step: 50
    }
  },
  emits: ['on-close-popup'],
  components: {},
  props: {
    data: {
      type: Object,
      default: {}
    }
  },
  mounted() {
  },
  watch: {
    'data.style'() {
      this.dialogVisible = this.data.showDialog;

      if(!this.dialogVisible) return;

      this.products = [];
      this.step = this.maxScrollItem;

      if (!!this.data.store && !!this.data.style) {
        this.fetchAllProduct();
      }
    },
    listProduct: {
      handler() {
        this.formatData()
      }
    }
  },
  methods: {
    onScroll() {
      const scrollTop = this.$refs.scroller.scrollTop;
      const boxHeight = this.$refs.scroller.offsetHeight;
      const tableHeight = this.$refs.table.clientHeight;

      if(this.step <= this.products.length && scrollTop > tableHeight - boxHeight - 250) {
        this.step += this.maxScrollItem;
      }
    },
    async fetchAllProduct() {
      this.isLoading = true;
      const query = {
        store_id: this.data.store,
        style: this.data.style,
      }
      const {data} = await getProduct(query);
      this.products = data.map(item => {
        return {...item}
      });
      this.formatData();
      this.isLoading = false;
    },
    async confirmChange() {
      this.isLoading = true;
      this.data.status = !this.currentState;
      this.confirmClick = true; // if click confirm then set state like current
      this.innerVisible = false;
      try {
        const input = {
          style: this.data.style,
          store_id: this.data.store
        }
        const result = await setProductStyleStatus(input);
        if (result.status == 200) {
          this.notification(result.data.message, 'success');
        }
      } catch (e) {
        this.notification(e.response.data.message, 'error');
      }
      this.isLoading = false;
    },
    cancelChange() {
      if (!this.confirmClick) { // if click cancel or dont click confirm then set state like old state
        this.data.status = this.currentState;
        this.innerVisible = false;
      }
    },
    setCurrentState(status) {
      this.confirmClick = false; // set click button confirm false
      this.currentState = status;
      this.innerVisible = true;
    },
    isShow(item) {
      return item.product != null && item.product_print_area_id != null;
    },
    async confirmChangeDetailStatus() {
      this.isLoading = true;
      this.innerVisibleDetail = false;
      const sku = this.products[this.currentIndex].sku;
      const {data} = await setDetailStatus({
        sku,
        status: !this.detailStatus,
        store_id: this.data.store,
      });
      this.products[this.currentIndex].status = !this.detailStatus;
      this.notification(data.message, data.success ? 'success' : 'error');
      this.isLoading = false;
    },
    setDetailProductStatus(status, index) {
      this.innerVisibleDetail = true;
      this.detailStatus = status;
      this.currentIndex = index;
    },
    cancelChangeDetailProduct() {
      this.products[this.currentIndex].status = this.detailStatus;
      this.innerVisibleDetail = false;
    },
    formatData() {
      this.titleTable = Object.keys(head(this.products)).filter(title => {
        return title.toLowerCase() !== 'status';
      }).map(title => {
        return {
          label: (title.charAt(0).toUpperCase() + title.slice(1)).replace('_', ' '),
          value: title,
        };
      });
    }
  },

}
</script>