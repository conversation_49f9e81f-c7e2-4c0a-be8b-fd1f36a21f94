<template>
  <div>
    <table class="border-collapse border w-full mt-5" v-loading="isLoading">
      <thead>
      <tr class="bg-gray-200 font-bold">
        <td class="text-left p-2" v-for="title in titleTable">{{ $t(title.label) }}</td>
        <td class="text-left p-2">{{ $t('Status') }}</td>
      </tr>
      </thead>
      <tbody>
      <tr
        class="border"
        v-for="(item, index) in listProduct"
        :key="index"
      >
        <td class="px-2 py-1" v-for="title in titleTable">
          <span>{{ !['sku', 'status'].includes(title.value) ? '$' : '' }}{{
              item[title.value]
            }}</span>
        </td>
        <!--        <td class="px-2 py-1" v-if="isShow(item)">{{ item.product?.color }}</td>-->
        <!--        <td class="px-2 py-1" v-if="isShow(item)">{{ item.product?.size }}</td>-->
        <!--        <td class="px-2 py-1" v-if="isShow(item)">{{ item.price }}</td>-->
        <!--        <td class="px-2 py-1" v-if="isShow(item)">{{ item.print_price }}</td>-->
        <!--        <td class="px-2 py-1" v-if="isShow(item)">{{ item.handling_fee }}</td>-->
        <td class="px-2 py-1">
          <el-switch v-model="item.status" @click="setCurrentState(!item.status, index)"/>
        </td>
      </tr>
      </tbody>
    </table>
    <el-dialog
      v-model="innerVisible"
      width="30%"
      :title="$t('Are you sure to update this ?')"
      @close="cancelChange"
      :show-close="false"
    >
      <div class="flex justify-center items-center">
        <icon
          class="text-warning"
          width="50"
          height="50"
          :data="iconWarning"
        />
      </div>
      <template #footer>
        <div class="flex justify-end">
          <el-button type="info" @click="cancelChange">{{ $t('Cancel') }}</el-button>
          <el-button type="primary" @click="confirmChange">{{ $t('Confirm') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {setStatus} from "@/api/storeProduct"
import {head} from "ramda";

export default {
  data() {
    return {
      isLoading: false,
      isAllowChange: false,
      currentState: '',
      currentIndex: '',
      innerVisible: false,
      confirmClick: false,
      titleTable: [],
    }
  },
  props: {
    listProduct: {
      type: Array,
      default: [],
    },
  },
  watch: {
    listProduct: {
      handler() {
        this.formatData()
      }
    }
  },
  methods: {
    isShow(item) {
      return item.product != null && item.product_print_area_id != null;
    },
    async confirmChange() {
      this.isLoading = true;
      this.listProduct[this.currentIndex].status = !this.currentState;
      const id = this.listProduct[this.currentIndex].id;
      this.confirmClick = true; // if click confirm then set state like current
      this.innerVisible = false;
      const {data} = await setStatus(id);
      this.notification(data.message, data.success ? 'success' : 'error');
      this.isLoading = false;
    },
    cancelChange() {
      if (!this.confirmClick) { // if click cancel or dont click confirm then set state like old state
        this.listProduct[this.currentIndex].status = this.currentState;
        this.innerVisible = false;
      }
    },
    setCurrentState(status, index) {
      this.confirmClick = false; // set click button confirm false
      this.currentState = status;
      this.currentIndex = index;
      this.innerVisible = true;
    },
    formatData() {
      this.titleTable = Object.keys(head(this.listProduct)).filter(title => {
        return title.toLowerCase() !== 'status';
      }).map(title => {
        return {
          label: (title.charAt(0).toUpperCase() + title.slice(1)).replace('_', ' '),
          value: title,
        };
      });
    }
  },
}
</script>
