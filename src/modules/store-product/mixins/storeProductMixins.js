import { getPrintArea } from '@/api/productStyle'
import { getProductAttributes } from '@/api/product'
import { downloadCsv } from "@/utilities/helper.js";
import { mapGetters } from 'vuex';

export default {
  data() {
    return {
      isLoading: false,
      stores: [],
      styles: [],
      printAreas: [],
      style: null,
      store: null,
      status: true,
      products: [],
      errorsShow: {},
      selectedProductSizes: [],
      selectedProductColors: [],
      selectedPrintAreas: [],
      printPriceSet: 0,
    }
  },
  computed: {
    ...mapGetters([
      "getAllProducts",
    ]),
    productColors() {
      let style = this.styles[this.style];
      return style ? style.colors : [];
    },
    productSizes() {
      let style = this.styles[this.style];
      return style ? style.sizes : [];
    },
  },
  methods: {
    dataSubmit() {
      let result = [];
      for (const item of this.listProduct) {
        result.push({
          product_id: item.id,
          price: item.price,
          handling_fee: item.handling_fee,
          print_areas: item.printAreas
        })
      }
      return result;
    },
    formatCsvData(data) {
      let csvData = "Style, Color, Size, Swiftpod SKU, Product Name, Swiftpod price, Blank Price, Print price, Handling fee, Platen size, Print size, Name Of Print Area\n";
      for (let item of data) {
        csvData += `"${item.style || ''}","${item.color || ''}","${item.size || ''}","${item.sku || ''}","${item.name || ''}","${item.total_price || '0'}","${item.price || '0'}","${item.print_price || '0'}","${item.handling_fee || '0'}","${item.platen_size || ''}","${item.print_size || ''}","${item.name_area || ''}",\n`;
      }
      return csvData;
    },
    resetSelected() {
      this.selectedProductSizes = [];
      this.selectedProductColors = [];
      this.selectedPrintAreas = [];
    },
    // Sizes
    handleSelectAllSize(value) {
      this.selectedProductSizes = value
    },
    handleClearSize() {
      this.selectedProductSizes = []
    },
    // Color
    handleSelectAllColor(value) {
      this.selectedProductColors = value
    },
    handleClearColor() {
      this.selectedProductColors = []
    },
    // Print area
    handleSelectAllPrintArea(value) {
      this.selectedPrintAreas = value
    },
    handleClearPrintArea() {
      this.selectedPrintAreas = []
    },
    async fetchProductAttribute() {
      this.isLoading = true;
      const { data } = await getProductAttributes();
      this.styles = data;
      this.isLoading = false;
    },
    async fetchPrintArea() {
      this.isLoading = true;
      const { data } = await getPrintArea({ name: this.style });
      this.printAreas = data;
      this.isLoading = false;
    },
    getPrintArea(id, attr) {
      let printArea = this.printAreas.find(p => p.id == id);
      if (printArea) {
        return printArea[attr]
      }
    },
    async getProductList() {
      await this.$store.dispatch("getAllProducts");
    },
    transformDataExport() {
      let result = []
      for (const item of this.listProduct) {
        let oneRow = {
          style: item.style,
          color: item.color,
          size: item.size,
          sku: item.sku,
          name: item.name,
          handling_fee: item.handling_fee,
          price: item.price,
        }
        for (const area of item.printAreas) {
          oneRow = {
            ...oneRow,
            name_area: this.getPrintArea(area.id, 'name'),
            print_price: area.print_price,
            platen_size: this.getPrintArea(area.id, 'platen_size'),
            print_size: this.getPrintArea(area.id, 'print_size'),
            total_price: parseFloat(item.price + item.handling_fee + area.print_price),
          }
          result.push(oneRow)
        }
      }
      return result;
    },
    async exportPrice() {
      const csvData = this.formatCsvData(this.transformDataExport());
      downloadCsv('store-product.csv', csvData);
    },
    findProduct(style, color, size) {
      const product = this.getAllProducts.find(product => {
        return style == product.style &&
          size == product.size &&
          color == product.color
      })
      if (!!product) {
        return product;
      }
      return {};
    },
  }
}