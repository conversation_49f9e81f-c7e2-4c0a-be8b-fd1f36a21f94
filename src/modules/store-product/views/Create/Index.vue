<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ $t("Create store products") }}</h1>
      </div>
      <div class="top-head-right">
        <el-button type="info" v-if="!isLoading" @click="$emit('on-click-list')">{{ $t('Back') }}</el-button>
      </div>
    </div>
    <div class="table-content">
      <div class="flex justify-between my-4">
        <div>
          <el-select
            filterable
            class="mr-10"
            v-model="store"
            :placeholder="$t('Select store')"
          >
            <el-option
              v-for="store in stores"
              :key="store.id"
              :label="store.name"
              :value="store.id"
            />
          </el-select>
          <el-select
            filterable
            v-model="style"
            :placeholder="$t('Select style')"
          >
            <el-option
              v-for="style in styles"
              :key="style"
              :label="style.style"
              :value="style.style"
            />
          </el-select>
        </div>

        <div class="flex">
          <el-form-item :label="$t('Active')" class="mr-10 mt-1">
            <el-switch v-model="status" />
          </el-form-item>
          <el-button
            type="primary"
            :disabled="isLoading"
            @click="handleSave"
          >
            {{ $t("Save") }}
          </el-button>
        </div>
      </div>

      <div class="font-bold text-2xl my-4" v-if="style"> {{ $t('Style ') + style }}</div>

      <div class="" v-if="style && store">
        <Selection
          label="Size"
          :list="productSizes"
          :selectedList="selectedProductSizes"
          @on-selected-all="handleSelectAllSize"
          @on-clear="handleClearSize"
        />
        <Selection
          label="Color"
          :list="productColors"
          :selectedList="selectedProductColors"
          @on-selected-all="handleSelectAllColor"
          @on-clear="handleClearColor"
        />
        <Selection
          label="Print Area"
          :isObj="true"
          :list="printAreas"
          v-if="printAreas.length"
          :selectedList="selectedPrintAreas"
          @on-selected-all="handleSelectAllPrintArea"
          @on-clear="handleClearPrintArea"
        />
        <TableData 
          :listProduct="listProduct"
          :printPriceSet="printPriceSet"
          @setPrice="setPrintPrice"
          @getPrice="(value) => printPriceSet = value"
          :selectedProductColors="selectedProductColors"
          :selectedProductSizes="selectedProductSizes"
          :selectedPrintAreas="selectedPrintAreas"
          :printAreas="printAreas"
          :errorsShow="errorsShow"
        />
      </div>
    </div>
  </div>
</template>
