import { all } from '@/api/store'
import Selection from "@/modules/store-product/components/Selection.vue";
import { create } from '@/api/storeProduct'
import TableData from "@/modules/store-product/components/TableData.vue";
import storeProductMixins from '../../mixins/storeProductMixins';

export default {
  name: "StoreProductCreate",
  components: {
    Selection,
    TableData,
  },
  emits: ['on-click-list'],
  mixins: [storeProductMixins],
  watch: {
    style: {
      handler() {
        this.resetSelected()
        this.fetchPrintArea();
      },
      deep: true
    },
  },
  computed: {
    listProduct() {
      const result = []
      for (const size of this.selectedProductSizes) {
        for (const color of this.selectedProductColors) {
          const product = this.findProduct(this.style, color, size);
          let element = {
            id: product.id,
            sku: product.sku,
            name: product.name,
            style: this.style,
            size: size,
            color: color,
            price: 0,
            handling_fee: 0,
            printAreas: this.selectedPrintAreas.map(area => ({id: area, print_price: 0, name: area.name}))
          }
          result.push(element);
        }
      }
      return result;
    }
  },
  created() {
    this.fetchAllStore();
    this.getProductList();
    this.fetchProductAttribute();
  },
  methods: {
    async handleSave() {
      const data = this.dataSubmit()
      this.isLoading = true;
      try {
        await create(data)
        this.resetSelected()
        const message = this.$t('The store product were created successful!');
        this.notification(message, "success");
      } catch (e) {
        const message = this.$t('Create fail!');
        this.notification(message, "error");
        for (const error in e.response.data.errors) {
          let index = error.replace(/[^0-9]/g, '');
          let errorId = data.product[index].product_print_area_id;
          let errorProductId = data.product[index].product_id;
          this.errorsShow['error_' + errorProductId + '-' + errorId] = this.$t('The product with the print area has already taken.');
        }
      }
      this.isLoading = false;
    },
    async fetchAllStore() {
      this.isLoading = true;
      const { data } = await all();
      this.stores = data;
      this.isLoading = false;
    },
    setPrintPrice(row, attr = '') {
      if (attr == 'print_price_all') {
        row.forEach(r => r.print_price = this.printPriceSet);
        this.printPriceSet = 0;
      }
    },
  }
}