<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left">
        <span class="text-2xl font-bold">{{ $t('Store: ') + $route.query.storeName }}</span>
        <span class="ml-12 text-2xl font-bold">{{ $t('Style: ') + style }}</span>
        <div>
          <span class="mr-5">
            {{ $t('Active') }}
          </span>
          <el-switch v-model="status" />
        </div>
      </div>
      <div class="top-head-right">
        <el-button
          type="primary"
          :disabled="isLoading"
          @click="handleSave"
        >
          {{ $t("Save") }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <div class="mt-2" v-if="style">
        <Selection
          label="Color"
          :list="productColors"
          :selectedList="selectedProductColors"
          @on-selected-all="handleSelectAllColor"
          @on-clear="handleClearColor"
        />
        <Selection
          label="Size"
          :list="productSizes"
          :selectedList="selectedProductSizes"
          @on-selected-all="handleSelectAllSize"
          @on-clear="handleClearSize"
        />
        <Selection
          label="Print Area"
          :isObj="true"
          :list="printAreas"
          v-if="printAreas.length"
          :selectedList="selectedPrintAreas"
          @on-selected-all="handleSelectAllPrintArea"
          @on-clear="handleClearPrintArea"
        />
        <TableData 
          :listProduct="listProduct"
          v-if="listProduct.length && getAllProducts.length"
          @setPrice="setPrice"
          :selectedProductColors="selectedProductColors"
          :selectedProductSizes="selectedProductSizes"
          :selectedPrintAreas="selectedPrintAreas"
          :printAreas="printAreas"
          :errorsShow="errorsShow"
        />
      </div>
    </div>
  </div>
</template>
