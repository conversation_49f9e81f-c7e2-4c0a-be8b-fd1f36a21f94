import Selection from "@/modules/store-product/components/Selection.vue";
import { getProduct, update } from '@/api/storeProduct'
import TableData from "@/modules/store-product/components/TableData.vue";
import storeProductMixins from '../../mixins/storeProductMixins';

export default {
  name: "StoreProductEdit",
  components: {
    Selection,
    TableData
  },
  emits: ['on-click-list'],
  data() {
    return {
      storeName: null,
      listProduct: [],
    }
  },
  mixins: [storeProductMixins],
  watch: {
    selectedProductSizes: {
      handler() {
        this.setListProduct();
      },
      deep: true
    },
    selectedProductColors: {
      handler() {
        this.setListProduct();
      },
      deep: true
    },
    selectedPrintAreas: {
      handler() {
        this.setListProduct();
      },
      deep: true
    },
    getAllProducts: {
      handler() {
        this.setListProduct();
      },
      deep: true
    },
  },
  created() {
    this.status = this.$route.query.status == 1 ? true : false;
    this.style = this.$route.query.style;
    
    this.fetchAllProduct();
    this.getProductList();
    this.fetchProductAttribute();
    this.fetchPrintArea();
  },
  methods: {
    setListProduct() {
      const result = []
      for (const size of this.selectedProductSizes) {
        for (const color of this.selectedProductColors) {
          const product = this.findProduct(this.style, color, size);
          const productSample = this.findProductInList(product.id);
          let element = {
            id: product.id,
            sku: product.sku,
            name: product.name,
            style: this.style,
            size: size,
            color: color,
            price: parseFloat(productSample.price) || null,
            handling_fee: parseFloat(productSample.handling_fee) || null,
            printAreas: this.selectedPrintAreas.map(area => ({
              id: area,
              print_price: parseFloat(this.findProductInList(product.id, area).print_price) || null,
              name: this.getPrintArea(area, 'name')
            }))
          }
          result.push(element);
        }
      }
      this.listProduct = result
    },
    setSelectedAttr() {
      for (const pr of this.products) {
        if (pr.product) {
          if(!this.selectedProductSizes.includes(pr.product.size)) {
            this.selectedProductSizes.push(pr.product.size)
          }
          if(!this.selectedProductColors.includes(pr.product.color)) {
            this.selectedProductColors.push(pr.product.color)
          }
          if(pr.product_print_area_id && !this.selectedPrintAreas.includes(pr.product_print_area_id)) {
            this.selectedPrintAreas.push(pr.product_print_area_id)
          }
        }
      }
    },
    setPrice(row, attr = '', index = '') {
      if (attr == 'price') {
        this.products.forEach(pr => {
          if (pr.product_id == row.id) {
            pr.price = row.price
          }
        })
      }
      if (attr == 'handling_fee') {
        this.products.forEach(pr => {
          if (pr.product_id == row.id) {
            pr.handling_fee = row.handling_fee
          }
        })
      }
      if (attr == 'print_price') {
        this.products.forEach(pr => {
          if (pr.product_id == row.id && pr.product_print_area_id == row.printAreas[index].id) {
            pr.print_price = row.printAreas[index].print_price
          }
        })
      }
      if (attr == 'print_price_all') {
        this.products.forEach(pr => {
          if (pr.product_id == row.id) {
            pr.print_price = this.printPriceSet;
          }
        })
        this.setPrintPrice(row.printAreas);
      }
    },
    
    findProductInList(id, printId = '') {    
      let product;
      if (printId == '') {
        product = this.products.find(product => id == product.product_id)
      } else {
        product = this.products.find(product => id == product.product_id && product.product_print_area_id == printId)
      }
      if (!!product) {
        return product
      }
      return {};
    },
    async handleSave() {
      this.errorsShow = {}
      let data = {
        store_id: this.$route.query.store,
        product: this.dataSubmit(),
        style: this.style,
        status: this.status,
      }
      if(!data.product.length) {
        const message = this.$t('Please select size, color, print area and setup price to active.');
        this.notification(message, "error");
        this.status = false;
        return;
      }
      this.isLoading = true;
      try {
        await update(data)
        const message = this.$t('The store product were updated successful!');
        this.notification(message, "success");
      } catch (e) {
        this.isLoading = false;
        const message = this.$t('Update fail!');
        this.notification(message, "error");
        for (const error in e.response.data.errors) {
          let index = error.replace(/[^0-9]/g, '');
          let attr = error.replace(/.+\d+./g, '_');
          this.errorsShow['error_' + index + attr] = e.response.data.errors[error][0].replace(/.+\d+./g, " ");
        }
      }
      this.isLoading = false;
    },
    async fetchAllProduct() {
      this.isLoading = true;
      const query = {
        store: this.$route.query.store,
        style: this.$route.query.style,
      }
      const { data } = await getProduct(query);
      this.products = data;
      this.isLoading = false;
      this.setSelectedAttr();
    },
  }
}