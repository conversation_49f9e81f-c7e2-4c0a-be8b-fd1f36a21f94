<script src="./Script.js"></script>
<template>
  <div v-loading="isLoading" v-if="$route.query.name && $route.query.store && [0,2].includes(screenUpload)">
    <div class="top-head">
      <div class="top-head-left">
        <div v-if="$route.query.name">
          <h1 class="mb-2">{{ $t('Pricing') }} / {{ $route.query.name }}</h1>
        </div>
        <h1 v-else>{{ $t("Store products") }}</h1>
      </div>
      <div class="top-head-right">
        <el-button v-if="screenUpload == 2" type="primary" @click="screenUpload = 0">{{
            $t('Pricing setting')
          }}
        </el-button>
        <el-button v-else size="medium" type="primary" @click="clickImportClone">
          {{ $t('Import Pricing') }}
        </el-button>
        <el-button size="medium" type="primary" @click="clickExport">
          {{ $t('Export template') }}
        </el-button>
        <el-button size="medium" type="primary" @click="clickImport">
          {{ $t('Upload Csv') }}
        </el-button>
      </div>
    </div>
    <el-tabs v-if="screenUpload == 0" v-model="tabPricing">
      <el-tab-pane label="Product Pricing" name="product">
        <div class="table-content border">
          <el-table
            stripe
            size="small"
            :data="products"
            style="width: 100%"
            :max-height="maxHeight"
            element-loading-text="Loading..."
          >
            <el-table-column prop="style" :label="$t('Style')">
              <template #default="scope">
                {{ scope.row.style }}
              </template>
            </el-table-column>
            <el-table-column prop="product_name" :label="$t('Product Name')">
              <template #default="scope">
                {{ scope.row.name }}
              </template>
            </el-table-column>
            <el-table-column prop="product_name" :label="$t('Product SKU')">
              <template #default="scope">
                {{ scope.row.sku }}
              </template>
            </el-table-column>
            <el-table-column prop="brand" :label="$t('Brand')">
              <template #default="scope">
                {{ scope.row.brand?.name }}
              </template>
            </el-table-column>
            <el-table-column prop="status" :label="$t('Status')">
              <template #default="scope">
                <el-button type="success" size="small" round v-if="!!scope.row.status">{{ $t('Active') }}</el-button>
                <el-button type="danger" size="small" round v-else>{{ $t('Inactive') }}</el-button>
              </template>
            </el-table-column>
            <el-table-column prop="action" :label="$t('Action')">
              <template #default="scope">
                <el-button type="primary" size="small" @click="editStoreProduct(scope.row)">
                  {{ $t('Edit') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <ShowSku
          :data="skuData"
          @on-close-popup="handleClosePopup"
        />
      </el-tab-pane>
      <el-tab-pane label="Shipping Pricing" name="shipping">
        <div class="table-content border">
          <el-table
            stripe
            size="small"
            :data="shipments"
            style="width: 100%"
            :max-height="maxHeight"
            element-loading-text="Loading..."
          >
            <el-table-column prop="style" :label="$t('Type')">
              <template #default="scope">
                {{ scope.row.type }}
              </template>
            </el-table-column>
            <el-table-column :label="$t('Status')">
              <template #default="scope">
                <el-tag
                  :type="scope.row.status ? 'success' : 'error'"
                  class="rounded-xl"
                  effect="dark"
                  round
                  size="small"
                >{{ scope.row.status ? $t('Active') : $t('Inactive') }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="shipping_type" :label="$t('Shipment Type')">
              <template #default="scope">
                <p v-for="shipType in scope.row.shipping_type">{{ shipType }}</p>
              </template>
            </el-table-column>
            <el-table-column prop="action" :label="$t('Action')">
              <template #default="scope">
                <el-button type="primary" size="small" @click="showPopupShipment(scope.row)">
                  {{ $t('Edit') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <ShowShipment :detailShipment="detailShipment"
                      @on-close-popup="handleClosePopup"
        />
      </el-tab-pane>
      <el-tab-pane label="Surcharge Fee" name="surcharge">
        <div class="table-content border">
          <el-table
              stripe
              size="small"
              :data="surchargeFees"
              style="width: 100%"
              :max-height="maxHeight"
              element-loading-text="Loading..."
          >
            <el-table-column prop="type" :label="$t('Type')" width="150">
              <template #default="scope">
                {{ scope.row.name }}
              </template>
            </el-table-column>
            <el-table-column prop="api_value" :label="$t('API value')"  width="150">
              <template #default="scope">
                {{ scope.row.api_value }}
              </template>
            </el-table-column>
            <el-table-column prop="per" :label="$t('Per')" width="150">
              <template #default="scope">
                {{ scope.row.per }}
              </template>
            </el-table-column>
            <el-table-column prop="product_type" :label="$t('Apply for')">
              <template #default="scope">
                {{ scope.row.description }}
              </template>
            </el-table-column>
            <el-table-column :label="$t('Amount')"  width="300">
              <template #default="scope">
                <div v-if="typeEdit !== scope.row.name" class="flex items-center">
                  <span class="self-center" v-if="scope.row.value !== null">
                    ${{ formatNumber(scope.row.value) }}
                  </span>
                  <span class="self-center" v-else>N/A</span>
                  <el-link
                      class="el-link-edit"
                      :underline="false"
                      type="primary"
                      @click="selectSurcharge(scope.row)"
                  ><icon class=" ml-1 mb-1" :data="iconEdit" /></el-link>


                </div>
                <div v-else class="flex items-center">
                  <el-input class="!w-[100px]"
                            v-model="surchargeSelected.value"
                  />
                  <el-button :disabled="isLoading" class="items-center ml-2" type="primary" @click="updateSurcharge()">
                    {{ $t('Save') }}
                  </el-button>
                  <el-button @click="cancel(scope.row)">
                    {{ $t('Cancel') }}
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <ShowShipment :detailShipment="detailShipment"
                      @on-close-popup="handleClosePopup"
        />
      </el-tab-pane>

      <el-tab-pane label="Promotions" name="promotion">
        <div class="table-content border">
          <el-table
              stripe
              size="small"
              :data="promotions"
              style="width: 100%"
              :max-height="maxHeight"
              element-loading-text="Loading..."
          >
            <el-table-column prop="id" :label="$t('ID')" width="100">
              <template #default="scope">
                {{ scope.row.id }}
              </template>
            </el-table-column>
            <el-table-column prop="promotion_type" :label="$t('Promotion Type')"  width="150">
              <template #default="scope">
                <el-link type="primary" :underline="false" @click="showDetail(scope.row)">{{ scope.row.promotion_type?.name }}</el-link>

              </template>
            </el-table-column>
            <el-table-column prop="amount" :label="$t('Amount')" width="150">
              <template #default="scope">
                $ {{ formatNumberFloat(scope.row.detail.amount, 2, true) }}
              </template>
            </el-table-column>
            <el-table-column prop="start_date" :label="$t('Start Date')">
              <template #default="scope">
                {{ convertTime(scope.row.start_time) }}
              </template>
            </el-table-column>
            <el-table-column prop="end_date" :label="$t('End Date')">
              <template #default="scope">
                {{ scope.row.end_time ? convertTime(scope.row.end_time) : '' }}
              </template>
            </el-table-column>
            <el-table-column prop="status" :label="$t('Status')">
              <template #default="scope">
                <div v-if="generateStatus(scope.row) != 'Active'" :class="[generateBG(generateStatus(scope.row))]" class="rounded-xl w-[90px] px-2 text-center text-white" size="small" round>{{ generateStatus(scope.row) }}</div>
                <div v-else class="rounded-xl border w-[150px] flex justify-around cursor-pointer">
                  <span class="w-1/2 text-center"
                  :class="[generateStatus(scope.row) == 'Active' ? 'border-r rounded-xl bg-[#2C88DB] text-white' : '']"
                        @click="openPopupConfirm(scope.row, 'Active')"
                  >{{ generateStatus(scope.row) }}</span>
                  <span class="w-1/2 text-center"
                        @click="openPopupConfirm(scope.row, 'Inactive')"
                        :class="[generateStatus(scope.row) == 'Inactive' ? 'border-l rounded-xl bg-[#d3455c] text-white' : '']"
                  >Inactive</span>
                </div>

              </template>
            </el-table-column>
            <el-table-column :label="$t('Action')"  width="100">
              <template #default="scope">
                <div class="flex items-center">
                  <el-link
                      :disabled="scope.row.is_public"
                      class="el-link-edit"
                      :underline="false"
                      type="primary"
                      @click="updatePromotion(scope.row)"
                  ><icon class=" ml-1 mb-1" :data="iconEdit" /></el-link>


                </div>
              </template>
            </el-table-column>
          </el-table>

        </div>
        <div class="flex justify-center mt-4">
          <el-button type="primary" @click="addPromotion()">
            <icon :data="iconAdd" />
            {{ $t('Add') }}</el-button>
        </div>
      </el-tab-pane>
    </el-tabs>
    <Export :storeId="store" v-else/>

  </div>
  <div v-else-if="screenUpload == 1">
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ tab == 'import' ? $t('Import CSV') : $t('History') }}</h1>
      </div>
      <div class="top-head-right">
        <el-button type="primary" @click="screenUpload = 0">{{ $t('Pricing setting') }}</el-button>
      </div>
    </div>
    <el-tabs v-model="tab">
      <el-tab-pane label="Import" name="import">
        <Import :storeId="store" :employeeId="employeeID"></Import>
      </el-tab-pane>
      <el-tab-pane label="History" name="history">
        <History :storeId="store"/>
      </el-tab-pane>
    </el-tabs>
  </div>
  <div v-else-if="screenUpload == 3">
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ tab == 'import' ? $t('Import Pricing') : $t('History') }}</h1>
      </div>
      <div class="top-head-right">
        <el-button type="primary" @click="screenUpload = 0">{{ $t('Pricing setting') }}</el-button>
      </div>
    </div>
    <el-tabs v-model="tab">
      <el-tab-pane label="Import" name="import">
        <ClonePricing :storeId="store" :employeeId="employeeID"></ClonePricing>
      </el-tab-pane>
      <el-tab-pane label="History" name="history">
        <HistoryClone :storeId="store"/>
      </el-tab-pane>
    </el-tabs>
  </div>
  <AdditionalPrintAreaDetail/>

  <div>
    <el-dialog
        v-model="dialogConfirm"
        @close="reset"
        :show-close="false"
        width="350px"
    >
      <div class="mb-4 break-words text-[20px] !mt-[-40px] text-center flex">
        <icon class="!text-red-700 mb-1 !w-[40px] !h-[40px] mr-2" :data="iconError2"></icon>

        <p class="text-center font-bold">
          {{ $t('Are you sure to deactivate an active promotion?') }}
        </p>
      </div>
      <div class="break-words mb-[-20px] italic font-medium
">
        <p>

          {{ $t('Note: Deactivated promotions cannot be reactivated. Please create a new one if needed.') }}
        </p>
      </div>
      <template #footer>
        <div class="dialog-footer flex justify-center">
          <el-button type="info" @click="dialogConfirm = false">No</el-button>
          <el-button type="primary" @click="selectStatus()">
            Yes
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialogPromotion"
        :title="titlePromotion"
        @close="reset"
        width="600px"
    >
      <el-form :model="promotion"
               :rules="promotionRules"
               ref="formData"

               label-width="auto"
               style="max-width: 600px">
        <el-form-item label="Promotion Type" prop="promotion_type_id">
          <el-select
              v-model="promotion.promotion_type_id"
              placeholder="Select Promotion Type"
              style="width: 240px"
              filterable
          >
            <el-option
                v-for="item in promotionTypes"
                :key="item.id"
                :label="item.name"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <AdditionalPrintArea
            :promotion="promotion"
            :productTypes="productTypes"
            :printAreas="printAreas"
        />
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="reset">Cancel</el-button>
          <el-button @click="onSubmit('draft')" :disabled="isLoading">Draft</el-button>
          <el-button type="primary" :disabled="isLoading" @click="onSubmit('public')">
            Publish
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
