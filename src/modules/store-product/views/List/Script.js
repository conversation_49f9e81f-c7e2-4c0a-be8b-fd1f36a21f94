import {list} from '@/api/storeProduct'
import {getList} from '@/api/productPrintSide'
import { fetchAll } from '@/api/productType'
import {getTypes, create, update, fetch as fetchPromotion, detail, updateStatus} from '@/api/promotion'
import EditStoreProduct from "@/modules/store-product/views/Edit/Index.vue";
import ShowColor from '@/modules/store-product/components/ShowColor.vue';
import ShowSku from '@/modules/store-product/components/ShowSku.vue';
import Import from '@/modules/store-product/components/Import.vue';
import ClonePricing from '@/modules/store-product/components/ClonePricing.vue';
import AdditionalPrintArea from '@/modules/store-product/components/AdditionalPrintArea.vue';
import AdditionalPrintAreaDetail from '@/modules/store-product/components/AdditionalPrintAreaDetail.vue';
import IncrementTimer from "@/components/IncrementTimer.vue";
import {fetch} from "@/api/storeShipment";
import { getSurchargeFees, createOrUpdate } from "@/api/storeSurcharge";
import ShowShipment from "@/modules/store-product/components/ShowShipment.vue";
import History from "@/modules/store-product/components/History.vue";
import HistoryClone from "@/modules/store-product/components/HistoryClone.vue";
import Export from '@/modules/store-product/components/Export.vue';
import formatNumberMixin from "@/mixins/formatNumber.js";
import dateMixin from "@/mixins/date.js";
import moment from "moment-timezone";
import EventBus from "@/utilities/eventBus";

export default {
  name: "StoreProductList",
    mixins: [formatNumberMixin, dateMixin],
  data() {
    return {
      tab: 'import',
      isLoading: false,
      stores: [],
      store: null,
      products: [],
      employee: {},
      codeEmployee: '',
      employeeError: '',
      skuData: {},
      employeeID: '',
      tabPricing: 'product',
      jobType: 'import_pricing',
      screenUpload: 0,
      shipments: [],
      surchargeFees: [],
      typeEdit: null,
      detailShipment: {},
      screenExport: false,
      styleSearch: null,
      surchargeSelected: null,
      surchargeOriginSelected: null,
      surchargeFeeOptions : [],
      promotions: [],
      promotionTypes: [],
      promotion: {},
      dialogPromotion: false,
      titlePromotion: 'Add Promotion',
      promotionRules: {
        promotion_type_id: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: ['blur'],
          },
        ],
        amount: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: ['blur', 'change'],

          },
          {
            type: 'number',
            min: 0.01,
            message: this.$t('Value must be greater than 0'),
            trigger: ['blur', 'change'],
          },
        ],
        start_time: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: ['blur'],
          },
          { validator: this.validStartDate, trigger: 'change' },
        ],
        end_time: [
          {
            required: false,
            message: this.$t('This field cannot be left blank.'),
            trigger: ['blur', 'change'],
          },
          { validator: this.validateEndDate, trigger: 'change' },

        ],

      },
      productTypes: [],
      printAreas: [],
      errors: [],
      dialogConfirm: false,
      itemSelected: null,
      statusOrder: {
        'Active': 0,
        'Published': 1,
        'Draft': 2,
        'Inactive': 3
      }

    }
  },
  components: {
    EditStoreProduct,
    ShowColor,
    ShowSku,
    Import,
    IncrementTimer,
    ShowShipment,
    History,
    Export,
    ClonePricing,
    HistoryClone,
    AdditionalPrintArea,
    AdditionalPrintAreaDetail
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 230);
    }
  },
  mounted() {
    if (this.$route.query.store) {
      this.store = this.$route.query.store;
      this.fetchData();
    }
    this.promotion.store_id = this.store;
    this.getPromotionTypes();
    this.getPrintAreas();
    this.getProductTypes();
  },
  methods: {
    validateEndDate(rule, value, callback) {
      if (value && this.promotion.start_time && new Date(value) <= new Date(this.promotion.start_time)) {
        callback(new Error('End date cannot be earlier than start date'));
      } else {
        callback();
      }
    },
    validStartDate(rule, value, callback) {
      if (value && value <= moment().tz('America/Los_Angeles').format('YYYY-MM-DD HH:mm:ss')) {
        callback(new Error('Start date cannot be earlier than now'));
      } else {
        callback();
      }
    },
    async openPopupConfirm(item, status = null) {
      if (item.is_active == 1 && status == 'Inactive') {
        this.dialogConfirm = true;
        this.itemSelected = item
      }
    },

    async selectStatus() {
      try {
        this.isLoading = true;
          const res = await updateStatus(this.itemSelected.id, {is_active: 0})
          this.notification((res?.output?.message ?? 'Success'), "success");
          const response = await fetchPromotion({store_id: this.store})
          this.promotions = this.sortPromotions(response.data ?? []);
          this.dialogConfirm = false;
          this.itemSelected = null;
      } catch (e) {
        this.notification(e.message ?? 'Error', "error");
      }
      this.isLoading = false;

    },
    generateStatus(item) {
      if (!item.is_public) {
        return 'Draft';
      } else if (moment.tz(item.start_time, 'America/Los_Angeles').isAfter(moment.tz('America/Los_Angeles'))) {
        return 'Published';
      } else if (!item.end_time || moment.tz(item.end_time, 'America/Los_Angeles').isAfter(moment.tz('America/Los_Angeles'))) {
        return 'Active';
      }
      return 'Inactive';
    },
    generateBG(status) {
      switch (status) {
        case 'Draft':
          return 'bg-[#897a5f]';
        case 'Published':
          return 'bg-[#1cad9f]';
        case 'Inactive':
          return 'bg-[#d3455c]';
        case 'Active':
          return 'bg-[#2d88da]';
      }
    },
    showDetail(item) {
      EventBus.$emit('showPromotionDetail', item);
    },
    addPromotion() {
      this.dialogPromotion = true;
      this.titlePromotion = 'Add Promotion'
    },
    async updatePromotion(item) {
      try {
        this.isLoading = true;
        const res = await detail(item.id);
        this.promotion = res.data;
        this.promotion.amount = res.data.detail.amount
        this.promotion.product_types =  res.data.detail.product_types ?? [];
        this.promotion.print_areas =  res.data.detail.print_areas ?? [];
        this.promotion.start_date = moment.tz(this.promotion.start_time, 'America/Los_Angeles').format('YYYY-MM-DD');
        this.promotion.start_hour = moment.tz(this.promotion.start_time, 'America/Los_Angeles').format('HH:mm:ss');
        this.promotion.end_date = this.promotion.end_time ? moment.tz(this.promotion.end_time, 'America/Los_Angeles').format('YYYY-MM-DD') : '';
        this.promotion.end_hour = this.promotion.end_time ? moment.tz(this.promotion.end_time, 'America/Los_Angeles').format('HH:mm:ss') : '';
        this.dialogPromotion = true;
        this.titlePromotion = 'Update Promotion'
      } catch (e) {
        this.notification(e.message ?? 'Error', "error");
      }
      this.isLoading = false;

    },
    async getPromotionTypes() {
      const res = await getTypes({});
      this.promotionTypes = res.data;
    },
    async getPrintAreas() {
      const res = await getList();
      this.printAreas = res.data;
    },
    async getProductTypes() {
      const res = await fetchAll({});
      this.productTypes = res.data;
    },
    async onSubmit(type) {
      const isValid = await this.$refs.formData.validate();
      if (!isValid) return;
      this.promotion.store_id = this.store
      try {
        this.isLoading = true
        this.promotion.is_public = type == 'public' ? 1 : 0;

        if (this.promotion.id) {
          var res = await update(this.promotion.id, this.promotion)

        } else {
          var res = await create(this.promotion)
        }
        this.notification(res.data.message || "Success", "success");
        this.reset();
        this.fetchData();

      } catch (e) {
        this.notification(e.response.data.message || "Error", "error");

        this.errors = e.response.data.errors
      }
      this.isLoading = false;

    },
    reset() {
      this.dialogPromotion = false;
      this.dialogConfirm = false;
      this.itemSelected = null;
      this.promotion = {}
      this.errors = null;
      this?.$refs?.formData?.clearValidate();
    },
    editStoreProduct(item) {
      this.skuData = {
        style: item.style,
        store: this.store,
        storeName: this.$route.query.name,
        status: !!item.status,
        showDialog: true,
      };
    },
    selectSurcharge(item) {
      this.typeEdit = item.name;
      this.surchargeSelected = item;
      this.surchargeOriginSelected = JSON.parse(JSON.stringify(item));

    },
    resetData(){
      this.surchargeSelected.value = this.surchargeOriginSelected.value;
    },
    async updateSurcharge() {
      this.isLoading = true;
      try {
        const params = {
          store_id: this.store,
          type: this.surchargeSelected.name,
          value: this.surchargeSelected.value,
          product_type: this.surchargeSelected.product_type,
        }
        await createOrUpdate(params);
        this.notification("Update success", "success");
        this.typeEdit = null;
      } catch (e) {
        this.resetData();
        this.notification(e.response?.data?.errors?.value[0] ?? 'The given data in valid.', "error");
      } finally {
        this.isLoading = false;
      }
    },
    cancel() {
      this.typeEdit = null
      this.resetData();
    },
    async fetchSurcharge() {
      getSurchargeFees({store_id: this.store})
    },
    generateType(inputText) {
      const words = inputText?.split('_');

      const convertedWords = words?.map(word => word.charAt(0).toUpperCase() + word.slice(1));

      const resultText = convertedWords?.join(' ');

      return resultText;
    },
    showPopupShipment(item) {
      this.detailShipment = {...item, storeName: this.$route.query.name};
    },
    async fetchData() {
      this.isLoading = true;
      const result = await Promise.all([
        list({store_id: this.store}),
        fetch(this.store),
        getSurchargeFees({store_id: this.store}),
        fetchPromotion({store_id: this.store})
      ]);
      this.products = result[0].data;
      this.shipments = result[1].data?.data;
      this.surchargeFees = result[2].data;
      this.promotions = result[3].data;
      this.promotions = this.sortPromotions(this.promotions);
      this.isLoading = false;
    },
    sortPromotions(promotions) {
      return promotions.map(item => ({
        ...item,
        status: this.generateStatus(item)
      }))
          .sort((a, b) => {
            const aStatus = a.status;
            const bStatus = b.status;

            if (this.statusOrder[aStatus] !== this.statusOrder[bStatus]) {
              return this.statusOrder[aStatus] - this.statusOrder[bStatus];
            }

            if (aStatus === 'Active') {
              return moment.tz(b.start_time, 'America/Los_Angeles').valueOf() - moment.tz(a.start_time, 'America/Los_Angeles').valueOf();
            }

            if (aStatus === 'Published') {
              return moment.tz(a.start_time, 'America/Los_Angeles').valueOf() - moment.tz(b.start_time, 'America/Los_Angeles').valueOf();
            }

            if (aStatus === 'Draft') {
              return moment.tz(b.start_time, 'America/Los_Angeles').valueOf() - moment.tz(a.start_time, 'America/Los_Angeles').valueOf();
            }

            if (aStatus === 'Inactive') {
              return moment.tz(b.end_time, 'America/Los_Angeles').valueOf() - moment.tz(a.end_time, 'America/Los_Angeles').valueOf();
            }

            return 0;
          });
    },

    handleClosePopup() {
      this.skuData = {};
      this.fetchData();
    },

    async clickImport() {
      this.screenUpload = 1;
    },
    async clickExport() {
      this.screenUpload = 2;
    },
    async clickImportClone() {
      this.screenUpload = 3;
    },
  }
}