import { list } from '@/api/store.js';
import { list as listAccount } from '@/api/account';
import { list as listClients, all as allClients } from '@/api/client';
import { equals } from 'ramda';
import EventBus from '@/utilities/eventBus';
import StoreCreate from '@/modules/store/views/Create/Index.vue';
import StoreEdit from '@/modules/store/views/Edit/Index.vue';
import ClientTable from '@/modules/store/views/components/ClientTable.vue';
import StoreTable from '../components/StoreTable.vue';
import { makeid } from '@/utilities/helper.js';
import { listAll } from '@/api/user.js';

export default {
  name: 'Store',
  components: {
  StoreCreate,
    StoreEdit,
    StoreTable,
    ClientTable
  },
  mixins: [],
  data() {
    return {
      isLoading: false,
      filter: this.setDefaultFilter(),
      filter_client: this.setDefaultFilterClient(),
      items: [],
      accounts: [],
      clients: [],
      userHasRoleSaleTeams: [],
      activeTab: "store",
      storeKey: makeid(10),
      clientKey: makeid(10),
      storeStatus: [
        {
          label: 'Active',
          value: '1',
        },
        {
          label: 'Inactive',
          value: '0',
        },
      ]
    };
  },
  computed: {
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },
    maxHeight() {
      return parseInt(window.innerHeight - 230);
    },
  },
  mounted() {
    this.fetchData();
    this.fetchAccount();
    this.fetchAllClients();
    this.fetchUserHasRoleSaleTeam();
  },
  methods: {
    setDefaultFilter() {
      return {
        name: '',
        sort_column: '',
        sort_by: '',
        limit: 25,
        page: 1,
        without_pagination: 0,
      };
    },
    setDefaultFilterClient() {
      return {
        name: '',
        limit: 25,
        page: 1,
        without_pagination: 0,
      };
    },
    createStore() {
      EventBus.$emit('showCreateStore');
    },
    async fetchData() {
      this.filter = this.getRouteParam();
      await this.fetchListStore();
    },
    async fetchAllClients() {
      this.isLoading = true;
      const res = await allClients();
      this.isLoading = false;
      const data = res.data || [];
      this.clients = data;
    },
    async fetchListClients() {
      this.isLoading = true;
      const filter = this.filter_client;
      const res = await listClients(filter);
      this.isLoading = false;
      const data = res.data || [];
      this.clients = data.data || [];
      this.total_client = data.total || 0;
    },
    triggerTabChange() {
      if (this.activeTab === "store") {
        this.fetchData();
      } else {
        this.fetchListClients();
      }
    },
    async fetchListStore() {
      this.isLoading = true;
      this.setRouteParam();
      const res = await list(this.filter);
      this.isLoading = false;
      const data = res.data || [];
      this.total = data.total;
      this.items = data.data;
    },
    async fetchAccount() {
      this.isLoading = true;
      const res = await listAccount();
      this.isLoading = false;
      const data = res.data || [];
      this.accounts = data.data;
    },
    async fetchUserHasRoleSaleTeam() {
      const res =  await listAll();
      this.userHasRoleSaleTeams = res?.data?.filter((item) => {
        return item?.roles?.some((role) => role?.name == 'Sales Team');
      });
    },
    pricingProduct(row) {
      this.$router.push({
        name: 'store_product',
        query: { store: row.id, name: row.name },
      });
    },
    setRouteParam() {
      const params = this.filter;
      this.$router.replace({ name: 'store_list', query: params });
    },
    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);
      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }
      filter.page = +filter.page || 1;
      return filter;
    },
    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);
      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }
      filter.page = +filter.page || 1;
      return filter;
    },
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchListStore();
      });
    },
    onFilter(filter) {
      this.filter = filter;
      this.$nextTick(() => {
        this.fetchListStore();
        // if (item) {
        //   this.$refs[item].handleClose();
        // }
      });
    },
    onFilterClient(filter) {
      this.filter_client = filter;
      this.$nextTick(() => {
        this.fetchListClients();
        // if (item) {
        //   this.$refs[item].handleClose();
        // }
      });
    },
   
    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.$nextTick(() => {
        this.fetchListStore();
      });
    },
    editStore(item) {
      EventBus.$emit('showEditStore', item);
    },
    getStatus(dataStatus) {
      let status = '';
      switch (dataStatus) {
        case 1:
        case 2:
        case 3:
        case 4:
          status = 'Active';
          break;
        case 0:
          status = 'Inactive';
          break;
      }
      return status;
    },
    tableCellClassName(data) {
      if (['store_status', 'auto_shipment'].includes(data?.column?.property)) {
        return 'text-center';
      }
    },
    headerCellClassName(data) {
      if (['store_status', 'auto_shipment'].includes(data?.column?.property)) {
        return 'text-center';
      }
    },
  },
};
