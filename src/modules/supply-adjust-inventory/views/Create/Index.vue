<script src="./Script.js"></script>
<template>
  <el-dialog
      v-model="dialogVisible"
      :title="$t('Create Adjust Supply')"
      custom-class="el-dialog-custom"
      @close="closeModal"
      width="40%"
      :destroy-on-close="true"
  >
    <el-form
        status-icon
        ref="createAdjustSupply"
        :model="data"
        :rules="dataRules"
        @submit.prevent="onSubmit()"
        :label-position="'top'"
        class="w-full"
    >
      <div class="bg-gray-50 p-3 border rounded mb-3">
        <el-form-item v-show="!Object.keys(employee).length" :error="employeeError" :label="$t('Employee ID')" required>
          <el-input v-model="data.employee_id" @keyup.enter="scanEmployeeID"></el-input>
        </el-form-item>
        <div v-if="Object.keys(employee).length" >
          <div class="flex justify-between">
            <b class="text-base">Hi {{ employee.name }}, Have a nice day!</b>
            <el-link type="danger" @click="resetEmployee" :underline="false">{{ $t('Logout') }}</el-link>
          </div>
          <div class="text-lg text-fuchsia-500" >
            <IncrementTimer/>
          </div>
        </div>
      </div>
      <div class="flex mb-2">
        <el-form-item :label="$t('Supply SKU or Name')" required class="w-full" prop="supply_id">
          <el-select
              :placeholder="$t('Select Supply Name')"
              class="w-full mr-2"
              filterable
              v-model="data.supply_id"
              @change="getSupplyQuantity"
          >
            <el-option
                v-for="item in supplies"
                :key="item.id"
                :label="item.sku + ' - ' + item.name"
                :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </div>
      <div class="flex mb-2">
        <el-form-item :label="$t('Quantity available')" class="w-full mr-2">
          <h3 class="text-center w-full mb-0">
            {{ availableQuantity }}
          </h3>
        </el-form-item>
        <el-form-item :label="$t('New quantity on hand')" prop="quantity" class="w-full mr-2">
          <el-input-number
              v-model="data.quantity"
              :precision="0"
              :step="1"
              :min="0"
              class="w-full"
          />
        </el-form-item>
        <el-form-item :label="$t('Quantity adjusted')" class="w-full">
          <h3 class="text-center w-full mb-0">
            {{ adjustQuantity }}
          </h3>
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="resetData">{{ $t('Reset') }}</el-button>
      <el-button
          type="primary"
          @click="onSubmit('createAdjustSupply')"
          :disabled="isLoading"
          :loading="isLoading"
      >{{ $t('Create') }}</el-button>
    </template>
  </el-dialog>
</template>
