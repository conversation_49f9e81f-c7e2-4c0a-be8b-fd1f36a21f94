import EventBus from '@/utilities/eventBus';
import {create, fetchListSupplies, getSupplyQuantity} from '@/api/supplyAdjustInventory';
import {
    employeeLogoutTimeChecking,
    employeeTimeChecking,
} from '@/api/employee.js';
import IncrementTimer from '@/components/IncrementTimer.vue';

export default {
    name: 'CreateSupplyAdjustInventory',
    props: ['employees', 'countries'],
    components: {
        IncrementTimer,
    },
    data() {
        return {
            dialogVisible: false,
            supplies: [],
            data: this.setDefaultData(),
            isLoading: false,
            dataRules: {
                quantity: [this.commonRule()],
                supply_id: [this.commonRule()],
            },
            availableQuantity: 0,
            employee: {},
            employeeError: '',
            job_type: 'supply_adjust_inventory',
            id_time_checking: null,
        };
    },
    watch: {},
    computed: {
        adjustQuantity() {
            return this.data.quantity - this.availableQuantity;
        },
    },
    created() {
        EventBus.$on('showCreateSuppliesAdjustInventory', () => {
            this.dialogVisible = true;
        });
    },
    mounted() {
        this.fetchSupplies();
    },
    methods: {
        async fetchSupplies() {
            const {data} = await fetchListSupplies({'without_pagination': true});
            this.supplies = data || [];
        },
        async resetEmployee() {
            const res = await employeeLogoutTimeChecking(this.id_time_checking);
            this.employee = {};
            this.employeeError = '';
            this.data.employee_id = '';
            this.id_time_checking = null;
        },
        async scanEmployeeID() {
            if (this.id_time_checking) {
                return true;
            }
            if (!this.data.employee_id) {
                this.employeeError = 'Employee ID field cannot be left blank.';
                return false;
            }
            const res = await employeeTimeChecking({
                code: Number(this.data.employee_id),
                job_type: this.job_type,
            });
            if (!res.data.data) {
                this.employeeError = "Can't find your employee ID, please scan again";
                return false;
            }
            this.employeeError = '';
            this.employee = res.data.data;
            this.id_time_checking = res.data.id_time_checking;
            this.$refs.productSku.focus();
            return true;
        },
        resetData() {
            const fieldsIgnore = ['employee_id'];
            const defaultData = this.setDefaultData();
            for (let property in defaultData) {
                if (fieldsIgnore.includes(property)) continue;
                this.data[property] = defaultData[property];
            }
            this.productSku = '';
            this.$refs.createAdjustSupply.resetFields();
            this.availableQuantity = 0;
        },
        commonRule() {
            return {
                required: true,
                message: this.$t('This field cannot be left blank.'),
                trigger: 'change',
            };
        },
        setDefaultData() {
            return {
                quantity: '',
                supply_id: '',
                employee_id: '',
            };
        },
        async getSupplyQuantity() {
            try {
                if (this.data.supply_id) {
                    const res = await getSupplyQuantity(this.data.supply_id);
                    this.availableQuantity = res.data.quantity ?? 0;
                }
            } catch (e) {
                const message = this.$t(
                    'An error occurred, please try again!'
                );
                this.notification(message, 'error');
            }
        },
        async onSubmit(formName) {
            if (!this.scanEmployeeID()) return;
            const isValid = await this.$refs[formName].validate();
            if (!isValid) {
                return;
            }
            if (Number(this.availableQuantity) === this.data.quantity) {
                this.notification(
                    this.$t('The quantity is matched, so no need to adjust!'),
                    'error'
                );
                return;
            }
            this.isLoading = true;
            this.data.id_time_checking = this.id_time_checking;
            this.data.employee_id = this.employee.id;
            try {
                const res = await create(this.data);
                this.dialogVisible = false;
                this.availableQuantity = this.data.quantity;
                this.notification(res.data.message);
                this.$emit('refresh');
            } catch (e) {
                const keyFirstData = Object.keys(e.response?.data)[0];
                const firstData = e.response?.data[keyFirstData];
                let message = firstData[0] || 'Adjust pulling shelves error!';
                this.notification(message, 'error');
            } finally {
                this.isLoading = false;
            }
        },
        closeModal() {
            this.resetData();
            this.resetEmployee();
            EventBus.$emit("fetchAdjustSupplyList");
        },
    },
};
