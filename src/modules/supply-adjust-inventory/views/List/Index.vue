<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head mb-4">
      <div class="top-head-left"><h1>{{ $t('Adjust Supply') }}</h1></div>
      <div class="top-head-right">
        <el-button @click="exportExcel" plain type="primary">
          <span class="icon-margin-right"><icon :data="iconExport" /></span>{{ $t('Export') }}
        </el-button>
        <el-button type="primary" @click="createAdjustInventory">
          <span class="icon-margin-right"><icon :data="iconAdd" /></span>{{ $t('Create') }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <div class="filter">
        <el-input
            :placeholder="$t('Scan SKU')"
            class="el-form-item-scan-sku max-w-fit mr-2"
            ref="sku"
            v-model="filter.sku"
        />
        <el-select
            :placeholder="$t('Select Supply Name')"
            class="mr-2"
            filterable
            v-model="filter.supply"
        >
          <el-option
              v-for="item in supplies"
              :key="item.id"
              :label="item.name"
              :value="item.id"
          >
          </el-option>
        </el-select>
        <div class="btn-filter">
          <template v-if="hasFilter">
            <el-link type="danger" @click="onClearFilter" :underline="false"
            >{{ $t('Clear') }}</el-link
            >
          </template>
          <el-button type="primary" @click="onFilter">
            <span class="icon-margin-right"><icon :data="iconFilter" /></span
            >{{ $t('Filter') }}
          </el-button>
        </div>
      </div>
      <el-table
          border
          stripe size="small" :data="items"
          :max-height="maxHeight"
          v-loading="isLoading"
          element-loading-text="Loading..."
      >

        <el-table-column :label="$t('Date')" min-width="200">
          <template #default="scope">
            {{ listViewDateFormat(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Supply Name')" min-width="200">
          <template #default="scope">
            {{ scope.row.supply.name }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('SKU')" min-width="100">
          <template #default="scope">
            {{ scope.row.supply.sku }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Available')" min-width="100">
          <template #default="scope">
            {{ scope.row.quantity_available }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('On Hand')" min-width="100">
          <template #default="scope">
            {{ scope.row.quantity_on_hand }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Adjust')" min-width="100">
          <template #default="scope">
            {{ scope.row.quantity_adjust }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('User')" min-width="150">
          <template #default="scope">
            {{ scope.row.user.username }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Employee')" min-width="100">
          <template #default="scope">
            {{ scope.row.employee.name }}
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom">
        <div class="total">{{$t('Total:')}} {{ items.length ? formatNumber(total) : 0 }}</div>
        <el-pagination
            :disabled="isLoading"
            background
            layout="prev, pager, next"
            :page-size="filter.limit"
            :total="total"
            @current-change="changePage"
            v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
              v-model="filter.limit"
              :placeholder="$t('Select')"
              size="mini"
              @change="onFilter"
          >
            <el-option
                v-for="item in limits"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
  </div>
  <create-supply-adjust-inventory @refresh="fetchAdjustList" :employees="employees"/>
</template>
