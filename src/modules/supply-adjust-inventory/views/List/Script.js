import {
  list,
  fetchListSupplies,
  exportAdjustSupplyExcel,
} from '@/api/supplyAdjustInventory';
import EventBus from "@/utilities/eventBus";
import CreateSupplyAdjustInventory from "@/modules/supply-adjust-inventory/views/Create/Index.vue";
import warehouseMixin from "@/mixins/warehouse";
import { API_URL } from "@/utilities/constants";
import { clone } from 'ramda';

export default {
    name: "SupplyAdjustInventory",
    mixins: [warehouseMixin],
    components: {
        CreateSupplyAdjustInventory
    },
    data() {
        return {
            items: [],
            isLoading: false,
            filter: this.setDefaultFilter(),
            supplies: [],
        }
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 230);
        },
        buildLinkDownload() {
            const link = `${API_URL}/supply-adjust/export`;
            let params = this.filter;
            delete params.page;
            delete params.limit;
            params.warehouse_id = this.userWarehouseId;
            params.export = true;
            if (this.filter.date && this.filter.date.length) {
                params.start_date = this.formatDate(this.filter.date[0], false);
                params.end_date = this.formatDate(this.filter.date[1], false);
            }
            params = new URLSearchParams(params);
            return `${link}?${params.toString()}`;
        },
    },
    mounted() {
        this.getData();
    },
    created() {
        EventBus.$on("fetchAdjustSupplyList", () => {
            this.getData();
        });
    },
    methods: {
        async exportExcel() {
            this.percentage = 0;

            const config = {
                responseType: 'arraybuffer',
                onDownloadProgress: (progressEvent) => {
                    this.percentage = parseInt(
                        Math.round((progressEvent.loaded / progressEvent.total) * 100)
                    );
                },
            };
            try {
                let params = clone(this.filter);
                delete params.page;
                delete params.limit;
                params.warehouse_id = this.userWarehouseId;
                params.export = true;
                if (this.filter.date && this.filter.date.length) {
                  params.start_date = this.formatDate(
                    this.filter.date[0],
                    false
                  );
                  params.end_date = this.formatDate(this.filter.date[1], false);
                }
                params = new URLSearchParams(params);

                const response = await exportAdjustSupplyExcel(params, config);

                var blob = new Blob([response.data], {
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                });
                var downloadElement = document.createElement('a');
                var href = window.URL.createObjectURL(blob);
                downloadElement.href = href;
                downloadElement.download = `adjust_supply.xlsx`;
                document.body.appendChild(downloadElement);
                downloadElement.click();
                document.body.removeChild(downloadElement);
                window.URL.revokeObjectURL(href);
            } catch (error) {
                this.notification('Export fail.', 'error');
            }
        },
        getData() {
            this.filter = this.getRouteParam();
            this.fetchSupplies();
            this.fetchAdjustList();
        },
        setDefaultFilter() {
            return {
                limit: 25,
                page: 1,
                sku: "",
                supply: "",
            };
        },
        async fetchSupplies() {
            const { data } = await fetchListSupplies({'without_pagination' : true});
            this.supplies = data || [];
        },
        async fetchAdjustList() {
            this.isLoading = true;
            this.setRouteParam('supply_adjust_inventory');
            const { data } = await list(this.filter);
            this.items = data.data;
            this.total = data.total;
            this.isLoading = false;
        },
        onClearFilter() {
            this.filter = this.setDefaultFilter();
            this.fetchAdjustList();
        },
        changePage(page) {
            this.filter.page = page;
            this.fetchAdjustList();
        },
        onFilter() {
            this.filter.page = 1;
            this.fetchAdjustList();
        },
        createAdjustInventory() {
            EventBus.$emit("showCreateSuppliesAdjustInventory");
        },
    }
}