import EventBus from "@/utilities/eventBus";
import { create } from "@/api/supplyCategory";

export default {
    name: "CreateSupplyCategory",
    data() {
        return {
            dialogVisible: false,
            data: this.setDefaultData(),
            serverErrors: [],
            isLoading: false
        }
    },
    created() {
        EventBus.$on("showCreateSupplyCategory", () => {
            this.data = this.setDefaultData();
            this.serverErrors = [];
            this.dialogVisible = true;
        });
    },
    methods: {
        isError(field) {
            return !!this.serverErrors[field];
        },
        getErrorMessage(field) {
            return this.serverErrors[field][0];
        },
        setDefaultData() {
            return {
                name: ""
            };
        },
        async onSubmit() {
            if (this.isLoading) return;
            this.isLoading = true;
            try {
                const res = await create(this.data);
                this.dialogVisible = false;
                this.notification(res.data.message);
                this.$emit("refresh");
            } catch (e) {
                this.serverErrors = e.response.data.errors;
                this.notification("Failed to create category", "error");
            } finally {
                this.isLoading = false;
            }
        },
    }
}
