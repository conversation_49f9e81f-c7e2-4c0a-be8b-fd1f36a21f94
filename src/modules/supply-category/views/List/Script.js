import { getList, destroy } from "@/api/supplyCategory";
import EventBus from "@/utilities/eventBus";
import CreateSupplyCategory from "@/modules/supply-category/views/Create/Index.vue";
import UpdateSupplyCategory from "@/modules/supply-category/views/Update/Index.vue";

export default {
    name: "SupplyUnitList",
    components: {
        CreateSupplyCategory,
        UpdateSupplyCategory
    },
    data() {
        return {
            items: [],
            isLoading: false,
            filter: this.setDefaultFilter(),
        }
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 230);
        }
    },
    mounted() {
        this.filter = this.getRouteParam();
        this.fetchDataSupplyCategory();
    },
    methods: {
        setDefaultFilter() {
            return {
                limit: 25,
                page: 1,
                name: ""
            };
        },
        async fetchDataSupplyCategory() {
            this.isLoading = true;
            this.setRouteParam();
            const { data } = await getList(this.filter);
            this.items = data.data;
            this.total = data.total;
            this.isLoading = false;
        },
        changePage(page) {
            this.filter.page = page;
            this.fetchDataSupplyCategory();
        },
        onFilter() {
            this.filter.page = 1;
            this.fetchDataSupplyCategory();
        },
        createSupplyUnit() {
            EventBus.$emit("showCreateSupplyCategory");
        },
        updateSupplyUnit(item) {
            EventBus.$emit("showUpdateSupplyCategory", item);
        },
        resetFilter(){
            this.filter = this.setDefaultFilter()
            this.fetchDataSupplyCategory();
        },
        async deleteSupplyUnit(item) {
            const res = await destroy(item.id);
            this.notification(res.data.message);
            this.fetchDataSupplyCategory();
        }
    }
}