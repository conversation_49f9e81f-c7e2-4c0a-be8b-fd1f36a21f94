import EventBus from "@/utilities/eventBus";
import {update} from "@/api/supplyCategory";

export default {
    name: "UpdateSupplyCategory",
    data() {
        return {
            supplyUnitId: null,
            dialogVisible: false,
            data: this.setDefaultData(),
            serverErrors: [],
            isLoading: false
        }
    },
    created() {
        EventBus.$on("showUpdateSupplyCategory", (item) => {
            this.supplyUnitId = item.id;
            this.data = Object.assign(this.data, item);
            this.serverErrors = [];
            this.dialogVisible = true;
        });
    },
    methods: {
        isError(field) {
            return !!this.serverErrors[field];
        },
        getErrorMessage(field) {
            return this.serverErrors[field][0];
        },
        setDefaultData() {
            return {
                name: "",
            };
        },
        async onSubmit() {
            if (this.isLoading) return;
            this.isLoading = true;
            try {
                const res = await update(this.supplyUnitId, this.data);
                this.dialogVisible = false;
                this.notification(res.data.message);
                this.$emit("refresh");
            } catch (e) {
                if (e.response.status == 422) {
                    this.serverErrors = e.response.data.errors;
                }
                this.notification("Failed to update category", "error");
            } finally {
                this.isLoading = false;
            }
        },
    }
}
