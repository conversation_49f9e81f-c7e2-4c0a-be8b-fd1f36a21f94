<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
        v-model="openDialogAddInventoryAddition"
        :title="$t('Create Supply Addition')"
        custom-class="el-dialog-custom el-dialog-supply-inventory-addition"
        @close="closeModal"
        destroy-on-close
        :close-on-click-modal="false"
        width="88%"
    >

      <template #default>
        <div class="add-inventory-addition flex-col">
          <el-form
              status-icon
              ref="addInventoryAddition"
              :model="data"
              :rules="dataRules"
              @submit.prevent="onSubmit(`addInventoryAddition`)"
              :label-position="'top'"
              height="65%"

          >
            <div class="bg-gray-50 p-3 border rounded mb-3">
              <el-form-item v-show="!Object.keys(employee).length" :error="employeeError" :label="$t('Employee ID')" required>
                <el-input class="el-form-manual-item-employee" v-model="employeeID" @keyup.enter="scanEmployeeID"></el-input>
              </el-form-item>
              <div v-if="Object.keys(employee).length">
                <div class="flex justify-between">
                  <b class="text-base">Hi {{ employee.name }}, Have a nice day!</b>
                  <el-link type="danger" @click="resetEmployee" :underline="false">{{ $t('Logout') }}</el-link>
                </div>
                <div class="text-lg text-fuchsia-500" >
                  <IncrementTimer/>
                </div>
              </div>
            </div>
            <el-form-item :label="$t('Vendor')">
              <el-select
                  ref="vendor"
                  v-model="data.vendor_id"
                  :placeholder="$t('Select vendor')"
                  @change="fetchPurchaseOrderByVendor"
                  class="el-form-manual-item-vendor"
                  :automatic-dropdown="true"
              >
                <el-option
                    v-for="item in getVendorsForMil"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="PO#" prop="po_number">
              <el-select
                  v-model="data.po_number"
                  filterable
                  reserve-keyword
                  :placeholder="$t('Please enter a keyword')"
                  :loading="isLoadingSearch"
                  :allow-create="purchaseOrders.length ? false : true"
                  @change="selectPurchaseOrder"
                  class="el-form-manual-item-po"
              >
                <el-option
                    v-for="item in purchaseOrders"
                    :key="item.id"
                    :label="getLabelPurchaseOrder(item)"
                    :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <div class="el-item-group two">
              <el-form-item :label="$t('SKU')" prop="sku">
                <el-input
                    v-model="data.sku"
                    class="el-form-manual-item-gtin"
                ></el-input>
              </el-form-item>
              <!-- (' + quantity + ') -->
              <el-form-item
                  :label="
                      quantity ? 'Quantity' : 'Quantity'
                    "
                  prop="quantity"
              >
                <el-input-number
                    v-model="data.quantity"
                    :precision="0"
                    :step="1"
                    :min="0"
                    class="el-form-manual-item-qty"
                    @keyup.enter="focusByElClass('el-form-manual-item-product')"
                />
              </el-form-item>
            </div>
            <el-form-item :label="$t('Supply')" prop="supply_id">
              <el-select
                  v-model="data.supply_id"
                  filterable
                  :placeholder="$t('Select Supply')"
                  @change="selectProduct"
                  class="el-form-manual-item-product"
              >
                <el-option
                    v-for="item in products"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                    :disabled="item.disabled"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('Box ID')" prop="box_id">
                <el-input class="el-form-manual-item-employee" v-model="data.box_id" @blur="trimBoxID"></el-input>
              </el-form-item>
            <el-form-item :label="$t('Location')" prop="location_id">
              <el-select
                  v-model="data.location_id"
                  filterable
                  :placeholder="$t('Select Location')"
                  class="el-form-manual-item-product"
              >
                <el-option
                    v-for="item in locationList"
                    :key="item.id"
                    :label="item.barcode"
                    :value="item.id"
                    :disabled="item.disabled"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div class="flex justify-end">
            <el-button @click="resetData">{{ $t('Reset') }}</el-button>
            <el-button
                type="primary"
                @click="onSubmit(`addInventoryAddition`)"
                :disabled="isLoading"
                :loading="isLoading"
            >{{ $t('Add') }}</el-button
            >
          </div>
        </div>
        <div class="add-inventory-addition-history">
          <h3 class="title mb-3">{{ $t('Import history') }}</h3>
          <div class="list-item">
            <el-table
                border
                :data="history"
                style="width: 100%"
                size="small"
                :row-class-name="tableRowClassName"
            >

              <el-table-column :label="$t('Supply')" width="120">
                <template #default="scope">
                  {{ scope.row.supply }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('Qty')" width="120">
                <template #default="scope">
                  {{ scope.row.quantity }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('PO')" width="120">
                <template #default="scope">
                  {{ scope.row.po_number }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('SKU')" width="150">
                <template #default="scope">
                  {{ scope.row.sku }}
                </template>
              </el-table-column>

              <el-table-column
                  prop="action"
                  :label="$t('Action')"
                  width="80"
                  fixed="right"
              >
                <template #default="scope">
                  <el-popconfirm
                      v-if="!scope.row.is_deleted"
                      :title="'Are you sure to delete ?'"
                      @confirm="inventoryAdditionDestroy(scope.row)"
                  >
                    <template #reference>
                      <el-link :underline="false" type="danger"
                      ><icon :data="iconDelete"
                      /></el-link>
                    </template>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
