import EventBus from "@/utilities/eventBus.js";
import {add, revert} from "@/api/supplyInventoryAddition.js";
import {
    getPurchaseOrderByPoNumber,
    getPurchaseOrderByVendor,
} from "@/api/supplyInventoryAddition.js";
import {mapGetters} from "vuex";
import {list} from "@/api/supplyLocation.js";
import {searchProductInPurchaseOrder} from "@/api/product.js";
import {isEmpty} from "ramda";
import {employeeLogoutTimeChecking, employeeTimeChecking} from "@/api/employee.js";
import IncrementTimer from "@/components/IncrementTimer.vue";
import { debounce } from '@/utilities/helper.js';

export default {
    name: "InventoryAdditionAdd",
    components: {IncrementTimer},
    props: ['employees'],
    data() {
        return {
            data: {},
            isLoading: false,
            openDialogAddInventoryAddition: false,
            isLoadingSearch: false,
            purchaseOrders: [],
            invoicePurchaseOrders: [],
            products: [],
            locationList: [],
            history: [],
            dataByTrackingNumber: "",
            quantity: "",
            employeeID: '',
            employee: {},
            employeeError: '',
            job_type: "supply_addition",
            id_time_checking: null
        };
    },
    computed: {
        ...mapGetters(["getVendors"]),
        getVendorsForMil() {
            return this.getVendors;
        },
        dataRules() {
           return this.defaultDataRules();
        },
    },
    mounted() {
        this.fetchPurchaseOrderByVendor();
        this.getSupplyLocationList();
    },
    created() {
        EventBus.$on("showCreateInventoryAddition", () => {
            this.openDialogAddInventoryAddition = true;
            this.focusByElClass('el-form-manual-item-employee');
        });
    },
    methods: {
        async getSupplyLocationList() {
            const res = await list({ is_fetch_all: true});
            this.locationList = res?.data ?? [];
        },
        async remoteMethod(event) {
            const query = event.target.value;
            if (!query) {
               return
            }
            const res = await getPurchaseOrderByVendor(
                {
                    keyword: query,
                    vendor_id: this.data.vendor_id,
                    incoming : true,
                    export: true
                }
            );
            this.purchaseOrders = res.data || [];
        },
        async resetEmployee() {
            const res = await employeeLogoutTimeChecking(this.id_time_checking);
            this.employee = {};
            this.employeeError = '';
            this.employeeID = '';
            this.id_time_checking = null
        },
        trimBoxID() {
            if (this.data.box_id.trim().length === 0)
                this.data.box_id = '';
        },
        async scanEmployeeID() {

            if (this.id_time_checking) {
                return true;
            }
            if (!this.employeeID) {
                this.employeeError = "Employee ID field cannot be left blank.";
                return false;
            }
            const res = await employeeTimeChecking({
                code: Number(this.employeeID),
                job_type: this.job_type
            })
            if (!res.data.data) {
                this.employeeError = "Can't find your employee ID, please scan again";
                return false;
            }
            this.employeeError = "";
            this.employee = res.data.data;
            this.id_time_checking = res.data.id_time_checking;
            if (this.currentTab == 'manual') {
                this.focusByElClass('el-form-manual-item-vendor');
                return;
            }
            this.focusByElClass();
            return true;
        },
        async fetchPurchaseOrderByVendor() {
            const res = await getPurchaseOrderByVendor({
                vendor_id: this.data.vendor_id || null,
                export: true,
                incoming: true,
            });
            this.purchaseOrders = res.data || [];
            this.focusByElClass('el-form-manual-item-po');
        },

        defaultDataRules() {
            return {
                quantity: [
                    {
                        required: true,
                        message: this.$t('This field cannot be left blank.'),
                        trigger: "blur",
                    },
                    {type: "number", message: this.$t('Quantity must be a number')},
                ],
                supply_id: [
                    {
                        required: true,
                        message: this.$t('This field cannot be left blank.'),
                        trigger: "change",
                    },
                ],
                po_number: [
                    {
                        required: true,
                        message: this.$t('This field cannot be left blank.'),
                        trigger: "change",
                    },
                ],
                box_id: [
                    {
                        required: true,
                        message: this.$t('This field cannot be left blank.'),
                        trigger: "change",
                    },
                ],
                location_id: [
                    {
                        required: true,
                        message: this.$t('This field cannot be left blank.'),
                        trigger: "change",
                    },
                ],
            };
        },
        async searchProductByGTIN() {
            const purchaseOrder = this.purchaseOrders.find(
                (item) => +item.id === this.data.po_number
            );
            if (!purchaseOrder?.id) {
                this.notification('PO is required.', "error");
                return;
            }
            try {
                const res = await searchProductInPurchaseOrder({gtin: this.data.gtin, po_id: purchaseOrder.id});
                const data = res.data;
                if (data && data.id) {
                    this.data.quantity = data.gtin_case || 0;
                    this.data.product_id = data.id;
                    this.focusByElClass('el-form-manual-item-qty');
                }
            } catch (e) {
                const data = e.response.data || {};
                let message = data?.message || "Product not found.";
                if (!isEmpty(data) && !data?.message) {
                    const keyFirstData = Object.keys(data)[0];
                    const firstData = data[keyFirstData];
                    message = firstData[0];
                }
                this.data.quantity = '';
                this.data.product_id = '';
                this.notification(message, "error");
            }
        },
        selectProduct() {
            const product = this.products.find(
                (item) => +item.id === +this.data.supply_id
            );
            this.data.sku = product.sku;
            this.data.quantity = product.case_quantity;
        },
        getLabelPurchaseOrder(item) {
            if (item.invoice_number) {
                return item.po_number + " - Invoice Number: " + item.invoice_number;
            }
            return item.po_number;
        },
        selectPurchaseOrder(id) {
            const purchaseOrderCurrent = this.purchaseOrders.find(
                (item) => +item.id === +id
            );
            this.quantity = "";
            if (!purchaseOrderCurrent) {
                return;
            }
            this.data.vendor_id = purchaseOrderCurrent.vendor_id || "";
            this.data.invoice_number = "";
            this.invoicePurchaseOrders = [];
            this.products = [];
            const products = purchaseOrderCurrent.items || [];
            this.setProductItems(products);
            this.focusByElClass('el-form-manual-item-product');
        },
        async searchPurchaseOrder(query) {
            if (query) {
                this.isLoadingSearch = true;
                const res = await getPurchaseOrderByPoNumber({
                    po_number: query,
                    vendor_id: this.data.vendor_id,
                });
                this.isLoadingSearch = false;
                const data = res.data || [];
                this.purchaseOrders = data;
            } else {
                this.purchaseOrders = [];
            }
        },
        setProductItems(items) {
            items = items.map((item) => {
                return {
                    id: item.supply && item.supply.id,
                    name: item.supply && item.supply.name,
                    sku: item.supply && item.supply.sku,
                    quantity: item.quantity,
                    quantity_onhand: item.quantity_onhand,
                    case_quantity: item.supply?.case_quantity ?? 0,
                };
            });
            this.products = items;
        },
        resetInsert(onAction = 'default') {
            this.data = {
                ...this.data,
                gtin: null,
                quantity: null,
                product_id: null,
                barcode: null,
            };
            if (onAction == 'default') {
                this.data = {
                    ...this.data,
                    location_id: null,
                    location_name: null,
                }
            }
            this.$nextTick(() => {
                this.$refs["addInventoryAddition"].clearValidate();
            });
        },
        resetData() {
            this.data = {
                vendor_id: "",
                po_number: "",
                po_id: "",
                sku: "",
                quantity: "",
                box_id: "",
                location_id: "",
                supply_id: "",
                barcode: "",
                tracking_number: "",
                invoice_number: "",
            };
        },
        closeModal() {
            if (this.history.length) {
                this.$emit("refresh");
            }
            this.purchaseOrders = [];
            this.invoicePurchaseOrders = [];
            this.boxs = [];
            this.products = [];
            this.history = [];
            this.resetData();
            this.resetEmployee();
        },
        async inventoryAdditionDestroy(item) {
            try {
                await revert({id: item.id});
                this.notification("Inventory addition delete successfully!");
                this.history.forEach((history) => {
                    if (+history.id === +item.id) {
                        history.is_deleted = true;
                    }
                });
            } catch (e) {
                let message =
                    e.response.data.message ||
                    "An error occurred, please try again later!";
                this.notification(message, "error");
            }
        },
        tableRowClassName(data) {
            return data.row.is_deleted ? "is-delete" : "";
        },
        async onSubmit(formName) {
            if (!this.scanEmployeeID()) return;
            const isValid = await this.$refs[formName].validate();
            if (!isValid) {
                return;
            }
            this.isLoading = true;
            try {
                let data = Object.assign({}, this.data);
                const getVendor = this.getVendors.find(
                    (item) => +item.id === +data.vendor_id
                );
                const PONumber = this.purchaseOrders.find(
                    (item) => +item.id === this.data.po_number
                );
                data.po_number =
                    (PONumber && PONumber.po_number) || this.data.po_number;
                data.po_id = (PONumber && PONumber.id) || "";
                data.employee_id = this.employee.id;
                data.id_time_checking = this.id_time_checking
                const res = await add(data);
                const resData = res.data;
                this.isLoading = false;
                const getProduct = this.products.find(
                    (item) => +item.id === +data.supply_id
                );
                let itemHistory = {
                    id: resData.id,
                    po_number: data.po_number,
                    invoice_number: data.invoice_number,
                    sku: data.sku,
                    supply: getProduct && getProduct.name,
                    quantity: data.quantity,
                };
                this.history.push(itemHistory);
                this.resetInsert('success');
                this.focusByElClass('el-form-manual-item-barcode');
                this.notification(this.$t('Inventory addition add successfully.'));
            } catch (e) {
                const data = e.response.data;
                this.isLoading = false;
                let message = this.$t('Inventory addition add error.');
                if (!isEmpty(data)) {
                    const keyFirstData = Object.keys(data)[0];
                    const firstData = data[keyFirstData];
                    message = firstData[0];
                }
                this.notification(message, "error");
            }
        },
    },
};
