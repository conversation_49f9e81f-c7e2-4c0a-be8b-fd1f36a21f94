<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
      v-model="openDialogAddInventoryAddition"
      :title="$t('Create Supply Deduction')"
      custom-class="el-dialog-custom"
      @close="closeModal"
      destroy-on-close
      :close-on-click-modal="false"
      width="35%"
    >
      <template #default>
        <div class="add-inventory-addition flex-col">
          <el-form
            status-icon
            ref="addInventoryDeductionForm"
            :model="data"
            :rules="dataRules"
            @submit.prevent="onSubmit(`addInventoryDeductionForm`)"
            label-position="top"
          >
            <div class="bg-gray-50 p-3 border rounded mb-2">
              <el-form-item
                v-show="!Object.keys(employee).length"
                :error="employeeError"
                :label="$t('Employee ID')"
                required
                class="mt-1"
              >
                <el-input
                  class="el-form-manual-item-employee"
                  v-model="employeeID"
                  @keyup.enter="scanEmployeeID"
                ></el-input>
              </el-form-item>
              <div v-if="Object.keys(employee).length">
                <div class="flex justify-between">
                  <b class="text-base"
                    >Hi {{ employee.name }}, Have a nice day!</b
                  >
                  <el-link
                    type="danger"
                    @click="resetEmployee"
                    :underline="false"
                    >{{ $t('Logout') }}</el-link
                  >
                </div>
                <div class="text-lg text-fuchsia-500">
                  <IncrementTimer />
                </div>
              </div>
            </div>
            <div class="el-item-group">
              <el-form-item :label="$t('SKU')" prop="supply_id" class="w-[70%]">
                <el-select
                  ref="supply_id"
                  v-model="data.supply_id"
                  :placeholder="$t('Input Supply SKU or Name')"
                  @change="selectSupply"
                  class="el-form-manual-item-vendor"
                  filterable
                >
                  <el-option
                    v-for="item in dataSupplies"
                    :key="item.id"
                    :label="`${item.name} (${item.sku})`"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item
                :label="$t('BoxID')"
                prop="box_barcode"
                class="w-[70%] ml-3"
              >
                <el-select
                  ref="box_barcode"
                  v-model="data.box_barcode"
                  :placeholder="box_select_placeholder"
                  class="el-form-manual-item-vendor"
                  @change="getBoxInfo"
                  filterable
                >
                  <el-option
                    v-for="item in dataBoxes"
                    :key="item.id"
                    :label="`${item.barcode}`"
                    :value="item.barcode"
                  >
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item
                :label="$t('Box Quantity')"
                prop="box_quantity"
                class="ml-3"
              >
                <el-input
                  ref="box_quantity"
                  v-model="data.box_quantity"
                  readonly
                  class="el-form-manual-item-vendor pointer-events-none bg-white"
                  filterable
                />
              </el-form-item>
            </div>

            <el-form-item :label="$t('Quantity')" prop="quantity">
              <el-input-number
                v-model="data.quantity"
                :precision="0"
                :step="1"
                :min="0"
                :max="data.box_quantity > 0 ? data.box_quantity : Infinity"
                class="el-form-manual-item-qty mb-2"
                style="width: 60%"
                @keyup.enter="focusByElClass('el-form-manual-item-product')"
              />
            </el-form-item>
          </el-form>
          <div class="flex justify-end">
            <el-button @click="resetData">{{ $t('Reset') }}</el-button>
            <el-button
              type="primary"
              @click="onSubmit(`addInventoryDeductionForm`)"
              :disabled="isLoading"
              :loading="isLoading"
              >{{ $t('Create') }}</el-button
            >
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
