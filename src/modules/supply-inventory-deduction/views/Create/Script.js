import EventBus from '@/utilities/eventBus.js';
import {
  add,
  fetchSupplies,
  getBoxList,
  getBoxInfo,
} from '@/api/supplyDeduction.js';
import { mapGetters } from 'vuex';
import { isEmpty, isNil } from 'ramda';
import {
  employeeLogoutTimeChecking,
  employeeTimeChecking,
} from '@/api/employee.js';
import IncrementTimer from '@/components/IncrementTimer.vue';

export default {
  name: 'InventoryDeductionAdd',
  components: { IncrementTimer },
  props: ['employees'],
  data() {
    return {
      data: {
        quantity: 0,
        box_quantity: '',
      },
      openDialogAddInventoryAddition: false,
      isLoading: false,
      isLoadingSearch: false,
      employeeID: '',
      employee: {},
      employeeError: '',
      job_type: 'supply_deduction',
      box_select_placeholder: 'Input Box ID',
      id_time_checking: null,
      dataSupplies: [],
      dataBoxes: [],
    };
  },
  computed: {
    ...mapGetters(['getVendors']),
    getVendorsForMil() {
      return this.getVendors.filter((item) => +item.type === 1);
    },
    getLocationsByType() {
      return this.locations.filter(
        (item) => item.type === this.locationCurrentType
      );
    },
    dataRules() {
      let dataRules = this.defaultDataRules();
      return dataRules;
    },
  },
  mounted() {
    this.fetchSupplies();
  },
  created() {
    EventBus.$on('showCreateSupplyDeduction', () => {
      this.openDialogAddInventoryAddition = true;
      this.dataBoxes = [];
      this.focusByElClass('el-form-distributor-item-employee');
    });
  },
  methods: {
    async selectSupply() {
      const supply =
        (await this.dataSupplies.find((supply) => {
          return supply.id == this.data.supply_id;
        })) || [];
      await this.getAllBoxes(supply?.sku);
      let firstBox = {};
      if (!isEmpty(this.dataBoxes) || !isNil(this.dataBoxes)) {
        firstBox = await this.dataBoxes.find((box) => {
          return box.supply_id == this.data.supply_id;
        });
      }

      if (isEmpty(firstBox) || isNil(firstBox)) {
        this.box_select_placeholder = 'Not available';
        this.data.box_barcode = '';
        this.data.box_quantity = '';
      } else {
        this.box_select_placeholder = 'Input Box ID';
        this.data.box_barcode = firstBox.barcode;
        this.data.box_quantity = firstBox.quantity;
      }
    },
    async fetchSupplies() {
      const res = await fetchSupplies();
      this.dataSupplies =
        res.data.map((supply) => ({
          id: supply.id,
          name: supply.name,
          sku: supply.sku,
        })) || [];
    },
    async getAllBoxes(supplySKU) {
      if (isEmpty(supplySKU) || isNil(supplySKU)) {
        this.dataBoxes = [];
        return;
      }
      const params = {
        supply_sku: supplySKU,
      };
      const res = await getBoxList(params);
      if (isEmpty(res.data) || isNil(res.data)) {
        this.dataBoxes = [];
        return;
      }

      this.dataBoxes = res.data.map((box) => ({
        id: box.id,
        quantity: box.quantity,
        supply_id: box?.supply?.id,
        barcode: box?.barcode,
        sku: box?.supply?.sku,
      }));
    },
    async resetEmployee() {
      const res = await employeeLogoutTimeChecking(this.id_time_checking);
      this.employee = {};
      this.employeeError = '';
      this.employeeID = '';
      this.id_time_checking = null;
    },
    async scanEmployeeID() {
      if (this.id_time_checking) {
        return true;
      }
      if (!this.employeeID) {
        this.employeeError = 'Employee ID field cannot be left blank.';
        return false;
      }

      const res = await employeeTimeChecking({
        code: Number(this.employeeID),
        job_type: this.job_type,
      });

      if (!res.data.data) {
        this.employeeError = "Can't find your employee ID, please scan again";
        return false;
      }
      this.employeeError = '';
      this.employee = res.data.data;
      this.id_time_checking = res.data.id_time_checking;
      this.focusByElClass();
      return true;
    },
    defaultDataRules() {
      return {
        quantity: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: 'blur',
          },
          { type: 'number', message: this.$t('Quantity must be a number') },
        ],
        supply_id: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: 'change',
          },
        ],
      };
    },
    resetData() {
      this.$refs['addInventoryDeductionForm'].resetFields();
      this.dataBoxes = [];
      this.box_select_placeholder = 'Input Box ID';
      this.data = {
        supply_id: '',
        quantity: 0,
        box_barcode: '',
        box_quantity: '',
      };
    },
    closeModal() {
      this.resetData();
      this.resetEmployee();
      EventBus.$emit('fetchSupplyDeduction');
    },
    async getBoxInfo() {
      if (isEmpty(this.data.box_barcode) || isNil(this.data.box_barcode))
        return;
      const params = {
        barcode: this.data.box_barcode,
      };
      const boxInfo = await getBoxInfo(params);
      if (!isEmpty(this.data.box_barcode) && !isNil(this.data.box_barcode)) {
        this.data.box_quantity = boxInfo.data.quantity;
        this.data.supply_id = boxInfo.data?.supply?.id;
      }
    },
    async onSubmit(formName) {
      if (!this.scanEmployeeID()) return;
      const isValid = await this.$refs[formName].validate();
      if (!isValid) {
        return;
      }
      let params = Object.assign({}, this.data);
      params.employee_id = this.employee.id;
      params.id_time_checking = this.id_time_checking;
      if (
        this.data.quantity > this.data.box_quantity &&
        !isEmpty(this.data.box_barcode) &&
        !isNil(this.data.box_barcode)
      ) {
        this.notification('Cannot exceed Box Quantity.', 'error');
        return;
      }
      this.isLoading = true;
      try {
        this.isLoading = false;
        const res = await add(params);
        this.resetData();
        this.isLoading = false;
        this.focusByElClass('el-form-manual-item-barcode');
        this.getAllBoxes();
        this.notification(this.$t('Successfully deducted supplies!'));
      } catch (e) {
        const data = e.response.data;
        this.isLoading = false;
        let message = this.$t('Supplies deduction create error.');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.notification(message, 'error');
      }
    },
    focusByElClass(elClass = 'el-form-item-tracking-number') {
      setTimeout(() => {
        const el = document.getElementsByClassName(elClass);
        if (!el || !el.length) {
          return;
        }

        const firtsElError = (el && el[0]) || undefined;
        const input = firtsElError.querySelector('input');
        document.body.scrollTop = (input.offsetTop - 120) | 0;
        input.focus();
      });
    },
  },
};
