<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ $t("Supply Deduction") }}</h1>
      </div>
      <div class="top-head-right">
        <el-button @click="openModalExport" plain type="primary">
          <span class="icon-margin-right"><icon :data="iconExport" /></span>{{ $t('Export') }}
        </el-button>
        <el-button type="primary" @click="createInventoryAddition">
          <span class="icon-margin-right"><icon :data="iconMinus" /></span
          >{{ $t("Deduct") }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <div class="filter">
        <div class="label">{{ $t("Filter by:") }}</div>
        <div class="filter-item">
          <el-dropdown
            ref="keyword"
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('keyword') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('keyword')">
                <el-tooltip effect="dark" content="PO#" placement="top-start">
                  <span>{{ filter.keyword }}</span>
                </el-tooltip>
              </template>
              <template v-else> Supply SKU or Name </template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input
                  :placeholder="$t('Enter search keyword')"
                  class="search"
                  v-model="filter.keyword"
                  @keydown.enter="onFilter('keyword')"
                  clearable
                  @clear="clearFilterItem('keyword')"
                />
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item">
          <el-dropdown
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': filter.date && filter.date.length }"
          >
            <span class="el-dropdown-link">
              <template v-if="filter.date && filter.date.length">
                <el-tooltip
                  effect="dark"
                  :content="$t('Date')"
                  placement="top-start"
                >
                  <span>
                    {{
                      templateDateRange(filter.date[0], filter.date[1])
                    }}</span
                  >
                </el-tooltip>
              </template>
              <template v-else> {{ $t("Date") }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-date-picker
                  format="YYYY-MM-DD"
                  v-model="filter.date"
                  type="daterange"
                  range-separator="To"
                  :start-placeholder="$t('Start date')"
                  :end-placeholder="$t('End date')"
                  @change="onChangeDate"
                >
                </el-date-picker>
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item">
          <el-dropdown
            ref="keyword"
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('box_barcode') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('box_barcode')">
                <el-tooltip effect="dark" placement="top-start">
                  <span>{{ filter.box_barcode }}</span>
                </el-tooltip>
              </template>
              <template v-else>Box ID</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input
                  :placeholder="$t('Enter search box barcode')"
                  class="search"
                  v-model="filter.box_barcode"
                  @keydown.enter="onFilter('box_barcode')"
                  clearable
                  @clear="clearFilterItem('box_barcode')"
                />
              </div>
            </template>
          </el-dropdown>
        </div>

        <div class="filter-item" v-if="hasFilter">
          <el-link type="danger" @click="onClearFilter" :underline="false">
            {{ $t("Clear") }}
          </el-link>
        </div>
      </div>
      <el-table
        stripe
        size="small"
        border
        :data="items"
        style="width: 100%"
        :max-height="maxHeight"
        v-loading="isLoading"
        element-loading-text="Loading..."
        :row-class-name="tableRowClassName"
      >
        <el-table-column prop="created_at" :label="$t('Date')" min-width="80">
          <template #default="scope">
            {{ formatDate(scope.row.created_at, false) }}
          </template>
        </el-table-column>

        <el-table-column
          :label="$t('Supply Name')"
          min-width="90"
        >
          <template #default="scope">
            {{ scope.row.supply.name }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('SKU')" min-width="90">
          <template #default="scope">
            {{ scope.row.supply.sku }}
          </template>
        </el-table-column>
        <el-table-column  :label="$t('Quantity')" min-width="60">
          <template #default="scope">
            {{ formatNumber(scope.row.quantity) }}
          </template>
        </el-table-column>
        <el-table-column  :label="$t('Box ID')" min-width="60">
          <template #default="scope">
            {{ scope.row?.supply_box?.barcode }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('User')" min-width="100">
          <template #default="scope">
            {{ scope.row?.user?.username }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Employee')" min-width="100">
          <template #default="scope">
            {{ scope.row?.employee?.name }}
          </template>
        </el-table-column>
        <el-table-column prop="action" :label="$t('Action')" min-width="30">
          <template #default="scope">
            <el-popconfirm
              :title="'Are you sure to delete ?'"
              @confirm="deleteInventoryDeduction(scope.row)"
              v-if="!scope.row.is_deleted"
            >
              <template #reference>
                <el-link :underline="false" type="danger"
                  ><icon :data="iconDelete"
                /></el-link>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom">
        <div class="total">
          {{ $t("Total:") }} {{ items.length ? formatNumber(total) : 0 }}
        </div>
        <el-pagination
          :disabled="isLoading"
          background
          layout="prev, pager, next"
          :page-size="filter.limit"
          :total="total"
          @current-change="changePage"
          v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
            v-model="filter.limit"
            :placeholder="$t('Select')"
            size="mini"
            @change="onFilter"
          >
            <el-option
              v-for="item in limits"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
    <el-dialog
        v-model="dialogExport"
        :title="$t('Export Supply Deduction')"
        custom-class="el-dialog-custom custom-dialog rounded-xl"
        @close="resetData"
        :close-on-click-modal="false"
    >
      <template #default>
        <div
            class="my-4 mx-auto items-center flex flex-col justify-center"
        >
          <el-date-picker
              format="YYYY-MM-DD"
              v-model="created_at"
              :clearable="false"
              type="daterange"
              range-separator="To"
              :start-placeholder="$t('Start Date')"
              :end-placeholder="$t('End Date')"
          >
          </el-date-picker>
        </div>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetData">Cancel</el-button>
          <el-button @click="exportExcel" type="primary">Export</el-button>
        </div>
      </template>
    </el-dialog>
    <CreateInventoryDeduction @refresh="fetchData" :employees="employees" />
  </div>
</template>
