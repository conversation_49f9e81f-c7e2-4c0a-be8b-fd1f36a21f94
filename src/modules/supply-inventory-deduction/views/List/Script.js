import { list, revert, exportData } from "@/api/supplyDeduction.js";
import EventBus from "@/utilities/eventBus.js";
import CreateInventoryDeduction from "@/modules/supply-inventory-deduction/views/Create/Index.vue";
import { equals } from "ramda";
import warehouseMixin from "@/mixins/warehouse";
import { API_URL } from "@/utilities/constants";
import moment from 'moment/moment';

export default {
  name: "InventoryAddition",
  mixins: [warehouseMixin],
  components: {
    CreateInventoryDeduction,
  },
  data() {
    return {
      items: [],
      isLoading: false,
      filter: this.setDefaultFilter(),
      dialogExport: false,
      created_at: null,
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 230);
    },
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },
    buildLinkDownload() {
      const link = `${API_URL}/supply-deduction/export`;
      let params = this.filter;
      delete params.page;
      delete params.limit;
      params.warehouse_id = this.userWarehouseId;
      params.export = true;

      if (this.filter.date && this.filter.date.length) {
        params.start_date = this.formatDate(this.filter.date[0], false);
        params.end_date = this.formatDate(this.filter.date[1], false);
      }
      params = new URLSearchParams(params);
      return `${link}?${params.toString()}`;
    },
  },
  beforeUnmount() {
    EventBus.$off("showCreateSupplyDeduction");
  },
  mounted() {
    this.fetchData();
    this.setDefaultDate();
  },
  created() {
    EventBus.$on("fetchSupplyDeduction", () => {
      this.fetchData();
    });
  },
  methods: {
    async exportExcel() {
      try {
        const payload = {
          'warehouse_id': this.userWarehouseId,
        }
        if (this.created_at) {
          payload.start_date = this.formatDate(this.created_at[0], false);
          payload.end_date = this.formatDate(this.created_at[1], false);
        }
        const response = await exportData(payload);

        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', 'supply_deduction.xlsx');
        document.body.appendChild(link);
        link.click();
        link.remove();
      } catch (error) {
        this.notification('Error downloading the file.', 'error');
      }
    },
    openModalExport() {
      this.dialogExport = true;
    },
    resetData() {
      this.dialogExport = false;
      this.setDefaultDate();
    },
    setDefaultDate() {
      let time = new Date();
      this.created_at = [
        this.formatDate(moment(time).subtract(7, 'days'), false),
        this.formatDate(new Date(), false),
      ];
    },
    onFilter(item = "") {
      this.filter.page = 1;
      this.$nextTick(() => {
        this.fetchSupplyDeduction();
        if (item) {
          this.$refs[item].handleClose();
        }
      });
    },
    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.$nextTick(() => {
        this.fetchSupplyDeduction();
      });
    },
    onChangeDate() {
      if (this.filter.date && this.filter.date.length) {
        this.filter.date[0] = this.formatDate(this.filter.date[0], false);
        this.filter.date[1] = this.formatDate(this.filter.date[1], false);
        this.onFilter();
      }
    },
    clearFilterItem(item) {
      this.filter[item] = "";
      this.$refs[item].handleClose();
      this.onFilter();
    },
    setDefaultFilter() {
      let params = {
        limit: 25,
        page: 1,
        box_barcode: '',
        keyword: "",
        date: "",
      };
      return params;
    },
    setRouteParam() {
      const params = this.filter;
      this.$router.replace({ name: "supply_inventory_deduction", query: params });
    },
    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);
      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }
      filter.page = +filter.page || 1;
      return filter;
    },
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchSupplyDeduction();
      });
    },
    createInventoryAddition() {
      EventBus.$emit("showCreateSupplyDeduction");
    },
    async fetchData() {
      this.filter = this.getRouteParam();
      this.fetchSupplyDeduction();
    },
    async fetchSupplyDeduction() {
      this.isLoading = true;
      this.setRouteParam();
      const res = await list(this.filter);
      this.isLoading = false;
      const data = res.data || [];
      this.total = data.total;
      this.items = data.data;
    },
    async deleteInventoryDeduction(item) {
      try {
        await revert({ id: item.id });
        this.notification("Inventory deduction delete successfully!");
        this.fetchData();
      } catch (e) {
        let message = e.response.data.message || "An error occurred, please try again later!";
        this.notification(message, "error");
      }
    },
    tableRowClassName(data) {
      return data.row.is_deleted ? "is-delete" : "";
    },
    hasChangeFilterByItem(name) {
      const query = this.$route.query;
      if (query[name]) {
        return true;
      }
      return false;
    },
  },
};
