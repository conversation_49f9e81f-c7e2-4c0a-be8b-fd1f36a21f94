<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ $t('Supply Overview') }}</h1>
      </div>
      <div class="top-head-right">
        <el-button type="primary" @click="exportExcel">
          <span class="icon-margin-right"><icon :data="iconExport"/></span>{{ $t('Export') }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <div class="filter">
        <div class="flex flex-col mr-2">
          <label class="mb-1">{{ $t('Supply Category') }}</label>
          <el-select
              v-model="filter.category_id"
              :placeholder="$t('Choose category')"
              clearable
              filterable
          >
            <el-option v-for="(item, index) in categoryOptions"
                       :label="item.name"
                       :key="item.id"
                       :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
        <div class="flex flex-col mr-2">
          <label class="mb-1">{{ $t('Supply Name or SKU') }}</label>
          <el-input
              :placeholder="$t('Search')"
              v-model="filter.name"
              @keyup.enter="onFilter"
          />
        </div>
        <div class="btn-filter mt-5">
          <template v-if="hasFilter">
            <el-link type="danger" @click="onClearFilter" :underline="false">{{
                $t('Clear')
              }}
            </el-link>
          </template>
          <el-button type="primary" @click="onFilter">
            <span class="icon-margin-right"><icon :data="iconFilter"/></span
            >{{ $t('Filter') }}
          </el-button>
        </div>
      </div>
      <el-table
          border
          stripe
          size="small"
          :data="items"
          style="width: 100%"
          :max-height="maxHeight"
          v-loading="isLoading"
          element-loading-text="Loading..."
          :cell-class-name="tableCellClassName"
          @sort-change="sortTable"
          class="mt-8"
      >
        <el-table-column prop="category_id" :label="$t('Category')" min-width="100">
          <template #default="scope">
            {{ scope.row.category_name }}
          </template>
        </el-table-column>
        <el-table-column
            prop="name" :label="$t('Supply Name')" min-width="100"   sortable="custom">
          <template #default="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>
        <el-table-column prop="sku" :label="$t('SKU')" min-width="100">
          <template #default="scope">
            {{ scope.row.sku }}
          </template>
        </el-table-column>
        <el-table-column prop="unit" :label="$t('Unit')" min-width="100">
          <template #default="scope">
            {{ scope.row.unit_name }}
          </template>
        </el-table-column>
        <el-table-column
            prop="quantity"
            :label="$t('Quantity')"
            min-width="100"
            sortable="custom"
        >
          <template #default="scope">
            {{ formatNumber(scope.row.quantity) || 0 }}
          </template>
        </el-table-column>
        <el-table-column
            prop="incoming_stock"
            :label="$t('Incoming Stock')"
            min-width="100"
            sortable="custom"
        >
          <template #default="scope">
            {{ formatNumber(scope.row.incoming_stock) || 0 }}
          </template>
        </el-table-column>

      </el-table>
      <div class="bottom">
        <div class="total">
          {{ $t('Total:') }} {{ items.length ? formatNumber(total) : 0 }}
        </div>
        <el-pagination
            :disabled="isLoading"
            background
            layout="prev, pager, next"
            :page-size="filter.limit"
            :total="total"
            @current-change="changePage"
            v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
              v-model="filter.limit"
              :placeholder="$t('Select')"
              size="mini"
              @change="onFilter"
          >
            <el-option
                v-for="item in limits"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
  </div>
</template>
