import { overview, fetchCategory } from "@/api/supplyInventory.js";
import filterMixin from "@/mixins/filter";
import { equals } from "ramda";
import dateMixin from "@/mixins/date.js";
import warehouseMixin from "@/mixins/warehouse";
import { API_URL } from "@/utilities/constants";

export default {
  name: "InventoryOverview",
  mixins: [filterMixin, dateMixin,warehouseMixin],
  components: {},
  data() {
    return {
      items: [],
      isLoading: false,
      filter: this.setDefaultFilter(),
      categoryOptions : [],
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 265);
    },
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },
    buildLinkDownload() {
      const link = `${API_URL}/supply-inventory/export`;
      let params = this.filter;
      delete params.page;
      delete params.limit;
      params.warehouse_id = this.userWarehouseId;
      if (this.filter.date && this.filter.date.length) {
        params.start_date = this.formatDate(this.filter.date[0], false);
        params.end_date = this.formatDate(this.filter.date[1], false);
      }
      params = new URLSearchParams(params);
      return `${link}?${params.toString()}`;
    },
  },
  beforeUnmount() {},
  mounted() {
    this.fetchData();
    this.fetchCategory();
  },
  methods: {
    setDefaultDate() {
      const today = new Date();
      const priorDate = new Date(new Date().setDate(today.getDate() - 30));
      return (
        (today &&
          priorDate && [
            this.formatDate(priorDate, false),
            this.formatDate(today, false),
          ]) ||
        ""
      );
    },
    getBrandNameById(id) {
      const item = this.getBrands.find((item) => +item.id === +id);
      return item && item.name;
    },
    async fetchCategory() {
      const res = await fetchCategory({without_pagination:true});
      const data = res.data || []
      this.categoryOptions = data.map(item => ({id: item.id, name: item.name})) ;
    },
    onFilter(item = "") {
      this.filter.page = 1;
      this.$nextTick(() => {
        this.fetchInventoryOverview();
      });
    },
    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.$nextTick(() => {
        this.fetchInventoryOverview();
      });
    },
    onChangeDate() {
      if (this.filter.date && this.filter.date.length) {
        this.filter.date[0] = this.formatDate(this.filter.date[0], false);
        this.filter.date[1] = this.formatDate(this.filter.date[1], false);
        this.onFilter();
      }
    },
    clearFilterItem(item) {
      this.filter[item] = "";
      this.$refs[item].handleClose();
      this.onFilter();
    },
    setDefaultFilter() {
      let params = {
        category_id: "",
        name: "",
        limit : 25,
        page : 1
      };
      return params;
    },
    setRouteParam() {
      const params = this.filter;
      this.$router.replace({ name: "supply_inventory_overview", query: params });
    },
    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
        limit: Number(routeQuery.limit) || 25
      };
      return filter;
    },
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchInventoryOverview();
      });
    },
     fetchData() {
      this.filter = this.getRouteParam();
      this.fetchInventoryOverview();
    },
    async fetchInventoryOverview() {
      this.isLoading = true;
      this.setRouteParam();
      const res = await overview(this.filter);
      this.isLoading = false;
      const data = res.data || [];
      this.total = data.total;
      this.items = data.data;
    },
    tableRowClassName(data) {
      return data.row.is_deleted ? "is-delete" : "";
    },
    hasChangeFilterByItem(name) {
      const query = this.$route.query;
      if (query[name]) {
        return true;
      }
      return false;
    },
    sortTable(data) {
      let sortColumn = "";
      let sortBy = "";
      if (data.prop && data.order) {
        sortColumn = data.prop;
        if (data.order === "ascending") {
          sortBy = "ASC";
        } else if (data.order === "descending") {
          sortBy = "DESC";
        }
      }
      this.filter.sort_column = sortColumn;
      this.filter.sort_by = sortBy;
      this.$nextTick(() => {
        this.fetchInventoryOverview();
      });
    },
    exportExcel() {
      return (window.location.href = this.buildLinkDownload);
    },

  },

};
