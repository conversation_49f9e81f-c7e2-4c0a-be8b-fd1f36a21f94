<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
      v-model="hasBarcodeOtherLocation"
      @close="cancelConfirm"
      :close-on-click-modal="false"
      width="40%"
      center
    >
      <div class="text-center">
        <el-result icon="warning" :title="$t('Warning')" class="mt-0">
        </el-result>
        <span class="text-base whitespace-normal">
          Box {{ box_other }} is in location {{ box_other_location }}, do you
          want to move it to location {{ this_location }} ?
        </span>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelConfirm">{{ $t('Cancel') }}</el-button>
          <el-button type="primary" @click="confirmNew()">{{
            $t('Confirm')
          }}</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="openDialogAddTestCount"
      :title="$t('Test Count Adjustment')"
      custom-class="el-dialog-custom el-dialog-test-count "
      @close="closeModal"
      :close-on-click-modal="false"
      width="80%"
    >
      <template #default>
        <div class="add-test-count" :key="tableKey">
          <el-form
            status-icon
            ref="addTestCount"
            :model="data"
            :rules="dataRules"
            @submit.prevent="submitCompleteWithValidate('addTestCount')"
            :label-position="'top'"
            class="w-full"
          >
            <div class="bg-gray-50 p-3 border rounded mb-3">
              <el-form-item
                v-show="!Object.keys(employee).length"
                :error="employeeError"
                :label="$t('Employee ID')"
                required
              >
                <el-input
                  v-model="data.employee_id"
                  @keyup.enter="scanEmployeeID"
                ></el-input>
              </el-form-item>
              <div v-if="Object.keys(employee).length">
                <div class="flex justify-between">
                  <b class="text-base"
                    >Hi {{ employee.name }}, Have a nice day!</b
                  >
                  <el-link
                    type="danger"
                    @click="resetEmployee"
                    :underline="false"
                    >{{ $t('Logout') }}</el-link
                  >
                </div>
                <div class="text-lg text-fuchsia-500">
                  <IncrementTimer />
                </div>
              </div>
            </div>
            <el-form-item
              :label="$t('Location')"
              prop="location"
              class="location"
              ref="supplyLocationInputRef"
            >
              <el-autocomplete
                class="w-full"
                v-model="data.location"
                ref="locationRef"
                @keyup.enter="selectOptionByEnter($event)"
                :fetch-suggestions="querySearchAsync"
                :trigger-on-focus="true"
                placeholder="Please input"
                :select-when-unmatched="true"
                @select="handleSelect"
              ></el-autocomplete>

              <el-button
                type="primary"
                :disabled="isLoading"
                :loading="isLoading"
                @click="dialogSubmitPendingTestCount('addTestCount')"
              >
                {{ $t('Pause test') }}
              </el-button>
              <el-button
                type="primary"
                :disabled="isLoading"
                :loading="isLoading"
                @click="submitCompleteWithValidate('addTestCount')"
                >{{ $t('Complete') }}</el-button
              >
            </el-form-item>
            <div class="el-item-group two">
              <el-form-item :label="$t('BoxID')" prop="barcode">
                <el-input
                  :placeholder="$t('BoxId')"
                  class="input-barcode-test-count"
                  v-model="data.barcode"
                  @keyup.enter="getScanBarcodeNew()"
                ></el-input>
              </el-form-item>
              <el-form-item :label="$t('Note')" prop="note">
                <el-input
                  :placeholder="$t('Note')"
                  v-model="data.note"
                ></el-input>
              </el-form-item>
            </div>
            <el-form-item>
              <div class="list-box table-content">
                <div class="box-item">
                  <div class="flex flex-row">
                    <h3>{{ $t('List Box In Location') }}</h3>
                    <span class="ml-1">
                      {{
                        '(' +
                        $t('Found: ') +
                        countBarcodeFoundTableLeft() +
                        ', ' +
                        $t('Move In: ') +
                        countBarcodeMoveInTableLeft() +
                        ', ' +
                        $t('Total: ') +
                        countBarcodeTableLeft() +
                        ')'
                      }}
                    </span>
                  </div>
                  <div class="box-item-info">
                    <el-table
                      border
                      :data="leftBoxs"
                      style="width: 100%"
                      size="small"
                      :row-class-name="tableRowClassName"
                      :max-height="maxHeight"
                    >
                      <el-table-column
                        type="index"
                        :label="$t('No.')"
                        width="50"
                      />
                      <el-table-column :label="$t('Box')" min-width="150">
                        <template #default="scope">
                          {{ scope.row.barcode }}
                        </template>
                      </el-table-column>

                      <el-table-column :label="$t('Supply')" min-width="150">
                        <template #default="scope">
                          {{ scope.row.supply_name }}
                        </template>
                      </el-table-column>

                      <el-table-column :label="$t('Quantity')" min-width="150">
                        <template #default="scope">
                          {{ scope.row.quantity }}
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
                <div class="box-item">
                  <div class="flex flex-row">
                    <h3>{{ $t('New Box') }}</h3>
                    <span class="ml-1">
                      {{ '(' + $t('Total: ') + countBarcodeTableRight() + ')' }}
                    </span>
                  </div>
                  <div class="box-item-info">
                    <el-table
                      border
                      :data="resultBoxs"
                      style="width: 100%"
                      size="small"
                      :max-height="maxHeight"
                    >
                      <el-table-column
                        type="index"
                        :label="$t('No.')"
                        width="50"
                      />
                      <el-table-column
                        :label="$t('Box')"
                        min-width="150"
                        prop="scanGtin"
                      >
                        <template #default="scope">
                          {{ scope.row.barcode }}
                        </template>
                      </el-table-column>
                      <el-table-column
                        :label="$t('Supply SKU')"
                        min-width="150"
                      >
                        <template #default="scope">
                          <el-form-item
                            :class="scope.row.is_input ? '' : 'is-error'"
                          >
                            <el-input
                              v-model="scope.row.sku"
                              @keyup.enter="
                                scanSupplySku(scope.$index, scope.row)
                              "
                            />
                          </el-form-item>
                        </template>
                      </el-table-column>

                      <el-table-column :label="$t('Quantity')" min-width="100">
                        <template #default="scope">
                          <el-form-item
                            prop="quantity"
                            :class="
                              scope.row.is_input_quantity_valid
                                ? ''
                                : 'is-error'
                            "
                          >
                            <el-input v-model="scope.row.quantity" @blur="validateNewBoxQuantity"/>
                          </el-form-item>
                        </template>
                      </el-table-column>

                      <el-table-column
                        prop="action"
                        :label="$t('Action')"
                        width="80"
                        fixed="right"
                      >
                        <template #default="scope">
                          <el-popconfirm
                            v-if="!scope.row.is_deleted"
                            :title="'Are you sure to delete ?'"
                            @confirm="boxDestroyNew(scope.row, scope.$index)"
                          >
                            <template #reference>
                              <el-link :underline="false" type="danger"
                                ><icon :data="iconDelete"
                              /></el-link>
                            </template>
                          </el-popconfirm>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="barcodeNotFoundModal"
      @close="closeDialogBarcodeNotFound"
      :close-on-click-modal="false"
      width="40%"
      center
    >
      <div class="text-center">
        <el-result icon="warning" :title="$t('Warning')" class="mt-0">
        </el-result>
        <span class="text-base whitespace-normal">
          {{ messageBarcodeNotFound }}
        </span>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="closeDialogBarcodeNotFound()">{{
            $t('OK')
          }}</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="submitComplete"
      @close="closeSubmitComplete"
      :close-on-click-modal="false"
      width="40%"
      center
    >
      <div class="text-center">
        <el-result icon="warning" :title="$t('Warning')" class="mt-0">
        </el-result>
        <span class="text-base whitespace-normal">
          {{ confirmRemoveNotFound }}
        </span>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            @click="closeSubmitComplete()"
            :disabled="isLoading"
            :loading="isLoading"
          >
            {{ $t('No') }}
          </el-button>
          <el-button
            type="primary"
            @click="onSubmitNew('addTestCount')"
            :disabled="isLoading"
            :loading="isLoading"
            >{{ $t('Yes') }}</el-button
          >
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="submitPendingTestCount"
      @close="closeSubmitPendingTestCount()"
      :close-on-click-modal="false"
      width="40%"
      center
    >
      <div class="text-center">
        <el-result icon="warning" :title="$t('Warning')" class="mt-0">
        </el-result>
        <span class="text-base whitespace-normal">
          {{ confirmPendingTestCount }}
        </span>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            @click="closeSubmitPendingTestCount()"
            :disabled="isLoading"
            :loading="isLoading"
            >{{ $t('Cancel') }}</el-button
          >
          <el-button
            type="primary"
            @click="pauseTestCount('addTestCount')"
            :disabled="isLoading"
            :loading="isLoading"
            >{{ $t('Confirm') }}</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>
