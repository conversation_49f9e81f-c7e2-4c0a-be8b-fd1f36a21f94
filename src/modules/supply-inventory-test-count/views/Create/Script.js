import EventBus from '@/utilities/eventBus.js';
import {
  addSupplyTestCount,
  pauseSupplyTestCount,
} from '@/api/supplyTestCount.js';
import { getBoxByLocationIdForTestCount, scanBox } from '@/api/supplyBox.js';
import { searchSupply } from '@/api/supplyTestCount.js';
import { list } from '@/api/supplyLocation.js';
import { debounce } from '@/utilities/helper.js';
import { isEmpty, isNil } from 'ramda';
import { mapGetters } from 'vuex';
import {
  employeeLogoutTimeChecking,
  employeeTimeChecking,
} from '@/api/employee.js';
import IncrementTimer from '@/components/IncrementTimer.vue';
import { makeid } from '@/utilities/helper.js';

export default {
  name: 'TestCountAdd',
  props: ['employees'],
  components: { IncrementTimer },
  data() {
    return {
      data: this.setDefaultData(),
      isLoading: false,
      openDialogAddTestCount: false,
      locations: [],
      dataRules: {
        location: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: 'blur',
          },
        ],
      },
      locationTypes: [
        { label: this.$t('Rack'), value: 0 },
        { label: this.$t('Pulling Shelves'), value: 1 },
      ],
      locationCurrentType: 0,
      resultBoxs: [],
      leftBoxs: [],
      boxs: [],
      hasBarcodeOtherLocation: false,
      box_other: '',
      box_other_location: '',
      this_location: '',
      boxOtherProductName: '',
      boxOtherQuantity: '',
      confirmRemoveNotFound: 'Are you sure you want to complete a test count?',
      barcodeNotFoundModal: false,
      messageBarcodeNotFound: '',
      // checkBarcodeNotFound: false,
      submitComplete: false,
      submitPendingTestCount: false,
      confirmPendingTestCount:
        'All the data will be saved when you pause the test count',
      employee: {},
      employeeError: '',
      job_type: 'supply_test_count',
      id_time_checking: null,
      keyword: '',
      links: [],
      showSuggestions: true,
      tableKey: makeid(8),
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 320);
    },
    getLocationsByType() {
      return this.locations.map(({ barcode, id }) => ({ value: barcode, id }));
    },
    ObjectDataLocationSuggestions() {
      const dataObject = {};
      this.locations
        .filter((item) => item.type === this.locationCurrentType)
        .forEach(({ barcode, id }) => {
          dataObject[barcode.toLowerCase()] = id;
        });
      return dataObject;
    },
    optionLocations() {
      if (!this.keyword) {
        return this.getLocationsByType.slice(0, 50);
      }
      return this.getLocationsByType
        .filter(
          (item) =>
            item.barcode.toLowerCase().indexOf(this.keyword.toLowerCase()) > -1
        )
        .slice(0, 20);
    },
    ...mapGetters({
      products: 'getAllProducts',
    }),
    checkBarcodeNotFound() {
      if (typeof this.leftBoxs === 'undefined') {
        return false;
      }
      let barcodeNotFound = this.leftBoxs
        .filter((item) => item.is_success === false)
        .map((item) => item.barcode);
      if (barcodeNotFound.length == 0) {
        return false;
      }
      return true;
    },
  },
  mounted() {
    this.getProductList();
    this.fetchLocation();
  },
  created() {
    EventBus.$on('showCreateTestCount', (data) => {
      this.setDefaultData();
      this.leftBoxs = [];
      this.resultBoxs = [];
      this.boxs = [];
      this.keyword = '';
      if (data) {
        this.data.location_id = data.location_id;
        this.data.location = data.location;
        this.changeLocationNew();
      }

      this.openDialogAddTestCount = true;
      this.submitComplete = false;
      this.tableKey = makeid(8);
    });
  },
  methods: {
    searchLocation(query) {
      this.keyword = query;
    },
    async resetEmployee() {
      const res = await employeeLogoutTimeChecking(this.id_time_checking);
      this.employee = {};
      this.employeeError = '';
      this.data.employee_id = '';
      this.id_time_checking = null;
    },
    async scanEmployeeID() {
      if (this.id_time_checking) {
        return true;
      }
      if (!this.data.employee_id) {
        this.employeeError = 'Employee ID field cannot be left blank.';
        return false;
      }
      const res = await employeeTimeChecking({
        code: Number(this.data.employee_id),
        job_type: this.job_type,
      });

      if (!res.data.data) {
        this.employeeError = "Can't find your employee ID, please scan again";
        return false;
      }
      this.employeeError = '';
      this.employee = res.data.data;
      this.id_time_checking = res.data.id_time_checking;
      this.$refs.locationRef.focus();
      return true;
    },
    async selectOptionByEnter(evt) {
      const value = evt.target.value || '';
      if (!value) return;
      if (!this.ObjectDataLocationSuggestions[value.toLowerCase()]) return;
      this.data.location_id =
        this.ObjectDataLocationSuggestions[value.toLowerCase()];
      this.$refs.locationRef.close();
      await this.changeLocationNew();
      return;
    },
    async changeLocation() {
      const res = await getBoxByLocationId(this.data.location_id);
      const data = res.data || [];
      let dataConvert = [];
      for (let item of data) {
        let value = item;
        value.product_name = item.product?.name;
        value.product_sku = item.product?.sku;
        delete value.product;
        dataConvert.push(value);
      }
      const obj = {};
      if (dataConvert && dataConvert.length) {
        dataConvert.forEach((item) => {
          this.this_location = item.location.barcode;
          obj[item.barcode] = item.id;
        });
      }
      this.boxs = Object.assign({}, obj);
      this.leftBoxs = dataConvert || [];
      this.resultBoxs = [];
      this.focusByElClass();
    },
    async fetchLocation() {
      const params = {
        is_fetch_all: true,
      };
      const res = await list(params);
      const data = res.data || [];
      this.locations = data;
    },
    setDefaultData() {
      return {
        barcode: '',
        location_id: '',
        note: '',
        product_name: '',
        resultBoxs: [],
        leftBoxs: [],
        boxs: [],
        employee_id: '',
        location: '',
      };
    },
    confirm() {
      this.resultBoxs.unshift({
        barcode: this.data.barcode,
        product_name: this.boxOtherProductName,
        is_moving: 1,
      });
      // loai tru barcode trung trong mang
      this.resultBoxs = [
        ...new Map(
          this.resultBoxs.map((obj) => [JSON.stringify(obj), obj])
        ).values(),
      ];
      this.data.barcode = '';
      this.focusByElClass();
      this.hasBarcodeOtherLocation = false;
    },
    scanBarcode: debounce(async function () {
      await this.getScanBarcode();
    }, 500),

    async getScanBarcode() {
      this.hasBarcodeOtherLocation = false;
      try {
        var productName = '';
        if (
          this.leftBoxs.some((item) =>
            this.ciEquals(item.barcode, this.data.barcode)
          )
        ) {
          productName = this.findProductName(this.leftBoxs, this.data.barcode);
          // unshift them vao dau cua mang
          this.resultBoxs.unshift({
            barcode: this.data.barcode,
            product_name: productName.product_name,
          });
          // loai tru barcode trung trong mang
          this.resultBoxs = [
            ...new Map(
              this.resultBoxs.map((obj) => [JSON.stringify(obj), obj])
            ).values(),
          ];
          const currentIndex = this.leftBoxs.findIndex(
            (item) => item.barcode === this.data.barcode
          );
          if (currentIndex >= 0) {
            this.leftBoxs.splice(currentIndex, 1);
          }
          this.$notify.success({
            title: 'Success',
            message: 'Scan box' + this.data.barcode + ' success',
            showClose: false,
            duration: 2000,
          });
        } else if (
          this.resultBoxs.some((item) =>
            this.ciEquals(item.barcode, this.data.barcode)
          )
        ) {
          this.$notify.success({
            title: 'Info',
            message: 'Box ' + this.data.barcode + ' has been scanned',
            showClose: false,
            duration: 2000,
          });
        } else {
          const res = await toLowerCase({
            barcode: this.data.barcode,
            location_id: this.data.location_id,
          });
        }
        this.data.barcode = '';
        this.focusByElClass();
      } catch (e) {
        const data = e.response.data;
        let message = this.$t('Box does not exist');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          if (keyFirstData === 'barcode_another_location') {
            // this.hasBarcodeOtherLocation = true;
            this.box_other = data.box.barcode;
            this.box_other_location = data.location.barcode;
            this.boxOtherProductName = data.product.name;
            this.openConfirm();
          } else if (keyFirstData === 'barcode') {
            this.messageBarcodeNotFound = firstData[0];
            this.barcodeNotFoundModal = true;
          } else {
            this.data.barcode = '';
            this.focusByElClass();
          }
          message = firstData[0];
        }
        this.notification(message, 'error');
      }
    },
    resetData(fieldsIgnore = ['employee_id']) {
      this.$refs['addTestCount'].resetFields();
      const defaultData = this.setDefaultData();
      for (let property in defaultData) {
        if (fieldsIgnore.includes(property)) continue;
        this.data[property] = defaultData[property];
      }
    },
    closeModal() {
      this.resetData();
      this.resetEmployee();
    },
    boxDestroy(data, index) {
      const hasCurrentBarcodeInLocation =
        this.boxs && this.boxs[data.barcode] ? true : false;
      if (hasCurrentBarcodeInLocation) {
        this.leftBoxs.unshift({
          barcode: data.barcode,
          product_name: data.product_name,
        });
      }
      this.resultBoxs.splice(index, 1);
    },
    async onSubmit(formName) {
      const isValid = await this.$refs[formName].validate();
      if (!isValid) {
        return;
      }
      this.isLoading = true;
      try {
        let data = {
          location_id: this.data.location_id,
          note: this.data.note,
          barcode_not_found: this.leftBoxs.map((item) => item.barcode),
        };

        let barcodeExited = this.resultBoxs
          .filter((item) => !item.is_moving)
          .map((item) => item.barcode);
        barcodeExited = barcodeExited && barcodeExited.filter((item) => item);
        let barcodeMoving = this.resultBoxs
          .filter((item) => item.is_moving === 1)
          .map((item) => item.barcode);
        barcodeMoving = barcodeMoving && barcodeMoving.filter((item) => item);

        data.barcode_found = barcodeExited;
        data.barcode_moving = barcodeMoving;

        await add(data);
        this.$emit('refresh', true);
        this.isLoading = false;
        this.openDialogAddTestCount = false;
        this.notification(this.$t('Test count add successfully.'));
      } catch (e) {
        const data = e.response.data;
        this.isLoading = false;
        let message = this.$t('Test count add error.');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.notification(message, 'error');
      }
    },
    findProductName(array, name) {
      return array.find((element) => element.barcode == name);
    },
    cancelConfirm() {
      this.box_other = '';
      this.box_other_location = '';
      this.boxOtherProductName = '';
      this.data.barcode = '';
      this.hasBarcodeOtherLocation = false;
      this.focusByElClass();
    },
    ciEquals(a, b) {
      const check =
        typeof a === 'string' && typeof b === 'string'
          ? a.localeCompare(b, undefined, { sensitivity: 'accent' }) === 0
          : a === b;
      if (check) {
        this.data.barcode = a;
        return true;
      }
      this.data.barcode = b;
      return false;
    },
    focusByElClass(elClass = 'input-barcode-test-count') {
      this.$nextTick(() => {
        const el = document.getElementsByClassName(elClass);
        if (!el || !el.length) {
          return;
        }
        const firtsElError = (el && el[0]) || undefined;
        const input = firtsElError.querySelector('input');
        // document.body.scrollTop = (input.offsetTop - 120) | 0;
        input.focus();
      });
    },
    closeDialogBarcodeNotFound() {
      this.messageBarcodeNotFound = '';
      this.data.barcode = '';
      this.barcodeNotFoundModal = false;
      this.focusByElClass();
    },

    // new
    async changeLocationNew() {
      const res = await getBoxByLocationIdForTestCount(this.data.location_id);
      const data = res.data || [];
      if (data.test_count_pause) {
        this.leftBoxs = data.data || [];
        this.resultBoxs = data.barcode_new || [];
        this.focusByElClass();
      } else {
        this.leftBoxs = data.data || [];
        this.resultBoxs = [];
      }
      if (data.data) {
        this.leftBoxs = [];
        data.data.forEach((item) => {
          this.leftBoxs.push({
            id: item.id,
            barcode: item.barcode,
            is_success: item?.is_success ?? false,
            location_barcode: item.location?.barcode || '',
            supply_name: item?.is_moving ? item.supply_name : item.supply?.name,
            is_moving: item?.is_moving ?? false,
            quantity: item?.quantity ?? 0,
          });
        });
      } else {
        this.leftBoxs = [];
      }
      this.resultBoxs = data.barcode_new || [];
      this.focusByElClass();
    },

    async getScanBarcodeNew() {
      this.hasBarcodeOtherLocation = false;
      if (isNil(this.data.barcode) || isEmpty(this.data.barcode)) return;
      try {
        var productName = '';
        this.data.barcode = String(this.data.barcode).trim();
        if (
          this.leftBoxs.some((item) =>
            this.ciEquals(item.barcode, this.data.barcode)
          )
        ) {
          productName = this.findProductName(this.leftBoxs, this.data.barcode);
          const currentIndex = this.leftBoxs.findIndex(
            (item) => item.barcode === this.data.barcode
          );
          if (currentIndex >= 0) {
            this.leftBoxs[currentIndex].is_success = true;
            const dataSuccess = this.leftBoxs[currentIndex];
            this.leftBoxs.splice(currentIndex, 1);
            this.leftBoxs.unshift(dataSuccess);
          }
          this.leftBoxs = [
            ...new Map(
              this.leftBoxs.map((obj) => [JSON.stringify(obj), obj])
            ).values(),
          ];
          this.$notify.success({
            title: 'Success',
            message: 'Scan box' + this.data.barcode + ' success',
            showClose: false,
            duration: 2000,
          });
        } else {
          const res = await scanBox({
            barcode: this.data.barcode,
            location_id: this.data.location_id,
          });
        }
        this.data.barcode = '';
        this.focusByElClass();
      } catch (e) {
        const data = e.response.data;
        let message = this.$t('Box does not exist');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          if (keyFirstData === 'barcode_another_location') {
            this.box_other = data.box.barcode;
            this.box_other_location = data.location.barcode;
            this.boxOtherProductName = data.supply.name;
            this.boxOtherQuantity = data.box?.quantity;
            this.confirmNew();
          } else if (keyFirstData === 'barcode') {
            this.messageBarcodeNotFound = firstData[0];
            this.barcodeNotFoundModal = true;
            this.notification(this.messageBarcodeNotFound, 'error');
          } else if (keyFirstData === 'supply_not_having_barcode') {
            let isBarcodeResultBox = this.resultBoxs.filter(
              (item) => item.barcode === this.data.barcode
            );
            if (
              isBarcodeResultBox.length == 0 &&
              !isEmpty(String(this.data.barcode).trim())
            ) {
              this.resultBoxs.unshift({
                barcode: this.data.barcode,
                sku: '',
                quantity: 0,
                is_input: true,
                is_input_quantity_valid: true,
              });

              this.focusByElClass();
              this.data.barcode = '';
              message = this.$t('Box new needs to be added SKU.');
              this.notification(message, 'success');
            } else {
              message = this.$t('This box already exists in new box table.');
              this.notification(message, 'error');
              this.focusByElClass();
              this.data.barcode = '';
            }
          } else {
            if (data?.errors) {
              const keyFirstData = Object.keys(data?.errors)[0];
              const firstData = data.errors[keyFirstData][0];
              message = firstData;
            }
            this.notification(message, 'error');
          }
        }
      }
    },
    confirmNew() {
      this.leftBoxs.unshift({
        barcode: this.data.barcode,
        supply_name: this.boxOtherProductName,
        quantity: this.boxOtherQuantity,
        is_moving: 1,
        is_success: true,
      });
      // loai tru barcode trung trong mang
      this.leftBoxs = [
        ...new Map(
          this.leftBoxs.map((obj) => [JSON.stringify(obj), obj])
        ).values(),
      ];
      let message = this.$t('Box moved success');
      this.notification(message, 'success');
      this.data.barcode = '';
      this.focusByElClass();
      this.hasBarcodeOtherLocation = false;
    },
    tableRowClassName(data) {
      return data.row.is_success && data.row.is_success == true
        ? 'is-success'
        : '';
    },
    async getProductList() {
      await this.$store.dispatch('getAllProducts');
    },
    async scanSupplySku(indexRow, data) {
      try {
        const supply = await searchSupply({ sku: data.sku });
        const supplyData = supply.data?.data;
        if (supplyData) {
          this.resultBoxs[indexRow].sku = supplyData?.sku;
          this.resultBoxs[indexRow].supply_id = supplyData?.id;
          this.resultBoxs[indexRow].is_input = true;
          this.resultBoxs[indexRow].quantity = supplyData?.case_quantity ?? 0;
          if (this.resultBoxs.length === indexRow + 1) {
            this.focusByElClass();
            this.notification(this.$t('Supply SKU found'), 'success');
          } else {
            this.focusByElClassName(this.resultBoxs[indexRow + 1].barcode);
            this.notification(this.$t('Supply SKU found'), 'success');
          }
        }
      } catch (e) {
        this.resultBoxs[indexRow].sku = '';
        this.notification(
          this.$t('Supply SKU not found, please re-enter!'),
          'error'
        );
      }
    },
    boxDestroyNew(data, index) {
      this.resultBoxs.splice(index, 1);
    },
    async onSubmitNew(formName) {
      this.isLoading = true;
      try {
        let data = {
          location_id: this.data.location_id,
          note: this.data.note,
          employee_id: this.data.employee_id,
          id_time_checking: this.id_time_checking,
        };
        let barcodeExited = this.leftBoxs
          .filter((item) => item.is_success === true && item.is_moving !== 1)
          .map((item) => item.barcode);
        barcodeExited = barcodeExited && barcodeExited.filter((item) => item);

        let barcodeMoving = this.leftBoxs
          .filter((item) => item.is_moving === 1)
          .map((item) => item);
        barcodeMoving = barcodeMoving && barcodeMoving.filter((item) => item);

        let barcodeNotFound = this.leftBoxs
          .filter((item) => item.is_success === false)
          .map((item) => item.barcode);
        barcodeNotFound =
          barcodeNotFound && barcodeNotFound.filter((item) => item);

        data.barcode_found = barcodeExited;
        data.barcode_moving = barcodeMoving;
        data.barcode_not_found = barcodeNotFound;
        data.barcode_new = this.resultBoxs;

        await addSupplyTestCount(data);
        this.submitComplete = false;
        this.$emit('refresh', true);
        this.$emit('refreshTestCountPause', true);
        this.isLoading = false;
        this.openDialogAddTestCount = false;
        this.notification(this.$t('Test count add successfully.'));
      } catch (e) {
        const data = e.response.data;
        this.isLoading = false;
        let message = this.$t('Test count add error.');
        if (data?.errors) {
          const keyFirstData = Object.keys(data?.errors)[0];
          const firstData = data.errors[keyFirstData][0];
          message = firstData;
        }
        this.notification(message, 'error');
      }
    },
    checkBarcodeNotFound() {
      if (typeof this.leftBoxs === 'undefined') {
        return false;
      }
      let barcodeNotFound = this.leftBoxs
        .filter((item) => item.is_success === false)
        .map((item) => item.barcode);
      if (barcodeNotFound.length) {
        return false;
      }
      return true;
    },
    validateNewBoxSku() {
      let checkFlag = true;
      this.resultBoxs.forEach(function (value, i) {
        if (value.sku === '' || typeof value.supply_id == 'undefined') {
          value.is_input = false;
          checkFlag = false;
        }
      });

      if (!checkFlag) {
        this.tableKey = makeid(8);
      }

      return checkFlag;
    },
    validateNewBoxQuantity() {
      let checkFlag = true;

      this.resultBoxs = this.resultBoxs?.map((result) => {
        console.log(result);
        let is_input_quantity_valid = true;
        if (
          !/^-?\d+$/.test(result.quantity) ||
          (/^-?\d+$/.test(result.quantity) && parseInt(result.quantity) <= 0)
        ) {
          is_input_quantity_valid = false;
          checkFlag = false;
        } else {
          is_input_quantity_valid = true;
        }

        return {
          ...result,
          is_input_quantity_valid,
        };
      });

      if (!checkFlag) {
        this.tableKey = makeid(8);
      }

      return checkFlag;
    },
    focusByElClassName(elClass) {
      this.$nextTick(() => {
        const el = document.getElementsByClassName(elClass);
        if (!el || !el.length) {
          return;
        }
        const firtsElError = (el && el[0]) || undefined;
        const input = firtsElError.querySelector('input');
        document.body.scrollTop = (input.offsetTop - 120) | 0;
        input.focus();
      });
    },
    closeSubmitComplete() {
      this.submitComplete = false;
    },
    countBarcodeTableLeft() {
      if (typeof this.leftBoxs === 'undefined') {
        return 0;
      } else {
        return this.leftBoxs.length;
      }
    },
    countBarcodeFoundTableLeft() {
      if (typeof this.leftBoxs === 'undefined') {
        return 0;
      }
      let barcodeFound = this.leftBoxs
        .filter((item) => item.is_success === true && item.is_moving !== 1)
        .map((item) => item.barcode);

      return barcodeFound.length;
    },
    countBarcodeMoveInTableLeft() {
      if (typeof this.leftBoxs === 'undefined') {
        return 0;
      }
      let barcodeMoveIn = this.leftBoxs
        .filter((item) => item.is_success === true && item.is_moving == 1)
        .map((item) => item.barcode);

      return barcodeMoveIn.length;
    },
    countBarcodeTableRight() {
      if (typeof this.resultBoxs === 'undefined') {
        return 0;
      } else {
        return this.resultBoxs.length;
      }
    },
    async submitCompleteWithValidate(formName) {
      if (!this.scanEmployeeID()) return;
      this.$refs[formName].validate((valid) => {
        if (!valid) {
          return;
        }
      });
      const validateNewBoxSkuFlag = this.validateNewBoxSku();
      if (!validateNewBoxSkuFlag) {
        let message = this.$t('Please scan SKU for new boxes.');
        this.notification(message, 'error');
        return;
      }

      const validateNewBoxQuantityFlag = this.validateNewBoxQuantity();
      if (!validateNewBoxQuantityFlag) {
        let message = this.$t(
          'New box quantity must be an integer and greater than 0.'
        );
        this.notification(message, 'error');
        return;
      }

      this.submitComplete = true;
    },
    async pauseTestCount(formName) {
      const isValid = await this.$refs[formName].validate();
      if (!isValid) {
        return;
      }
      this.isLoading = true;
      try {
        let data = {
          location_id: this.data.location_id,
          note: this.data.note,
        };
        let barcodeExited = this.leftBoxs
          .filter((item) => item.is_success === true && item.is_moving !== 1)
          .map((item) => item.barcode);
        barcodeExited = barcodeExited && barcodeExited.filter((item) => item);

        let barcodeMoving = this.leftBoxs
          .filter((item) => item.is_moving === 1)
          .map((item) => item);
        barcodeMoving = barcodeMoving && barcodeMoving.filter((item) => item);

        let barcodeNotFound = this.leftBoxs
          .filter((item) => item.is_success === false)
          .map((item) => item.barcode);
        barcodeNotFound =
          barcodeNotFound && barcodeNotFound.filter((item) => item);

        data.barcode_found = barcodeExited;
        data.barcode_moving = barcodeMoving;
        data.barcode_not_found = barcodeNotFound;
        data.barcode_new = this.resultBoxs;
        data.id_time_checking = this.id_time_checking;

        await pauseSupplyTestCount(data);
        this.submitComplete = false;
        this.$emit('refreshTestCountPause', true);
        this.isLoading = false;
        this.openDialogAddTestCount = false;
        this.submitPendingTestCount = false;
        this.notification(this.$t('Test count pause successfully.'));
      } catch (e) {
        const data = e.response.data;
        this.isLoading = false;
        let message = this.$t('Test count add pause error.');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.notification(message, 'error');
      }
    },
    closeSubmitPendingTestCount() {
      this.submitPendingTestCount = false;
    },
    async dialogSubmitPendingTestCount(formName) {
      const isValid = await this.$refs[formName].validate();
      if (!isValid) {
        return;
      }
      this.submitPendingTestCount = true;
    },

    async querySearchAsync(queryString, cb) {
      const results = queryString
        ? this.getLocationsByType
            .filter((item) =>
              item.value.toLowerCase().includes(queryString.toLowerCase())
            )
            .slice(0, 20)
        : this.getLocationsByType.slice(0, 20);
      cb(results);
    },
    handleSelect(item) {
      this.data.location = item.value;
      this.data.location_id = item.id;
      this.changeLocationNew();
      const locationRef = this.$refs.supplyLocationInputRef.$el;
      const childErr = locationRef.querySelector(
        '.el-form-item__content .el-form-item__error'
      );
      if (locationRef.classList.contains('is-error')) {
        locationRef.classList.remove('is-error');
      }
      // Remove the child element if it exists
      if (childErr) {
        childErr.remove();
      }
    },
  },
};
