<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ $t('Supply Test Count') }}</h1>
      </div>
      <div class="top-head-right">
        <el-button type="primary" @click="openListTestCountPause">
          <span class="icon-margin-right"><icon :data="iconHistory" /></span
          >{{ $t('Pending Test Count') }}
        </el-button>
        <el-button type="primary" @click="createTestCount('')">
          <span class="icon-margin-right"><icon :data="iconAdd" /></span
          >{{ $t('Create') }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <div class="filter">
        <div class="label">{{ $t('Filter by:') }}</div>
        <div class="filter-item">
          <el-dropdown
            ref="location"
            trigger="click"
            class="el-dropdown-filter-item"
            :class="{ 'is-active': hasChangeFilterByItem('location') }"
          >
            <span class="el-dropdown-link">
              <template v-if="hasChangeFilterByItem('location')">
                <el-tooltip
                  effect="dark"
                  :content="$t('Location')"
                  placement="top-start"
                >
                  <span>{{ filter.location }}</span>
                </el-tooltip>
              </template>
              <template v-else>{{ $t(' Location ') }}</template>
              <span class="icon">
                <icon :data="iconChevronDown" />
              </span>
            </span>
            <template #dropdown>
              <div class="el-dropdown-menu-filter-item">
                <el-input
                  :placeholder="$t('Enter search keyword')"
                  class="search"
                  v-model="filter.location"
                  @keydown.enter="onFilter('location')"
                  clearable
                  @clear="clearFilterItem('location')"
                />
              </div>
            </template>
          </el-dropdown>
        </div>
        <div class="filter-item" v-if="hasFilter">
          <el-link type="danger" @click="onClearFilter" :underline="false"
            >{{ $t('Clear') }}
          </el-link>
        </div>
      </div>
      <el-table
        stripe
        size="small"
        border
        :data="items"
        style="width: 100%"
        :max-height="maxHeight"
        v-loading="isLoading"
        element-loading-text="Loading..."
        :row-class-name="tableRowClassName"
      >
        <el-table-column :label="$t('ID')" width="100">
          <template #default="scope">
            <el-link
              :underline="false"
              type="primary"
              @click="viewDetailTestCount(scope.row)"
            >
              {{ scope.row.id }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column :label="$t('Date')" min-width="200">
          <template #default="scope">
            {{ utcToLocalTime(scope.row.created_at).format('lll') }}
            <!--            {{ scope.row.created_at }}-->
          </template>
        </el-table-column>
        <el-table-column :label="$t('Location')" min-width="150">
          <template #default="scope">
            {{ scope.row?.supply_location?.barcode }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Boxes Available')" min-width="150">
          <template #default="scope">
            {{ scope.row.box_available }}
          </template>
        </el-table-column>
        <el-table-column label="Boxes On-hand" min-width="150">
          <template #default="scope">
            {{ scope.row.box_on_hand }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Adjustment')" min-width="150">
          <template #default="scope">
            {{ scope.row.box_on_hand - scope.row.box_available }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Employee')" min-width="150">
          <template #default="scope">
            {{ scope.row.employee?.name }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Note')">
          <template #default="scope">
            {{ scope.row.note }}
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom">
        <div class="total">
          {{ $t('Total:') }} {{ items.length ? formatNumber(total) : 0 }}
        </div>
        <el-pagination
          :disabled="isLoading"
          background
          layout="prev, pager, next"
          :page-size="filter.limit"
          :total="total"
          @current-change="changePage"
          v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
            v-model="filter.limit"
            :placeholder="$t('Select')"
            size="mini"
            @change="onFilter"
          >
            <el-option
              v-for="item in limits"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
    <CreateTestCount
      @refresh="fetchData"
      @refreshTestCountPause="fetchDataTestCountPause"
      :employees="employees"
    />
    <view-detail-test-count />
    <el-dialog
      v-model="listTestCountPause"
      @close="closeDialogTestCount"
      width="80%"
    >
      <template #title> {{ $t('Pending Test Count') }} </template>
      <template #default>
        <el-table
          border
          :data="allTestCountPause"
          style="width: 100%"
          class="el-table-expand-purchase-order-box"
          v-loading="isLoading"
          height="500"
          :row-class-name="tableRowClassName"
        >
          <el-table-column :label="$t('Location')" min-width="150">
            <template #default="scope">
              {{ scope.row.location }}
            </template>
          </el-table-column>

          <el-table-column :label="$t('Box found')" min-width="100">
            <template #default="scope">
              {{
                scope?.row?.data?.barcode_found
                  ? scope.row.data.barcode_found.length
                  : ''
              }}
            </template>
          </el-table-column>

          <el-table-column :label="$t('Box moving')" min-width="100">
            <template #default="scope">
              {{
                scope?.row?.data?.barcode_moving
                  ? scope.row.data.barcode_moving.length
                  : ''
              }}
            </template>
          </el-table-column>

          <el-table-column :label="$t('Box new')" min-width="100">
            <template #default="scope">
              {{
                scope?.row?.data?.barcode_new
                  ? scope.row.data.barcode_new.length
                  : ''
              }}
            </template>
          </el-table-column>

          <el-table-column :label="$t('Box not found')" min-width="120">
            <template #default="scope">
              {{
                scope?.row?.data?.barcode_not_found
                  ? scope.row.data.barcode_not_found.length
                  : ''
              }}
            </template>
          </el-table-column>

          <el-table-column :label="$t('Created at')" width="200">
            <template #default="scope">
              {{ utcToLocalTime(scope.row.created_at).format('lll') }}
            </template>
          </el-table-column>

          <el-table-column :label="$t('Updated at')" width="200">
            <template #default="scope">
              {{ utcToLocalTime(scope.row.updated_at).format('lll') }}
            </template>
          </el-table-column>

          <el-table-column :label="$t('Action')" width="200">
            <template #default="scope">
              <el-button type="primary" @click="createTestCount(scope.row)">{{
                $t('Continue')
              }}</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="bottom">
          <div class="total">
            {{ $t('Total:') }}
            {{
              allTestCountPause.length ? formatNumber(totalTestCountPause) : 0
            }}
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
