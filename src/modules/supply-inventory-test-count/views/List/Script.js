import { list, allTestCountPause } from '@/api/supplyTestCount.js';
import EventBus from '@/utilities/eventBus.js';
import CreateTestCount from '@/modules/supply-inventory-test-count/views/Create/Index.vue';
import { equals } from 'ramda';
import { mapGetters } from 'vuex';
import ViewDetailTestCount from '@/modules/supply-inventory-test-count/components/ViewDetailTestCount.vue';

export default {
  name: 'TestCount',
  components: {
    CreateTestCount,
    ViewDetailTestCount,
  },
  data() {
    return {
      items: [],
      isLoading: false,
      filter: this.setDefaultFilter(),
      listTestCountPause: false,
      allTestCountPause: [],
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 230);
    },
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },
    ...mapGetters({
      getUsers: 'getUsers',
      employees: 'getEmployees',
    }),
  },
  beforeUnmount() {
    EventBus.$off('showCreateTestCount');
    EventBus.$off('showViewDetailTestCount');
  },
  mounted() {
    this.fetchData();
    this.fetchDataTestCountPause();
  },
  methods: {
    async fetchEmployee() {
      await this.$store.dispatch('getEmployees');
    },
    getUserNameById(id) {
      const item = this.getUsers.find((item) => +item.id === +id);
      return item && item.username;
    },
    async fetchUser() {
      await this.$store.dispatch('getUsers');
    },
    onFilter(item = '') {
      this.filter.page = 1;
      this.$nextTick(() => {
        this.fetchTestCount();
        if (item) {
          this.$refs[item].handleClose();
        }
      });
    },
    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.$nextTick(() => {
        this.fetchTestCount();
      });
    },
    onChangeDate() {
      if (this.filter.date && this.filter.date.length) {
        this.filter.date[0] = this.formatDate(this.filter.date[0], false);
        this.filter.date[1] = this.formatDate(this.filter.date[1], false);
        this.onFilter();
      }
    },
    clearFilterItem(item) {
      this.filter[item] = '';
      this.$refs[item].handleClose();
      this.onFilter();
    },
    setDefaultFilter() {
      let params = {
        limit: 25,
        page: 1,
        location: '',
      };
      return params;
    },
    setRouteParam() {
      const params = this.filter;
      this.$router.replace({ name: 'supply_test_count', query: params });
    },
    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);
      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }
      filter.page = +filter.page || 1;
      return filter;
    },
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchTestCount();
      });
    },
    createTestCount(data) {
      EventBus.$emit('showCreateTestCount', data);
      this.listTestCountPause = false;
    },
    async fetchData() {
      this.filter = this.getRouteParam();
      this.fetchEmployee();
      this.fetchTestCount();
    },
    async fetchTestCount() {
      this.isLoading = true;
      this.setRouteParam();
      const res = await list(this.filter);
      this.isLoading = false;
      const data = res.data || [];
      this.total = data.total;
      this.items = data.data;
    },
    async deleteTestCount(item) {
      await revert({ id: item.id });
      this.fetchData();
    },
    tableRowClassName(data) {
      return data.row.is_deleted ? 'is-delete' : '';
    },
    hasChangeFilterByItem(name) {
      const query = this.$route.query;
      if (query[name]) {
        return true;
      }
      return false;
    },
    viewDetailTestCount(item) {
      EventBus.$emit('showViewDetailTestCount', item);
    },
    openListTestCountPause() {
      this.listTestCountPause = true;
    },
    closeDialogTestCount() {
      this.listTestCountPause = false;
    },
    async fetchDataTestCountPause() {
      const res = await allTestCountPause();
      this.isLoading = false;
      const dataTestCountPause = res.data || [];
      this.totalTestCountPause = dataTestCountPause.total;
      this.allTestCountPause = dataTestCountPause.data;
    },
  },
};
