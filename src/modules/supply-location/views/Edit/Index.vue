<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
      v-model="openDialogEditLocation"
      :title="$t('Edit location')"
      custom-class="el-dialog-custom el-dialog-location"
      @close="resetData"
      :close-on-click-modal="false"
    >
      <template #default>
        <div class="add-location">
          <el-form
            status-icon
            ref="editLocationForm"
            :model="location"
            :rules="locationRules"
            @submit.prevent="onSubmit('editLocationForm')"
            label-width="130px"
            :label-position="'left'"
          >
            <el-form-item
              :label="$t('Location ID')"
              prop="barcode"
              @keyup.enter="onSubmit('editLocationForm')"
            >
              <el-input
                v-model="location.barcode"
                type="text"
                autocomplete="off"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            @click="onSubmit('editLocationForm')"
            :disabled="isLoading"
            :loading="isLoading"
            >{{ $t('Update') }}</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>
