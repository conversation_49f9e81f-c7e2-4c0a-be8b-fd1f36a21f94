import EventBus from "@/utilities/eventBus.js";
import { update } from "@/api/supplyLocation.js";
import { isEmpty } from "ramda";

export default {
  name: "LocationEdit",
  components: {},
  data() {
    return {
      location: {},
      isLoading: false,
      openDialogEditLocation: false,
      locationRules: {
        barcode: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: "blur",
          },
        ],
      },
    };
  },
  mounted() {},
  created() {
    EventBus.$on("showEditLocation", (data) => {
      this.openDialogEditLocation = true;
      this.location = {
        ...data,
      };
    });
  },
  methods: {
    resetData() {
      this.$refs["editLocationForm"].resetFields();
      this.location = {
        barcode: "",
        type: "",
      };
    },
    async onSubmit(formName) {
      const isValid = await this.$refs[formName].validate();
      if (!isValid) {
        return;
      }
      this.isLoading = true;
      const dataUpdate = {
        "barcode": this.location.barcode,
        "id" : this.location.id,
      }
      try {
        await update(dataUpdate);
        this.isLoading = false;
        this.notification(this.$t('Location update successfully.'));
        this.openDialogEditLocation = false;
        this.$emit("refresh");
      } catch (e) {
        const data = e.response.data;
        this.isLoading = false;
        let message = this.$t('Location update error.');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data.errors)[0];
          const firstData = data.errors[keyFirstData];
          message = firstData[0];
        }
        this.notification(message, "error");
      }
    },
  },
};
