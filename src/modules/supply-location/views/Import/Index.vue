<script src="./Script.js"></script>
<template>
  <div>
    <el-dialog
      v-model="openDialogImportLocation"
      :title="$t('Import Supply Location')"
      custom-class="el-dialog-custom el-dialog-location"
      @close="resetData"
      :close-on-click-modal="false"
    >
      <template #default>
        <div class="add-location">
          <el-form
            status-icon
            ref="importLocationForm"
            :model="location"
            :rules="locationRules"
            @submit.prevent="onSubmit('importLocationForm')"
            label-width="130px"
            :label-position="'left'"
          >
            <el-form-item :label="$t('LocationID')" prop="items">
              <el-input
                v-model="location.items"
                :rows="6"
                type="textarea"
                :placeholder="'Location 1\nLocation 2\nLocation 3'"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            @click="onSubmit('importLocationForm')"
            :disabled="isLoading"
            :loading="isLoading"
            >{{ $t('Import') }}</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>
