import EventBus from "@/utilities/eventBus.js";
import { importLocation } from "@/api/supplyLocation.js";
import { isEmpty } from "ramda";

export default {
  name: "LocationImport",
  components: {},
  data() {
    return {
      location: {},
      isLoading: false,
      openDialogImportLocation: false,
      locationRules: {
        items: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: "blur",
          },
        ],
      },
    };
  },
  mounted() {},
  created() {
    EventBus.$on("showImportLocation", () => {
      this.openDialogImportLocation = true;
    });
  },
  methods: {
    resetData() {
      this.$refs["importLocationForm"].resetFields();
      this.location = {
        items: "",
      };
    },
    async onSubmit(formName) {
      const isValid = await this.$refs[formName].validate();
      if (!isValid) {
        return;
      }
      this.isLoading = true;
      try {
        await importLocation(this.location);
        this.isLoading = false;
        this.notification(this.$t('Location import successfully.'));
        this.openDialogImportLocation = false;
        this.$emit("refresh");
      } catch (e) {
        const data = e.response.data;
        this.isLoading = false;
        let message = data?.message ?? this.$t('Location create error.');
        if (typeof data.errors == 'undefined') {
          this.notification(message, "error");
          return;
        }

        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data.errors)[0];
          const firstData = data.errors[keyFirstData];
          message = firstData[0] ?? data.message;
          this.notification(message, "error");
          return;
        }
      }
    },
  },
};
