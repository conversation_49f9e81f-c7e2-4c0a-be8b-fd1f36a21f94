<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left">
        <h1>{{ $t("Supply Locations") }}</h1>
      </div>
      <div class="top-head-right">
        <el-button @click="importLocation">{{ $t(" Import") }}</el-button>
      </div>
    </div>
    <div class="table-content mt-5">
      <div class="filter">
        <el-input
          :placeholder="$t('Search location id')"
          class="search"
          v-model="filter.barcode"
          @keyup.enter="onFilter"
        />
        <div class="btn-filter ml-3">
          <template v-if="hasFilter">
            <el-link type="danger" @click="onClearFilter" :underline="false"
              >{{ $t("Clear") }}
            </el-link>
          </template>
          <el-button type="primary" @click="onFilter">
            <span class="icon-margin-right"><icon :data="iconFilter" /></span
            >{{ $t("Filter") }}
          </el-button>
        </div>
      </div>
      <el-table
        border
        stripe
        size="small"
        :data="items"
        style="width: 100%"
        :max-height="maxHeight"
        v-loading="isLoading"
        element-loading-text="Loading..."
      >
        <el-table-column prop="barcode" :label="$t('Location')" min-width="150">
          <template #default="scope">
            {{ scope.row.barcode }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Box')" min-width="150">
          <template #default="scope">
            <router-link
              :to="{
                path: '/supply-box',
                query: {
                  location_id: scope.row.barcode,
                  limit: 25,
                  page: 1,
                },
              }"
            >
              {{
                scope.row.total_box !== null ? scope.row.total_box : 0
              }}
            </router-link>
          </template>
        </el-table-column>
        <el-table-column
          prop="total product"
          :label="$t('Product')"
          min-width="150"
        >
          <template #default="scope">
            {{
              scope.row.total_quantity !== null ? scope.row.total_quantity : 0
            }}
          </template>
        </el-table-column>
        <el-table-column
          prop="created at"
          :label="$t('Created at')"
          min-width="150"
        >
          <template #default="scope">
            {{
              scope.row?.created_at
                ? utcToLocalTime(scope.row.created_at).format("lll")
                : ""
            }}
          </template>
        </el-table-column>
        <el-table-column
          prop="updated_at"
          :label="$t('Updated at')"
          min-width="200"
        >
          <template #default="scope">
            {{
              scope.row?.updated_at
                ? utcToLocalTime(scope.row.updated_at).format("lll")
                : ""
            }}
          </template>
        </el-table-column>

        <el-table-column prop="action" :label="$t('Action')" width="80">
          <template #default="scope">
            <el-link
              class="el-link-edit mr-2"
              :underline="false"
              type="primary"
              @click="editLocation(scope.row)"
            >
              <icon :data="iconEdit" />
            </el-link>

            <el-popconfirm
              :title="'Are you sure to delete ' + scope.row.barcode + '?'"
              @confirm="deleteLocation(scope.row)"
            >
              <template #reference>
                <el-link :underline="false" type="danger">
                  <icon :data="iconDelete" />
                </el-link>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom">
        <div class="total">{{$t('Total:')}} {{ items.length ? formatNumber(total) : 0 }}</div>
        <el-pagination
          :disabled="isLoading"
          background
          layout="prev, pager, next"
          :page-size="filter.limit"
          :total="total"
          @current-change="changePage"
          v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
            v-model="filter.limit"
            :placeholder="$t('Select')"
            size="mini"
            @change="onFilter"
          >
            <el-option
              v-for="item in limits"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
    <EditLocation @refresh="fetchData" />
    <ImportLocation @refresh="fetchData" />
  </div>
</template>
