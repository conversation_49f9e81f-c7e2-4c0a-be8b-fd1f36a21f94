import { list, destroy } from "@/api/supplyLocation.js";
import EventBus from "@/utilities/eventBus.js";
import EditLocation from "@/modules/supply-location/views/Edit/Index.vue";
import ImportLocation from "@/modules/supply-location/views/Import/Index.vue";
import filterMixin from "@/mixins/filter";
import { equals } from "ramda";
import dateMixin from "@/mixins/date";

export default {
  name: "Location",
  mixins: [filterMixin, dateMixin],
  components: {
    EditLocation,
    ImportLocation,
  },
  data() {
    return {
      items: [],
      isLoading: false,
      filter: this.setDefaultFilter(),
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 230);
    },
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },
  },
  beforeUnmount() {
    EventBus.$off("showCreateLocation");
    EventBus.$off("showEditLocation");
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    onFilter() {
      this.filter.page = 1;
      this.$nextTick(() => {
        this.fetchLocation();
      });
    },
    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.$nextTick(() => {
        this.fetchLocation();
      });
    },
    setDefaultFilter() {
      let params = {
        limit: 25,
        barcode: "",
        page: 1,
      };
      return params;
    },
    setRouteParam() {
      const params = this.filter;
      console.log(params);
      this.$router.replace({ name: "supply_location", query: params });
    },
    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);
      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }
      filter.page = +filter.page || 1;
      return filter;
    },
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchLocation();
      });
    },
    createLocation() {
      EventBus.$emit("showCreateLocation");
    },
    importLocation() {
      EventBus.$emit("showImportLocation");
    },
    async fetchData() {
      this.filter = this.getRouteParam();
      this.fetchLocation();
    },
    async fetchLocation() {
      this.isLoading = true;
      this.setRouteParam();
      const res = await list(this.filter);
      this.isLoading = false;
      const data = res.data || [];
      this.total = data.total;
      this.items = data.data;
    },
    editLocation(item) {
      EventBus.$emit("showEditLocation", item);
    },
    async deleteLocation(item) {
      try {
        await destroy(item.id);
        this.notification(this.$t("Delete location successfully."));
        this.fetchData();
      } catch (e) {
        const data = e.response.data;
        let message = data?.message || this.$t("Delete fail.");
        this.notification(message, "error");
      }
    },
  },
};
