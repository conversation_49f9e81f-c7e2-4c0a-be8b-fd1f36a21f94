<script src="./Script.js"></script>
<template>
  <div>
    <div class="top-head">
      <div class="top-head-left"><h1>{{ $t('Manage Boxes') }}</h1></div>
     <div class="top-head-right">

     </div>
    </div>
    <div class="table-content mt-5">
      <div class="filter">
        <el-select
            :placeholder="$t('Supply Name')"
            class="mr-2"
            v-model="filter.supply_name"
            @change="onFilter"
            filterable
            clearable
        >
          <el-option
              v-for="item in allSupplyData"
              :key="item.id"
              :label="item.name"
              :value="item.name"
          >
          </el-option>
        </el-select>

        <el-input
          :placeholder="$t('Supply SKU')"
          class="search mr-2"
          v-model="filter.supply_sku"
          style="width: 200px"
          clearable
          @keyup.enter="onFilter"
        />

        <el-input
          :placeholder="$t('Search Box ID')"
          class="search mr-2"
          style="width: 200px"
          v-model="filter.box_id"
          clearable
          @keyup.enter="onFilter"
        />

        <el-select
            :placeholder="$t('Select Location')"
            class="mr-2"
            v-model="filter.location_id"
            @change="onFilter"
            filterable
            clearable
        >
          <el-option
              v-for="item in allLocationData"
              :key="item.id"
              :label="item.barcode"
              :value="item.barcode"
          >
          </el-option>
        </el-select>

        <div class="btn-filter">
          <template v-if="hasFilter">
            <el-link type="danger" @click="onClearFilter" :underline="false"
              >{{ $t('Clear') }}</el-link
            >
          </template>
          <el-button type="primary" @click="onFilter">
            <span class="icon-margin-right"><icon :data="iconFilter" /></span
            >{{ $t('Filter') }}
          </el-button>
        </div>
      </div>
      <el-table
        border
        stripe size="small" :data="items"
        style="width: 100%"
        :max-height="maxHeight"
        v-loading="isLoading"
        element-loading-text="Loading..."
      >
        <el-table-column fixed="" prop="barcode" :label="$t('Box ID')" min-width="150">
          <template #default="scope">
              {{ scope.row.barcode }}
          </template>
        </el-table-column>
        <el-table-column prop="location" :label="$t('Location')" min-width="150">
          <template #default="scope">
            {{ scope.row?.location?.barcode }}
          </template>
        </el-table-column>
        <el-table-column prop="product_name" :label="$t('Supply Name')" min-width="300">
          <template #default="scope">
            {{ scope.row?.supply?.name }}
          </template>
        </el-table-column>
        <el-table-column prop="product_sku" :label="$t('Supply SKU')" min-width="150">
          <template #default="scope">
            {{ scope.row?.supply?.sku }}
          </template>
        </el-table-column>
        <el-table-column prop="quantity" :label="$t('Quantity')" width="100">
          <template #default="scope">
            {{ scope.row.quantity }}
          </template>
        </el-table-column>
        <el-table-column prop="country" :label="$t('COO')" width="100" v-if="warehouse_id === 18">
          <template #default="scope">
            <span class="break-normal">
               {{ scope.row?.inventory_addition?.country?.name }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" :label="$t('Created At')" min-width="200">
          <template #default="scope">
            {{ utcToLocalTime(scope.row.created_at).format('lll') }}
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom">
        <div class="total">{{$t('Total:')}} {{ items.length ? formatNumber(total) : 0 }}</div>
        <el-pagination
          :disabled="isLoading"
          background
          layout="prev, pager, next"
          :page-size="filter.limit"
          :total="total"
          @current-change="changePage"
          v-model:currentPage="filter.page"
        >
        </el-pagination>
        <div class="limit" :disabled="isLoading">
          <el-select
            v-model="filter.limit"
            :placeholder="$t('Select')"
            size="mini"
            @change="onFilter"
          >
            <el-option
              v-for="item in limits"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
  </div>
</template>
