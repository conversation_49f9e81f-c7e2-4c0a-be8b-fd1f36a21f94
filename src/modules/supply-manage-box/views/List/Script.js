import { list, getAllSupplyList, getSupplyLocationList } from "@/api/supplyBox.js";
import { getProductAttributes } from "@/api/product";
import {equals} from "ramda";
import {mapGetters} from "vuex";
import warehouseMixin from '@/mixins/warehouse';

export default {
  name: "SupplyBox",
  mixins: [warehouseMixin],
  data() {
    return {
      items: [],
      isLoading: false,
      allSupplyData: [],
      allLocationData: [],
      filter: this.setDefaultFilter(),
      types: [
        {
          label: this.$t('Box ID'),
          value: "box_id",
        },
        {
          label: this.$t('Location'),
          value: "location",
        },
        {
          label: this.$t('Supply Name'),
          value: "supply_name",
        },
        {
          label: this.$t('Supply SKU'),
          value: "supply_sku",
        },
      ],
      styleOptions: [],
      warehouse_id : null
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 230);
    },
    hasFilter() {
      const defaultFilter = this.setDefaultFilter();
      return !equals(defaultFilter, this.filter);
    },
    ...mapGetters({
      employees: "getEmployees"
    })
  },
  mounted() {
    this.warehouse_id = this.getWarehouseId();
    this.getProductAttributes();
    this.getAllSupply();
    this.fetchData();
    this.fetchEmployee();
    this.getSupplyLocationList();
  },
  methods: {
    async getSupplyLocationList() {
      const res = await getSupplyLocationList({ is_fetch_all: true});
      this.allLocationData = res?.data ?? [];
    },
    async fetchEmployee() {
      await this.$store.dispatch("getEmployees");
    },
    async getAllSupply() {
      const params = {
        without_pagination: true
      }
      const res = await getAllSupplyList(params);
      this.allSupplyData = res?.data || [];
    },
    onFilter() {
      this.filter.page = 1;
      this.$nextTick(() => {
        this.fetchBox();
      });
    },
    onClearFilter() {
      this.filter = this.setDefaultFilter();
      this.$nextTick(() => {
        this.fetchBox();
      });
    },
    setDefaultFilter() {
      return {
        supply_name: "",
        supply_sku: "",
        box_id: "",
        location_id: "",
        limit: 25,
        page: 1,
      };
    },
    setRouteParam() {
      const params = this.filter;
      this.$router.replace({ name: "supply_box", query: params });
    },
    async getProductAttributes() {
      const { data } = await getProductAttributes();
      this.styleOptions = data || [];
    },
    getRouteParam() {
      let routeQuery = this.$route.query || {};
      let filter = this.setDefaultFilter();
      filter = {
        ...filter,
        ...routeQuery,
      };
      filter.limit = +filter.limit || 10;
      const scopeLimit = this.limits.map((item) => item.value);
      if (!scopeLimit.includes(filter.limit)) {
        filter.limit = 10;
      }
      filter.page = +filter.page || 1;
      return filter;
    },
    changePage(page) {
      this.filter.page = page;
      this.$nextTick(() => {
        this.fetchBox();
      });
    },
    async fetchData() {
      this.filter = this.getRouteParam();
      this.fetchBox();
    },
    async fetchBox() {
      this.isLoading = true;
      this.setRouteParam();
      const res = await list(this.filter);
      this.isLoading = false;
      const data = res.data || [];
      this.total = data.total;
      this.items = data.data;
    },
  },
};
