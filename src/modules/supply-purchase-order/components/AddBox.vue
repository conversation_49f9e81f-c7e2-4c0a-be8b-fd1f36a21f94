<template>
  <div class="add-purchase-order">
    <el-dialog
      v-model="openDialogAddBox"
      custom-class="el-dialog-custom el-dialog-purchase-order"
      :close-on-click-modal="false"
      width="60%"
    >
      <template #title>
        Add Box {{ purchaseOrderItem && purchaseOrderItem.po_number }}
      </template>
      <template #default>
        <el-form
          ref="addBoxPurchaseOrderForm"
          label-width="200px"
          :label-position="'left'"
          :model="data"
          :rules="rules"
          @submit.prevent="onSubmit('addBoxPurchaseOrderForm')"
        >
          <el-form-item
            :label="$t('Box Number')"
            prop="box_number"
            :class="{
              'is-error': serverError && serverError['box_number'],
            }"
          >
            <el-input v-model="data.box_number"></el-input>
          </el-form-item>
          <!-- <el-form-item
            :label="$t('Invoice Number')"
            prop="invoice_number"
            :class="{
              'is-error': serverError && serverError['invoice_number'],
            }"
          >
            <el-input
              @input="resetInput('invoice_number')"
              v-model="data.invoice_number"
            ></el-input>
          </el-form-item> -->
          <el-form-item
            :label="$t('Tracking Number')"
            prop="tracking_number"
            :class="{
              'is-error': serverError && serverError['tracking_number'],
            }"
          >
            <el-input
              @input="resetInput('tracking_number')"
              v-model="data.tracking_number"
            ></el-input>
          </el-form-item>
          <el-form-item :label="$t('Select product')">
            <el-select
              :placeholder="$t('Select product')"
              class="el-select-managment"
              filterable
              @change="selectProduct"
            >
              <el-option
                v-for="item in purchaseOrderProducts"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <div>
            <el-table
              border
              :data="products"
              style="width: 100%"
              :max-height="400"
              size="small"
            >
              <el-table-column fixed prop="name" :label="$t('Name')" min-width="200">
                <template #default="scope">
                  <div>
                    {{ scope.row.name }}
                  </div>
                  <div v-if="scope.row.sku">SKU: {{ scope.row.sku }}</div>
                  <div v-if="scope.row.gtin">GTIN: {{ scope.row.gtin }}</div>
                </template>
              </el-table-column>
              <el-table-column fixed prop="quantity" :label="$t('Quantity')">
                <template #default="scope">
                  <el-input-number
                    size="small"
                    v-model="scope.row.quantity"
                    :precision="0"
                    :step="1"
                    :min="0"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="action" :label="$t('Action')" width="80">
                <template #default="scope">
                  <el-popconfirm
                    :title="'Are you sure to delete ' + scope.row.name + '?'"
                    @confirm="removeProductItem(scope.$index)"
                  >
                    <template #reference>
                      <el-link :underline="false" type="danger"
                        ><icon :data="iconDelete"
                      /></el-link>
                    </template>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
            <div class="flex justify-end mt-2">
              <el-button
                type="primary"
                class="el-button-select-product"
                @click="onSubmit('addBoxPurchaseOrderForm')"
                :disabled="isLoading"
                :loading="isLoading"
                >{{ $t('Save') }}</el-button
              >
            </div>
          </div>
        </el-form>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import EventBus from "@/utilities/eventBus.js";
import { addBox, fetchProductsPurchaseOrder } from "@/api/purchaseOrder.js";
import { isEmpty } from "ramda";

export default {
  name: "AddBox",
  components: {},
  props: {},
  data() {
    return {
      items: [],
      openDialogAddBox: false,
      isLoading: false,
      data: {},
      rules: {},
      products: [],
      purchaseOrderItem: "",
      serverError: "",
      purchaseOrderProducts: [],
    };
  },
  watch: {},
  created() {
    EventBus.$on("showAddBox", (data) => {
      this.openDialogAddBox = true;
      this.data = this.setDefaultData();
      this.products = [];
      this.purchaseOrderItem = data;
      this.fetchProductsPurchaseOrder();
    });
  },
  computed: {},
  mounted() {},
  methods: {
    resetInput(key) {
      if (this.serverError && this.serverError[key]) {
        this.serverError[key] = "";
      }
    },
    async fetchProductsPurchaseOrder() {
      if (!this.purchaseOrderItem || !this.purchaseOrderItem.id) {
        return;
      }
      const res = await fetchProductsPurchaseOrder(this.purchaseOrderItem.id);
      this.purchaseOrderProducts = res.data || [];
    },
    removeProductItem(index) {
      this.products.splice(index, 1);
    },
    selectProduct(id) {
      const product = this.purchaseOrderProducts.find(
        (item) => +item.id === +id
      );
      this.selectProducts([product]);
    },
    selectProducts(products) {
      products = products.map((item) => {
        return {
          name: item.name,
          sku: item.sku,
          gtin: item.gtin,
          quantity: item.gtin_case && item.gtin_case > 0 ? item.gtin_case : 1,
          product_id: item.id,
        };
      });
      if (this.products.length) {
        this.products.forEach((item) => {
          products.forEach((newItem) => {
            if (item.product_id === newItem.product_id) {
              item.quantity += 1;
              newItem.remove = 1;
            }
          });
        });
        products = products.filter((item) => item.remove !== 1);
        this.products = [...this.products, ...products];
      } else {
        this.products = products;
      }
    },
    setDefaultData() {
      return {
        box_number: "",
        invoice_number: "",
        tracking_number: "",
      };
    },
    async onSubmit(formName) {
      const isValid = await this.$refs[formName].validate();
      if (!isValid) {
        return;
      }
      if (!this.products || !this.products.length) {
        this.notification(this.$t('Product is required.'), "error");
        return;
      }
      let params = this.data;
      params.po_id = this.purchaseOrderItem.id;
      const products = this.products.map((item) => {
        return {
          product_id: item.product_id,
          sku: item.sku,
          gtin: item.gtin,
          quantity: item.quantity,
        };
      });
      params.items = products;

      this.isLoading = true;
      try {
        await addBox(params);
        this.isLoading = false;
        this.$emit("onRefresh");
        this.notification(this.$t('Purchase order add box success.'));
        this.openDialogAddBox = false;
      } catch (e) {
        this.isLoading = false;
        let message = this.$t('Purchase order add box error.');
        const data = e.response.data || {};
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
          let serverError = {};
          Object.keys(data).forEach((key) => {
            serverError[key] = {};
            if (data[key]) {
              data[key].forEach((item) => {
                serverError[key][item] = item;
              });
            }
          });
          this.serverError = serverError;
        }
        this.notification(message, "error");
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
