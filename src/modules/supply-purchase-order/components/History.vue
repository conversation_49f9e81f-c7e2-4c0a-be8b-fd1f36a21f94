<template>
  <div class="history">
    <h4>{{ $t("History") }}</h4>
    <div class="create-message">
      <div v-if="showMessage">
        <div class="create-message-group">
          <el-input
            class="el-input-create-message"
            clearable
            v-model="message"
            :rows="2"
            type="textarea"
            :placeholder="$t('Message ...')"
            :autosize="{ minRows: 2, maxRows: 4 }"
          />
          <el-link
            class="clear-create-message"
            type="primary"
            @click="resetCreateMessage"
            :underline="false"
            ><span><icon :data="iconClose" /></span
          ></el-link>
        </div>
        <el-button
          type="primary"
          size="small"
          @click="createMessage"
          :disabled="isLoading"
          :loading="isLoading"
          ><span><icon :data="iconAdd" /></span
          >{{ $t("Add comment") }}</el-button
        >
      </div>
      <el-link
        class="el-link-create-message"
        type="primary"
        v-else
        @click="showCreateMessage"
        :underline="false"
        :disabled="disabledEdit"
        ><span><icon :data="iconAdd" /></span>{{ $t(" Add comment") }}</el-link
      >
    </div>
    <el-timeline class="timeline">
      <el-timeline-item
        v-for="(item, index) in purchaseOrder.history"
        :key="index"
        :type="'primary'"
        :hollow="true"
        :timestamp="item.created_at"
        :hide-timestamp="true"
      >
        <div style="white-space: pre-line" v-html="getMessage(item)"></div>
        <div class="el-timeline-item__content mt-1" v-if="item.message">
          {{ item.message }}
        </div>
        <div class="el-timeline-item__timestamp is-bottom">
          <span class="user" v-if="item && item.user"
            ><icon class="relative" style="top: -2px" :data="iconUser2" />
            {{ item.user.username }}</span
          >
          {{ item.created_at }}
        </div>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script>
import { addHistory, listHistory } from "@/api/supplyPurchaseOrder.js";
import { mapGetters } from "vuex";
import EventBus from "@/utilities/eventBus.js";
export default {
  name: "PurchaseOrderHistory",
  props: {
    purchaseOrderId: Number,
    disabledEdit: Boolean,
    purchaseOrder : {}
  },
  data() {
    return {
      message: "",
      showMessage: false,
      isLoading: false,
    };
  },
  created() {
  },
  computed: {
    ...mapGetters(["getUserProfile"]),
  },
  methods: {
    fetchDetail(){


    },
    showCreateMessage() {
      this.showMessage = true;
      this.$nextTick(() => {
        const elTextarea = document.getElementsByClassName(
          "el-input-create-message"
        );
        if (!elTextarea || !elTextarea.length) {
          return;
        }
        const firtsEl = (elTextarea && elTextarea[0]) || undefined;
        const input = firtsEl.querySelector("textarea");
        input.focus();
      });
    },
    resetCreateMessage() {
      this.showMessage = false;
      this.message = "";
    },
    getMessage(item) {
      let message = item.message;
      switch (item.type) {
        case "create":
          message = this.$t("Create Purchase Order");
          break;
        case "update":
          message = this.$t("Update Purchase Order");
          break;
        case "delete":
          message = this.$t("Delete Purchase Order");
          break;
        case "cancel":
          message = this.$t("Cancelled Purchase Order");
          break;
          case "comment":
          message = this.$t("Comment Purchase Order");
          break;
        default:
          message = item.message;
          break;
      }
      return message;
    },
    async createMessage() {
      let message = {
        message: this.message,
        po_id: this.purchaseOrderId,
      };
      this.isLoading = true;
      const res = await addHistory(message);
      const data = res.data;
      this.isLoading = false;

      message = {
        ...message,
        user: this.getUserProfile,
        ...data,
      };
      this.purchaseOrder.history.unshift(message);
      this.notification(this.$t("Add history successfully."));
      this.resetCreateMessage();
    },
  },
};
</script>

<style lang="scss" scoped>
.history {
  margin-bottom: 20px;
  .timeline {
    max-height: 250px;
    overflow-y: auto;
    .el-timeline-item__timestamp {
      display: flex;
      align-items: center;
      svg {
        max-height: 14px;
        position: relative;
        top: -2px;
      }
      .user {
        margin-right: 8px;
      }
    }
  }
  .create-message {
    margin-bottom: 20px;
  }
  .create-message-group {
    position: relative;
    margin-bottom: 8px;
    .clear-create-message {
      position: absolute;
      right: 20px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 2;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      border: 1px solid var(--el-border-color-light);
      color: var(--el-border-color-light);
      display: flex;
      align-items: center;
      justify-content: center;
      svg {
        max-height: 10px;
      }
      &:hover {
        color: var(--el-border-color-base);
        border-color: var(--el-border-color-base);
      }
    }
  }
  .el-link-create-message {
    svg {
      max-height: 12px;
      position: relative;
      top: -1px;
    }
  }
}
</style>
