<template>
  <div>
    <el-dialog
      v-model="modalProductSelect"
      :title="$t('Select supply')"
      custom-class="el-dialog-custom"
      :close-on-click-modal="false"
      width="40%"
    >
      <template #default>
        <div class="el-input-sticky">
          <el-select
            v-model="filter.supply"
            filterable
            clearable
            class="mr-2"
            :placeholder="$t('Select name or sku')"
          >
            <el-option :label="'Select supply'" :value="''" />
            <el-option
              v-for="item in supplies"
              :key="item.value"
              :label="item.name + ' (' + item.sku + ')'"
              :value="item.value"
            />
          </el-select>
        </div>
        <el-table
            border
            stripe
            size="small"
            :data="selectSupplies"
            style="width: 100%"
            element-loading-text="Loading..."
            v-if="selectSupplies && selectSupplies.length"
            class="mt-3"
        >
          <el-table-column prop="style" :label="$t('Supply')">
            <template #default="scope">
              {{ scope.row.name }}
            </template>
          </el-table-column>
          <el-table-column prop="style" :label="$t('SKU')">
            <template #default="scope">
              {{ scope.row.sku }}
            </template>
          </el-table-column>
          <el-table-column prop="color" :label="$t('Unit')">
            <template #default="scope">
              {{ scope.row.unit }}
            </template>
          </el-table-column>
          <el-table-column
              prop="action"
              :label="$t('Actions')"
              width="80"
              fixed="right"
              class="text-center"
          >
            <template #default="scope">
              <el-popconfirm
                  :title="'Are you sure to delete ?'"
                  @confirm="remove(scope.$index)"
              >
                <template #reference>
                  <el-link :underline="false" type="danger"
                  ><icon :data="iconDelete"
                  /></el-link>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </template>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="select">{{ $t('Add') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import EventBus from "@/utilities/eventBus.js";
import { getSuppLy } from "@/api/supplyPurchaseOrder";

export default {
  name: "ProductSelect",
  props: {},
  data() {
    return {
      modalProductSelect: false,
      selectSupplies: [],
      supplies: [],
      filter: this.clearFilter(),
    };
  },
  beforeUnmount() {
    EventBus.$off("productSelect");
    EventBus.$off("selectProducts");
  },
  computed: {
    hasFilter() {
      if (!this.filter.supply) {
        return false;
      }
      return true;
    },
  },
  watch: {
    hasFilter: {
      handler() {
        if (this.hasFilter) {
          const data = this.supplies.find((item) => item.value === this.filter.supply);
          const hasSupply = this.selectSupplies.find(
              (item) => item.value === data.value
          );
          if (hasSupply) {
            this.notification(this.$t('Supply already exists.'), "info");
            return;
          }
          this.selectSupplies.unshift(data);
        }
      },
      deep: true,
    },
  },
  created() {
    EventBus.$on("productSelect", () => {
      this.setDefaultFilter();
      this.modalProductSelect = true;
    });
  },
  mounted() {
    this.fetchProductAttribute();
  },
  methods: {
    remove(index) {
      this.selectSupplies.splice(index, 1);
    },
    async getProductByParams() {
      const res = await getProductByParams(this.filter);
      const data = res.data || {};
      if (!data || !data.id) {
        this.notification(this.$t('Product is not found.'), "error");
        return;
      }

      const hasProduct = this.selectProducts.find(
        (item) => item.id === data.id
      );

      if (hasProduct) {
        this.notification(this.$t('Product already exists.'), "info");
        return;
      }

      this.filter = this.clearFilter();
      this.selectProducts.unshift(data);
    },
    async fetchProductAttribute() {
      const res = await getSuppLy();
      const data = res.data || [];
      let supplies = [];
      supplies =
          data.length &&
          data.map((item) => {
          return {
            name: item.name,
            value: item.id,
            unit : item.unit.name,
            sku : item.sku,
          };
        });
      this.supplies = supplies;
    },
    clearFilter() {
      return {
        supply: "",
      };
    },
    setDefaultFilter() {
      this.selectSupplies = [];
      this.filter = this.clearFilter();
    },
    select() {
      EventBus.$emit("selectProducts", this.selectSupplies);
      this.modalProductSelect = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.el-input-sticky {
  position: sticky;
  // top: -20px;
  top: 0;
  background-color: #fff;
  z-index: 5;
}
.load-product {
  margin-top: 20px;
  .list-item {
    list-style: none;
    &:last-of-type {
      border-bottom: 1px solid var(--el-border-color-base);
    }
    .item,
    .sub-item {
      border: 1px solid var(--el-border-color-base);
      border-bottom: none;
      display: flex;
      font-size: 12px;
      padding: 6px 12px;
      align-items: center;
      .item-check {
        width: 24px;
        display: flex;
        align-items: center;
        label {
          height: auto;
        }
      }
      .item-name {
        flex: 1;
      }
      .item-stock {
        width: 100px;
      }
    }
    .sub-item {
      padding-left: 36px;
    }
  }
}
</style>
