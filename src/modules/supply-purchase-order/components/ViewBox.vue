<template>
  <div class="add-purchase-order">
    <el-dialog
      v-model="openDialogViewBox"
      custom-class="el-dialog-custom el-dialog-purchase-order"
      :close-on-click-modal="false"
      width="50%"
      max-width="400"
    >
      <template #title> View Box {{ item && item.po_number }} </template>
      <template #default>
        <div class="flex justify-end mb-2">
          <el-button type="primary" @click="createBox" size="small">
            <span class="icon-margin-right"><icon :data="iconAdd" /></span
            >{{ $t('Create') }}
          </el-button>
        </div>
        <el-table
          border
          :data="items"
          style="width: 100%"
          size="small"
          class="el-table-expand-purchase-order-box"
          v-loading="isLoading"
        >
          <el-table-column type="expand" width="48">
            <template #default="props">
              <!-- start -->
              <el-table
                :data="props.row.items"
                style="width: 100%; margin-left: 47px"
                size="small"
                :cell-class-name="tableExpandClassName"
                :row-class-name="tableExpandClassName"
                class="el-table-border-none"
              >
                <!-- <el-table-column :label="$t('SKU')" min-width="150">
                  <template #default="scope">
                    {{ scope.row.sku }}
                  </template>
                </el-table-column> -->
                <el-table-column :label="$t('External SKU')" width="200">
                  <template #default="scope">
                    {{ scope.row.external_sku }}
                  </template>
                </el-table-column>
                <el-table-column :label="$t('Product Name')" width="200">
                  <template #default="scope">
                    <div class="truncate">
                      {{ scope.row.product?.name }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('GTIN')" width="200">
                  <template #default="scope">
                    {{ scope.row.gtin }}
                  </template>
                </el-table-column>
                <el-table-column :label="$t('Quantity')" width="200">
                  <template #default="scope">
                    {{ scope.row.quantity }}
                  </template>
                </el-table-column>
                <el-table-column prop="action" :label="$t('Action')" width="80">
                  <template #default="scope">
                    <el-popconfirm
                      :title="'Are you sure to delete?'"
                      @confirm="removeBoxItem(scope.row)"
                    >
                      <template #reference>
                        <el-link :underline="false" type="danger"
                          ><icon :data="iconDelete"
                        /></el-link>
                      </template>
                    </el-popconfirm>
                  </template>
                </el-table-column>
              </el-table>
              <!-- end -->
            </template>
          </el-table-column>
          <el-table-column :label="$t('Box Number')" min-width="150">
            <template #default="scope">
              <el-link
                :underline="false"
                type="primary"
                v-if="scope.row.status != 'lost' && !scope.row.received"
                @click="showDetails(scope.row)"
              >
                {{ scope.row.box_number }}
              </el-link>
              <span v-else>{{ scope.row.box_number }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('Invoice Number')" width="200">
            <template #default="scope">
              {{ scope.row.invoice_number }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('Tracking Number')" width="200">
            <template #default="scope">
              {{ scope.row.tracking_number }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('Received')" width="180">
            <template #default="scope">
              <span class="text-red-400" v-if="scope.row.status == 'lost'">{{ $t('Lost') }}</span>
              <span v-else>{{ formatDate(scope.row.received_at, false) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="action"
            :label="$t('Action')"
            width="80"
            fixed="right"
          >
            <template #default="scope">
              <el-link
                :underline="false"
                type="primary"
                @click="editBox(scope.row)"
                ><icon :data="iconEdit"
              /></el-link>
              <el-popconfirm
                :title="'Are you sure to delete?'"
                @confirm="removeBox(scope.row)"
              >
                <template #reference>
                  <el-link :underline="false" type="danger" class="ml-2"
                    ><icon :data="iconDelete"
                  /></el-link>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showBoxDataInner"
      width="25%"
      :title="$t('Manual Update Status')"
      append-to-body
    >
      <div v-loading="updateBoxLoading">
        <p>{{ $t('PO')}}: {{ item.po_number}}</p>
        <p>{{ $t('Box Number')}}: {{ boxDataInner.box_number }}</p>
        <p>{{ $t('Invoice Number')}}: {{ boxDataInner.invoice_number }}</p>
        <p>{{ $t('Tracking Number')}}: {{ boxDataInner.tracking_number }}</p>
        <el-select v-model="boxDataInner.set_status" :placeholder="$t('Status')">
          <el-option label="Lost" value="lost" />
        </el-select>
        <div class="flex justify-end mt-5">
          <el-button type="primary" @click="handleUpdateBox">{{ $t('Update') }}</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
  <AddBox @onRefresh="fetchBoxs" />
  <EditBox @onRefresh="fetchBoxs" />
</template>

<script>
import EventBus from "@/utilities/eventBus.js";
import {
  getBoxsById,
  destroyBox,
  destroyBoxItem,
} from "@/api/purchaseOrder.js";
import AddBox from "@/modules/purchase-order/components/AddBox.vue";
import EditBox from "@/modules/purchase-order/components/EditBox.vue";
import dateMixin from "@/mixins/date.js";
import { updateBox } from "@/api/purchaseOrder.js";

export default {
  name: "ViewBox",
  mixins: [dateMixin],
  props: {},
  components: {
    AddBox,
    EditBox,
  },
  data() {
    return {
      items: [],
      item: "",
      showBoxDataInner: false,
      boxDataInner: {},
      openDialogViewBox: false,
      isLoading: false,
      updateBoxLoading: false,
    };
  },
  watch: {},
  created() {
    EventBus.$on("showViewBox", (data) => {
      this.item = data;
      this.openDialogViewBox = true;
      this.fetchBoxs();
    });
  },
  computed: {},
  mounted() {},
  beforeUnmount() {
    EventBus.$off("showAddBox");
  },
  methods: {
    tableExpandClassName() {
      return "is-border-none";
    },
    showDetails(row) {
      this.showBoxDataInner = true;
      this.boxDataInner = {
        set_status: '',
        ...row
      };
    },
    async handleUpdateBox() {
      this.updateBoxLoading = true;
      try {
         const data = {
          ...this.boxDataInner,
          items: this.boxDataInner.items.length && this.boxDataInner.items.map((item) => {
            if (!!item.product_id && !!item.sku && item.gtin && item.quantity) {
              return {
                product_id: item.product_id,
                sku: item.sku,
                gtin: item.gtin,
                quantity: item.quantity,
                id: item.id || undefined,
              };
            }
          })
        }
        await updateBox(data);
        this.showBoxDataInner = false;
        this.updateBoxLoading = false;
        this.fetchBoxs();
      } catch (e) {
        this.updateBoxLoading = false;
        for (const item in e.response.data) {
          return !!e.response.data[item].length && this.notification(e.response.data[item][0], "error");
        }
      }
    },
    async removeBoxItem(item) {
      await destroyBoxItem(item.id);
      this.fetchBoxs();
    },
    async removeBox(item) {
      await destroyBox(item.id);
      this.fetchBoxs();
    },
    createBox() {
      EventBus.$emit("showAddBox", this.item);
    },
    editBox(boxitem) {
      const data = {
        item: boxitem,
        purchaseOrderItem: this.item,
      };
      EventBus.$emit("showEditBox", data);
    },
    async fetchBoxs() {
      if (!this.item || !this.item.id || this.isLoading) {
        return;
      }
      this.isLoading = true;
      const res = await getBoxsById(this.item.id);
      this.items = res.data || [];
      this.isLoading = false;
    },
  },
};
</script>

<style lang="scss" scoped></style>
