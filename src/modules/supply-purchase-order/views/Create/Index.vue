<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div class="add-purchase-order">
    <el-dialog
        v-model="openDialogAddPurchaseOrder"
        :title="$t('Create Purchase Order')"
        custom-class="el-dialog-custom el-dialog-purchase-order"
        @close="resetData"
        :close-on-click-modal="false"
        width="80%"
    >
      <template #default>
        <el-form
            ref="addPurchaseOrderForm"
            label-width="200px"
            :label-position="'left'"
            class="el-form-add-purchase-order"
            :model="data"
            :rules="rules"
            @submit.prevent="onSubmit('addPurchaseOrderForm')"
        >
          <el-form-item :label="$t('Vendor')">
            <el-select
                v-model="data.vendor_id"
                :placeholder="$t('Select vendor')"
                class="w-auto"
                :style="{ border: serverError && serverError['vendor_id'] ? '0.1px solid #f56c6c' : '' }"
            >
              <el-option
                  v-for="item in getVendors"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
              >
              </el-option>
            </el-select>
            <div v-if="serverError && serverError['vendor_id']" class="el-form-item__error">{{getErrorMessage('vendor_id')}}</div>
          </el-form-item>
          <div class="el-item-group">
            <el-form-item :label="$t('Order Number')" prop="order_number">
              <el-input v-model="data.order_number"></el-input>
            </el-form-item>
            <el-form-item :label="$t('Order Date')" prop="order_date">
              <el-date-picker
                  v-model="data.order_date"
                  type="date"
                  :placeholder="$t('Pick a day')"
                  :style="{ border: serverError && serverError['vendor_id'] ? '0.1px solid #f56c6c' : '' }"
              >
              </el-date-picker>
              <div v-if="serverError && serverError['order_date']" class="el-form-item__error">{{getErrorMessage('order_date')}}</div>
            </el-form-item>
          </div>
          <div class="el-item-group">
            <el-form-item
                :label="$t('PO Number')"
                prop="po_number"
                :class="{
                'is-error': serverError && serverError['po_number'],
              }"
            >
              <el-input v-model="data.po_number"></el-input>
              <div v-if="serverError && serverError['po_number']" class="el-form-item__error">{{getErrorMessage('po_number')}}</div>
            </el-form-item>
            <el-form-item
                :label="$t('Invoice Number')"
                prop="invoice_number"
                :class="{
                'is-error': serverError && serverError['invoice_number'],
              }"
            >
              <el-input v-model="data.invoice_number"></el-input>
              <div v-if="serverError && serverError['invoice_number']" class="el-form-item__error">{{getErrorMessage('invoice_number')}}</div>
            </el-form-item>
          </div>

          <div class="el-item-group">
            <el-form-item
                :label="$t('Expected Delivery Date')"
                prop="delivery_date"
            >
              <el-date-picker
                  v-model="data.delivery_date"
                  type="date"
                  :placeholder="$t('Pick a day')"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item :label="$t('Payment Terms')" prop="payment_terms">
              <el-select
                  :placeholder="$t('Select payment term')"
                  class="el-select-managment"
                  v-model="data.payment_terms"
                  filterable
              >
                <el-option
                    v-for="item in getPaymentTerms"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                >
                </el-option>
                <el-option
                    class="el-select-managment-btn"
                    @click="showPaymentTermManagement"
                    value=""
                >
                  <icon :data="iconAdd" />{{ $t("Add payment term") }}
                </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="el-item-group">
            <el-form-item
                :label="$t('Shipping Carrier')"
                prop="tracking_carrier"
            >
              <el-select
                  :placeholder="$t('Select tracking carrier')"
                  class="el-select-managment"
                  v-model="data.tracking_carrier"
                  filterable
              >
                <el-option
                    v-for="item in getCarriers"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                >
                </el-option>
                <el-option
                    class="el-select-managment-btn"
                    @click="showCarrierManagement"
                    value=""
                >
                  <icon :data="iconAdd" />{{ $t("Add carrier") }}
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('Tracking Number')" prop="tracking_number">
              <el-input v-model="data.tracking_number"></el-input>
            </el-form-item>
          </div>
          <el-form-item :label="$t('Tag')" prop="tag">
            <el-select
                :placeholder="$t('Select tag')"
                class="el-select-managment"
                v-model="data.tag"
                filterable
                multiple
                @change="changeTag"
            >
              <el-option
                  v-for="item in getTags"
                  :key="item.id"
                  :label="item.name"
                  :value="String(item.id)"
              >
              </el-option>
              <el-option
                  class="el-select-managment-btn"
                  @click="showTagManagement"
                  value=""
              >
                <icon :data="iconAdd" />{{ $t("Add tag") }}
              </el-option>
            </el-select>
          </el-form-item>

          <div>
            <el-table
                border
                :data="products"
                :style="{
                  width: '100%',
                  border: serverError && serverError['items'] ? '0.5px solid #F56C6C' : 'none'
                }"
                :max-height="400"
            >
              <el-table-column
                  fixed
                  prop="name"
                  :label="$t('Name')"
                  min-width="200"
              >
                <template #default="scope">
                  <div>
                    {{ scope.row.name }}
                  </div>
                  <div v-if="scope.row.sku">SKU: {{ scope.row.sku }}</div>
                </template>
              </el-table-column>
              <el-table-column fixed prop="total" :label="$t('Unit')">
                <template #default="scope">{{ scope.row.unit }} </template>
              </el-table-column>
              <el-table-column fixed prop="quantity" :label="$t('Quantity')">
                <template #default="scope">
                  <el-input-number
                      size="small"
                      v-model="scope.row.quantity"
                      :precision="0"
                      :step="1"
                      :min="0"
                      @change="changeProductItem(scope.row)"
                  />
                </template>
              </el-table-column>
              <el-table-column fixed prop="price" :label="$t('Price')">
                <template #default="scope">
                  <el-input-number
                      v-model="scope.row.price"
                      :min="0"
                      :precision="2"
                      :step="0.1"
                      size="small"
                      @change="changeProductItem(scope.row)"
                  />
                </template>
              </el-table-column>
              <el-table-column fixed prop="total" :label="$t('Total')">
                <template #default="scope"> ${{ formatNumber(scope.row.total) || 0 }} </template>
              </el-table-column>
              <el-table-column prop="action" :label="$t('Action')" width="80">
                <template #default="scope">
                  <el-popconfirm
                      :title="'Are you sure to delete ' + scope.row.name + '?'"
                      @confirm="removeProductItem(scope.$index)"
                  >
                    <template #reference>
                      <el-link :underline="false" type="danger"
                      ><icon :data="iconDelete"
                      /></el-link>
                    </template>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
            <div>
              <div v-if="serverError && serverError['items']" style="color: #F56C6C">{{$t('The supply is required')}}</div>
              <el-button class="el-button-select-product" @click="selectProduct"><span class="icon-margin-right"><icon :data="iconAdd" /></span>{{ $t("Add supply") }}</el-button
              >
            </div>
          </div>
          <div class="grid grid-cols-2 total gap-8 mt-8">
            <div>
              <el-form-item :label="$t('Customer Note')" prop="note">
                <el-input :rows="1" type="textarea" v-model="data.note" />
              </el-form-item>
            </div>
            <div>
              <div class="border rounded p-3 w-full text-base">
                <div class="flex items-center justify-between mb-1">
                  <div class="flex-[0_0_168px]">
                    {{ $t("Sub Total") }}
                  </div>
                  <div class="font-semibold">
                    ${{ formatNumber(totalPrice) || 0 }}
                  </div>
                </div>

                <div class="flex items-center justify-between mb-1">
                  <div class="flex-[0_0_168px]">
                    {{ $t("Other fees & charges") }}
                  </div>
                  <div>
                    <el-input-number class="el-input-number-custom-purchase-order" v-model="data.fee" :min="0" :precision="2" />
                  </div>
                </div>

                <div class="flex items-center justify-between">
                  <div class="flex-[0_0_168px]">
                    {{ $t("Total") }}
                  </div>
                  <div class="font-semibold">
                    ${{ formatNumber(lastPrice) || 0 }}
                  </div>
                </div>
              </div>

            </div>
          </div>
        </el-form>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetData">{{ $t("Reset") }}</el-button>
          <el-button
              type="primary"
              @click="onSubmit('addPurchaseOrderForm')"
              :disabled="isLoading"
              :loading="isLoading"
          >{{ $t("Save") }}</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>
