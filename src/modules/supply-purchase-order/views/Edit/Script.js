import EventBus from "@/utilities/eventBus.js";
import { mapGetters } from "vuex";
import { update, details, searchOrderParent } from "@/api/supplyPurchaseOrder.js";
import PurchaseOrderHistory from "@/modules/supply-purchase-order/components/History.vue";
import { isEmpty } from "ramda";
import purchaseOrderMixin from "@/mixins/purchaseOrder.js";
import dateMixin from "@/mixins/date.js";

export default {
  name: "PurchaseOrderEdit",
  components: {
    PurchaseOrderHistory,
  },
  mixins: [purchaseOrderMixin, dateMixin],
  data() {
    return {
      data: this.setDefautData(),
      rules: {
      },
      isLoading: false,
      isLoadingSearch: false,
      items: [],
      products: [],
      totalPrice: 0,
      serverError: {},
      purchaseOrderId: this.$route.params.id,
      isLoadingFetchData: false,
      openDialogEditPurchaseOrder: false,
      purchaseOrderHistory : {}
    };
  },
  computed: {
    ...mapGetters(["getVendors"]),
    ...mapGetters(["getPaymentTerms"]),
    ...mapGetters(["getCarriers", "getTags"]),
    disabledEdit() {
      return ['completed'].includes(this.data.order_status);
    },
    lastPrice() {
      return parseFloat(parseFloat(this.totalPrice) + parseFloat(this.data.fee)).toFixed(2)
    }
  },
  beforeUnmount() {
    EventBus.$off("productSelect");
  },
  created() {
    EventBus.$on("showEditPurchaseOrder", (data) => {
      this.purchaseOrderId = data.id;
      this.openDialogEditPurchaseOrder = true;
      this.fetchDetails();
    });
    EventBus.$on("selectProducts", (products) => {
      this.selectProducts(products);
    });
    EventBus.$on("updateStatus", (status) => {
      this.data.order_status = status;
    });
  },
  mounted() {},
  methods: {
    showUpdateStatus() {
      EventBus.$emit("showUpdateStatus", this.data);
    },
    resetData() {
      this.data = this.setDefautData();
      this.items = [];
      this.products = [];
      this.totalPrice = 0;
      this.serverError = {};
    },
    setDefautData() {
      return {
        vendor_id: "",
        order_number: "",
        reference_number: "",
        order_date: "",
        delivery_date: "",
        payment_terms: "",
        tracking_carrier: "",
        tracking_number: "",
        note: "",
        tag: "",
        fee: 0,
      };
    },
    async fetchDetails() {
      this.isLoadingFetchData = true;
      const res = await details(this.purchaseOrderId);
      this.isLoadingFetchData = false;
      const data = res.data;
      if (!data.tag) {
        data.tag = "";
      } else {
        data.tag = data.tag.split(",");
      }
      this.data = {
        ...data,
        payment_terms: data.payment_terms || "",
        tracking_carrier: data.tracking_carrier || "",
      };
      const items =
        (data.items &&
          data.items.map((item) => {
            return {
              id : item.id,
              supply_id : item.supply.id,
              name: (item.supply && item.supply.name) || "",
              unit_id: item.supply.unit.id,
              unit: item.supply.unit.name,
              sku: (item.supply && item.supply.sku) || "",
              price : item.price || 0,
              quantity : item.quantity,
              quantity_onhand: item?.quantity_onhand || 0,
              total: item.total || 0,
            };
          })) ||
        [];
      this.products = items;
      this.purchaseOrderHistory =data;
      this.getTotalPrice();
    },
    selectProduct() {
      EventBus.$emit("productSelect");
    },
    showPaymentTermManagement() {
      EventBus.$emit("showPaymentTermManagement");
    },
    showCarrierManagement() {
      EventBus.$emit("showListCarrier");
    },
    showTagManagement() {
      EventBus.$emit("showListTag");
    },
    async searchPurchaseOrder(query) {
      if (query) {
        this.isLoadingSearch = true;
        const res = await searchOrderParent({ keyword: query });
        this.isLoadingSearch = false;
        const data = res.data;
        this.items = data;
      } else {
        this.items = [];
      }
    },

    selectProducts(products) {
      products = products.map((item) => {
        return {
          supply_id : item.value,
          sku: item.sku,
          unit: item.unit,
          price :  0,
          quantity : 1,
          quantity_onhand: 0,
          total: 0,
          name : item.name
        };
      });
      if (this.products.length) {
        this.products.forEach((item) => {
          products.forEach((newItem) => {
            if (item.supply_id === newItem.supply_id) {
              item.quantity += 1;
              item.total = this.getTotalItem(item);
              newItem.remove = 1;
            }
          });
        });
        products = products.filter((item) => item.remove !== 1);
        this.products = [...this.products, ...products];
      } else {
        this.products = products;
      }
      this.getTotalPrice();
    },
    changeProductItem(item) {
      this.products.forEach((product) => {
        if (product.id === item.id) {
          product.total = this.getTotalItem(product);
        }
      });
      this.getTotalPrice();
    },
    removeProductItem(index) {
      this.products.splice(index, 1);
      this.getTotalPrice();
    },
    getTotalPrice() {
      this.$nextTick(() => {
        let totalPrice = 0;
        if (this.products && this.products.length) {
          this.products.forEach((item) => {
            totalPrice = parseFloat(totalPrice) + parseFloat(item.total);
          });
        }
        totalPrice = parseFloat(totalPrice).toFixed(2);
        this.totalPrice = totalPrice;
      });
    },
    getTotalItem(item) {
      if (!item.price || !item.quantity) {
        return 0;
      }
      return parseFloat(item.price * item.quantity).toFixed(2);
    },
    async onSubmit(formName) {
      const isValid = await this.$refs[formName].validate();
      if (!isValid) {
        return;
      }
      let params = this.data;
      const products = this.products.map((item) => {
        return {
          id: item.id || undefined,
          supply_id: item.supply_id,
          quantity: item.quantity,
          price: item.price,
          total: item.total,
        };
      });
      params.items = products;
      params.sub_total = this.totalPrice;
      params.total = this.lastPrice;
      params = {
        ...params,
        history : undefined,
        user : undefined,
        fee: parseFloat(params.fee).toFixed(2)
      }
      this.isLoading = true;
      params.order_date = this.formatDate(params.order_date);
      params.delivery_date = this.formatDate(params.delivery_date);
      try {
        await update(params);
        this.isLoading = false;
        this.$emit("onRefresh");
        this.openDialogEditPurchaseOrder = false;
        this.notification(this.$t("Update purchase order successfully."));
      } catch (e) {
        this.isLoading = false;
        let message = this.$t("Purchase order update error.");
        const data = e.response.data || {};
        if (!isEmpty(data)) {
          let serverError = {};
          Object.keys(data.errors).forEach((key) => {
            serverError[key] = data.errors[key];
          });
          this.serverError = serverError;
          this.focusFirtError();
        }
        this.notification(message, "error");
      }
    },
    focusFirtError() {
      this.$nextTick(() => {
        const elErrors = document.getElementsByClassName("is-error");
        if (!elErrors || !elErrors.length) {
          return;
        }
        const firtsElError = (elErrors && elErrors[0]) || undefined;
        const input = firtsElError.querySelector("input");
        document.body.scrollTop = (input.offsetTop - 120) | 0;
        input.focus();
      });
    },
    changeTag() {
      if (this.data.tag && Array.isArray(this.data.tag)) {
        this.data.tag = this.data.tag.filter((tag) => tag);
      }
    },
    getErrorMessage(field) {
      return this.serverError[field][0];
    },
  },
};
