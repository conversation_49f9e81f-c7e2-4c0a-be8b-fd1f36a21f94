<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
    <div>
        <div class="top-head mb-4">
            <div class="top-head-left">
                <h1>{{ $t('Ink Consumption') }}</h1>
            </div>
        </div>
        <el-form>
            <div class="flex gap-3 pl-5">
                <el-form-item>
                    <el-date-picker v-model="date" type="year" placeholder="Report Year" @change="getDataReport()">
                    </el-date-picker>
                </el-form-item>

                <el-form-item>
                    <el-select class="w-full" v-model="category" @change="getDataReport()">
                        <el-option v-for="item in categories" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item>
                    <el-button type="danger" plain @click="onReset()">Reset</el-button>
                </el-form-item>

            </div>
        </el-form>

        <apexchart ref="chart" v-loading="isLoading" type="bar" :height="heightChart" :options="chartOptions"
            :series="series">
        </apexchart>
    </div>
</template>