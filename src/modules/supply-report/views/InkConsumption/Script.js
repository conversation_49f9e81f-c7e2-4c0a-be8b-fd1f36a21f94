import { getDataReport } from "@/api/supplyReport";
import moment from "moment";
import VueApexCharts from 'vue3-apexcharts';

const COLOR_INK = 1;
const WHITE_INK = 2;
const CHART_OPTIONS_INITIAL = {
    chart: {
        toolbar: {
            show: false
        }
    },
    plotOptions: {
        bar: {
            horizontal: false,
            columnWidth: '55%',
            endingShape: 'rounded'
        },
    },
    dataLabels: {
        enabled: false
    },
    stroke: {
        show: true,
        width: 2,
        colors: ['transparent']
    },
    xaxis: {
        categories: []
    },
    yaxis: {
        title: {
            text: ''
        }
    },
    fill: {
        opacity: 1
    },
    tooltip: {
        custom: function ({ series, seriesIndex, dataPointIndex, w }) {
            const { warehouse, month, category, unitName } = w.globals.initialSeries[seriesIndex].data[dataPointIndex];
            const val = series[seriesIndex][dataPointIndex];

            return `                            
                <div style="background-color: #DFE6ED; padding: 10px 20px; font-size: 11px;">
                    <div style="font-weight: bold; text-align: center; font-size: 14px;">${category}</div>
                    <div style="text-align: center;">${month} - ${warehouse}</div>
                    <hr style="margin: 10px 0; padding: 0; border-color: #000;" />
                    <div style="font-weight: bold; margin-left: 10px;">${val} ${unitName} Consumed</div>
                </div>
                `;
        }
    },
    colors: []
};

export default {
    name: "InkConsumption",
    components: { apexchart: VueApexCharts },
    data() {
        return {
            date: "",
            params: {
                year: moment(this.date).format('YYYY'),
            },
            category: COLOR_INK,
            categories: [
                {
                    id: COLOR_INK,
                    name: 'Color Ink'
                },
                {
                    id: WHITE_INK,
                    name: 'White Ink'
                }
            ],
            series: [],
            chartOptions: CHART_OPTIONS_INITIAL,
            isLoading: false,
        }
    },

    async mounted() {
        await this.getDataReport();
    },

    computed: {
        heightChart: function () {
            return window.innerHeight - 200
        }
    },

    methods: {
        onReset() {
            this.date = "";
            this.category = COLOR_INK;
            this.getDataReport();
        },

        async getDataReport() {
            this.isLoading = true;
            this.params.year = !this.date ? moment().format('YYYY') : moment(this.date).format('YYYY');
            const { data } = await getDataReport(this.params);
            let isShowYear = moment().format('YYYY') == this.params.year && moment().format('MM') != 12;
            let xLabel = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

            if (data?.data_report?.length > 0) {
                this.chartOptions = CHART_OPTIONS_INITIAL;
                this.series = [];
                xLabel = [];

                data?.data_report.forEach(item => {
                    const { month, warehouses } = item;
                    let _month = moment(month, 'YYYY-MM');
                    let total = warehouses.reduce((accumulator, w) => {
                        let qty = this.category == WHITE_INK ? w.qty_ink_white : w.qty_ink_color;
                        return accumulator + qty;
                    }, 0);
                    total = Math.round(total * 1000) / 1000;
                    let label = isShowYear ? [`${total} ${data.unit}`, _month.format('MMM'), _month.format('YYYY')] : [`${total} ${data.unit}`, _month.format('MMM')];
                    xLabel.push(label);

                    warehouses.map(w => {
                        let index = this.series.findIndex(x => x.name == w.name)
                        let _data = {
                            y: this.category == WHITE_INK ? w.qty_ink_white : w.qty_ink_color,
                            x: label,
                            warehouse: w.name,
                            month: _month.format('MMMM YYYY'),
                            unitName: data.unit,
                            category: this.categories.find(x => x.id == this.category).name,
                        }

                        if (index == -1) {
                            this.series.push({
                                name: w.name,
                                data: [_data],
                                color: w.color,
                            });
                        } else {
                            this.series[index].data.push(_data);
                        }
                    })

                });
            }

            this.$refs.chart.updateOptions({
                yaxis: {
                    title: {
                        text: data.unit
                    }
                },
                xaxis: {
                    categories: xLabel
                },
            });

            this.isLoading = false;
        },
    }
}