<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
    <div>
        <div class="top-head mb-4">
            <div class="top-head-left">
                <h1>{{ $t('Ink Consumption Forecast') }}</h1>
            </div>
        </div>
        <el-form>
            <div class="flex gap-3 pl-5">
                <el-form-item>
                    <el-select class="w-full" v-model="params.warehouse_id">
                        <el-option key="facility-all" label="Facility" value=""> </el-option>
                        <el-option v-for="item in getUserWarehouses" :key="item.id" :label="item.name"
                            :value="String(item.id)">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item>
                    <el-date-picker v-model="date" type="year" placeholder="Report Year">
                    </el-date-picker>
                </el-form-item>

                <el-form-item>
                    <el-select class="w-full" v-model="params.color_type">
                        <el-option v-for="item in categories" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item>
                    <el-select class="w-full" v-model="params.unit_type">
                        <el-option v-for="item in unitTypes" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" plain @click="getDataReport()">Filter</el-button>
                    <el-button type="danger" plain @click="onReset()">Reset</el-button>
                </el-form-item>

            </div>
        </el-form>

        <div class="responsive-table-container">
            <el-table :data="dataReport" :height="height" style="width: 100%" :cell-class-name="getCellClassName"
                :header-cell-class-name="getHeaderCellClassName">
                <el-table-column prop="warehouse" label="Facility" width="130">
                    <template #header>
                        <div style="white-space: nowrap;">Facility</div>
                    </template>
                    <template #default="scope">
                        <div class="break-word">
                            {{ scope?.row?.warehouse }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="ink_label" width="100">
                    <template #header>
                        <div style="white-space: nowrap;">Ink Color</div>
                    </template>
                    <template #default="scope">
                        <div class="text-center">
                            {{ scope?.row?.ink_label }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column width="100">
                    <template #header="scope">
                        <div style="white-space: nowrap;">Forecast<br />Accuracy</div>
                    </template>
                    <template #default>
                        <div class="text-center">Forecast</div>
                        <div class="text-center">Actual</div>
                        <div class="text-center">Variance</div>
                    </template>
                </el-table-column>
                <el-table-column v-for="(item, index) in headers" :prop="index" :key="index">
                    <template #header>
                        <div class="text-nowrap">{{ item }}</div>
                    </template>
                    <template #default="scope">
                        <div class="text-right text-nowrap">
                            {{ scope?.row?.months[index]?.forecast != '-' ? `${scope?.row?.months[index]?.forecast}
                            ${unitName}` : scope?.row?.months[index]?.forecast }}
                        </div>
                        <div class="text-right text-nowrap">
                            {{ scope?.row?.months[index]?.actual != '-' ? `${scope?.row?.months[index]?.actual}
                            ${unitName}` : scope?.row?.months[index]?.actual }}
                        </div>
                        <div class="text-right text-nowrap">
                            {{ scope?.row?.months[index]?.variance != '-' ? `${scope?.row?.months[index]?.variance}
                            ${unitName}` : scope?.row?.months[index]?.variance }}
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>