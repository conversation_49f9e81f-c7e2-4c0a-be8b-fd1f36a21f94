import { getDataReportForecast } from "@/api/supplyReport";
import moment from "moment";
import warehouseMixin from "@/mixins/warehouse";

const COLOR_INK = 'ink_color_cc';
const WHITE_INK = 'ink_white_cc';

export default {
    name: "InkConsumptionForecast",

    mixins: [warehouseMixin],

    data() {
        return {
            height: 0,
            date: "",
            params: {
                year: "",
                warehouse_id: "",
                color_type: "",
                unit_type: "liter",
            },
            category: COLOR_INK,
            categories: [
                {
                    id: "",
                    name: 'Ink Color'
                },
                {
                    id: COLOR_INK,
                    name: 'Color Ink'
                },
                {
                    id: WHITE_INK,
                    name: 'White Ink'
                }
            ],
            unitTypes: [
                {
                    id: 'liter',
                    name: 'Liter'
                },
                {
                    id: 'tank',
                    name: 'Tank'
                }
            ],
            headers: [],
            dataReport: [],
            isLoading: false,
            unitName: 'L',
        }
    },

    created() {
        this.height = window.innerHeight - 200;
    },

    async mounted() {
        await this.getDataReport();
    },

    methods: {
        onReset() {
            this.date = "";
            this.params = {
                year: "",
                warehouse_id: "",
                unit_type: "liter",
            };
            this.getDataReport();
        },

        async getDataReport() {
            this.isLoading = true;
            this.params.year = this.date ? moment(this.date).format('YYYY') : "";
            const { data } = await getDataReportForecast(this.params);
            this.unitName = this.params.unit_type == 'tank' ? 'T' : 'L';
            this.dataReport = data?.dataReport || [];
            this.headers = data?.months || [];
            this.isLoading = false;
        },

        getCellClassName({ column }) {
            if (column.property === moment().format('YYYYMM')) {
                return 'current-month';
            }

            return '';
        },

        getHeaderCellClassName({ column }) {
            let _class = 'text-center table-cell-header';

            if (column.property === moment().format('YYYYMM')) {
                _class += ' current-month';
            }

            return _class;
        },
    }
}