import { getList, destroy } from "@/api/supplyUnit";
import EventBus from "@/utilities/eventBus";
import CreateSupplyUnit from "@/modules/supply-unit/views/Create/Index.vue";
import UpdateSupplyUnit from "@/modules/supply-unit/views/Update/Index.vue";

export default {
    name: "SupplyUnitList",
    components: {
        CreateSupplyUnit,
        UpdateSupplyUnit
    },
    data() {
        return {
            items: [],
            isLoading: false,
            filter: this.setDefaultFilter(),
        }
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 189);
        }
    },
    mounted() {
        this.filter = this.getRouteParam();
        this.fetchDataSupplyUnit();
    },
    methods: {
        setDefaultFilter() {
            return {
                limit: 25,
                page: 1,
                name: ""
            };
        },
        async fetchDataSupplyUnit() {
            this.isLoading = true;
            this.setRouteParam();
            const { data } = await getList(this.filter);
            this.items = data.data;
            this.total = data.total;
            this.isLoading = false;
        },
        changePage(page) {
            this.filter.page = page;
            this.fetchDataSupplyUnit();
        },
        onFilter() {
            this.filter.page = 1;
            this.fetchDataSupplyUnit();
        },
        createSupplyUnit() {
            EventBus.$emit("showCreateSupplyUnit");
        },
        updateSupplyUnit(item) {
            EventBus.$emit("showUpdateSupplyUnit", item);
        },
        resetFilter(){
            this.filter = this.setDefaultFilter()
            this.fetchDataSupplyUnit();
        },
        async deleteSupplyUnit(item) {
            const res = await destroy(item.id);
            this.notification(res.data.message);
            this.fetchDataSupplyUnit();
        }
    }
}