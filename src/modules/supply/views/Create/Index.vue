<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <el-dialog v-model="dialogVisible" destroy-on-close :title="$t('Create supply')" custom-class="el-dialog-custom"
    :destroy-on-close="true">
    <el-form status-icon :model="data" @submit.prevent="onSubmit" :label-position="'top'">
      <el-row :gutter="12">
        <el-col :span="8">
          <el-form-item :label="$t('Category')" class="w-full" required :class="{ 'is-error': isError('category_id') }">
            <el-select placeholder="Select category" class="w-full" v-model="data.category_id" filterable>
              <el-option v-for="item in categoryOptions" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
            <div v-if="isError('category_id')" class="el-form-item__error">{{ getErrorMessage('category_id') }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('Unit')" class="w-full" required :class="{ 'is-error': isError('unit_id') }">
            <el-select placeholder="Select unit" class="w-full" v-model="data.unit_id" filterable>
              <el-option v-for="item in unitOptions" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
            <div v-if="isError('unit_id')" class="el-form-item__error">{{ getErrorMessage('unit_id') }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('Vendor')" class="w-full" required :class="{ 'is-error': isError('vendor_id') }">
            <el-select placeholder="Select vendor" class="w-full" v-model="data.vendor_id" filterable>
              <el-option v-for="item in getVendors" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
            <div v-if="isError('vendor_id')" class="el-form-item__error">{{ getErrorMessage('vendor_id') }}</div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="12">
        <el-col :span="8">
          <el-form-item :label="$t('Value')" :class="{ 'is-error': isError('conversion_value') }" required>
            <el-input v-model="data.conversion_value" @keyup.enter="onSubmit"></el-input>
            <div v-if="isError('conversion_value')" class="el-form-item__error">{{ getErrorMessage('conversion_value') }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('Unit Conversion')" class="w-full" :class="{ 'is-error': isError('unit_conversion_id') }">
            <el-select placeholder="Select Measurement" class="w-full" v-model="data.unit_conversion_id" filterable>
              <el-option v-for="item in unitOptions" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
            <div v-if="isError('unit_conversion_id')" class="el-form-item__error">{{ getErrorMessage('unit_conversion_id') }}</div>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item :label="$t('Case Quantity')" class="w-full">
            <el-input-number class="mr-3" v-model="data.case_quantity" :min="0"  precision="0"/>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="12">
        <el-col :span="24">
          <el-form-item :label="$t('Name')" :class="{ 'is-error': isError('name') }" required>
            <el-input v-model="data.name" @keyup.enter="onSubmit"></el-input>
            <div v-if="isError('name')" class="el-form-item__error">{{ getErrorMessage('name') }}</div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="12">
        <el-col :span="24">
          <el-form-item :label="$t('SKU')" :class="{ 'is-error': isError('sku') }" required>
            <el-input v-model="data.sku" @keyup.enter="onSubmit"></el-input>
            <div v-if="isError('sku')" class="el-form-item__error">{{ getErrorMessage('sku') }}</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <el-button type="primary" @click="onSubmit" :disabled="isLoading" :loading="isLoading">{{ $t('Create') }}
      </el-button>
    </template>
  </el-dialog>
</template>