import EventBus from "@/utilities/eventBus";
import {update} from "@/api/supply";
import {mapGetters} from "vuex";

export default {
    name: "UpdateSupply",
    props: {
        categoryOptions: {type: Array, default: () => []},
        unitOptions: {type: Array, default: () => []},
    },
    computed: {
        ...mapGetters(["getVendors"]),
    },
    data() {
        return {
            dialogVisible: false,
            data: this.setDefaultData(),
            serverErrors: [],
            isLoading: false,
            supplyId: "",
        }
    },
    mounted() {
        this.fetchVendor()
    },
    created() {
        EventBus.$on("showUpdateSupply", (item) => {
            this.supplyId = item.id;
            item.unit_conversion_id = item.unit_conversion_id || "";
            this.data = Object.assign(this.data, item);
            this.serverErrors = [];
            this.dialogVisible = true;
        });
    },
    methods: {
        isError(field) {
            return !!this.serverErrors[field];
        },
        getErrorMessage(field) {
            return this.serverErrors[field][0];
        },
        setDefaultData() {
            return {
                category_id: "",
                unit_id: "",
                brand_id: "",
                name: "",
                sku: "",
                conversion_value: "",
                unit_conversion_id: "",
                case_quantity: 0,
            };
        },
        async onSubmit() {
            if (this.isLoading) return;
            // const isValid = await this.$refs.createWeightCubic.validate();
            // if (!isValid) {
            //     return;
            // }

            if (!this.data.unit_conversion_id) {
                this.data.unit_conversion_id = this.data.unit_id;
            }

            this.isLoading = true;
            try {
                const res = await update(this.supplyId, this.data);
                this.dialogVisible = false;
                this.notification(res.data.message);
                this.$emit("refresh");
            } catch (e) {
                this.serverErrors = e.response.data.errors;
                this.notification("Failed to update supply", "error");
            } finally {
                this.isLoading = false;
            }
        },
        async fetchVendor() {
            await this.$store.dispatch("getVendors");
        },
    }
}
