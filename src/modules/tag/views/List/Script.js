import { destroy } from "@/api/tag.js";
import EventBus from "@/utilities/eventBus.js";
import CreateTag from "@/modules/tag/views/Create/Index.vue";
import EditTag from "@/modules/tag/views/Edit/Index.vue";
import { mapGetters } from "vuex";

export default {
  name: "Tag",
  props: {
    sourceTag: {
      type: String,
      default: "purchase_order",
    },
  },
  components: {
    CreateTag,
    EditTag,
  },
  data() {
    return {
      items: [],
      isLoading: false,
      openDialogListTag: false,
    };
  },
  computed: {
    ...mapGetters(["getTags"]),
  },
  beforeUnmount() {
    EventBus.$off("showCreateTag");
    EventBus.$off("showEditTag");
  },
  mounted() {},
  created() {
    EventBus.$on("showListTag", () => {
      this.openDialogListTag = true;
    });
  },
  methods: {
    createTag() {
      EventBus.$emit("showCreateTag");
    },
    async fetchData() {
      this.fetchTag();
    },
    async fetchTag() {
      this.isLoading = true;
      await this.$store.dispatch("getTags", { source: this.sourceTag });
      this.isLoading = false;
    },
    editTag(item) {
      EventBus.$emit("showEditTag", item);
    },
    async deleteTag(item) {
      await destroy(item.id);
      this.notification(this.$t("Delete tag successfully."));
      this.fetchData();
    },
  },
};
