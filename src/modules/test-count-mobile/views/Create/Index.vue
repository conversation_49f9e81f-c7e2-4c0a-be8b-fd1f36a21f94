<style src="./Style.scss" lang="scss" scoped>
</style>
<script src="./Script.js"></script>
<template>
  <div class="content">
    <HeaderMobile :name="$t('Test count')"></HeaderMobile>
    <div class="content-body">
      <el-tabs v-model="currentTab" class="el-tabs-test-count">
        <el-tab-pane :label="$t('Scan')" name="scan">
          <el-form status-icon ref="addTestCount" :label-position="'top'" @submit.prevent="onSubmit()">
            <div class="bg-gray-50 p-3 border rounded mb-3">
              <el-form-item v-show="!Object.keys(employee).length" :error="employeeError" :label="$t('Employee ID')"
                required>
                <el-input v-model="employee_id" @change="scanEmployeeID" class="el-form-item-employee"></el-input>
              </el-form-item>
              <div v-if="Object.keys(employee).length">
                <div class="flex justify-between">
                  <b class="text-base">Hi {{ employee.name }}, Have a nice day!</b>
                  <el-link type="danger" @click="resetEmployee" :underline="false">{{ $t('Logout') }}</el-link>
                </div>
                <div class="text-lg text-fuchsia-500">
                  <IncrementTimer />
                </div>
              </div>
            </div>
            <el-form-item prop="location.barcode" :label="$t('Location')" class="el-form-item-location">
              <el-input class="flex-1" v-model="location.barcode" @change="changeLocation">
              </el-input>
            </el-form-item>
            <el-form-item :label="$t('BoxID')" prop="boxId" class="el-form-item-box-id d-flex">
              <el-input class="flex-1" v-model="boxId" @change="scanBoxId">
              </el-input>
            </el-form-item>
            <el-form-item v-if="isNewBox" :label="$t('GTIN')" prop="gtin" class="el-form-item-gtin d-flex">
              <el-input class="flex-1" v-model="gtin" @change="scanGTIN">
              </el-input>
            </el-form-item>
          </el-form>
          <div></div>
        </el-tab-pane>
        <el-tab-pane v-if="location.id != ''" :label="$t('List box in location')" name="list_box">
          <el-table class="table-content" stripe size="small" border :data="listBoxInLocation" style="width: 100%"
            :row-class-name="tableRowClassName">
            <el-table-column type="index" :label="$t('No.')" width="50" />
            <el-table-column :label="$t('Box')">
              <template #default="scope">
                {{ scope.row.barcode }}
              </template>
            </el-table-column>
            <el-table-column :label="$t('Product')">
              <template #default="scope">
                {{ scope.row.product_name }}
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane v-if="newBox.length > 0 && location.id != ''" :label="$t('New box')" name="new_box">
          <el-table class="table-content" stripe size="small" border :data="newBox" style="width: 100%">
            <el-table-column type="index" :label="$t('No.')" width="50" />
            <el-table-column :label="$t('Box')">
              <template #default="scope">
                {{ scope.row.barcode }}
              </template>
            </el-table-column>
            <el-table-column :label="$t('GTIN')">
              <template #default="scope">
                {{ scope.row.gtin }}
              </template>
            </el-table-column>
            <el-table-column prop="action" :label="$t('Action')" width="80" fixed="right">
              <template #default="scope">
                <el-popconfirm v-if="!scope.row.is_deleted" :title="'Are you sure to delete ?'"
                  @confirm="removeBoxNew(scope.row, scope.$index)">
                  <template #reference>
                    <el-link :underline="false" type="danger">
                      <icon :data="iconDelete" />
                    </el-link>
                  </template>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane v-if="location.id != ''" :label="$t('Complete')" name="complete">
          <div>
            List box in Location: <b>{{listBoxInLocation.length}}</b> boxs
          </div>
          <div class="text-danger">
            Lost: <b>{{countLostBox()}}</b> boxs
          </div>
          <div>
            New box: <b>{{newBox.length}}</b> boxs
          </div>
          <div>
            <label for="">Note</label>
            <el-input v-model="note" :rows="3" type="textarea" placeholder="Note" />
          </div>
        </el-tab-pane>
      </el-tabs>
      <div class="flex justify-end list-button">
        <el-button v-if="currentTab != 'scan'" @click="prevTab">{{ $t('Prev') }}</el-button>
        <el-button v-if="currentTab != 'complete' && location.id != ''" type="primary" @click="nextTab">{{ $t('Next') }}
        </el-button>
        <el-button v-if="currentTab == 'complete'" type="primary" @click="pauseTest" :disabled="isLoading"
          :loading="isLoading">{{ $t('Pause test') }}</el-button>
        <el-button v-if="currentTab == 'complete'" type="success" @click="onComplete" :disabled="isLoading"
          :loading="isLoading">{{ $t('Complete') }}</el-button>
      </div>
    </div>
  </div>
</template>
