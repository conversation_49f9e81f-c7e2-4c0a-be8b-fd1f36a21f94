import { add, pause } from '@/api/testCount.js';
import { getBoxByLocationIdForTestCount, scanBox } from '@/api/box.js';
import { list } from '@/api/location.js';
import { searchProductForInventory } from '@/api/product.js';
import { debounce } from '@/utilities/helper.js';
import { isEmpty } from 'ramda';
import {
  employeeLogoutTimeChecking,
  employeeTimeChecking,
} from '@/api/employee.js';
import IncrementTimer from '@/components/IncrementTimer.vue';
import HeaderMobile from '@/components/HeaderMobile.vue';

export default {
  name: 'TestCountMobile',
  components: { IncrementTimer, HeaderMobile },
  data() {
    return {
      location: {
        id: '',
        barcode: '',
      },
      boxId: '',
      gtin: '',
      note: '',
      isLoading: false,
      tabs: ['scan', 'list_box', 'new_box', 'complete'],
      currentTab: 'scan',
      newBox: [],
      listBoxInLocation: [],
      isNewBox: false,
      locations: [],
      employee: {},
      employee_id: null,
      employeeError: '',
      job_type: 'test_count',
      id_time_checking: null,
    };
  },
  mounted() {
    this.focusByElClass('el-form-item-employee');
    this.fetchLocation();
  },
  methods: {
    async resetEmployee() {
      const res = await employeeLogoutTimeChecking(this.id_time_checking);
      this.employee = {};
      this.employeeError = '';
      this.employee_id = '';
      this.id_time_checking = null;
    },
    async scanEmployeeID() {
      if (this.id_time_checking) {
        return true;
      }
      if (!this.employee_id) {
        this.employeeError = 'Employee ID field cannot be left blank.';
        return false;
      }
      const res = await employeeTimeChecking({
        code: Number(this.employee_id),
        job_type: this.job_type,
      });

      if (!res.data.data) {
        this.employeeError = "Can't find your employee ID, please scan again";
        return false;
      }
      this.employeeError = '';
      this.employee = res.data.data;
      this.id_time_checking = res.data.id_time_checking;
      this.focusByElClass('el-form-item-location');
      return true;
    },
    async fetchLocation() {
      const params = {
        without_pagination: true,
      };
      const res = await list(params);
      const data = res.data || [];
      this.locations = data;
    },
    validateLocation(value) {
      if (!value) return false;
      for (const item of this.locations) {
        if (item.barcode.toLowerCase() === value.toLowerCase()) {
          this.location.id = item.id;
          return true;
        }
      }
      this.location.id = '';
      this.location.barcode = '';
      return false;
    },
    async changeLocation() {
      try {
        let check = this.validateLocation(this.location.barcode);
        if (check) {
          const res = await getBoxByLocationIdForTestCount(this.location.id);
          const data = res.data || [];
          this.listBoxInLocation = data.data || [];
          this.newBox = data.barcode_new || [];
          this.notification('Get  box in location success.', 'success');
          this.focusByElClass('el-form-item-box-id');
        } else {
          this.notification('Location is invalid.', 'error');
          this.location.id = '';
          this.location.barcode = '';
          this.listBoxInLocation = [];
          this.newBox = [];
        }
      } catch (e) {
        const data = e.response?.data;
        let message = this.$t('Get box error.');
        if (!isEmpty(data) && data != undefined) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.notification(message, 'error');
      }
    },
    async scanBoxId() {
      if (this.boxId == '') return;
      this.isNewBox = false;
      try {
        const currentIndex = this.listBoxInLocation.findIndex(
          (item) => item.barcode.toUpperCase() == this.boxId.toUpperCase()
        );
        if (currentIndex >= 0) {
          this.listBoxInLocation[currentIndex].is_success = true;
          const dataSuccess = this.listBoxInLocation[currentIndex];
          this.listBoxInLocation.splice(currentIndex, 1);
          this.listBoxInLocation.unshift(dataSuccess);
          this.listBoxInLocation = [
            ...new Map(
              this.listBoxInLocation.map((obj) => [JSON.stringify(obj), obj])
            ).values(),
          ];
          this.notification('Scan box' + this.boxId + ' success', 'success');
        } else {
          const res = await scanBox({
            barcode: this.boxId,
            location_id: this.location.id,
          });
        }
        this.boxId = '';
        this.focusByElClass();
      } catch (e) {
        const data = e.response?.data;
        let message = this.$t('Box does not exist');
        if (!isEmpty(data) && data != undefined) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          if (keyFirstData === 'barcode_another_location') {
            let dataBoxMoving = {
              barcode: data.box.barcode,
              product_name: data.product.name,
            };
            this.addNewMoving(dataBoxMoving);
          } else if (keyFirstData === 'barcode') {
            const messageBarcodeNotFound = firstData[0];
            this.notification(messageBarcodeNotFound, 'error');
          } else if (keyFirstData === 'barcode_not_product') {
            let isExistBox = this.newBox.filter(
              (item) => item.barcode == this.boxId
            );
            if (isExistBox.length == 0) {
              this.isNewBox = true;
              message = this.$t('Box new needs to be added gtin');
              this.notification(message, 'success');
              this.focusByElClass('el-form-item-gtin');
            } else {
              message = this.$t('Box new already exists in table box new');
              this.notification(message, 'error');
              this.focusByElClass();
              this.boxId = '';
            }
          } else {
            message = firstData[0];
            this.notification(message, 'error');
            this.boxId = '';
            this.focusByElClass();
          }
        } else {
          this.notification('Oops, something went wrong..', 'error');
        }
      }
    },
    addNewMoving(data) {
      this.listBoxInLocation.unshift({
        barcode: data.barcode,
        product_name: data.product_name,
        is_moving: 1,
        is_success: true,
      });
      // loai tru barcode trung trong mang
      this.listBoxInLocation = [
        ...new Map(
          this.listBoxInLocation.map((obj) => [JSON.stringify(obj), obj])
        ).values(),
      ];
      let message = this.$t('Box moved success');
      this.notification(message, 'success');
    },
    async scanGTIN() {
      if (this.gtin == '') return;
      try {
        const res = await searchProductForInventory({ gtin: this.gtin });
        const product = res.data || [];
        if (!isEmpty(product)) {
          this.newBox.unshift({
            barcode: this.boxId,
            gtin: product.gtin,
            quantity: product.gtin_case,
            product_id: product.id,
            style: product.style,
            color: product.color,
            size: product.size,
          });
          // loai tru barcode trung trong mang
          this.newBox = [
            ...new Map(
              this.newBox.map((obj) => [JSON.stringify(obj), obj])
            ).values(),
          ];
          let message = this.$t('Add new box success');
          this.notification(message, 'success');
          (this.gtin = ''), (this.boxId = ''), (this.isNewBox = false);
          this.focusByElClass();
        } else {
          this.notification(this.$t('Product not found.'), 'error');
          this.gtin = '';
          this.focusByElClass('el-form-item-gtin');
        }
      } catch (e) {
        const data = e.response.data;
        let message = this.$t('Add new box error.');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.notification(message, 'error');
        this.gtin = '';
        this.focusByElClass('el-form-item-gtin');
      }
    },
    tableRowClassName(data) {
      return data.row.is_success && data.row.is_success == true
        ? 'is-success'
        : '';
    },
    removeBoxNew(data, index) {
      this.newBox.splice(index, 1);
      this.notification('Remove box new success', 'success');
      if (this.newBox.length == 0) {
        currentTab = this.tabs[0];
      }
    },
    nextTab() {
      let indexCurrent = this.tabs.findIndex((item) => item == this.currentTab);
      indexCurrent++;
      if (this.tabs[indexCurrent] == 'new_box' && this.newBox.length == 0) {
        indexCurrent++;
      }
      this.currentTab = this.tabs[indexCurrent];
    },
    prevTab() {
      let indexCurrent = this.tabs.findIndex((item) => item == this.currentTab);
      indexCurrent--;
      if (this.tabs[indexCurrent] == 'new_box' && this.newBox.length == 0) {
        indexCurrent--;
      }
      this.currentTab = this.tabs[indexCurrent];
    },
    countLostBox() {
      const boxs = this.listBoxInLocation.filter(
        (item) => item.is_success !== true && item.is_moving !== 1
      );
      return boxs.length;
    },
    async onComplete() {
      if (!this.scanEmployeeID()) return;
      this.isLoading = true;
      try {
        let data = {
          location_id: this.location.id,
          note: this.note,
          employee_id: this.employee_id,
          id_time_checking: this.id_time_checking,
        };
        let barcodeExisted = this.listBoxInLocation
          .filter((item) => item.is_success === true && item.is_moving !== 1)
          .map((item) => item.barcode);
        barcodeExisted =
          barcodeExisted && barcodeExisted.filter((item) => item);

        let barcodeMoving = this.listBoxInLocation
          .filter((item) => item.is_moving === 1)
          .map((item) => item);
        barcodeMoving = barcodeMoving && barcodeMoving.filter((item) => item);

        let barcodeNotFound = this.listBoxInLocation
          .filter((item) => item.is_success === false)
          .map((item) => item.barcode);
        barcodeNotFound =
          barcodeNotFound && barcodeNotFound.filter((item) => item);

        data.barcode_found = barcodeExisted;
        data.barcode_moving = barcodeMoving;
        data.barcode_not_found = barcodeNotFound;
        data.barcode_new = this.newBox;

        await add(data);
        this.isLoading = false;
        this.notification(this.$t('Test count add successfully.'), 'success');
      } catch (e) {
        const data = e.response.data;
        this.isLoading = false;
        let message = this.$t('Test count add error.');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.notification(message, 'error');
      }
    },
    async pauseTest() {
      this.isLoading = true;
      try {
        let data = {
          location_id: this.location.id,
          note: this.note,
        };
        let barcodeExisted = this.listBoxInLocation
          .filter((item) => item.is_success === true && item.is_moving !== 1)
          .map((item) => item.barcode);
        barcodeExisted =
          barcodeExisted && barcodeExisted.filter((item) => item);

        let barcodeMoving = this.listBoxInLocation
          .filter((item) => item.is_moving === 1)
          .map((item) => item);
        barcodeMoving = barcodeMoving && barcodeMoving.filter((item) => item);

        let barcodeNotFound = this.listBoxInLocation
          .filter((item) => item.is_success === false)
          .map((item) => item.barcode);
        barcodeNotFound =
          barcodeNotFound && barcodeNotFound.filter((item) => item);

        data.barcode_found = barcodeExisted;
        data.barcode_moving = barcodeMoving;
        data.barcode_not_found = barcodeNotFound;
        data.barcode_new = this.newBox;
        data.id_time_checking = this.id_time_checking;

        await pause(data);
        this.isLoading = false;
        this.notification(this.$t('Test count pause successfully.'));
      } catch (e) {
        console.log(e);
        const data = e.response.data;
        this.isLoading = false;
        let message = this.$t('Test count add pause error.');
        if (!isEmpty(data)) {
          const keyFirstData = Object.keys(data)[0];
          const firstData = data[keyFirstData];
          message = firstData[0];
        }
        this.notification(message, 'error');
      }
    },
    focusByElClass(elClass = 'el-form-item-box-id') {
      this.$nextTick(() => {
        const el = document.getElementsByClassName(elClass);
        if (!el || !el.length) {
          return;
        }
        const firtsElError = (el && el[0]) || undefined;
        const input = firtsElError.querySelector('input');
        // document.body.scrollTop = (input.offsetTop - 120) | 0;
        input.focus();
      });
    },
  },
};
