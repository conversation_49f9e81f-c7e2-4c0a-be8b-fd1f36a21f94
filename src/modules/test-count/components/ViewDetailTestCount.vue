<template>
  <div class="">
    <el-dialog
        v-model="openDialogViewBox"
        :close-on-click-modal="false"
        width="60%"
    >
      <template #title> View Detail Test Count for {{location}} </template>
      <template #default>
        <div class="statistical mb-3">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="border radius p-3">
                <span class="label mr-2">Total Box:</span>
                <span class="value" v-loading="isLoading">{{
                    totalBox
                  }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="border radius p-3">
                <span class="label mr-2">Total Quantity:</span>
                <span class="value" v-loading="isLoading">{{
                    totalProduct
                  }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="border radius p-3">
                <span class="label mr-2">Lost:</span>
                <span class="value" v-loading="isLoading">{{
                    totalProductNotFound
                  }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
        <el-table
            border
            :data="items"
            style="width: 100%"
            size="small"
            class="el-table-expand-purchase-order-box"
            v-loading="isLoading"
            height="500"
            :row-class-name="tableRowClassName"
        >
          <el-table-column :label="$t('Box')" min-width="150">
            <template #default="scope">
              <router-link
                  :to="{ path: '/box', query: { keyword: location, type: 'location', limit: 25, page: 1 }}"
                  class="text-[#409eff]"
              >
                {{ scope.row.barcode }}
              </router-link>
            </template>
          </el-table-column>
          <el-table-column :label="$t('Product')" min-width="250">
            <template #default="scope">
              {{ scope.row.product_name }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('Quantity')" min-width="150">
            <template #default="scope">
              {{ scope.row.quantity }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('Result')" min-width="150">
            <template #default="scope">
              <el-tag v-if="scope.row.type === 'not_found'" class="mr-2 mb-1" size="small" type="danger">
                {{$t("Not Found")}}
              </el-tag>

              <el-tag v-if="scope.row.type === 'found'" class="mr-2 mb-1" size="small" type="success">
                {{$t("Found")}}
              </el-tag>

              <el-tag v-if="scope.row.type === 'box_moving'" class="mr-2 mb-1" size="small" type="warning">
                {{$t("Move In")}}
              </el-tag>

              <el-tag v-if="scope.row.type === 'box_new'" size="small mb-2" type="primary">
                {{$t("New")}}
              </el-tag>

            </template>
<!--            <template #default="scope">-->
<!--               {{ typeBox(scope.row.type) }}-->
<!--            </template>-->
          </el-table-column>

        </el-table>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import { details } from "@/api/testCount.js";
  import EventBus from "@/utilities/eventBus.js";

  export default {
    name: "ViewDetailTestCount",
    data() {
      return {
        openDialogViewBox: false,
        items: [],
        isLoading: false,
        location:'',
        totalProduct: 0,
        totalProductNotFound: 0,
        totalBox: 0
      }
    },
    watch: {},
    created() {
      EventBus.$on("showViewDetailTestCount", (data) => {
        this.item = data;
        console.log(data);
        this.location = data.location
        this.openDialogViewBox = true;
        this.fetchDetailTestCount()
      });
    },
    computed: {},
    mounted() {
    },
    methods: {
      async fetchDetailTestCount() {
        if (!this.item || !this.item.id) {
          return;
        }
        this.isLoading = true;
        const res = await details(this.item.id);
        this.items = res.data.data || [];
        this.totalProduct = res.data.total_product;
        this.totalProductNotFound = res.data.total_product_not_found;
        this.totalBox = res.data.total_box;
        this.isLoading = false;
      },
      typeBox(type){
        if(type === "not_found"){
          return "Not Found"
        }
        if(type === "found"){
          return "Found"
        }
        if(type === "box_moving"){
          return "Move In"
        }
        if(type === "box_new"){
          return "New"
        }
      }

    }

  }
</script>

<style lang="scss">

</style>