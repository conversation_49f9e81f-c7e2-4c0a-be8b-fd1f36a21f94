import EventBus from "@/utilities/eventBus.js";
import UpdateUser from "@/modules/user/views/Update/Index.vue";
import CreateUser from "@/modules/user/views/Create/Index.vue";
import { mapGetters } from "vuex";
import {deleteUser, listAll} from "@/api/user";
import helperMixin from '@/mixins/helpers.js';
import { fullList } from "@/api/department.js";

export default {
  name: "User",
  components: {
    CreateUser,
    UpdateUser,
  },
  mixins: [helperMixin],
  data() {
    return {
      items: [],
      isLoading: false,
      departments: [],
      getAllUsers: [],
      filter: this.setDefaultFilter(),
    };
  },
  computed: {
    maxHeight() {
      return parseInt(window.innerHeight - 140);
    },
    ...mapGetters(['getUserRoles']),
    userRolesWithAdmin() {
      return [...this.getUserRoles, { id: 'admin', name: 'Admin' }];
    }

  },
  beforeUnmount() {
    EventBus.$off("showCreateUser");
    EventBus.$off("showEditUser");
  },
  mounted() {
    this.fetchData();
    this.fetchWarehouse();
    this.fetchUserRole();
    this.fetchStore();
    this.fetchDepartments();
  },
  methods: {
    async fetchStore() {
      await this.$store.dispatch("getStores", { without_pagination: 1 });
    },
    getWarehouses(item) {
      if (Number(item.is_admin) === 1) return 'All';
      let output = '';
      for (let warehouse of item.warehouses) {
        output += warehouse.name + ' || ';
      }
      return output.substr(0, output.length - 4);
    },
    async fetchUserRole() {
      await this.$store.dispatch("getUserRoles");
    },
    async fetchWarehouse() {
      await this.$store.dispatch("getWarehouses");
    },
    createUser() {
      EventBus.$emit("showCreateUser");
    },
    async fetchData() {
      this.fetchUser();
    },
    async fetchUser() {
      this.isLoading = true;
      const params = {
        name : this.filter.name,
        role : this.filter.role,
        department : this.filter.department,
      }
      console.log(params)
      const res  = await listAll(params);
      this.getAllUsers = res?.data || [];
      this.isLoading = false;
    },
    updateUser(item) {
      EventBus.$emit("showUpdateUser", item);
    },
    async deleteUser(id) {
      try {
        const res = await deleteUser(id);
        await this.fetchData();
        this.notification(res.data.message);
      } catch (e) {
        this.notification(e.response.data.message, "error");
      }
    },
    setDefaultFilter() {
      return {
        name: "",
        role: "",
        department: "",
      };
    },
    onFilter() {
      this.fetchUser();
    },
    onClearFilter(){
      this.filter = this.setDefaultFilter();
      this.fetchData();
    },
    async fetchDepartments() {
      const { data } = await fullList();
      this.departments = data;
    },
  },
};
