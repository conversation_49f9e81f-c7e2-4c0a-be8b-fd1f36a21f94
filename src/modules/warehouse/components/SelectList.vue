<template>
  <div class="my-5">
    <div class="font-bold" :class="[checkSku ? 'mb-2' : '']">{{ $t(label) }}</div>
    <div class="mt-2 text-sm" v-if="type === 'selectMerchant'">Note: If one merchant is ON and the condition to check bypass zipcode is ticked, it means this warehouse will receive orders from this merchant</div>
    <div v-if="type === 'selectMerchant'"> and will not check the zipcode condition below.</div>
    <p class="text-[12px] w-72 lg:w-full">{{ $t(description) }}</p>
    <div :class="[checkSku ? 'bg-[#F0F4FB] rounded-md px-6 py-1' : '']">
      <div v-if="type != 'fix'" class="mt-4">
        <el-select class="w-56 mr-2" v-if="['select', 'selectMerchant'].includes(type)" v-model="input" :placeholder="$t('Select ' + label)"
          filterable>
          <el-option v-for="store in filterList" :key="store.id" :label="labelSelect(store)" :value="store.id">
            <div class="item-select">
              <span class="store-name">{{ store.name }}</span>
              <i class="account-name text-xs">{{ store?.account?.name }}</i>
            </div>
          </el-option>
        </el-select>
        <el-select class="w-56 mr-2" v-if="['selectCarrier'].includes(type)" v-model="input" :placeholder="$t('Exclude Shipping Carrrier')"
                   filterable>
          <el-option v-for="carrier in filterListCarrier" :key="carrier.id" :label="carrier.name" :value="carrier.code">
            <div class="item-select">
              <span class="carrier-name">{{ carrier.name }}</span>
              <i class="account-name text-xs">{{ carrier.code }}</i>
            </div>
          </el-option>
        </el-select>
        <el-select class="w-56 mr-2" v-if="type == 'selectShipping'" v-model="input" :placeholder="$t('Select ' + label)" filterable>
          <el-option v-for="shipping in filterList" :key="shipping.id" :label="labelSelectShipping(shipping)"
            :value="shipping.id">
            <div class="item-select">
              <span class="store-name">{{ shipping.name }}</span>
            </div>
          </el-option>
        </el-select>
        <el-input v-if="type == 'input' && !checkSku" class="!w-56 mr-2" v-model="input" />
        <button v-if="!checkSku" class="rounded py-1 px-3 text-white bg-blue-400 hover:bg-blue-500"
          @click.prevent="handleAddItem">
          {{ $t('Add') }}
        </button>
        <!-- enable/disable list sku -->
        <div v-if="checkSku">
          <el-select class="w-40 mr-2" v-model="product.style" @change="selectStyle" filterable
            :placeholder="$t('Choose style')">
            <el-option v-for="item in styles" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select class="w-40 mr-2" v-model="product.color" @change="selectColors" filterable
            :placeholder="$t('Choose color')">
            <el-option v-for="item in colors" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <span class="mr-2">or</span>
          <el-input v-if="type == 'input'" class="!w-40 mr-2" v-model="sku" placeholder="Enter SKU"
            @keyup.enter="getProductBySku" />
          <p class="text-[12px] text-red-400 self-center break-normal" v-if="errorInputSku">
            {{ $t(errorInputSku) }}
          </p>
          <!--list sku filter sau khi enter -->
          <div v-if="filterLists.length">
            <div v-for="(item, idx) in filterLists" :key="idx" class="item-select item-sku w-full  mt-5">
              <div class="d-flex justify-space-between">
                <div class="w-[200px]">
                  <div class="d-flex">
                    <span class="store-name d-flex w-[110px]">{{ item.sku }}</span>
                    <span
                      :class="[generateBGColorFilter(item), generateTextColorFilter(item), 'd-flex', 'rounded-xl', 'px-2', 'py-[1px]', 'pt-[3px]', 'text-xs', 'text-center']">{{
                      generateStockFilter(item) }}</span>
                  </div>
                  <span class="account-name text-xs">Size: {{
                    item.size
                  }}</span>
                </div>
                <el-switch class="" v-model="item.status" @change="toggleSKU(item)"
                  style="--el-switch-on-color: #13ce66" />
              </div>
            </div>
          </div>
          <el-divider class="!mb-1"></el-divider>
          <p class="mt-4" v-if="checkSku"> Active SKU </p>
        </div>
      </div>
      <!-- list sku co trong warehouse -->
      <div v-if="type == 'selectCarrier'" class="mt-2 text-[#7A8896]">
        Note: add a Shipping Carrier to be excluded from the warehouse
      </div>
      <el-row>
        <el-col :span="span" v-for="(item, index) in selectedList" class=" d-flex"
          :class="[checkSku ? '!mt-3' : 'mt-5']" :key="label + index">
          <div class="w-56 item-select item-select-disable" v-if="type == 'select' && !!item.value">
            <span class="store-name">{{ getNameFromList(item.value) }}</span>
            <span class="account-name text-xs">{{
              getAccountNameFromList(item.value)
            }}</span>
          </div>
          <div
              class="w-56 item-select item-select-disable flex items-center justify-between"
              v-if="type == 'selectMerchant' && !!item.value"
          >
            <div>
              <span class="store-name font-bold text-gray-800 block">{{ getNameFromList(item.value) }}</span>
              <span class="account-name text-xs text-gray-600">{{ getAccountNameFromList(item.value) }}</span>
            </div>
          </div>

          <div class="w-56 item-select item-select-disable" v-if="type == 'selectShipping' && !!item.value">
            <span class="store-name">{{ getNameFromList(item.value) }}</span>
            <span class="account-name text-xs">{{
              getAccountNameFromList(item.value)
            }}</span>
          </div>
          <div class="w-56 item-select item-select-disable flex" v-if="type == 'selectCarrier' && !!item.value">
            <div class="mr-2">{{ getNameFromListCarrier(item.value) }} </div>
            <div class="text-xs block text-gray-500">
                {{ getAccountNameCarrierList(item.value) }}
            </div>
          </div>
          <div class="w-56 item-select item-select-disable" v-if="type == 'fix' && !!item.value">
            <span class="store-name">{{ getNameFromList(item.value) }}</span>
          </div>

          <div class="item-select" :class="[checkSku ? 'item-sku w-full' : 'item-select-disable w-56']"
            v-if="type == 'input' && !!item.value">
            <div class="d-flex justify-space-between" v-if="checkSku">
              <div class="w-[200px]">
                <div class="d-flex">
                  <span class="store-name d-flex w-[110px]">{{ item.value }}</span>
                  <span
                    :class="[generateBGColor(item), generateTextColor(item), 'd-flex', 'rounded-xl', 'px-2', 'py-[1px]', 'pt-[3px]', 'text-xs', 'text-center']">{{
                    generateStock(item) }}</span>
                </div>
                <span class="account-name text-xs">{{ item.size }}</span>
              </div>
              <div class="mt-[1px] flex">
                <el-switch class="mr-4" v-model="item.status" style="--el-switch-on-color: #13ce66" />
              </div>
            </div>
            <span v-else class="store-name">{{ item.value }}</span>
          </div>

          <div v-if="!checkSku" class="inline-flex my-auto">
            <!--   cần check để có thể chuyển đổi confirm zipcode         -->
            <div v-if="type == 'selectMerchant'" class="flex items-center">
              <el-switch
                  class="mx-5"
                  v-model="item.status"
                  style="--el-switch-on-color: #13ce66"
                  @change="changeActiveMerchant(item)"
              />
              <div class="mr-3">|</div>
              <el-tooltip
                  effect="dark"
                  placement="top-start"
                  :content="(item.statusZipcode ? 'Store bypass on zip code validation is ON.' : 'Store bypass on zip code validation is OFF.')"
              >
              <el-checkbox v-model="item.statusZipcode" :disabled="!item.status" :label="'Bypass zipcode'"></el-checkbox>
              </el-tooltip>
            </div>
            <el-switch v-else class="mx-5" v-model="item.status" style="--el-switch-on-color: #13ce66" />

          <el-input-number
          v-if="type == 'fix' && !!item.value && item.value === 'tiktok_order'"
            v-model="inputLimitTiktok"
            :min="0"
            :max="9999999"
          ></el-input-number>

          <el-input-number
          v-if="type == 'fix' && !!item.value && item.value === 'mugs_single_order'"
            v-model="inputLimitSingleMug"
            :min="0"
            :max="9999999"
          ></el-input-number>

          <el-input-number
          v-if="type == 'fix' && !!item.value && item.value === 'inner_neck_label' || type == 'fix' && !!item.value && item.value === 'outer_neck_label'" 
            v-model="inputLimitNeck"
            :min="0"
            :max="9999999"
          ></el-input-number>

        <div class="relative" v-if="type != 'fix' && buttonDelete">
              <div class="bg-white shadow absolute w-[210px] z-10 p-4 right-0" :class="{
                block: indexItem === index,
                hidden: indexItem !== index,
              }">
                <div class="text-sm">Are you sure to delete this?</div>
                <div class="mt-2">
                  <el-button size="small" @click="closePopConfirm">No</el-button>
                  <el-button type="primary" size="small" @click="removeItem(index)">Yes</el-button>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-dialog v-model="dialogVisible" title="Confirm" width="30%" @close="closeDialogConfirm">
        <span>Are you sure to delete this?</span>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="closeDialogConfirm">Cancel</el-button>
            <el-button type="primary" @click="confirmDeleted">
              Confirm
            </el-button>
          </span>
        </template>
      </el-dialog>
    </div>

  </div>
</template>

<script>
import {fetchProductByAttribute, getInfoProduct} from '@/api/product';
import {Warning} from '@element-plus/icons-vue';
import {isEmpty} from 'ramda';
import EventBus from "@/utilities/eventBus";
import formatNumberMixin from '@/mixins/formatNumber.js';

export default {
  components: {
    Warning,
  },
  mixins: [formatNumberMixin],
  data() {
    return {
      indexItem: undefined,
      input: null,
      errorInputSku: null,
      dataInfo: [],
      outOfStock: 'OOS',
      incoming: 'Incoming',
      inStock: 'In_stock',
      product: {
        style: null,
        color: null,
      },
      colors: [],
      filterLists: [],
      sku: null,
      dialogVisible: false,
      indexItemDelete: null,
      inputLimitTiktok: this.limitTiktok,
      inputLimitSingleMug: this.limitSingleMug,
      inputLimitNeck: this.limitNeck
    };
  },
  computed: {
    filterList() {
      return this.list.filter(
        (__) => !this.selectedList.find((s) => s.value == __.id)
      );
    },
    filterListCarrier() {
      return this.list.filter(
          (__) => !this.selectedList.find((s) => s.value == __.code)
      );
    },
  },
  beforeUnmount() {
    EventBus.$off('closeDialog');
  },
  mounted() {
    EventBus.$on('closeDialog', () => {
      this.product = {
        style: null,
        color: null,
      };
      this.sku = null;
      this.filterLists = [];
      this.indexItem = undefined;
    });
    if (this.orderValue) {
      this.selectedList.sort((a, b) => a.value.localeCompare(b.value));
    }
  },
  props: {
    selectedList: {
      type: Array,
      default: [],
    },
    span: {
      type: Number,
      default: 24,
    },
    list: {
      type: Array,
      default: [],
    },
    label: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: 'input',
    },
    description: {
      type: String,
      default: '',
    },
    buttonDelete: {
      type: Boolean,
      default: true,
    },
    checkSku: {
      type: Boolean,
      default: false,
    },
    orderValue: {
      type: Boolean,
      default: false,
    },
    resetForm: {
      type: Boolean,
      default: false,
    },
    attributes: {
      type: Array,
      default: [],
    },
    styles: {
      type: Array,
      default: [],
    },
    warehouseId: {
      type: Number,
      default: null,
    },
    limitTiktok: {
      type: String,
      default: null,
    },
    limitNeck: {
      type: String,
      default: null,
    },
    limitSingleMug: {
      type: String,
      default: null,
    },
    byPassZipcode: {
      type: Array,
      default: [],
    }

  },
  watch: {
    limitTiktok(newVal) {
      this.inputLimitTiktok = newVal;
    },
    limitNeck(newVal) {
      this.inputLimitNeck = newVal;
    },
    limitSingleMug(newVal) {
      this.inputLimitSingleMug  = newVal;
    },
    inputLimitTiktok(newVal) {
      this.$emit('update:limitTiktok', newVal);
    },
    inputLimitNeck(newVal) {
      this.$emit('update:limitNeck', newVal);
    },
    inputLimitSingleMug(newVal) {
      this.$emit('update:limitSingleMug', newVal);
    },

  },
  methods: {
    closePopConfirm() {
      this.indexItem = undefined;
    },
    removeItem(index) {
      this.selectedList.splice(index, 1);
      this.indexItem = undefined;
    },
    openDialogConfirm(index) {
      this.dialogVisible = true;
      this.indexItemDelete = index
    },
    confirmDeleted() {
      this.selectedList.splice(this.indexItemDelete, 1);
      this.dialogVisible = false;
    },
    closeDialogConfirm() {
      this.indexItemDelete = null;
      this.dialogVisible = false;
    },
    toggleSKU(item) {
      let isUpdate = false;
      this.selectedList.forEach(i => {
        if (i.value == item.sku) {
          i.status = item.status;
          isUpdate = true;
          return;
        }
      });
      if (isUpdate) return;
      if (item.status) {

        let data = {
          stock_status: 'OOS',
          stock: 0,
          incoming_stock: 0,
        };
        item.product_quantities.forEach((i) => {
          if (i?.warehouse_id == this.warehouseId) {
            if (i?.quantity > 0) {
              data = {
                stock_status: i?.quantity + ' items',
                stock: i?.quantity,
                incoming_stock: i?.incoming_stock ?? 0,
              }
            } else if (i?.incoming_stock > 0) {
              data = {
                stock_status: 'Incoming',
                stock: 0,
                incoming_stock: i?.incoming_stock,
              }
            } else {
              data = {
                stock_status: 'OOS',
                stock: 0,
                incoming_stock: 0,
              }
            }
          }
        });
        this.selectedList.unshift({ value: item.sku, status: true, ...data });
        this.dataInfo = this.dataInfo.concat({
          sku: item.sku,
          style: item.style,
          color: item.color,
          size: item.size,
        });
      }
      // else {
      //   let index = this.selectedList.findIndex((__) => __.value == item.sku);
      //   if (index == -1) return;
      //   this.selectedList[index]
      //   this.selectedList.splice(index, 1);
      // }
    },
    async selectColors() {
      const { data } = await fetchProductByAttribute({ ...this.product, warehouse_id: this.warehouseId });
      this.filterLists = this.generateStatus(data || []);
    },
    async getProductBySku() {
      this.sku = this.sku?.trim();
      if (!this.sku) return;
      const response = await fetchProductByAttribute({ warehouse_id: this.warehouseId, sku: this.sku });
      this.filterLists = this.generateStatus(response?.data || []);
      if (response?.data && response?.data?.length > 0) {
        this.product.style = response?.data[0]?.style;
        this.product.color = response?.data[0]?.color;
      }
    },
    async selectStyle() {
      this.product.color = '';
      if (!this.product.style) {
        this.colors = [];
        this.sizes = [];
        return;
      }
      const currentStyle = this.attributes[this.product.style];
      let colors = currentStyle.colors;
      colors =
        colors.length &&
        colors.map((item) => {
          return {
            label: item,
            value: item,
          };
        });
      this.colors = colors;
      const { data } = await fetchProductByAttribute({ ...this.product, warehouse_id: this.warehouseId });
      this.filterLists = this.generateStatus(data || []);
    },
    generateStatus(data) {
      data.forEach((item) => {
        this.selectedList.forEach((i) => {
          if (i.value == item.sku) {
            item.status = i.status;
          }
        });
      });
      return data;
    },
    generateStock(item) {
      switch (item?.stock_status) {
        case this.outOfStock:
          return 'OOS';
          break;
        case this.incoming:
          return 'Incoming';
          break;
        default:
          return this.formatNum(item?.stock ?? 0) + ' items';

      }
    },
    generateBGColor(item) {
      switch (item?.stock_status) {
        case this.outOfStock:
          return 'bg-[#F8EAEB]';
          break;
        case this.incoming:
          return 'bg-[#ECF1FD]';
          break;
        default:
          return 'bg-[#EBF5EF]';
          break;
      }
    },
    generateStockFilter(item) {
      let result = 'OOS';
      if (item.product_quantities?.length && item.product_quantities?.length == 0) {
        return result;
      }
      item.product_quantities.forEach((i) => {
        if (i?.warehouse_id == this.warehouseId) {

          if (i?.quantity > 0) {
            result = this.formatNum(i?.quantity ?? 0) + ' items';
          } else if (i?.incoming_stock > 0) {
            result = 'Incoming';
          } else {
            result = 'OOS';
          }
        }
      });
      return result;
    },
    generateBGColorFilter(item) {
      let result = 'bg-[#F8EAEB]';
      if (item.product_quantities?.length && item.product_quantities?.length == 0) {
        return result;
      }
      item.product_quantities.forEach((i) => {
        if (i?.warehouse_id == this.warehouseId) {
          if (i?.quantity > 0) {
            result = 'bg-[#EBF5EF]';
          } else if (i?.incoming_stock > 0) {
            result = 'bg-[#ECF1FD]';
          }
        }
      });
      return result;
    },
    generateTextColorFilter(item) {
      let result = 'text-[#BF3639]';

      if (item.product_quantities?.length && item.product_quantities?.length == 0) {
        return result;
      }
      item.product_quantities.forEach((i) => {
        if (i?.warehouse_id == this.warehouseId) {
          if (i?.quantity > 0) {
            result = 'text-[#3F9763]';
          } else if (i?.incoming_stock > 0) {
            result = 'text-[#4D6FE4]';
          }
        }
      });
      return result;
    },
    generateTextColor(item) {
      switch (item?.stock_status) {
        case this.outOfStock:
          return 'text-[#BF3639]';
        case this.incoming:
          return 'text-[#4D6FE4]';
        default:
          return 'text-[#3F9763]';
      }
    },
    async handleAddItem() {
      if (!this.input) return;
      if (['input', 'selectMerchant', 'selectCarrier'].includes(this.type)) {
        if (
          this.selectedList.filter((__) => !!__.value && __.value == this.input)
            .length > 0
        )
          return (this.errorInputSku =
            this.label + ' is duplicated. Please check again.');

        if (this.checkSku) {
          const { data } = await getInfoProduct({ sku: [this.input] });
          if (!isEmpty(data)) {
            this.dataInfo = this.dataInfo.concat(data);
          } else {
            return (this.errorInputSku = 'SKU not found.');
          }
        }
        this.selectedList.unshift({ value: this.input, status: true });
      } else {
        this.selectedList.push({ value: this.input, status: true });
      }
      this.input = null;
      this.errorInputSku = null;

      if (this.orderValue) {
        this.selectedList.sort((a, b) => a.value.localeCompare(b.value));
      }
    },
    async getListProduct() {
      const response = await fetchProductByAttribute(this.product);
    },
    getNameFromList(id) {
      const item = this.list.find((__) => __.id == id);
      return item?.name || '';
    },
    getAccountNameFromList(id) {
      const item = this.list.find((__) => __.id == id);
      return item?.account?.name || '';
    },
    getNameFromListCarrier(code) {
      const item = this.list.find((__) => __.code == code);
      return item?.name || '';
    },
    getAccountNameCarrierList(code) {
      const item = this.list.find((__) => __.code == code);
      return item?.code || '';
      return ' ('+ item?.code + ')' || '';
    },
    labelSelect(store) {
      return store.name + ' - ' + store?.account?.name;
    },
    labelSelectShipping(shippingAccount) {
      return shippingAccount.name;
    },
    updateLimitTiktok(newValue) {
      this.inputLimitTiktok = newValue;
    },
    updateLimitSingleMug(newValue) {
      this.inputLimitSingleMug = newValue;
    },
    updateLimitNeck(newValue) {
      this.inputLimitNeck = newValue;
    },
    handleConfirmStatusZipcode(item){
      item.statusZipcode = !item.statusZipcode
    },
    changeActiveMerchant(item) {
      item.statusZipcode = false
    }

  },
};
</script>
<style scoped lang="scss">
.d-inline-block {
  display: inline-block;
  margin-top: auto;
  margin-bottom: auto;
}

.d-flex {
  display: flex;
}

.item-select {
  display: inline-block;
  padding: 5px 10px;

  .account-name {
    display: flex;
    color: #999;
    line-height: 1;
  }
}

.item-select-disable {
  border: solid 1px #ccc;
  border-radius: 5px;
}

.item-sku {
  border-radius: 5px;
  background-color: white;
}

.el-select-dropdown__item {
  height: auto;
  line-height: 1.4;
}
</style>
