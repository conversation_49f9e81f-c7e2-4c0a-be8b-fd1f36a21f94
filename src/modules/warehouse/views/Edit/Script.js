import EventBus from "@/utilities/eventBus.js";
import { update } from "@/api/warehouse.js";
import { countries, fetchStateByCountry } from "@/api/default.js";
import { CloseBold } from '@element-plus/icons-vue';
import { S3_URL } from "@/utilities/constants";

export default {
  name: "WarehouseEdit",
  components: { CloseBold },
  data() {
    return {
      warehouse: {},
      isLoading: false,
      openDialogEitWarehouse: false,
      warehouseRules: {
        name: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: "blur",
          },
        ],
        code: [
          {
            required: true,
            message: this.$t('This field cannot be left blank.'),
            trigger: 'change',
          },
        ],
      },
      countries: [],
      states: [],
      currentCountry: "",
      currentState: "",
      image: null,
      imageUrl: null,
      urlStorage: S3_URL,
    };
  },
  props: {
    timezones: {
      type: Array,
      default: []
    }
  },
  watch: {
    image(value) {
      if (value) {
        this.imageUrl = value
        this.warehouse.image = value;
      }
    }
  },
  mounted() {},
  created() {
    EventBus.$on("showEditWarehouse", (data) => {
      this.openDialogEitWarehouse = true;
      this.warehouse = {
        ...this.warehouse,
        ...data,
      };
      if (this.warehouse.image) {
        this.imageUrl = this.getImageUrl(this.warehouse);
      }
      this.fetchData();
    });
  },
  methods: {
    getImageUrl(item){
      return this.urlStorage + '/' + item.image
    },
    handleRemove() {
      this.$refs.image.value = '';
      this.image = '';
      this.imageUrl = ''
      this.warehouse.image = '';
    },
    handleFileUpload(){
      this.warehouse.image = this.$refs.image.input.files[0];
      this.imageUrl = URL.createObjectURL(this.warehouse.image);
    },
    async fetchData() {
      await this.fetchCountries();
      const country =
        (this.warehouse.country &&
          this.countries.find(
            (item) => item.iso2 === this.warehouse.country
          )) ||
        "";
      if (country && country.id) {
        this.currentCountry = country.id;
        await this.fetchStateByCountry(this.currentCountry);
        const state =
          (this.warehouse.state &&
            this.states.find((item) => item.iso2 === this.warehouse.state)) ||
          "";

        if (state && state.id) {
          this.currentState = state.id;
        }
      }
    },
    resetData() {
      this.$refs["editWarehouseForm"].resetFields();
      this.handleRemove();
      this.warehouse = {
        name: "",
        phone: "",
        street1: "",
        street2: "",
        country: "",
        city: "",
        state: "",
        zip: "",
        from_name: "",
        time_zone: "",
        code: '',
        is_stock: false,
        image: ''
      };
    },
    async fetchCountries() {
      const res = await countries();
      this.countries = res.data || [];
    },
    async fetchStateByCountry(id) {
      const res = await fetchStateByCountry(id);
      this.states = res.data || [];
    },
    async changeCountry() {
      this.currentState = "";
      await this.fetchStateByCountry(this.currentCountry);
    },
    async onSubmit(formName) {
      const isValid = await this.$refs[formName].validate();
      if (!isValid) {
        return;
      }
      const code = String(this.warehouse.code);
      if (code.length != 2 || !(/^[A-Z]*$/.test(code))) {
        this.notification('WIP Label 2 characters and uppercase', 'error');
        return;
      }
      this.isLoading = true;
      try {
        const country =
          (this.currentCountry &&
            this.countries.find((item) => item.id === this.currentCountry)) ||
          "";
        const state =
          (this.currentState &&
            this.states.find((item) => item.id === this.currentState)) ||
          "";
        this.warehouse.country = (country && country.iso2) || "";
        this.warehouse.state = (state && state.iso2) || "";
        let formData = new FormData();
        formData.append('id', this.warehouse.id ?? '');
        formData.append('name', this.warehouse.name ?? '');
        formData.append('phone', this.warehouse.phone ?? '');
        formData.append('street1', this.warehouse.street1 ?? '');
        formData.append('street2', this.warehouse.street2 ?? '');
        formData.append('country', this.warehouse.country ?? '');
        formData.append('city', this.warehouse.city ?? '');
        formData.append('state', this.warehouse.state ?? '');
        formData.append('zip', this.warehouse.zip ?? '');
        formData.append('from_name', this.warehouse.from_name ?? '');
        formData.append('time_zone', this.warehouse.time_zone ?? '');
        formData.append('code', this.warehouse.code ?? '');
        formData.append('color', this.warehouse.color ?? '');
        formData.append('is_stock', this.warehouse.is_stock ? 1 : 0);
        formData.append('_method', 'PUT');
        formData.append('image', this.warehouse.image ?? '');

        await update(this.warehouse.id, formData);
        this.notification(this.$t("Warehouse save successfully."));
        this.openDialogEitWarehouse = false;
        this.$emit("refresh");
        this.$store.dispatch("getUserProfile");
      } catch (e) {
        let message = this.$t("Warehouse save error.");
        this.notification(message, "error");
      } finally {
        this.isLoading = false;
      }
    },
  },
};
