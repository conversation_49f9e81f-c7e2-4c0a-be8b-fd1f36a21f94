import EventBus from "@/utilities/eventBus";
import { create } from "@/api/weightCubic";

export default {
    name: "CreateWeightCubic",
    data() {
        return {
            dialogVisible: false,
            data: this.setDefaultData(),
            serverErrors: [],
            isLoading: false
        }
    },
    created() {
        EventBus.$on("showCreateWeightCubic", () => {
            this.data = this.setDefaultData();
            this.serverErrors = [];
            this.dialogVisible = true;
        });
    },
    methods: {
        isError(field) {
            return !!this.serverErrors[field];
        },
        getErrorMessage(field) {
            return this.serverErrors[field][0];
        },
        setDefaultData() {
            return {
                name_cubic: "",
                weight_start: "",
                weight_end: "",
                height: "",
                long: "",
                width: "",
                cubic: ""
            };
        },
        async onSubmit() {
            if (this.isLoading) return;
            const isValid = await this.$refs.createWeightCubic.validate();
            if (!isValid) {
                return;
            }
            this.isLoading = true;
            this.data.cubic = this.data.long + 'x' + this.data.width + 'x' + this.data.height;
            try {
                const res = await create(this.data);
                this.dialogVisible = false;
                this.notification(res.data.message);
                this.$emit("refresh");
            } catch (e) {
                this.serverErrors = e.response.data.errors;
                let message = e.response.data.message;
                this.notification(message, "error");
            } finally {
                this.isLoading = false;
            }
        },
    }
}
