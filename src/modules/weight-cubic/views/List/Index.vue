<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
    <div>
        <div class="top-head mb-4">
            <div class="top-head-left">
                <h1>{{ $t('Weight Cubic') }}</h1>
            </div>
            <div class="top-head-right">
                <el-button type="primary" @click="createWeightCubic">
                    <span class="icon-margin-right">
                        <icon :data="iconAdd" /></span>{{ $t('Create') }}
                </el-button>
            </div>
        </div>
        <div class="table-content">
            <div class="filter-top">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-input :placeholder="$t('Name')" v-model="filter.name" @keyup.enter="onFilter" />
                    </el-col>
                    <el-col :span="6">
                        <div class="btn-filter">
                            <el-button type="primary" @click="onFilter">
                                <span class="icon-margin-right">
                                    <icon :data="iconFilter" /></span>{{ $t("Filter") }}
                            </el-button>
                            <el-button @click="resetFilter">
                                <span class="icon-margin-right">{{ $t("Reset") }}</span>
                            </el-button>
                        </div>
                    </el-col>
                </el-row>
            </div>
            <el-table border stripe size="small" :data="items" :max-height="maxHeight" v-loading="isLoading" element-loading-text="Loading...">
                <el-table-column prop="id" :label="$t('ID')" min-width="100"></el-table-column>
                <el-table-column prop="name_cubic" :label="$t('Name')" min-width="200"></el-table-column>
                <el-table-column prop="weight_start" :label="$t('Weight Start')" min-width="200"></el-table-column>
                <el-table-column prop="weight_end" :label="$t('Weight End')" min-width="200"></el-table-column>
                <el-table-column prop="cubic" :label="$t('Cubic')" min-width="200"></el-table-column>
                <el-table-column prop="action" :label="$t('Action')" fixed="right" width="100">
                    <template #default="scope">
                        <el-link class="el-link-edit" :underline="false" type="primary" @click="updateWeightCubic(scope.row)">
                            <icon :data="iconEdit" />
                        </el-link>
                        <el-popconfirm :title="'Are you sure to delete ' + scope.row.name_cubic + '?'" @confirm="deleteWeightCubic(scope.row)">
                            <template #reference>
                                <el-link :underline="false" type="danger">
                                    <icon :data="iconDelete" />
                                </el-link>
                            </template>
                        </el-popconfirm>
                    </template>
                </el-table-column>
            </el-table>
            <div class="bottom">
                <div class="total">{{$t('Total:')}} {{ items.length ? formatNumber(total) : 0 }}</div>
                <el-pagination :disabled="isLoading" background layout="prev, pager, next" :page-size="filter.limit" :total="total" @current-change="changePage" v-model:currentPage="filter.page">
                </el-pagination>
                <div class="limit" :disabled="isLoading">
                    <el-select v-model="filter.limit" :placeholder="$t('Select')" size="mini" @change="onFilter">
                        <el-option v-for="item in limits" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </div>
            </div>
        </div>
    </div>
    <create-weight-cubic @refresh="getWeightCubic" />
    <update-weight-cubic @refresh="getWeightCubic" />
</template>