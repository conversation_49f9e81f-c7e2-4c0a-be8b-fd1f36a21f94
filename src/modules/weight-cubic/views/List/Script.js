import { getList, destroy } from "@/api/weightCubic";
import EventBus from "@/utilities/eventBus";
import CreateWeightCubic from "@/modules/weight-cubic/views/Create/Index.vue";
import UpdateWeightCubic from "@/modules/weight-cubic/views/Update/Index.vue";

export default {
    name: "ProductStyleList",
    components: {
        CreateWeightCubic,
        UpdateWeightCubic
    },
    data() {
        return {
            items: [],
            isLoading: false,
            filter: this.setDefaultFilter(),
        }
    },
    computed: {
        maxHeight() {
            return parseInt(window.innerHeight - 189);
        }
    },
    mounted() {
        this.filter = this.getRouteParam();
        this.getWeightCubic();
    },
    methods: {
        setDefaultFilter() {
            return {
                limit: 25,
                page: 1,
                name: ""
            };
        },
        async getWeightCubic() {
            this.isLoading = true;
            this.setRouteParam();
            const { data } = await getList(this.filter);
            this.items = data.data;
            this.total = data.total;
            this.isLoading = false;
        },
        changePage(page) {
            this.filter.page = page;
            this.getWeightCubic();
        },
        onFilter() {
            this.filter.page = 1;
            this.getWeightCubic();
        },
        createWeightCubic() {
            EventBus.$emit("showCreateWeightCubic");
        },
        updateWeightCubic(item) {
            EventBus.$emit("showUpdateWeightCubic", item);
        },
        resetFilter(){
            this.filter = this.setDefaultFilter()
            this.getWeightCubic();
        },
        async deleteWeightCubic(item) {
            const res = await destroy(item.id);
            this.notification(res.data.message);
            this.getWeightCubic();
        }
    }
}