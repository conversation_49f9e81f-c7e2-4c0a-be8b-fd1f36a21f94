<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
    <el-dialog v-model="dialogVisible" destroy-on-close :title="$t('Update Weight Cubic')+ ` #${weightCubicId}`" custom-class="el-dialog-custom" :destroy-on-close="true">
        <el-form status-icon ref="updateWeightCubic" :model="data" @submit.prevent="onSubmit" :label-position="'top'">
            <el-form-item :label="$t('Name')" :class="{'is-error': isError('name_cubic')}" required>
                <el-input v-model="data.name_cubic" @keyup.enter="onSubmit"></el-input>
                <div v-if="isError('name_cubic')" class="el-form-item__error">{{getErrorMessage('name_cubic')}}</div>
            </el-form-item>
            <el-form-item :label="$t('Weight start')" class="w-full mr-2" required :class="{'is-error': isError('weight_start')}">
                <el-input-number v-model="data.weight_start" @keyup.enter="onSubmit" required min="0"></el-input-number>
                <div v-if="isError('weight_start')" class="el-form-item__error">{{getErrorMessage('weight_start')}}</div>
            </el-form-item>
            <el-form-item :label="$t('Weight end')" class="w-full mr-2" required :class="{'is-error': isError('weight_end')}">
                <el-input-number v-model="data.weight_end" @keyup.enter="onSubmit" required min="0"></el-input-number>
                <div v-if="isError('weight_end')" class="el-form-item__error">{{getErrorMessage('weight_end')}}</div>
            </el-form-item>
            <div class="flex">
                <el-form-item :label="$t('Long')" class="w-full mr-2" required :class="{'is-error': isError('long')}">
                    <el-input-number v-model="data.long" @keyup.enter="onSubmit" required min="0"></el-input-number>
                    <div v-if="isError('long')" class="el-form-item__error">{{getErrorMessage('long')}}</div>
                </el-form-item>
                <el-form-item :label="$t('Width')" class="w-full mr-2" required :class="{'is-error': isError('width')}">
                    <el-input-number v-model="data.width" @keyup.enter="onSubmit" required min="0"></el-input-number>
                    <div v-if="isError('width')" class="el-form-item__error">{{getErrorMessage('width')}}</div>
                </el-form-item>
                <el-form-item :label="$t('Height')" class="w-full" required :class="{'is-error': isError('height')}">
                    <el-input-number v-model="data.height" @keyup.enter="onSubmit" required min="0"></el-input-number>
                    <div v-if="isError('height')" class="el-form-item__error">{{getErrorMessage('height')}}</div>
                </el-form-item>
            </div>
        </el-form>
        <template #footer>
            <el-button type="primary" @click="onSubmit" :disabled="isLoading" :loading="isLoading">{{ $t('Update') }}</el-button>
        </template>
    </el-dialog>
</template>