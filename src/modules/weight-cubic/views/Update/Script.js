import EventBus from "@/utilities/eventBus";
import { update } from "@/api/weightCubic";

export default {
    name: "UpdateProductStyle",
    data() {
        return {
            weightCubicId: '',
            dialogVisible: false,
            data: this.setDefaultData(),
            serverErrors: [],
            isLoading: false
        }
    },
    created() {
        EventBus.$on("showUpdateWeightCubic", (item) => {
            this.weightCubicId = item.id;
            this.data = Object.assign(this.data, item);
            let info = this.data.cubic.split('x');            
            this.data.long = info[0];
            this.data.width = info[1];
            this.data.height = info[2];
            this.serverErrors = [];
            this.dialogVisible = true;
        });
    },
    methods: {
        isError(field) {
            return !!this.serverErrors[field];
        },
        getErrorMessage(field) {
            return this.serverErrors[field][0];
        },
        setDefaultData() {
            return {
                name_cubic: "",
                weight_start: "",
                weight_end: "",
                height: "",
                long: "",
                width: "",
                cubic: ""
            };
        },
        async onSubmit() {
            if (this.isLoading) return;
            const isValid = await this.$refs.updateWeightCubic.validate();
            if (!isValid) {
                return;
            }
            this.isLoading = true;
            this.data.cubic = this.data.long + 'x' + this.data.width + 'x' + this.data.height;
            try {
                const res = await update(this.weightCubicId, this.data);
                this.dialogVisible = false;
                this.notification(res.data.message);
                this.$emit("refresh");
            } catch (e) {
                this.serverErrors = e.response.data.errors;
                let message = e.response.data.message;
                this.notification(message, "error");
            } finally {
                this.isLoading = false;
            }
        },
    }
}
