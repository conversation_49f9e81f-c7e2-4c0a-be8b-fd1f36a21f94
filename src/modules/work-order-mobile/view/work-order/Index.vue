<style src="./Style.scss" lang="scss" scoped></style>
<script src="./Script.js"></script>
<template>
  <div class="content">
    <HeaderMobile :name="$t('Work Order')"></HeaderMobile>
    <div class="content-body">
      <div class="">
        <div v-if="employee" class="mt-2 bg-gray-50 p-3 border rounded">
          <div class="flex justify-between">
            <b style="font-size: 18px" class="mr-2"
              >{{ $t('Hi') }} {{ employee.name }}
            </b>
            <el-link
              type="danger"
              class="ml-3"
              @click="resetEmployee"
              :underline="false"
              >{{ $t('Logout') }}
            </el-link>
          </div>
          <div class="text-lg text-indigo-500">
            <IncrementTimer />
          </div>
        </div>

        <div v-show="!employee" :error="employeeError">
          <el-input
            :placeholder="$t('Scan Employee')"
            class="mt-2 el-form-item-scan-employee"
            size="large"
            ref="employeeCode"
            v-model="codeEmployee"
            @change="getScanCodeEmloyee()"
          />
        </div>

        <el-button
          v-if="!workOrder"
          @click="getWorkOrder"
          :disabled="!employee"
          type="success"
          size="large"
          class="mt-2"
          style="width: 100%"
          >Get Work Order - Pending: {{ this.total }}
        </el-button>
      </div>

      <div v-if="this.workOrder" class="text-right">
        <strong>Work Order ID: {{ this.workOrder.id }}</strong>
        <br />
        Created at:
        {{ utcToLocalTime(this.workOrder.created_at).format('lll') }}
        <br />
        Assigned at:
        {{ utcToLocalTime(this.workOrder.located_at).format('lll') }}
      </div>

      <div class="flex justify-content-between">
        <el-input
          v-if="workOrder"
          :placeholder="$t('Scan Box ID')"
          class="el-form-item-scan-box-id mt-2"
          size="large"
          ref="boxId"
          v-model="boxNumber"
          trim
          @change="scanBox()"
        />
        <icon
          v-if="workOrder"
          :data="iconQrCode"
          width="40"
          height="40"
          class="btn-qr-scan mt-2 ml-2"
          @click="toggleModal()"
        ></icon>
        <el-dialog
          v-model="dialogVisible"
          title="Complete"
          width="90%"
          draggable
        >
          <p class="text-danger">Note: Unscanned boxes will be deleted from inventory</p>
          <el-input
            size="large"
            v-model="qrCode"
            @change="confirmComplete()"
            placeholder="Scan QR code to complete"
            class="mr-2 el-form-item-qrcode"
            ref="complete"
          ></el-input>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="toggleModal()" type="danger">Cancel</el-button>
            </span>
          </template>
        </el-dialog>
      </div>

      <div class="list-box">
        <el-row class="item-box" v-for="item in items">
          <el-col>
            <el-card
              class="box-card"
              :class="{
                'is-replace': item.is_replaced == 1,
                completed: item.status == 'completed',
              }"
            >
              <span class="box-number">{{ item.box_number }}</span>
              <span>{{ item.location_number }}</span>
              <span
                >{{ item.product.style }} / {{ item.product.size }} /
                {{ item.product.color }}
              </span>
              <el-button
                v-if="item.status != 'completed' && item.is_replaced == 0"
                @click="changeWorkOrderItem(item)"
                class="btn-change"
                type="warning"
                >Change
              </el-button>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>
