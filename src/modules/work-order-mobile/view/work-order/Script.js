import { assignWorkOrder, countPendingWorkOrder, confirmItem, confirmWorkOrder, changeItem } from "@/api/workOrder.js";
import { isEmpty } from "ramda";
import { employeeLogoutTimeChecking, employeeTimeChecking } from "@/api/employee.js";
import IncrementTimer from "@/components/IncrementTimer.vue";
import HeaderMobile from "@/components/HeaderMobile.vue";
import dateMixin from "@/mixins/date.js";


export default {
    name: "WorkOrder",
    components: {
        IncrementTimer,
        HeaderMobile
    },
    mixins: [dateMixin],
    data() {
        return {
            isLoading: false,
            filter: this.setDefaultFilter(),
            total: 0,
            items: null,
            boxNumber: "",
            codeEmployee: "",
            employee: null,
            employeeError: '',
            skuError: '',
            workOrder: null,
            job_type: 'work_order',
            time_checking_id: null,
            qrCode: null,
            dialogVisible: false
        };
    },

    created() {
        this.focusByElClassScanEmployee()
        this.countPendingWorkOrder()
    },


    methods: {
        maxHeight() {
            return parseInt(window.innerHeight - 230);
        },
        countPendingWorkOrder() {
            countPendingWorkOrder().then(res => {
                this.total = res.data
            })
        },
        confirmComplete() {
            if (this.qrCode != '3Hrx4Ku7YnGpUA8p') return false;
            if (this.time_checking_id == null) return false;
            confirmWorkOrder(this.workOrder.id, {'time_checking_id': this.time_checking_id}).then(res => {
                if (res.data.status) {
                    this.notification('Success', "success");
                } else {
                    this.notification('Error', "error");
                }
            })
            this.countPendingWorkOrder();
            this.qrCode = '';
            this.boxNumber = '';
            this.workOrder = null;
            this.items = null;
            this.dialogVisible = false;
        },

        changeWorkOrderItem(item) {
            changeItem(item.id).then(res => {
                this.getWorkOrder();
                this.sortItems();
            }).catch(() => {
                this.notification('No Similar Box', "error");
            })
        },
        scanBox() {
            const check = this.items.filter(i => i.box_number.toUpperCase() == this.boxNumber.trim().toUpperCase())
            if (check && check.length == 1) {
                check[0].status = 'completed';
                confirmItem(check[0]['id']);
                this.sortItems();
            } else {
                this.notification('Box Invalid', "error");
            }
            this.boxNumber = '';
            const num = this.items.length;
            const num2 = this.items.filter(i => (i.status == 'completed' || i.is_replaced == 1)).length;
            if (num == num2) {
                this.focusByElClass('el-form-item-qrcode');
            } else {
                this.focusByElClass('el-form-item-scan-box-id');
            }


        },
        getWorkOrder() {
            assignWorkOrder(this.employee.id).then(res => {
                if (res.data) {
                    this.workOrder = res.data;
                    this.items = res.data.items;
                    this.sortItems();
                    this.focusByElClass('el-form-item-scan-box-id');
                } else {
                    this.notification('No Data', "warning");
                }

            })
        },
        focusByElClass(elClass = "el-form-item-qrcode") {
            this.$nextTick(() => {
                const el = document.getElementsByClassName(elClass);
                if (!el || !el.length) {
                    return;
                }
                const firtsElError = (el && el[0]) || undefined;
                const input = firtsElError.querySelector("input");
                document.body.scrollTop = (input.offsetTop - 120) | 0;
                input.focus();
            });
        },

        setDefaultFilter() {
            return {
                qrCode: "",
            };
        },


        focusByElClassScanEmployee(elClass = "el-form-item-scan-employee") {
            this.$nextTick(() => {
                const el = document.getElementsByClassName(elClass);
                if (!el || !el.length) {
                    return;
                }
                const firtsElError = (el && el[0]) || undefined;
                const input = firtsElError.querySelector("input");
                document.body.scrollTop = (input.offsetTop - 120) | 0;
                input.focus();
            });
        },

        async getScanCodeEmloyee() {
            try {
                this.isLoading = true;
                const res = await employeeTimeChecking({
                    code: Number(this.codeEmployee),
                    job_type: this.job_type
                })
                if (!res.data.data) {
                    this.codeEmployee = "";
                    this.employeeError = "Scan employee code error, please scan again."
                    this.focusByElClassScanEmployee()
                    return;
                }
                this.employee = res.data.data
                this.time_checking_id = res.data.id_time_checking
                this.focusByElClass()
                this.isLoading = false;
            } catch (e) {
                const data = e.response.data;
                let message = this.$t('Not found');
                if (!isEmpty(data)) {
                    const keyFirstData = Object.keys(data)[0];
                    const firstData = data[keyFirstData];
                    message = firstData[0];
                }
                this.isLoading = false;
                this.notification(message, "error");
            }
        },

        async resetEmployee() {
            const res = await employeeLogoutTimeChecking(this.time_checking_id)
            console.log(res, this.time_checking_id);
            this.employee = null
            this.employeeError = ""
            this.codeEmployee = ""
            this.time_checking_id = null
            this.qrCode = ''
            this.items = null
            this.workOrder = null
            this.dialogVisible = false;
            this.focusByElClassScanEmployee()
            this.countPendingWorkOrder()
        },
        toggleModal() {
            if (this.dialogVisible) {
                this.dialogVisible = false;
                this.focusByElClass('el-form-item-scan-box-id');
            } else {
                this.dialogVisible = true;
                this.focusByElClass('el-form-item-qrcode');
            }
        },
        sortItems() {
            let arrTemp = [];
            this.items.forEach(item => {
                if (item.status == 'completed') {
                    arrTemp.push(item);
                }
            });
            this.items.forEach(item => {
                if (item.status != 'completed' && item.is_replaced != 1) {
                    arrTemp.push(item);
                }
            });
            this.items.forEach(item => {
                if (item.is_replaced == 1) {
                    arrTemp.push(item);
                }
            });
            this.items = arrTemp;
        }

    }
};
