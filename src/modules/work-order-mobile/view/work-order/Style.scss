.content {
  .content-body {
    padding: 15px;
    .btn-qr-scan {
      width: 50px;
      height: 50px;
    }
    .list-box {
      .item-box {
        margin-top: 15px;
        .box-card {
          background-color: #fbe192;
          border: solid 2px #f9d465;
          .box-number {
            font-size: 24px;
            font-weight: bold;
          }
          span {
            width: 100%;
            display: block;
          }
          .btn-change {
            position: absolute;
            right: 15px;
            bottom: 15px;
          }
          &.completed {
            background-color: #8dd7cf;
            border: solid 2px #67cabf;
          }
          &.is-replace {
            background-color: #e9a2ad;
            border: solid 2px #e07e8d;
            span {
              -webkit-text-decoration-line: line-through;
              text-decoration-line: line-through;
            }
          }
        }
      }
    }
  }
}
