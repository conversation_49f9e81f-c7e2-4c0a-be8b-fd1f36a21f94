import { createRouter, createWebHistory } from 'vue-router';
import { lastResponses } from "@/utilities/http.js";

import { flatten } from 'ramda';
import http from '@/utilities/http.js';
import { destroyToken, getToken } from '@/utilities/jwt.js';

const routes = import.meta.globEager('./modules/**/route.js');

let convertRoutes = flatten(
  Object.keys(routes).map((item) => routes[item].default)
);

convertRoutes = convertRoutes.map((route) => {
  let routeMeta = route.meta || {};
  const layout = routeMeta.layout || '';
  if (!layout) {
    routeMeta.layout = 'default';
  }
  return {
    ...route,
    meta: routeMeta,
  };
});

const router = createRouter({
  history: createWebHistory(),
  routes: convertRoutes,
});

router.beforeEach((to, from, next) => {
  if (to.path === '/logout') destroyToken();

  if (
    [
      '/sign-up',
      '/reset-password',
      '/user/active',
      '/user/change-password',
    ].includes(to.path) ||
    ['backlog', 'backlog_list', 'checkin', 'checkin_list'].includes(to.name)
  ) {
    return next();
  }

  const authRequired = ![
    '/login',
    '/sign-up',
    '/reset-password',
    '/user/active',
    '/user/change-password',
  ].includes(to.path);
  if (authRequired && !getToken()) {
    return next('/login');
  }

  http.defaults.headers.common['Authorization'] = getToken();

  next();
});

export default router;
