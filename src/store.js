import { createStore } from 'vuex';

import { UserStore } from '@/modules/auth/store/index';
import { BrandStore } from '@/modules/brand/store/index';
import { WarehouseStore } from '@/modules/warehouse/store/index';
import { VendorStore } from '@/modules/vendor/store/index';
import { PaymentTermStore } from '@/modules/payment-term/store/index';
import { CarrierStore } from '@/modules/carrier/store/index';
import { BarcodeStore } from '@/modules/barcode/store/index';
import { EmployeeStore } from '@/modules/employee/store/index';
import { LanguageStore } from '@/modules/language/store/index';
import { UserRoleStore } from '@/modules/user-role/store/index';
import { UserModuleStore } from '@/modules/user/store/index';
import { ProductStore } from '@/modules/product/store/index';
import { TagStore } from '@/modules/tag/store/index';
import { AccountStore } from '@/modules/account/store/index';
import { Store } from '@/modules/store/store/index';
import { FillingShelvesStore } from '@/modules/filling-shelves/store/index';
import { DepartmentStore } from '@/modules/department/store/index';
import { DocCategoryStore } from '@/modules/doc-category/store/index';
import { SettingStore } from '@/modules/Setting/store/index';
import { AddressStore } from '@/modules/sale-order/store/address/index';
import { SaleOrderStore } from '@/modules/sale-order/store/index';
import { InternalRequestStore } from '@/modules/internal-request/store/index';

export default createStore({
  state: {
    isCollapse: false,
    currentModuleMenu: 'insights',
  },
  getters: {
    getIsCollapse(state) {
      return state.isCollapse;
    },
    getCurrentModuleMenu(state) {
      return state.currentModuleMenu;
    },
  },
  mutations: {
    setIsCollapse(state) {
      state.isCollapse = !state.isCollapse;
    },
    setCurrentModuleMenu(state, currentModuleMenu) {
      state.currentModuleMenu = currentModuleMenu;
      localStorage.setItem('currentModuleMenu', currentModuleMenu);
    },
  },
  modules: {
    user: UserStore,
    brand: BrandStore,
    warehouse: WarehouseStore,
    vendor: VendorStore,
    paymentTerm: PaymentTermStore,
    carrier: CarrierStore,
    barcode: BarcodeStore,
    employee: EmployeeStore,
    language: LanguageStore,
    userRole: UserRoleStore,
    userModule: UserModuleStore,
    productStore: ProductStore,
    tagStore: TagStore,
    accountStore: AccountStore,
    store: Store,
    fillingShelves: FillingShelvesStore,
    docCategory: DocCategoryStore,
    department: DepartmentStore,
    setting: SettingStore,
    addressStore: AddressStore,
    saleOrder: SaleOrderStore,
    internalRequest: InternalRequestStore,
  },
});
