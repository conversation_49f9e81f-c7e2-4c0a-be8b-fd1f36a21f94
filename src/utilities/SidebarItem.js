import { WAREHOUSE_MEXICO } from '@/utilities/constants';

const SAN_JOSE_ID = 1;
const includeWarehouse = [SAN_JOSE_ID];

export default [
  // Module Insights
  {
    name: 'Insights',
    id: 'insights',
    router: {
      name: 'forecast_new_order',
    },

    sidebar: [
      {
        type: 'item',
        name: 'Forecast',        
        icon: 'iconForecast',
        router: {
          name: 'forecast_new_order',
        },
        allowView: ['forecast_shipped_order', 'forecast_new_order'],
      },

      {
        type: 'item',
        icon: 'iconChartBarLine',
        name: 'Item Report',
        id: 'sale_order_report',
        router: {
          name: 'sale_order_report',
        },
      },
      {
        type: 'item',
        icon: 'iconChartBarLine',
        name: 'Revenue Report',
        id: 'sale_order_revenue_report',
        router: {
          name: 'sale_order_revenue_report',
        },
      },
      {
        type: 'item',
        icon: 'iconMap',
        name: 'Delivery map',
        id: 'delivery_map',
        router: {
          name: 'delivery_map',
        },
      },
      {
        type: 'item',
        icon: 'iconChartColumnBarLine',
        name: 'Fulfillment Report',
        id: 'report_fulfillment',
        router: {
          name: 'report_fulfillment',
        },
      },
      {
        type: 'item',
        icon: 'iconTruck',
        name: 'Transit Report',
        id: 'transit_report',
        router: {
          name: 'transit_report',
        },
      },
      {
        type: 'item',
        icon: 'iconDashboard2',
        name: 'Performance Report',
        id: 'performance_report',
        router: {
          name: 'performance_report',
        },
      },
      // {
      //   type: 'item',
      //   icon: 'iconDashboard2',
      //   name: 'Performance Report',
      //   id: 'performance_report_for_manager',
      //   router: {
      //     name: 'performance_report_for_manager',
      //   },
      // },
      // {
      //   type: 'item',
      //   icon: 'iconDashboard',
      //   name: 'Department Report',
      //   id: 'performance_report_for_leader',
      //   router: {
      //     name: 'performance_report_for_leader',
      //   },
      // },

      {
        type: 'item',
        icon: 'iconChartLine',
        name: 'Report',
        id: 'insights_invoicing',
        router: {
          name: 'insights_invoicing',
        },
        sub: [
          {
            type: 'item',
            icon: 'iconFileList',
            name: 'Invoices',
            id: 'invoices',
            router: {
              name: 'invoices_stores',
            },
          },
        ],
      },
      {
        type: 'item',
        icon: 'iconCostReport',
        name: 'Cost Report',
        id: 'cost_report',
        router: {
          name: 'cost_report',
        },
      },
      // {
      //   type: 'item',
      //   icon: 'iconProductionReport',
      //   name: 'Production Report',
      //   id: 'production_report',
      //   router: {
      //     name: 'production_report',
      //   },
      // },
      {
        type: 'item',
        icon: 'iconDropletSolid',
        name: 'Ink Consumption',
        id: 'ink_consumption',
        router: {
          name: 'ink_consumption',
        },
        sub: [
          {
            type: 'item',
            name: 'Ink Consumption Report',
            id: 'ink_consumption_report',
            router: {
              name: 'ink_consumption',
            },
          },
          {
            type: 'item',
            name: 'Ink Consumption Forecast',
            id: 'ink_consumption_forecast',
            router: {
              name: 'ink_consumption_forecast',
            },
          },
        ],
      },
      {
        type: 'item',
        icon: 'iconLateOrder',
        name: 'Late Orders',
        id: 'looker_studio_late_orders',
        router: {
          name: 'looker_studio_late_orders',
        },
      },
      {
        type: 'item',
        icon: 'iconEasyPostAdjustment',
        name: 'EasyPost Adjustment',
        id: 'looker_studio_easypost_adjustment',
        router: {
          name: 'looker_studio_easypost_adjustment',
        },
      },
      {
        type: 'item',
        icon: 'iconTiktokOrdersFullfillmentTime',
        name: 'Tiktok Orders Fulfillment Time',
        id: 'looker_studio_tiktok_orders_fulfillment_time',
        router: {
          name: 'looker_studio_tiktok_orders_fulfillment_time',
        },
      },      
      {
        type: 'item',
        icon: 'iconLabelOrdersFulfillmentTime',
        name: 'Label Orders Fulfillment Time',
        id: 'looker_studio_label_orders_fulfillment_time',
        router: {
          name: 'looker_studio_label_orders_fulfillment_time',
        },
      },
      {
        type: 'item',
        name: 'Missing Scanned WIP',
        icon: 'iconMissing',
        iconClass: 'svg-line',
        router: {
          name: 'missing_scanned_wip',
        },
      },
      {
        type: 'item',
        icon: 'iconOffenders',
        name: 'Offenders by Department',
        id: 'looker_studio_offender_department',
        router: {
          name: 'looker_studio_offender_department',
        },
      },
      {
        type: 'item',
        name: 'QC - Fulfillment SLA',
        icon: 'iconProductionReport',
        router: {
          name: 'qc_rejected',
        },
      },
    ],
  },
  // Module Orders
  {
    name: 'Orders',
    id: 'orders',
    router: {
      name: 'sale_order',
    },
    sidebar: [
      /*{
        type: 'item',
        name: 'Sales Orders',
        icon: 'iconShoppingCartLine',
        router: {
          name: 'sale_order',
        },
        sub: [

        ],
      },*/

      // {
      //   type: 'item',
      //   name: 'Pending Orders',
      //   icon: 'iconPendingOrder',
      //   router: {
      //     name: 'pending_order',
      //   },
      // },
      // {
      //   type: 'item',
      //   name: 'Matching SKU',
      //   icon: 'iconMatching',
      //   router: {
      //     name: 'matching_sku',
      //   },
      // },
      {
        type: 'item',
        name: 'Print Files Error',
        icon: 'iconErrorFile',
        router: {
          name: 'print_file_error',
        },
      },
      {
        type: 'item',
        icon: '',
        name: 'Sales Orders',
        id: 'sale_order',
        router: {
          name: 'sale_order',
        },
      },
    ],
  },
  // Module Inventory
  {
    name: 'Inventory',
    id: 'inventory',
    router: {
      name: 'inventory_pulling_shelves',
    },
    sidebar: [
      // {
      //   type: "item",
      //   name: "Inventory Overview",
      //   icon: "iconDashboard",
      //   router: {
      //     name: "inventory_overview",
      //   },
      // },
      {
        type: 'item',
        name: 'Inventory Overview',
        icon: 'iconDashboard3',
        router: {
          name: 'inventory_pulling_shelves',
        },
      },
      // {
      //   type: 'item',
      //   name: 'Part Number Overview',
      //   includeWarehouse: WAREHOUSE_MEXICO,
      //   icon: 'iconDashboard3',
      //   router: {
      //     name: 'part_number_overview',
      //   },
      // },
      // {
      //   type: "item",
      //   name: "Inventory Details",
      //   icon: "iconDashboard2",
      //   router: {
      //     name: "inventory_details",
      //   },
      // },
      {
        type: 'item',
        name: 'Inventory Forecast',
        icon: 'iconDashboard2',
        router: {
          name: 'inventory_forecast',
        },
      },
      {
        type: 'item',
        name: 'Purchase Orders',
        icon: 'iconShoppingCartLine',
        router: {
          name: 'purchase_order',
        },
      },
      {
        type: 'item',
        name: 'Addition',
        icon: 'iconInboxArchiveLine',
        router: {
          name: 'inventory_addition',
        },
      },
      {
        type: 'item',
        name: 'Deduction',
        icon: 'iconInboxUnarchiveLine',
        router: {
          name: 'inventory_deduction',
        },
      },
      {
        type: 'item',
        name: 'Internal Request',
        icon: 'iconFileList',
        router: {
          name: 'internal_request',
        },
      },
      {
        type: 'item',
        name: 'Stock Transfer',
        icon: 'iconTruckLine',
        router: {
          name: 'stock_transfer',
        },
      },
      // {
      //   type: 'item',
      //   name: 'Locating Inventory',
      //   icon: 'iconBarcodeBox',
      //   router: {
      //     name: 'work_order',
      //   },
      //   allowView: ['work_order_history'],
      // },

      // {
      //   type: 'item',
      //   name: 'Filling Shelves',
      //   icon: 'iconQrCode',
      //   router: {
      //     name: 'filling-shelves',
      //   },
      //   allowView: ['filling-shelves-history'],
      // },

      {
        type: 'item',
        name: 'Adjust Pulling Shelves',
        icon: 'iconInboxUnarchiveLine',
        router: {
          name: 'adjust_pulling_shelves',
        },
      },
      {
        type: 'item',
        name: 'Box Moving',
        icon: 'iconFolderTransferLine',
        router: {
          name: 'box_moving',
        },
      },
      {
        type: 'item',
        name: 'Manage Boxes',
        icon: 'iconFolderLine',
        router: {
          name: 'box',
        },
      },
      {
        type: 'item',
        name: 'Test Count',
        icon: 'iconCheckBoxLine',
        router: {
          name: 'test_count',
        },
      },
      {
        type: 'item',
        name: 'Locations',
        icon: 'iconStackLine',
        router: {
          name: 'location',
        },
      },
    ],
  },
  // Module Supply Inventory
  {
    name: 'Supply Inventory',
    id: 'supply_inventory',
    router: {
      name: 'supply_inventory_overview',
    },
    sidebar: [
      {
        type: 'item',
        name: 'Supply Overview',
        icon: 'iconDashboard3',
        router: {
          name: 'supply_inventory_overview',
        },
      },
      {
        type: 'item',
        name: 'Purchase Orders',
        icon: 'iconShoppingCartLine',
        router: {
          name: 'supply_purchase_order',
        },
      },
      {
        type: 'item',
        name: 'Addition',
        icon: 'iconInboxArchiveLine',
        router: {
          name: 'supply_inventory_addition',
        },
      },
      {
        type: 'item',
        name: 'Deduction',
        icon: 'iconInboxUnarchiveLine',
        router: {
          name: 'supply_inventory_deduction',
        },
      },
      {
        type: 'item',
        name: 'Test Count',
        icon: 'iconCheckBoxLine',
        router: {
          name: 'supply_test_count',
        },
      },
      {
        type: 'item',
        name: 'Locations',
        icon: 'iconStackLine',
        router: {
          name: 'supply_location',
        },
      },
      {
        type: 'item',
        name: 'Manage Boxes',
        icon: 'iconFolderLine',
        router: {
          name: 'supply_box',
        },
      },
      {
        type: 'item',
        name: 'Adjust Supply',
        icon: 'iconInboxUnarchiveLine',
        router: {
          name: 'supply_adjust_inventory',
        },
      },
    ],
  },
  // Module Production
  {
    name: 'Production',
    id: 'production',
    router: {
      name: 'barcode',
    },
    sidebar: [
      {
        type: 'item',
        name: 'WIP Printing',
        icon: 'iconBarcodeBox',
        router: {
          name: 'barcode',
        },
      },
      {
        type: 'item',
        name: 'WIP Reassignment',
        icon: 'iconQrScan',
        router: {
          name: 'barcode_re_assign',
        },
      },
      {
        type: 'item',
        name: 'UV Printing',
        icon: 'iconOrnaments',
        router: {
          name: 'ornament_wip',
        },
        sub: [
          {
            type: 'item',
            name: 'WIP',
            icon: '',
            router: {
              name: 'ornament_wip',
            },
          },
          {
            type: 'item',
            name: 'Convert',
            icon: '',
            router: {
              name: 'ornament_convert',
            },
          },
          {
            type: 'item',
            name: 'Sticker',
            icon: '',
            router: {
              name: 'sticker',
            },
          },
          {
            type: 'item',
            name: 'Poster',
            icon: '',
            router: {
              name: 'poster',
            },
          },
        ],
      },
      // {
      //   type: 'item',
      //   name: 'Log Convert',
      //   icon: 'iconImageLine',
      //   router: {
      //     name: 'log_convert',
      //   },
      // },
      {
        type: 'item',
        name: 'Latex Printing',
        elIcon: true,
        icon: 'iconLatexPrintingLogo',
        router: {
          name: 'latex_barcode',
        },
      },
      {
        type: 'item',
        name: 'Mugs WIP',
        icon: 'iconMug',
        router: {
          name: 'mugs-wip',
        },
      },
      {
        type: 'item',
        name: 'DTF Printing',
        icon: 'iconBarcodeBox',
        router: {
          name: 'dtf_barcode',
        },
        sub: [
          {
            type: 'item',
            name: 'WIP',
            icon: '',
            router: {
              name: 'dtf_barcode',
            },
          },
          {
            type: 'item',
            name: 'WIP Film',
            icon: '',
            router: {
              name: 'dtf_film_barcode',
            },
          },
          {
            type: 'item',
            name: 'WIP Neck',
            icon: '',
            router: {
              name: 'dtf_neck_barcode',
            },
          },
          {
            type: 'item',
            name: 'Convert Neck',
            icon: '',
            router: {
              name: 'dtf-neck',
            },
          },
          {
            type: 'item',
            name: 'Preview',
            router: {
              name: 'dtf-preview',
            },
          },
        ],
      },
      {
        type: 'item',
        name: '3D Printing',
        icon: 'iconMug',
        router: {
          name: 'printing-3d',
        },
        sub: [
          {
            type: 'item',
            name: '3D Mug',
            icon: '',
            router: {
              name: 'printing-3d',
            },
          },
          {
            type: 'item',
            name: '3D Sticker',
            icon: '',
            router: {
              name: 'mug-sticker',
            },
          },
        ],
      },
      // {
      //   name: 'Insert printing',
      //   icon: 'iconBarcodeBox',
      //   router: {
      //     name: 'insert_printing',
      //   },
      // },

      {
        type: 'item',
        name: 'EMB Printing',
        icon: 'iconBarcodeBox',
        router: {
          name: 'barcode_emb',
        },
      },
      {
        type: 'item',
        name: 'Blank / Pretreated',
        icon: 'iconBarcodeBox',
        router: {
          name: 'blank_barcode',
        },
        sub: [
          {
            type: 'item',
            name: 'Blank WIP',
            icon: '',
            router: {
              name: 'blank_barcode',
            },
          },
          {
            type: 'item',
            name: 'Pretreated WIP',
            icon: '',
            router: {
              name: 'pretreated_barcode',
            },
          },
        ],
      },
      {
        type: 'item',
        name: 'QC Report',
        icon: 'iconQrCode',
        router: {
          name: 'qr_report',
        },
      },
      {
        type: 'item',
        name: 'Quality Control',
        icon: 'iconRuleLine',
        router: {
          name: 'quality-control',
        },
      },
      {
        type: 'item',
        name: 'Press',
        icon: 'iconQrCode',
        router: {
          name: 'press',
        },
      },
      /*    {
        type: "item",
        name: "Quality Control History",
        icon: "iconHistory",
        router: {
          name: "quality-control-history",
        },
      },*/
      // {
      //   type: 'item',
      //   name: 'Multiple Staging',
      //   icon: 'iconQrCode',
      //   router: {
      //     name: 'multiple-staging',
      //   },
      // },
      {
        name: 'Label',
        icon: 'iconBarcodeBox',
        router: {
          name: 'shipping_label',
        },
      },
      {
        type: 'item',
        name: 'Shipments',
        icon: 'iconShip',
        router: {
          name: 'Shipments',
        },
      },
      {
        name: 'FBA',
        icon: 'iconBarcodeBox',
        router: {
          name: 'shipping_label_fba',
        },
      },
      {
        type: 'item',
        name: 'Pretreat',
        icon: 'iconQrCode',
        router: {
          name: 'pretreat',
        },
      },
      {
        type: 'item',
        name: 'Folding',
        icon: 'iconQrCode',
        router: {
          name: 'performance-folding',
        },
      },
      {
        type: 'item',
        name: 'Exportation Report',
        icon: 'iconQrCode',
        includeWarehouse: WAREHOUSE_MEXICO,
        router: {
          name: 'exportation',
        },
      },
      {
        name: 'Manifest',
        icon: 'iconManifest',
        router: {
          name: 'manifest',
        },
      },
      {
        type: 'item',
        name: 'Items',
        icon: 'iconItem',
        router: {
          name: 'production_status',
        },
      },
    ],
  },
  // Module Setting
  {
    name: 'Setting',
    id: 'setting',
    router: {
      name: 'warehouse',
    },
    sidebar: [
      {
        type: 'item',
        name: 'Warehouses',
        icon: 'iconWarehouse',
        router: {
          name: 'warehouse',
        },
        sub: [
          {
            type: 'item',
            name: 'Reroute',
            icon: 'iconUsers',
            router: {
              name: 'warehouse',
            },
          },
          {
            type: 'item',
            name: 'On hold setting',
            icon: 'iconDepartment',
            router: {
              name: 'on-hold-setting',
            },
          },
        ],
        includeWarehouse: [...includeWarehouse],
      },
      {
        type: 'item',
        name: 'Shipping',
        icon: 'iconTruckLine',
        router: {
          name: 'shipping_carrier',
        },
        includeWarehouse: [...includeWarehouse],
      },
      {
        type: 'item',
        name: 'Setting',
        icon: 'iconSetting',
        router: {
          name: 'setting',
        },
        includeWarehouse: [...includeWarehouse],
      },
      {
        type: 'item',
        name: 'IP Management',
        icon: 'iconSetting',
        router: {
          name: 'ip-management',
        },
      },
      {
        type: 'item',
        name: 'Human Resources',
        icon: 'iconUsers',
        router: {
          name: 'employee',
        },
        sub: [
          {
            type: 'item',
            name: 'Employee',
            icon: 'iconUsers',
            router: {
              name: 'employee',
            },
          },
          {
            type: 'item',
            name: 'Departments',
            icon: 'iconDepartment',
            router: {
              name: 'department',
            },
          },
        ],
      },
      {
        type: 'item',
        name: 'User Profile',
        icon: 'iconUsers',
        router: {
          name: 'user',
        },
        sub: [
          {
            type: 'item',
            name: 'User',
            icon: 'iconUsers',
            router: {
              name: 'user',
            },
          },
          {
            type: 'item',
            name: 'User Role',
            icon: 'iconUsers',
            router: {
              name: 'user_role',
            },
          },
          {
            type: 'item',
            name: 'Role Permission',
            icon: 'iconUsers',
            router: {
              name: 'role-permission',
            },
          },
        ],
      },
      {
        type: 'item',
        name: 'Products',
        icon: 'iconTshirtLine',
        router: {
          name: 'group_product',
        },
        includeWarehouse: [...includeWarehouse],
        sub: [
          {
            type: 'item',
            name: 'Product',
            icon: 'iconTshirtLine',
            router: {
              name: 'group_product',
            },
            allowView: ['product'],
          },
          {
            type: 'item',
            name: 'Product Type',
            icon: 'iconTshirtLine',
            router: {
              name: 'product_type',
            },
          },
          {
            type: 'item',
            name: 'Product Style',
            icon: 'iconTshirtLine',
            router: {
              name: 'product_style',
            },
          },
          {
            type: 'item',
            name: 'Product Color',
            icon: 'iconTshirtLine',
            router: {
              name: 'product_color',
            },
          },
          {
            type: 'item',
            name: 'Product Size',
            icon: 'iconTshirtLine',
            router: {
              name: 'product_size',
            },
          },
          // {
          //   type: 'item',
          //   name: 'Product Type Weight',
          //   icon: 'iconTshirtLine',
          //   router: {
          //     name: 'product_type_weight',
          //   },
          // },
          // {
          //   type: 'item',
          //   name: 'Product SKU Level Weights',
          //   icon: 'iconTshirtLine',
          //   router: {
          //     name: 'product_sku_level_weight',
          //   },
          // },
          {
            name: 'Material Thickness/Heat Press',
            icon: 'iconPrintArea',
            router: {
              name: 'product_material_thickness',
            },
          },
          {
            name: 'Product Print Area',
            icon: 'iconPrintArea',
            router: {
              name: 'product_sku_print_area',
            },
          },
          {
            name: 'Product SKU Matching',
            icon: 'iconPrintArea',
            router: {
              name: 'product_sku_matching',
            },
          },
          {
            name: 'Print areas',
            icon: '',
            router: {
              name: 'product_print_area',
            },
          },
          {
            type: 'item',
            name: 'Weight Cubic',
            icon: 'iconSetting',
            router: {
              name: 'weight_cubic',
            },
          },
          {
            type: 'item',
            name: 'Brand',
            icon: 'iconSetting',
            router: {
              name: 'brand',
            },
          },
        ],
      },
      {
        type: 'item',
        name: 'Tiktok SKUs',
        icon: 'iconTshirtLine',
        router: {
          name: 'product_tiktok',
        },
      },
      {
        type: 'item',
        name: 'Rbt SKUs',
        icon: 'iconTshirtLine',
        router: {
          name: 'product_rbt',
        },
      },

      {
        type: 'item',
        name: 'Licenses',
        icon: 'iconLicense',
        router: {
          name: 'licenses',
        },
      },

      {
        type: 'item',
        name: 'Printing App',
        icon: 'iconPrinterLine',
        router: {
          name: 'printing_preset',
        },
        includeWarehouse: [...includeWarehouse],
        sub: [
          {
            type: 'item',
            name: 'Printing Preset',
            icon: 'iconPrinterLine',
            router: {
              name: 'printing_preset',
            },
          },
          {
            type: 'item',
            name: 'ICC Profile',
            icon: 'iconPrinterLine',
            router: {
              name: 'icc_profile',
            },
          },
          {
            type: 'item',
            name: 'Printing Preset Sku',
            icon: 'iconPrinterLine',
            router: {
              name: 'printing_preset_sku',
            },
          },
          {
            type: 'item',
            name: 'Printing Bulb Power',
            icon: 'iconDocument',
            router: {
              name: 'printing_bulb_power',
            },
          },
        ],
      },
      {
        type: 'item',
        name: 'WIP Printer',
        icon: 'iconPrinterLine',
        router: {
          name: 'printer',
        },
      },
      {
        type: 'item',
        name: 'Pretreat App',
        icon: 'iconPrinterLine',
        router: {
          name: 'pretreat_preset',
        },
        includeWarehouse: [...includeWarehouse],
        sub: [
          {
            type: 'item',
            name: 'Pretreat Preset',
            icon: 'iconPrinterLine',
            router: {
              name: 'pretreat_preset',
            },
          },
          {
            type: 'item',
            name: 'Pretreat Preset Sku',
            icon: 'iconPrinterLine',
            router: {
              name: 'pretreat_preset_sku',
            },
          },
          {
            type: 'item',
            name: 'Pretreat Bulb Power',
            icon: 'iconDocument',
            router: {
              name: 'pretreat_bulb_power',
            },
          },
        ],
      },
      {
        type: 'item',
        name: 'Promotion',
        icon: 'iconSetting',
        router: {
          name: 'promotion',
        },
        includeWarehouse: [...includeWarehouse],
      },
      {
        type: 'item',
        name: 'Vendors',
        includeWarehouse: [...includeWarehouse],
        icon: 'iconBuilding3Line',
        router: {
          name: 'vendor',
        },
      },
      {
        type: 'item',
        name: 'Sellbrite Sync',
        includeWarehouse: [...includeWarehouse],
        icon: 'iconStore',
        router: {
          name: 'sellbrite_sync',
        },
        allowView: ['sellbrite_sync_warehouses'],
      },
      {
        type: 'item',
        name: 'Backlog List',
        icon: 'iconFileList',
        router: {
          name: 'backlog_list',
        },
      },
      {
        type: 'item',
        name: 'Checkin List',
        icon: 'iconFileList',
        router: {
          name: 'checkin_list',
        },
      },
      {
        type: 'item',
        name: 'Account',
        icon: 'iconUsers',
        router: {
          name: 'account',
        },
      },
      {
        name: 'Store',
        icon: 'iconStore',
        router: {
          name: 'store_list',
        },
      },
      {
        name: 'Country Of Origin',
        icon: 'iconStore',
        router: {
          name: 'country_of_origin',
        },
      },
      {
        name: 'Part Number',
        icon: 'iconManifest',
        includeWarehouse: WAREHOUSE_MEXICO,
        router: {
          name: 'part_number',
        },
      },
      {
        type: 'item',
        name: 'Supply',
        icon: 'iconUsers',
        router: {
          name: 'supply',
        },
        sub: [
          {
            type: 'item',
            name: 'Supply',
            icon: 'iconUsers',
            router: {
              name: 'supply',
            },
          },
          {
            type: 'item',
            name: 'Supply Unit',
            icon: 'iconUsers',
            router: {
              name: 'supply_unit',
            },
          },
          {
            type: 'item',
            name: 'Supply Category',
            icon: 'iconUsers',
            router: {
              name: 'supply_category',
            },
          },
        ],
      },
      {
        name: 'Changelog',
        icon: 'iconUsers',
        router: {
          name: 'create_change_log',
        },
      },
    ],
  },

  {
    name: 'Screen Printing',
    id: 'screen_printing',
    includeWarehouse: WAREHOUSE_MEXICO,
    router: {
      name: 'screen_orders',
    },
    sidebar: [
      {
        type: 'item',
        name: 'Orders',
        icon: 'iconScreenOrder',
        includeWarehouse: WAREHOUSE_MEXICO,
        iconClass: 'svg-line',
        router: {
          name: 'screen_orders',
        },
        allowView: ['screen_order_detail'],
      },
      {
        type: 'item',
        name: 'Library',
        icon: 'iconWarehouse',
        includeWarehouse: WAREHOUSE_MEXICO,
        router: {
          name: 'library',
        },
      },
      {
        type: 'item',
        name: 'Clients',
        icon: 'iconPerson',
        includeWarehouse: WAREHOUSE_MEXICO,
        router: {
          name: 'client',
        },
      }
    ]
  },

  // Module Docs
  // {
  //   name: 'Support Docs',
  //   id: 'support_doc',
  //   asyncData: true,
  //   sort: 8,
  //   router: {
  //     name: 'document',
  //   },
  //   sidebar: [],
  // },
];
