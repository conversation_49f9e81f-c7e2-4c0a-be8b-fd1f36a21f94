export default [
  // Module Insights
  {
    name: "Insights",
    id: "insights",
    router: {
      name: "performance_report_for_manager",
    },
    sidebar: [
      {
        type: "item",
        icon: "iconDashboard2",
        name: "Performance Report",
        id: "performance_report_for_manager",
        router: {
          name: "performance_report_for_manager",
        },
        // sub: [
        //   {
        //     type: "item",
        //     icon: "iconDashboard2",
        //     name: "Performance Report",
        //     id: "performance_report_for_manager",
        //     router: {
        //       name: "performance_report_for_manager",
        //     },
        //   },
        //   {
        //     type: "item",
        //     icon: "iconDashboard",
        //     name: "Sub menu 2",
        //     id: "submenu2",
        //     router: {
        //       name: "submenu2",
        //     },
        //   },
        // ],
      },
      {
        type: "item",
        icon: "iconDashboard",
        name: "Department Report",
        id: "performance_report_for_leader",
        router: {
          name: "performance_report_for_leader",
        },
      },
      {
        type: "item",
        icon: "iconChartLine",
        name: "Invoice",
        id: "insights_invoicing",
        router: {
          name: "insights_invoicing",
        },
      },
    ],
  },
  // Module Orders
  {
    name: "Orders",
    id: "orders",
    router: {
      name: "sale_order",
    },
    sidebar: [
      {
        type: "item",
        name: "Sales Orders",
        icon: "iconShoppingCartLine",
        router: {
          name: "sale_order",
        },
      },
      {
        type: "item",
        name: "Items",
        icon: "iconShip",
        router: {
          name: "production_status",
        },
      },
      {
        type: "item",
        name: "Claims",
        icon: "iconClaim",
        router: {
          name: "claim",
        },
      },
      {
        type: "item",
        name: "Account",
        icon: "iconUsers",
        router: {
          name: "account",
        },
      },
      {
        name: "Store",
        icon: "iconStore",
        router: {
          name: "store_list",
        },
      },
      {
        type: "item",
        icon: "iconFileList",
        name: "Invoices",
        id: "invoices",
        router: {
          name: "invoices",
        },
      },
      {
        name: "Label",
        icon: "iconBarcodeBox",
        router: {
          name: "shipping_label",
        },
      },
      {
        type: "item",
        name: "Ticket",
        icon: "",
        router: {
          name: "internal_ticket",
        },
      },
    ],
  },
  // Module Inventory
  {
    name: "Inventory",
    id: "inventory",
    router: {
      name: "inventory_pulling_shelves",
    },
    sidebar: [
      // {
      //   type: "item",
      //   name: "Inventory Overview",
      //   icon: "iconDashboard",
      //   router: {
      //     name: "inventory_overview",
      //   },
      // },
      {
        type: "item",
        name: "Inventory Overview",
        icon: "iconDashboard3",
        router: {
          name: "inventory_pulling_shelves",
        },
      },
      {
        type: "item",
        name: "Inventory Details",
        icon: "iconDashboard2",
        router: {
          name: "inventory_details",
        },
      },
      {
        type: "item",
        name: "Inventory Forecast",
        icon: "iconDashboard2",
        router: {
          name: "inventory_forecast",
        },
      },
      {
        type: "item",
        name: "Purchase Orders",
        icon: "iconShoppingCartLine",
        router: {
          name: "purchase_order",
        },
      },
      {
        type: "item",
        name: "Addition",
        icon: "iconInboxArchiveLine",
        router: {
          name: "inventory_addition",
        },
      },
      {
        type: "item",
        name: "Deduction",
        icon: "iconInboxUnarchiveLine",
        router: {
          name: "inventory_deduction",
        },
      },

      {
        type: "item",
        name: "Locating Inventory",
        icon: "iconBarcodeBox",
        router: {
          name: "work_order",
        },
      },

      {
        type: "item",
        name: "Filling Shelves",
        icon: "iconQrCode",
        router: {
          name: "filling-shelves",
        },
      },

      {
        type: "item",
        name: "Adjust Pulling Shelves",
        icon: "iconInboxUnarchiveLine",
        router: {
          name: "adjust_pulling_shelves",
        },
      },
      {
        type: "item",
        name: "Products",
        icon: "iconTshirtLine",
        router: {
          name: "group_product",
        },
      },
      {
        type: "item",
        name: "Box Moving",
        icon: "iconFolderTransferLine",
        router: {
          name: "box_moving",
        },
      },
      {
        type: "item",
        name: "Manage Boxes",
        icon: "iconFolderLine",
        router: {
          name: "box",
        },
      },
      {
        type: "item",
        name: "Test Count",
        icon: "iconCheckBoxLine",
        router: {
          name: "test_count",
        },
      },
      {
        type: "item",
        name: "Locations",
        icon: "iconStackLine",
        router: {
          name: "location",
        },
      },
      {
        type: "item",
        name: "Vendors",
        icon: "iconBuilding3Line",
        router: {
          name: "vendor",
        },
      },
      {
        type: "item",
        name: "Sellbrite Sync",
        icon: "iconStore",
        router: {
          name: "sellbrite_sync",
        },
      },
    ],
  },
  // Module Production
  {
    name: "Production",
    id: "production",
    router: {
      name: "barcode",
    },
    sidebar: [
      {
        type: "item",
        name: "Barcode",
        icon: "iconBarcodeBox",
        router: {
          name: "barcode",
        },
      },
      {
        type: "item",
        name: "Barcode Reassignment",
        icon: "iconQrScan",
        router: {
          name: "barcode_re_assign",
        },
      },
      {
        type: "item",
        name: "Log Convert",
        icon: "iconImageLine",
        router: {
          name: "log_convert",
        },
      },
      {
        type: "item",
        name: "QC Report",
        icon: "iconQrCode",
        router: {
          name: "qr_report",
        },
      },
      {
        type: "item",
        name: "Quality Control",
        icon: "iconRuleLine",
        router: {
          name: "quality-control",
        },
      },
      /*    {
        type: "item",
        name: "Quality Control History",
        icon: "iconHistory",
        router: {
          name: "quality-control-history",
        },
      },*/
      {
        type: "item",
        name: "Multiple Staging",
        icon: "iconQrCode",
        router: {
          name: "multiple-staging",
        },
      },
      {
        type: "item",
        name: "Pretreat",
        icon: "iconQrCode",
        router: {
          name: "performance-pretreat",
        },
      },
      {
        type: "item",
        name: "Folding",
        icon: "iconQrCode",
        router: {
          name: "performance-folding",
        },
      },
    ],
  },
  // Module Setting
  {
    name: "Setting",
    id: "setting",
    router: {
      name: "warehouse",
    },
    sidebar: [
      {
        type: "item",
        name: "Preferences",
        icon: "iconSetting",
        router: {
          name: "preferences",
        },
      },
      {
        type: "item",
        name: "Company Profile",
        icon: "iconDashboard",
        router: {
          name: "company_profile",
        },
      },
      {
        type: "item",
        name: "User",
        icon: "iconUsers",
        router: {
          name: "user",
        },
      },
      {
        type: "item",
        name: "User Role",
        icon: "iconUsers",
        router: {
          name: "user_role",
        },
      },
      {
        type: "item",
        name: "Role Permission",
        icon: "iconUsers",
        router: {
          name: "role-permission",
        },
      },
      {
        type: "item",
        name: "Employee",
        icon: "iconUsers",
        router: {
          name: "employee",
        },
      },
      {
        type: "item",
        name: "Warehouses",
        icon: "iconWarehouse",
        router: {
          name: "warehouse",
        },
      },
      {
        type: "item",
        name: "Departments",
        icon: "iconDepartment",
        router: {
          name: "department",
        },
      },
      {
        type: "item",
        name: "Shipping",
        icon: "iconTruckLine",
        router: {
          name: "shipping_carrier",
        },
      },
      {
        type: "item",
        name: "Language",
        icon: "iconLanguage",
        router: {
          name: "language",
        },
      },
      {
        type: "item",
        name: "Product Style",
        icon: "iconTshirtLine",
        router: {
          name: "product_style",
        },
      },
      {
        type: "item",
        name: "Product Color",
        icon: "iconTshirtLine",
        router: {
          name: "product_color",
        },
      },
      {
        type: "item",
        name: "Product Size",
        icon: "iconTshirtLine",
        router: {
          name: "product_size",
        },
      },
      // {
      //   type: "item",
      //   name: "Product Type Weight",
      //   icon: "iconTshirtLine",
      //   router: {
      //     name: "product_type_weight",
      //   },
      // },
      {
        name: "Product SKU Print Area",
        icon: "iconPrintArea",
        router: {
          name: "product_print_area",
        },
      },
      {
        type: "item",
        name: "Weight Cubic",
        icon: "iconSetting",
        router: {
          name: "weight_cubic",
        },
      },
      {
        type: "item",
        name: "Printing Preset",
        icon: "iconPrinterLine",
        router: {
          name: "printing_preset",
        },
      },
      {
        type: "item",
        name: "Printing Preset Sku",
        icon: "iconPrinterLine",
        router: {
          name: "printing_preset_sku",
        },
      },
      {
        type: "item",
        name: "Event Setting",
        icon: "iconTimeLine",
        router: {
          name: "setting-move-warehouse",
        },
      },
      {
        type: "item",
        name: "Setting",
        icon: "iconSetting",
        router: {
          name: "setting",
        },
      },
    ],
  },
];
