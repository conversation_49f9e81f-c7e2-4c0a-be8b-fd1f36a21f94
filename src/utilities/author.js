import jwtDecode from "jwt-decode";

export default {
  init() {
    const token = localStorage.getItem("access_token");
    if(!token) return;
    return jwtDecode(token);
  },
  avatar() {
    const user = this.init();
    return user?.avatar;
  },
  email() {
    const user = this.init();
    return user?.email;
  },
  phone() {
    const user = this.init();
    return user?.phone;
  },
  name() {
    const user = this.init();
    return user?.name;
  },
  warehouseId() {
    const user = this.init();
    return user?.warehouse && user?.warehouse.id;
  },
};
