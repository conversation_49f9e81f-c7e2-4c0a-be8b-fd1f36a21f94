export const API_URL =
  import.meta.env.VITE_API_URL;
export const STORAGE_URL =
  import.meta.env.VITE_API_STORAGE;
export const S3_URL =
  import.meta.env.VITE_S3_STORAGE;

export const WAREHOUSE_MEXICO = [18];
export const NOT_APPLICABLE = 'N/A';
export const SAN_JOSE_ID = 1;

export const STORE_CODE_SAMPLE_ORDER = import.meta.env.VITE_STORE_CODE_SAMPLE_ORDER ? import.meta.env.VITE_STORE_CODE_SAMPLE_ORDER.split(',') : ["STTR"];
export const INTERNAL_NORMAL_REQUEST = 0;
export const INTERNAL_PRIORITY_REQUEST = 1;
export const INTERNAL_TIKTOK_REQUEST = 2;
export const RBT_EMPLOYEE_CODE = 13579;
export const INTERNAL_RESTRICTED_REQUEST = 3;
export const SHIPMENT_STATUS = {
  'unknown': 'Unknown',
  'pre_transit': 'Pre transit',
  'in_transit': 'In transit',
  'out_for_delivery': 'Out for delivery',
  'delivered': 'Delivered',
  'available_for_pickup': 'Available for pickup',
  'return_to_sender': 'Return to sender',
  'failure': 'Failure',
  'cancelled': 'Cancelled',
  'error': 'Error'
};

export const LIST_USER_ID_WITHOUT_CHECK_AUTO_LOGOUT = import.meta.env.MODE === 'test' ? [32] : [];

export const LAST_ACTIVE_TIMEOUT = import.meta.env.MODE === 'test' ?  7 * 24 * 60 * 60 * 1000 : 60 * 60 * 1000; // 60 minutes for production, 7 days for development
