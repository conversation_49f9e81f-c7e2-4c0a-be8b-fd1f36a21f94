import axios from "axios";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import { API_URL } from "./constants";
import { getToken } from "@/utilities/jwt.js";
import EventBus from "@/utilities/eventBus.js";
import router from "@/router.js";
import { saveUserLastTimeActive } from '@/utilities/userLastTimeActive.js';

const http = axios.create({ baseURL: API_URL });

http.defaults.headers.post["Content-Type"] = "application/json";
http.defaults.headers.common["Authorization"] = getToken();

const lastResponses = []; // Store last few API responses

// Before a request is made, start NProgress
http.interceptors.request.use((config) => {
    NProgress.start();
    
    // use setInterval or setTimeout
    const arrNotUpdateLastActive = [
      '/fetch-setting-by-key',
      '/language',
      '/setting/name',
      '/barcode/fetch-history-dtf',
      '/barcode/fetch-dtf',
      '/barcode/count-dtf-pending-by-order-type',
      'department/list-employee-checkin',
      '/back-log/fetch',
      '/employee-performance-report',
      '/manifest/pending',
      'back-log/',
      '/barcode',
      '/3d/count',
      '/3d/pdf',
      '/neck',
      '/barcode/count-by-order-type',
      '/barcode/fetch-by-order-type',
      '/barcode/count-priority-store',
      '/emb-wip',
      '/mug/count',
      '/mug/count-pending-priority-store',
      '/mug/pdf',
      '/ornament/count-pdf',
      '/ornament/list-pdf',
      '/sticker/list',
      '/sticker/count',
      '/ornament/count',
      '/ornament/count-pending-priority-store',
      '/ornament',
      '/poster/count',
      '/poster/pdf'
    ];
    if (config?.url && !arrNotUpdateLastActive.includes(config?.url)) {
      saveUserLastTimeActive();
    }

    return config;
});

// Before a response is returned, stop NProgress
http.interceptors.response.use(
    (response) => {
        NProgress.done();
        return response;
    },
    (error) => {
        NProgress.done();

        const currentRouteName = router.currentRoute.value.name;
        const authRouteNames = ["login"];
        
        if (!authRouteNames.includes(currentRouteName) &&
            error.response?.status === 401
        ) {
            if("Unauthorized, The IP has expired. Please contact the administrator." === error.response.data.error || "Unauthorized, Invalid Ip Address" === error.response.data.error) {
                window.localStorage.setItem(`IP_VIOLATION`, true);
            }

            EventBus.$emit("unauthorized");
            return;
        }
        
        return Promise.reject(error);
    }
);

export { lastResponses }; // Export responses
export default http;
