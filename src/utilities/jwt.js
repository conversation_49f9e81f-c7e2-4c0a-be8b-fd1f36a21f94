const ID_TOKEN_KEY = "access_token";
const ID_EXPIRES_TIME = "expires_time";

export const getToken = () => {
  return window.localStorage.getItem(ID_TOKEN_KEY);
};

export const saveToken = (token) => {
  window.localStorage.setItem(ID_TOKEN_KEY, token);
};

export const destroyToken = () => {
  window.localStorage.removeItem(ID_TOKEN_KEY);
};

export const setTokenExpires = (expiresTime) => {
  const newExpiresTime =
    parseInt(new Date().getTime() / 1000) + parseInt(expiresTime);
  window.localStorage.setItem(ID_EXPIRES_TIME, newExpiresTime);
};

export const getTokenExpires = () => {
  return window.localStorage.getItem(ID_EXPIRES_TIME);
};

export const destroyTokenExpires = () => {
  return window.localStorage.removeItem(ID_EXPIRES_TIME);
};

export default { getToken, saveToken, destroyToken };
