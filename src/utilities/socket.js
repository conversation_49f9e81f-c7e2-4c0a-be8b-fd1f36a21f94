import io from 'socket.io-client';

//Init Socket service
const URL = import.meta.env.VITE_WS_URL;

const socket = io(URL, {
    autoConnect: false,
});

export function socketClient() {
    socket.on('connect', () => {
        console.log('Connected to Socket.io server');
    });

    socket.on('disconnect', () => {
        console.log('Disconnected from Socket.io server');

    });

    socket.on('message', (message) => {
        console.log('message', message);
    });

    const asyncConnect = async () => {
        socket.connect();
    }

    return {
        socket,
        asyncConnect,
    };
}