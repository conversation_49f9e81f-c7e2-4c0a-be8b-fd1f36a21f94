import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import { resolve } from "path";
import svgicon from "vite-plugin-svgicon";

// https://vitejs.dev/config/
export default defineConfig({
    plugins: [
        vue(),
        svgicon({
            include: ["**/svg-icon/**/*.svg"],
        }),
    ],
    resolve: {
        alias: [{
                find: "@",
                replacement: resolve(__dirname, "src"),
            },
            {
                find: "~",
                replacement: resolve(__dirname, "node_modules"),
            },
            {
                find: "vue",
                replacement: "vue/dist/vue.esm-bundler.js",
            },
        ],
    },
});